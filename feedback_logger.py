"""
Phase 13 Step 2: Feedback Registry Format
Structured feedback logging with consistent schema for CLI and VS Code extension.
"""

import json
import logging
import os
from dataclasses import dataclass, asdict
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

# Import shared intelligence paths
try:
    from intel_paths import get_feedback_log_path
except ImportError:
    # Fallback for legacy systems
    def get_feedback_log_path():
        return "feedback_log.json"

@dataclass
class FeedbackEntry:
    """
    Structured feedback entry for consistent logging across CLI and Extension.
    
    Schema matches Phase 13 specification for unified intelligence sharing.
    """
    file: str                    # Target file path
    timestamp: str              # ISO format timestamp
    prompt: str                 # Original user prompt
    model: str                  # AI model used (e.g., "Mixtral", "GPT-4")
    output_score: int           # Quality score (0-100)
    rating: int                 # User rating (1-5 stars)
    tone: str                   # Tone setting (neutral, friendly, assertive, formal)
    fallback_used: bool         # Whether fallback logic was triggered
    
    # Optional fields for extended metadata
    injection_type: Optional[str] = None        # Type of injection (preview, apply)
    tags: Optional[List[str]] = None           # Associated tags
    execution_time: Optional[float] = None     # Time taken for injection
    feedback_text: Optional[str] = None        # User feedback comments
    shaping_applied: Optional[bool] = None     # Whether prompt shaping was applied
    confidence_score: Optional[float] = None   # Confidence in the output
    
    def __post_init__(self):
        """Validate and normalize fields after initialization."""
        # Ensure timestamp is in ISO format
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()
        
        # Validate rating range
        if not (1 <= self.rating <= 5):
            logging.warning(f"Invalid rating {self.rating}, clamping to 1-5 range")
            self.rating = max(1, min(5, self.rating))
        
        # Validate output score range
        if not (0 <= self.output_score <= 100):
            logging.warning(f"Invalid output_score {self.output_score}, clamping to 0-100 range")
            self.output_score = max(0, min(100, self.output_score))
        
        # Normalize tone to lowercase
        self.tone = self.tone.lower() if self.tone else "neutral"
        
        # Ensure tags is a list
        if self.tags is None:
            self.tags = []
        elif isinstance(self.tags, str):
            self.tags = [self.tags]


class FeedbackLogger:
    """
    Centralized feedback logging system with structured entries.
    Supports both CLI and VS Code extension with unified schema.
    """
    
    def __init__(self, log_path: Optional[str] = None):
        """
        Initialize feedback logger.
        
        Args:
            log_path (Optional[str]): Custom log file path. Uses shared intel path if None.
        """
        self.log_path = log_path or get_feedback_log_path()
        self._ensure_log_file_exists()
    
    def _ensure_log_file_exists(self) -> None:
        """Ensure the feedback log file exists and is properly initialized."""
        try:
            # Create directory if it doesn't exist
            log_dir = os.path.dirname(self.log_path)
            if log_dir:
                Path(log_dir).mkdir(parents=True, exist_ok=True)
            
            # Initialize empty log file if it doesn't exist
            if not os.path.exists(self.log_path):
                with open(self.log_path, 'w', encoding='utf-8') as f:
                    json.dump([], f, indent=2)
                logging.info(f"Initialized feedback log: {self.log_path}")
            
            # Validate existing log file
            else:
                try:
                    with open(self.log_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    if not isinstance(data, list):
                        raise ValueError("Log file must contain a JSON array")
                except (json.JSONDecodeError, ValueError) as e:
                    logging.warning(f"Invalid log file format, reinitializing: {e}")
                    with open(self.log_path, 'w', encoding='utf-8') as f:
                        json.dump([], f, indent=2)
        
        except Exception as e:
            logging.error(f"Failed to ensure log file exists: {e}")
            raise
    
    def log_feedback(self, entry: FeedbackEntry) -> bool:
        """
        Log a structured feedback entry.
        
        Args:
            entry (FeedbackEntry): Structured feedback entry to log
            
        Returns:
            bool: True if logging was successful, False otherwise
        """
        try:
            # Convert entry to dictionary
            entry_dict = asdict(entry)
            
            # Load existing entries
            entries = self.load_all_entries()
            
            # Add new entry
            entries.append(entry_dict)
            
            # Save back to file
            with open(self.log_path, 'w', encoding='utf-8') as f:
                json.dump(entries, f, indent=2, ensure_ascii=False)
            
            logging.debug(f"Logged feedback entry for {entry.file}")
            return True
            
        except Exception as e:
            logging.error(f"Failed to log feedback entry: {e}")
            return False
    
    def load_all_entries(self) -> List[Dict[str, Any]]:
        """
        Load all feedback entries from the log file.
        
        Returns:
            List[Dict[str, Any]]: List of feedback entry dictionaries
        """
        try:
            with open(self.log_path, 'r', encoding='utf-8') as f:
                entries = json.load(f)
            
            if not isinstance(entries, list):
                logging.warning("Log file does not contain a list, returning empty list")
                return []
            
            return entries
            
        except FileNotFoundError:
            logging.info("Feedback log file not found, returning empty list")
            return []
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in feedback log: {e}")
            return []
        except Exception as e:
            logging.error(f"Failed to load feedback entries: {e}")
            return []
    
    def get_entries_by_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Get all feedback entries for a specific file.
        
        Args:
            file_path (str): File path to filter by
            
        Returns:
            List[Dict[str, Any]]: Filtered feedback entries
        """
        all_entries = self.load_all_entries()
        return [entry for entry in all_entries if entry.get('file') == file_path]
    
    def get_entries_by_model(self, model: str) -> List[Dict[str, Any]]:
        """
        Get all feedback entries for a specific model.
        
        Args:
            model (str): Model name to filter by
            
        Returns:
            List[Dict[str, Any]]: Filtered feedback entries
        """
        all_entries = self.load_all_entries()
        return [entry for entry in all_entries if entry.get('model') == model]
    
    def get_entries_by_rating(self, min_rating: int = 1, max_rating: int = 5) -> List[Dict[str, Any]]:
        """
        Get feedback entries within a rating range.
        
        Args:
            min_rating (int): Minimum rating (inclusive)
            max_rating (int): Maximum rating (inclusive)
            
        Returns:
            List[Dict[str, Any]]: Filtered feedback entries
        """
        all_entries = self.load_all_entries()
        return [
            entry for entry in all_entries 
            if min_rating <= entry.get('rating', 0) <= max_rating
        ]
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the feedback log.
        
        Returns:
            Dict[str, Any]: Statistics summary
        """
        entries = self.load_all_entries()
        
        if not entries:
            return {
                "total_entries": 0,
                "average_rating": 0,
                "average_output_score": 0,
                "models_used": [],
                "files_processed": [],
                "tone_distribution": {},
                "fallback_usage": 0
            }
        
        # Calculate statistics
        ratings = [entry.get('rating', 0) for entry in entries if entry.get('rating')]
        output_scores = [entry.get('output_score', 0) for entry in entries if entry.get('output_score')]
        models = [entry.get('model', '') for entry in entries if entry.get('model')]
        files = [entry.get('file', '') for entry in entries if entry.get('file')]
        tones = [entry.get('tone', '') for entry in entries if entry.get('tone')]
        fallbacks = [entry.get('fallback_used', False) for entry in entries]
        
        # Tone distribution
        tone_dist = {}
        for tone in tones:
            tone_dist[tone] = tone_dist.get(tone, 0) + 1
        
        return {
            "total_entries": len(entries),
            "average_rating": sum(ratings) / len(ratings) if ratings else 0,
            "average_output_score": sum(output_scores) / len(output_scores) if output_scores else 0,
            "models_used": list(set(models)),
            "files_processed": len(set(files)),
            "tone_distribution": tone_dist,
            "fallback_usage": sum(fallbacks)
        }
    
    def cleanup_old_entries(self, days_to_keep: int = 30) -> int:
        """
        Remove entries older than specified days.
        
        Args:
            days_to_keep (int): Number of days to keep entries
            
        Returns:
            int: Number of entries removed
        """
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            entries = self.load_all_entries()
            
            # Filter entries
            kept_entries = []
            removed_count = 0
            
            for entry in entries:
                try:
                    entry_date = datetime.fromisoformat(entry.get('timestamp', ''))
                    if entry_date >= cutoff_date:
                        kept_entries.append(entry)
                    else:
                        removed_count += 1
                except (ValueError, TypeError):
                    # Keep entries with invalid timestamps
                    kept_entries.append(entry)
            
            # Save filtered entries
            if removed_count > 0:
                with open(self.log_path, 'w', encoding='utf-8') as f:
                    json.dump(kept_entries, f, indent=2, ensure_ascii=False)
                logging.info(f"Cleaned up {removed_count} old feedback entries")
            
            return removed_count
            
        except Exception as e:
            logging.error(f"Failed to cleanup old entries: {e}")
            return 0


# Convenience functions for easy usage
def log_injection_feedback(
    file: str,
    prompt: str,
    model: str,
    output_score: int,
    rating: int,
    tone: str = "neutral",
    fallback_used: bool = False,
    **kwargs
) -> bool:
    """
    Convenience function to log injection feedback.
    
    Args:
        file (str): Target file path
        prompt (str): Original user prompt
        model (str): AI model used
        output_score (int): Quality score (0-100)
        rating (int): User rating (1-5)
        tone (str): Tone setting
        fallback_used (bool): Whether fallback was used
        **kwargs: Additional optional fields
        
    Returns:
        bool: True if logging was successful
    """
    try:
        logger = FeedbackLogger()
        entry = FeedbackEntry(
            file=file,
            timestamp=datetime.now().isoformat(),
            prompt=prompt,
            model=model,
            output_score=output_score,
            rating=rating,
            tone=tone,
            fallback_used=fallback_used,
            **kwargs
        )
        return logger.log_feedback(entry)
    except Exception as e:
        logging.error(f"Failed to log injection feedback: {e}")
        return False


def get_feedback_statistics() -> Dict[str, Any]:
    """
    Get feedback statistics using default logger.
    
    Returns:
        Dict[str, Any]: Statistics summary
    """
    try:
        logger = FeedbackLogger()
        return logger.get_statistics()
    except Exception as e:
        logging.error(f"Failed to get feedback statistics: {e}")
        return {}


# Export key classes and functions
__all__ = [
    'FeedbackEntry',
    'FeedbackLogger',
    'log_injection_feedback',
    'get_feedback_statistics'
]
