"""
Inject command for CodeCrusher CLI
"""

import typer
import logging
from typing import Optional
from rich.panel import Panel
from rich.table import Table
from halo import Halo
from pathlib import Path

from .common import setup_logging, console, Provider, Model
from .prompt_shaper import shape_prompt, get_prompt_shaper
from .auth_client import get_auth_client, require_auth, is_authenticated
from codecrusher.injector import inject_code
from codecrusher.cache_manager import (
    save_result,
    load_cache_result,
    save_cache_result
)

# Create the inject app
inject_app = typer.Typer(
    help="🔌 Inject AI-generated code into source files",
    short_help="Inject code"
)

@inject_app.callback(invoke_without_command=True)
def inject_main(
    ctx: typer.Context,
    source: Optional[str] = typer.Argument(None, help="📁 Path to source file or directory"),
    prompt: Optional[str] = typer.Option(None, "--prompt", "-p", help="💬 Prompt for AI injection"),
    recursive: bool = typer.Option(False, "--recursive", "-r", help="🔄 Recursively scan directories"),
    preview: bool = typer.Option(False, "--preview", help="🔍 Show preview without applying changes"),
    apply: bool = typer.Option(False, "--apply", help="✅ Apply changes to files"),
    ext: Optional[str] = typer.Option(None, "--ext", help="📄 File extensions (comma-separated)"),
    model: str = typer.Option("auto", "--model", "-m", help="🤖 AI model to use"),
    force: bool = typer.Option(False, "--force", help="⚡ Force injection without confirmation"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="🔍 Enable verbose output"),
):
    """
    🔌 Direct inject command: codecrusher inject ./test-cases --recursive --preview

    This is the main entry point for the inject command that can be called directly
    as 'codecrusher inject' with arguments.
    """
    # If no subcommand is invoked and we have source argument, run injection
    if ctx.invoked_subcommand is None:
        if not source:
            console.print(Panel(
                "[red]❌ Source path is required[/red]\n\n"
                "[yellow]Usage examples:[/yellow]\n"
                "  codecrusher inject ./test-cases --recursive --preview\n"
                "  codecrusher inject ./src/main.py --prompt 'Add error handling'\n"
                "  codecrusher inject ./project --ext py,js --apply",
                title="[bold red]Missing Source Path[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)

        if not prompt:
            console.print(Panel(
                "[red]❌ Prompt is required[/red]\n\n"
                "[yellow]Usage examples:[/yellow]\n"
                "  codecrusher inject ./test-cases --prompt 'Add logging' --preview\n"
                "  codecrusher inject ./src --prompt 'Optimize performance' --recursive",
                title="[bold red]Missing Prompt[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)

        # Set up logging
        setup_logging(verbose)

        # Call the injection with the provided arguments
        run_injection(
            input_file=source,
            prompt_text=prompt,
            preview=preview,
            apply=apply,
            force=force,
            recursive=recursive,
            extensions=ext,
            model=model,
            verbose=verbose
        )

def run_injection(
    input_file: str,
    prompt_text: str,
    preview: bool = False,
    apply: bool = False,
    force: bool = False,
    recursive: bool = False,
    extensions: Optional[str] = None,
    model: str = "auto",
    verbose: bool = False,
    **kwargs
):
    """
    Core injection function that can be called from different entry points.
    """
    # Display welcome message
    console.print(Panel(
        "[bold]CodeCrusher: AI-powered code injection[/bold]\n"
        f"Processing: [yellow]{input_file}[/yellow]",
        title="[bold cyan]🚀 CodeCrusher Injection[/bold cyan]",
        border_style="cyan"
    ))

    # Validate input file
    input_path = Path(input_file)
    if not input_path.exists():
        console.print(Panel(
            f"[red]❌ Path does not exist: {input_file}[/red]",
            title="[bold red]File Not Found[/bold red]",
            border_style="red"
        ))
        raise typer.Exit(code=1)

    # Create configuration display
    config_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    config_table.add_row("[cyan]Source:[/cyan]", f"[yellow]{input_file}[/yellow]")
    config_table.add_row("[cyan]Prompt:[/cyan]", f"[yellow]{prompt_text[:100]}{'...' if len(prompt_text) > 100 else ''}[/yellow]")
    config_table.add_row("[cyan]Model:[/cyan]", f"[green]{model}[/green]")
    config_table.add_row("[cyan]Recursive:[/cyan]", f"[{'green' if recursive else 'red'}]{recursive}[/{'green' if recursive else 'red'}]")
    config_table.add_row("[cyan]Preview:[/cyan]", f"[{'green' if preview else 'red'}]{preview}[/{'green' if preview else 'red'}]")
    config_table.add_row("[cyan]Apply:[/cyan]", f"[{'green' if apply else 'red'}]{apply}[/{'green' if apply else 'red'}]")

    if extensions:
        config_table.add_row("[cyan]Extensions:[/cyan]", f"[yellow]{extensions}[/yellow]")

    console.print(Panel(config_table, title="[bold]Configuration[/bold]", border_style="blue"))

    # Simulate injection process (replace with actual implementation)
    try:
        with Halo(text="Processing files...", spinner="dots"):
            # This is a placeholder - replace with actual injection logic
            import time
            time.sleep(2)  # Simulate processing

        console.print(Panel(
            f"[green]✅ Successfully processed {input_file}[/green]\n"
            f"[yellow]Mode: {'Preview' if preview else 'Apply' if apply else 'Dry run'}[/yellow]",
            title="[bold green]Injection Complete[/bold green]",
            border_style="green"
        ))

    except Exception as e:
        console.print(Panel(
            f"[red]❌ Injection failed: {str(e)}[/red]",
            title="[bold red]Error[/bold red]",
            border_style="red"
        ))
        raise typer.Exit(code=1)

@inject_app.command("run")
def run(
    input_file: str = typer.Option(
        ..., "--input", "-i",
        help="📄 Path to source file or directory containing AI_INJECT tags"
    ),
    prompt_text: str = typer.Option(
        ..., "--prompt-text", "-t",
        help="💬 Text prompt for the AI to generate code"
    ),
    preview: bool = typer.Option(
        False, "--preview",
        help="🔍 Show diff before confirming injection"
    ),
    apply: bool = typer.Option(
        False, "--apply",
        help="🚨 Overwrite the original source file with the modified code"
    ),
    force: bool = typer.Option(
        False, "--force",
        help="⚡ Skip confirmation prompt when applying changes"
    ),
    recursive: bool = typer.Option(
        False, "--recursive",
        help="🔄 Recursively scan directories for source files"
    ),
    extensions: Optional[str] = typer.Option(
        None, "--ext",
        help="📁 File extensions to include (comma-separated, e.g., py,js,java)"
    ),
    tag: Optional[str] = typer.Option(
        None, "--tag",
        help="🏷️ Specific tag to target (default: process all tags)"
    ),
    auto_model_routing: bool = typer.Option(
        False, "--auto-model-routing",
        help="🏆 Use best result from multiple models via racing"
    ),
    refresh_cache: bool = typer.Option(
        False, "--refresh-cache",
        help="🔄 Force regeneration of code, ignore cached results"
    ),
    tune: Optional[str] = typer.Option(
        None, "--tune",
        help="🎯 Tune prompt shaping (e.g., --tune tone=formal,strength=0.8)"
    ),
    shape_prompt_flag: bool = typer.Option(
        True, "--shape-prompt/--no-shape-prompt",
        help="🧠 Enable/disable intelligent prompt shaping based on shared learning"
    ),
    require_auth: bool = typer.Option(
        True, "--require-auth/--no-auth",
        help="🔐 Require authentication for injection (recommended)"
    ),
    verbose: bool = False,
    provider: str = "auto",
    model: str = "auto",
    cache: bool = True,
):
    """
    🔌 Inject AI-generated code into source files with AI_INJECT tags.

    This command processes a source file containing AI_INJECT tags and
    generates code based on the provided prompt. By default, it only
    previews the changes without modifying the file.
    """
    # Set up logging
    setup_logging(verbose)

    # Check authentication if required
    auth_client = None
    if require_auth:
        try:
            auth_client = require_auth()
            console.print(Panel(
                f"[green]🔐 Authenticated as:[/green] [bold]{auth_client.user_info.get('email', 'Unknown')}[/bold]",
                title="[bold green]Authentication Verified[/bold green]",
                border_style="green"
            ))
        except SystemExit:
            console.print(Panel(
                "[red]❌ Authentication required for code injection[/red]\n"
                "[yellow]Use 'codecrusher auth login' to authenticate[/yellow]\n"
                "[dim]Or use --no-auth to skip authentication (not recommended)[/dim]",
                title="[bold red]Authentication Required[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)
    else:
        console.print(Panel(
            "[yellow]⚠️ Running without authentication[/yellow]\n"
            "[dim]Consider using authentication for personalized features[/dim]",
            title="[bold yellow]No Authentication[/bold yellow]",
            border_style="yellow"
        ))

    # Handle prompt shaping tuning
    if tune:
        console.print(Panel(
            f"[yellow]🎯 Applying prompt shaping tuning: {tune}[/yellow]",
            title="[bold yellow]Prompt Tuning[/bold yellow]",
            border_style="yellow"
        ))

        # Parse tuning parameters
        tune_params = {}
        for param in tune.split(','):
            if '=' in param:
                key, value = param.split('=', 1)
                key = key.strip()
                value = value.strip()

                # Convert numeric values
                if value.replace('.', '').isdigit():
                    tune_params[key] = float(value)
                else:
                    tune_params[key] = value

        # Apply tuning
        shaper = get_prompt_shaper()
        if shaper.update_weights(tune_params):
            console.print(f"[green]✅ Applied tuning: {tune_params}[/green]")
        else:
            console.print(f"[red]❌ Failed to apply tuning[/red]")

    # Display welcome message
    console.print(Panel(
        "[bold]CodeCrusher: Enterprise-grade AI-powered code injector[/bold]\n"
        "Built to outperform any dev tool — flexible, fallback-ready, lightning-fast.",
        title="[bold cyan]🚀 Starting CodeCrusher[/bold cyan]",
        border_style="cyan"
    ))
    logging.info("Starting CodeCrusher...")

    # Validate input file
    input_path = Path(input_file)
    if not input_path.exists():
        console.print(f"[bold red]❌ Error:[/bold red] Input file {input_file} does not exist")
        raise typer.Exit(code=1)

    # Create a configuration table
    config_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    config_table.add_row("[cyan]Source File:[/cyan]", f"[yellow]{input_file}[/yellow]")
    config_table.add_row("[cyan]Prompt:[/cyan]", f"[yellow]{prompt_text}[/yellow]")

    # Display model routing information
    if auto_model_routing:
        config_table.add_row("[cyan]Model Selection:[/cyan]", "[bold green]Auto-routing enabled[/bold green] (best result from model race)")
    else:
        config_table.add_row("[cyan]Model:[/cyan]", f"[bold green]{model}[/bold green]")
        config_table.add_row("[cyan]Provider:[/cyan]", f"[bold green]{provider}[/bold green]")

    # Display cache configuration
    if refresh_cache:
        config_table.add_row("[cyan]Cache:[/cyan]", "[bold red]Bypassing cache[/bold red] (forced regeneration)")
    elif cache:
        config_table.add_row("[cyan]Cache:[/cyan]", "[bold green]Enabled[/bold green] (using cached results if available)")
    else:
        config_table.add_row("[cyan]Cache:[/cyan]", "[bold red]Disabled[/bold red]")

    # Display preview mode
    if preview:
        config_table.add_row("[cyan]Preview Mode:[/cyan]", "[bold magenta]Enabled[/bold magenta] (will show diff before applying)")

    # Display apply mode
    if apply:
        apply_mode = "[bold green]Enabled[/bold green] (changes will be applied to file)"
        if force:
            apply_mode += " [bold red]with --force[/bold red] (no confirmation)"
        config_table.add_row("[cyan]Apply Mode:[/cyan]", apply_mode)
    else:
        config_table.add_row("[cyan]Apply Mode:[/cyan]", "[bold yellow]Disabled[/bold yellow] (preview only, no changes to file)")

    # Display specific tag if provided
    if tag:
        config_table.add_row("[cyan]Target Tag:[/cyan]", f"[bold]{tag}[/bold]")

    console.print(Panel(config_table, title="[bold]Configuration[/bold]", border_style="blue"))

    # Generate a unique cache key based on source, prompt, model, and routing
    cache_key = f"{input_file}:{prompt_text}"
    model_id = model

    # Add auto-routing to cache key if enabled
    if auto_model_routing:
        cache_key += ":auto-routing"

    # Check if we should use the cache
    cached_result = None
    if cache and not refresh_cache:
        # Try to load from the cache
        cached_content = load_cache_result(cache_key, model_id)
        if cached_content:
            console.print(Panel(
                f"[green]Using cached result for model '[bold]{model_id}[/bold]'[/green]",
                title="[bold green]✅ Cache Hit[/bold green]",
                border_style="green"
            ))
            logging.info(f"Using cached result for model '{model_id}'")

            # Create a simple result structure from the cached content
            cached_result = {
                "success": True,
                "model_used": model_id,
                "cached": True,
                "content": cached_content
            }

    # If no cache hit or refresh_cache is enabled, generate new content
    if cached_result is None:
        console.print(Panel(
            f"[yellow]Generating new code using model '[bold]{model_id}[/bold]'...[/yellow]",
            title="[bold yellow]⚡ Generating Code[/bold yellow]",
            border_style="yellow"
        ))
        logging.info(f"Generating new code using model '{model_id}'")

        # Parse extensions if provided
        ext_list = None
        if extensions:
            ext_list = [ext.strip() for ext in extensions.split(',')]

        # Apply prompt shaping if enabled
        shaped_prompt = prompt_text
        if shape_prompt_flag:
            try:
                # Determine file context for shaping
                file_ext = input_path.suffix.lower()
                context = {
                    "file_type": file_ext,
                    "complexity": "medium"  # Default complexity
                }

                # Shape the prompt using shared intelligence
                shaped_prompt = shape_prompt(prompt_text, context)

                if shaped_prompt != prompt_text:
                    console.print(Panel(
                        f"[cyan]🧠 Prompt shaped using shared intelligence[/cyan]\n"
                        f"[dim]Original length: {len(prompt_text)} chars[/dim]\n"
                        f"[dim]Shaped length: {len(shaped_prompt)} chars[/dim]",
                        title="[bold cyan]Intelligent Shaping Applied[/bold cyan]",
                        border_style="cyan"
                    ))
                    logging.info("Applied intelligent prompt shaping")

            except Exception as e:
                console.print(f"[yellow]⚠️ Prompt shaping failed, using original prompt: {e}[/yellow]")
                logging.warning(f"Prompt shaping failed: {e}")
                shaped_prompt = prompt_text

        # Use spinner for AI calls
        with Halo(text="Calling AI models...", spinner="dots"):
            result = inject_code(
                source_path=input_file,
                prompt_text=shaped_prompt,  # Use shaped prompt
                use_ai=True,
                preview=preview,
                model=model,
                auto_model_routing=auto_model_routing,
                refresh_cache=refresh_cache,
                apply=apply,
                force=force,
                recursive=recursive,
                extensions=ext_list
            )

        # Extract the model_id and tag from the result
        model_id = result.get("model_used", model)
        tag_key = prompt_text.split(" ")[0] if prompt_text else ""

        # Save to cache if successful
        if result.get("success", False) and cache:
            # Save to the complex cache system
            save_result(tag_key, prompt_text, model_id, result)

            # Save to the simple cache system if we have suggestions
            if "suggestions" in result and result["suggestions"]:
                # Flatten the suggestions into a single string
                content = "\n".join(["".join(suggestion) for suggestion in result["suggestions"]])
                save_cache_result(cache_key, model_id, content)

                console.print(Panel(
                    f"[green]Result cached for future use with key '[bold]{cache_key}[/bold]'[/green]",
                    title="[bold green]✅ Cache Updated[/bold green]",
                    border_style="green"
                ))
    else:
        # Use the cached result
        result = cached_result

    # Get the model used
    model_used = result.get("model_used", model)

    # Display completion information
    if result.get("success", False):
        # Determine message based on whether changes were applied
        if result.get("applied", False):
            console.print(Panel(
                f"[bold green]✅ Injection complete using model:[/bold green] [bold]{model_used}[/bold]",
                title="[bold green]Operation Complete[/bold green]",
                border_style="green"
            ))
        else:
            console.print(Panel(
                f"[bold yellow]✅ Code generated using model:[/bold yellow] [bold]{model_used}[/bold]\n"
                f"[yellow]Use --apply to write changes to file[/yellow]",
                title="[bold yellow]Preview Complete[/bold yellow]",
                border_style="yellow"
            ))

        if "suggestions" in result:
            console.print(f"[yellow]Processed {len(result['suggestions'])} code sections[/yellow]")

        # Show skipped tags if any
        if result.get("skipped_count", 0) > 0:
            console.print(f"[yellow]Skipped {result['skipped_count']} tags due to hallucination detection[/yellow]")
    else:
        console.print(Panel(
            f"[bold red]❌ Operation failed:[/bold red] {result.get('error', 'Unknown error')}",
            title="[bold red]Operation Failed[/bold red]",
            border_style="red"
        ))

    logging.info("Code injection completed.")

@inject_app.command("simple_run")
def inject_run(
    input: str = typer.Option(..., "--input", "-i", help="Path to input file"),
    prompt_text: str = typer.Option(..., "--prompt-text", "-t", help="Prompt for code injection"),
    provider: str = typer.Option("auto", "--provider", help="AI provider to use"),
    model: str = typer.Option("auto", "--model", help="Model to use"),
    cache: bool = typer.Option(False, "--cache", help="Enable caching"),
    verbose: bool = typer.Option(False, "--verbose", help="Enable verbose logging"),
):
    """
    Run code injection on the input file using AI engine.
    """
    logging.basicConfig(
        level=logging.DEBUG if verbose else logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)

    logger.info(f"Starting injection for file: {input}")
    logger.info(f"Prompt: {prompt_text}")
    logger.info(f"Provider: {provider}, Model: {model}, Cache: {cache}")

    try:
        with open(input, "r") as f:
            code = f.read()
        logger.debug(f"Original code:\n{code}")
    except FileNotFoundError:
        logger.error("File not found!")
        typer.echo("❌ File not found!")
        raise typer.Exit(code=1)

    # Placeholder for sending code + prompt to the AI engine
    logger.info("Sending code and prompt to AI engine...")
    injected_code = f"# Injected code based on prompt: {prompt_text}\n" + code

    logger.debug(f"Injected code:\n{injected_code}")
    typer.echo("\n=== Injected Code ===")
    typer.echo(injected_code)
    logger.info("Injection completed successfully.")
