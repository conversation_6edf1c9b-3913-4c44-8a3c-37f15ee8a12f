{"version": 3, "file": "lensProvider.js", "sourceRoot": "", "sources": ["../src/lensProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,iCAAiC;AACjC,+BAAuD;AAEvD;;GAEG;AACH,SAAgB,cAAc,CAAC,OAAgC;IAC3D,MAAM,YAAY,GAAG,IAAI,uBAAuB,EAAE,CAAC;IAEnD,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,SAAS,CAAC,wBAAwB,CACrC,EAAE,MAAM,EAAE,MAAM,EAAE,EAClB,YAAY,CACf,CACJ,CAAC;IAEF,yCAAyC;IACzC,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,oBAAoB,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC9F,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;IAE9E,kBAAkB;IAClB,YAAY,CAAC,oBAAoB,EAAE,CAAC;IAEpC,OAAO,YAAY,CAAC;AACxB,CAAC;AAlBD,wCAkBC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,MAAM,CAAC,QAAQ;IAChD,YACI,KAAmB,EACH,cAA8B,EAC9C,OAAwB;QAExB,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAHN,mBAAc,GAAd,cAAc,CAAgB;IAIlD,CAAC;CACJ;AARD,0CAQC;AAED;;GAEG;AACH,MAAa,uBAAuB;IAApC;QACY,2BAAsB,GAA8B,IAAI,MAAM,CAAC,YAAY,EAAQ,CAAC;QAC5E,0BAAqB,GAAuB,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;QACtF,sBAAiB,GAAqB,EAAE,CAAC;IAyGrD,CAAC;IAvGG;;OAEG;IACU,oBAAoB;;YAC7B,IAAI;gBACA,6BAA6B;gBAC7B,MAAM,eAAe,GAAG,MAAM,oBAAc,CAAC,eAAe,EAAE,CAAC;gBAC/D,IAAI,CAAC,eAAe,EAAE;oBAClB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;oBAC5B,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;oBACnC,OAAO;iBACV;gBAED,qBAAqB;gBACrB,MAAM,aAAa,GAAG,MAAM,oBAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC7D,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,OAAO,CAAC;gBAC/C,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;aACtC;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACzD,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBAC5B,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;aACtC;QACL,CAAC;KAAA;IAED;;OAEG;IACU,iBAAiB,CAC1B,QAA6B,EAC7B,KAA+B;;YAE/B,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrC,OAAO,EAAE,CAAC;aACb;YAED,MAAM,UAAU,GAAsB,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;YAErC,uCAAuC;YACvC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACtD,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,WAAW;gBACpC,CAAC,KAAK,CAAC,SAAS,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CACvE,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;gBAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;oBACpB,SAAS;iBACZ;gBAED,4DAA4D;gBAC5D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;gBAEzC,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,QAAQ,CAAC,SAAS,EAAE;oBACpD,SAAS;iBACZ;gBAED,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACzC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,EAClC,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CACpD,CAAC;gBAEF,kCAAkC;gBAClC,MAAM,OAAO,GAAmB;oBAC5B,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;oBACnC,OAAO,EAAE,kCAAkC;oBAC3C,SAAS,EAAE,CAAC,KAAK,CAAC;iBACrB,CAAC;gBAEF,UAAU,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;aAC/D;YAED,OAAO,UAAU,CAAC;QACtB,CAAC;KAAA;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAqB;QAC1C,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,iBAAiB;QACjB,KAAK,CAAC,IAAI,CAAC,UAAU,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;QAEpC,kCAAkC;QAClC,IAAI,KAAK,CAAC,QAAQ,EAAE;YAChB,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAClC;QAED,+BAA+B;QAC/B,IAAI,KAAK,CAAC,KAAK,EAAE;YACb,KAAK,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;SAC7D;QAED,4BAA4B;QAC5B,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAChD;aAAM;YACH,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC5B;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;CACJ;AA5GD,0DA4GC"}