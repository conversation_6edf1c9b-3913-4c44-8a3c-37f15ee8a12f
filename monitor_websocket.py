#!/usr/bin/env python3
"""
Monitor WebSocket connection stability for CodeCrusher dashboard.
"""

import asyncio
import websockets
import time
from datetime import datetime

async def monitor_websocket():
    """Monitor WebSocket connection for stability."""
    
    url = "ws://127.0.0.1:8001/ws/logs"
    connection_count = 0
    total_messages = 0
    start_time = time.time()
    
    print("🔍 WebSocket Connection Monitor")
    print("=" * 50)
    print(f"📡 Monitoring: {url}")
    print(f"🕒 Started: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 50)
    
    while True:
        try:
            connection_count += 1
            print(f"\n🔌 Connection attempt #{connection_count}")
            
            async with websockets.connect(url) as websocket:
                print(f"✅ Connected successfully at {datetime.now().strftime('%H:%M:%S')}")
                
                # Monitor for 30 seconds
                timeout_count = 0
                while timeout_count < 30:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        total_messages += 1
                        print(f"📨 Message #{total_messages}: {message[:50]}...")
                        timeout_count = 0  # Reset timeout counter
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        if timeout_count % 10 == 0:
                            print(f"⏰ No messages for {timeout_count} seconds...")
                
                print(f"✅ Connection stable for 30 seconds")
                
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            print("🔄 Retrying in 5 seconds...")
            await asyncio.sleep(5)
        
        # Show statistics
        elapsed = time.time() - start_time
        print(f"\n📊 Statistics:")
        print(f"   ⏱️  Runtime: {elapsed:.1f}s")
        print(f"   🔗 Connections: {connection_count}")
        print(f"   📨 Messages: {total_messages}")
        print(f"   📈 Avg messages/min: {(total_messages / elapsed * 60):.1f}")
        
        # Wait before next connection test
        await asyncio.sleep(10)

if __name__ == "__main__":
    try:
        asyncio.run(monitor_websocket())
    except KeyboardInterrupt:
        print("\n⚠️ Monitoring stopped by user")
    except Exception as e:
        print(f"\n💥 Monitor failed: {e}")
