<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Direct Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>🧪 WebSocket Direct Connection Test</h1>
    <p>Testing direct connection to <code>ws://127.0.0.1:8001/ws/logs</code></p>

    <button onclick="testConnection()">🔗 Test Connection</button>
    <button onclick="testHealth()">🏥 Test Health Endpoint</button>
    <button onclick="testWebSocketHealth()">🧪 Test WebSocket Health</button>
    <button onclick="clearLogs()">🧹 Clear Logs</button>

    <div id="logs"></div>

    <script>
        let ws = null;
        let logCount = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `[${timestamp}] ${message}`;
            document.getElementById('logs').appendChild(logDiv);
            logDiv.scrollIntoView();
            console.log(`[${timestamp}] ${message}`);
        }

        function testConnection() {
            if (ws) {
                ws.close();
            }

            log('🔗 Attempting direct WebSocket connection...', 'info');
            log('🌐 URL: ws://127.0.0.1:8001/ws/logs', 'info');

            try {
                ws = new WebSocket('ws://127.0.0.1:8001/ws/logs');

                ws.onopen = function(event) {
                    log('✅ WebSocket connection opened successfully!', 'success');
                    log('🎯 Ready to receive messages', 'success');
                };

                ws.onmessage = function(event) {
                    log(`📨 Message received: ${event.data}`, 'success');
                };

                ws.onclose = function(event) {
                    log(`🔌 WebSocket closed (code: ${event.code}, reason: ${event.reason || 'No reason'})`, 'info');
                    log(`📊 Was clean close: ${event.wasClean}`, 'info');
                };

                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    log('💡 Check if backend is running on port 8001', 'error');
                };

            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error}`, 'error');
            }
        }

        function testHealth() {
            log('🏥 Testing health endpoint...', 'info');

            fetch('http://127.0.0.1:8001/health')
                .then(response => response.json())
                .then(data => {
                    log(`✅ Health check successful: ${JSON.stringify(data)}`, 'success');
                })
                .catch(error => {
                    log(`❌ Health check failed: ${error}`, 'error');
                });
        }

        function testWebSocketHealth() {
            log('🧪 Testing WebSocket health endpoint...', 'info');
            log('🌐 URL: ws://127.0.0.1:8001/ws/health', 'info');

            try {
                const healthWs = new WebSocket('ws://127.0.0.1:8001/ws/health');

                healthWs.onopen = function(event) {
                    log('✅ WebSocket health connection opened!', 'success');
                };

                healthWs.onmessage = function(event) {
                    log(`📨 Health response: ${event.data}`, 'success');

                    try {
                        const response = JSON.parse(event.data);
                        if (response.status === 'ok' && response.message === 'WebSocket working') {
                            log('🎉 WebSocket health check PASSED!', 'success');
                        } else {
                            log('⚠️ Unexpected health response format', 'error');
                        }
                    } catch (e) {
                        log('⚠️ Health response is not valid JSON', 'error');
                    }
                };

                healthWs.onclose = function(event) {
                    log(`🔌 WebSocket health closed (code: ${event.code}, reason: ${event.reason || 'Health check complete'})`, 'info');
                };

                healthWs.onerror = function(error) {
                    log(`❌ WebSocket health error: ${error}`, 'error');
                };

            } catch (error) {
                log(`❌ Failed to create WebSocket health connection: ${error}`, 'error');
            }
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            logCount = 0;
        }

        // Auto-test on page load
        window.onload = function() {
            log('🚀 WebSocket Direct Test Page Loaded', 'info');
            log('👆 Click "Test Connection" to start WebSocket test', 'info');
        };
    </script>
</body>
</html>
