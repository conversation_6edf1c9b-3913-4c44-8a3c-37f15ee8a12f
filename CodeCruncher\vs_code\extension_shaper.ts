/**
 * VS Code Extension Prompt Shaper - Shared Intelligence Integration
 * 
 * This module provides prompt shaping functionality for the VS Code extension
 * that integrates with the shared intelligence system via API endpoints.
 */

interface PromptWeights {
  tone: string;
  fallback_level: number;
  model_hint: string;
  shaping_strength: number;
  fallback_sensitivity: number;
  last_updated?: string;
  version?: string;
}

interface ShapingContext {
  fileType?: string;
  complexity?: 'low' | 'medium' | 'high';
  language?: string;
  projectType?: string;
}

export class ExtensionPromptShaper {
  private weights: PromptWeights | null = null;
  private lastFetch: number = 0;
  private cacheTimeout: number = 30000; // 30 seconds
  private apiBaseUrl: string;

  constructor(apiBaseUrl: string = 'http://localhost:8001') {
    this.apiBaseUrl = apiBaseUrl;
  }

  /**
   * Load prompt weights from shared intelligence API
   */
  private async loadWeights(): Promise<PromptWeights> {
    const now = Date.now();
    
    // Use cached weights if recent
    if (this.weights && (now - this.lastFetch) < this.cacheTimeout) {
      return this.weights;
    }

    try {
      const response = await fetch(`${this.apiBaseUrl}/api/intel/shaping/weights`);
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const result = await response.json();
      this.weights = result.weights;
      this.lastFetch = now;
      
      console.log('🧠 Loaded prompt weights from shared intelligence:', this.weights);
      return this.weights;
      
    } catch (error) {
      console.warn('⚠️ Failed to load weights from API, using fallback:', error);
      
      // Fallback weights
      const fallbackWeights: PromptWeights = {
        tone: 'default',
        fallback_level: 1,
        model_hint: 'mixtral',
        shaping_strength: 0.5,
        fallback_sensitivity: 0.7
      };
      
      this.weights = fallbackWeights;
      return fallbackWeights;
    }
  }

  /**
   * Shape a prompt based on shared intelligence weights
   */
  async shapePrompt(basePrompt: string, context?: ShapingContext): Promise<string> {
    if (!basePrompt) {
      return basePrompt;
    }

    const weights = await this.loadWeights();
    let shapedPrompt = basePrompt;

    // Apply tone-based shaping
    if (weights.tone !== 'default' && weights.shaping_strength > 0.3) {
      shapedPrompt = this.applyTone(shapedPrompt, weights.tone, weights.shaping_strength);
    }

    // Apply model-specific hints
    if (weights.model_hint && weights.model_hint !== 'default') {
      shapedPrompt = this.applyModelHints(shapedPrompt, weights.model_hint);
    }

    // Apply context-specific shaping
    if (context) {
      shapedPrompt = this.applyContext(shapedPrompt, context);
    }

    console.log(`🎯 Shaped prompt with tone=${weights.tone}, model=${weights.model_hint}, strength=${weights.shaping_strength}`);
    return shapedPrompt;
  }

  /**
   * Apply tone-based shaping to the prompt
   */
  private applyTone(prompt: string, tone: string, strength: number): string {
    const toneModifiers: Record<string, { prefix: string; suffix: string }> = {
      formal: {
        prefix: 'Please provide a professional and detailed response. ',
        suffix: ' Ensure the output follows enterprise coding standards.'
      },
      assertive: {
        prefix: 'Generate a confident and decisive solution. ',
        suffix: ' Be direct and specific in your implementation.'
      },
      friendly: {
        prefix: 'Help me with this in a clear and approachable way. ',
        suffix: ' Make the solution easy to understand and implement.'
      },
      neutral: {
        prefix: '',
        suffix: ''
      }
    };

    const modifier = toneModifiers[tone] || toneModifiers.neutral;

    // Apply strength scaling
    if (strength < 0.5) {
      // Light shaping - only suffix
      return prompt + modifier.suffix;
    } else {
      // Full shaping - prefix and suffix
      return modifier.prefix + prompt + modifier.suffix;
    }
  }

  /**
   * Apply model-specific hints to optimize for the target model
   */
  private applyModelHints(prompt: string, modelHint: string): string {
    const modelOptimizations: Record<string, string> = {
      'gpt-4': '\n\nOptimize for GPT-4\'s advanced reasoning capabilities.',
      'mixtral': '\n\nProvide clear, structured output suitable for Mixtral processing.',
      'llama': '\n\nUse explicit instructions and clear formatting for Llama models.',
      'claude': '\n\nLeverage Claude\'s analytical strengths with detailed explanations.'
    };

    const hint = modelOptimizations[modelHint.toLowerCase()] || '';
    return prompt + hint;
  }

  /**
   * Apply context-specific shaping
   */
  private applyContext(prompt: string, context: ShapingContext): string {
    let contextualPrompt = prompt;

    // File type specific shaping
    if (context.fileType) {
      const fileType = context.fileType.toLowerCase();
      
      if (['.py', '.python'].includes(fileType)) {
        contextualPrompt += '\n\nEnsure Python best practices and PEP 8 compliance.';
      } else if (['.js', '.ts', '.javascript', '.typescript'].includes(fileType)) {
        contextualPrompt += '\n\nFollow modern JavaScript/TypeScript conventions.';
      } else if (fileType === '.java') {
        contextualPrompt += '\n\nAdhere to Java coding standards and design patterns.';
      } else if (['.tsx', '.jsx'].includes(fileType)) {
        contextualPrompt += '\n\nFollow React best practices and modern component patterns.';
      }
    }

    // Language specific shaping
    if (context.language) {
      contextualPrompt += `\n\nOptimize for ${context.language} language features and idioms.`;
    }

    // Complexity level shaping
    if (context.complexity) {
      switch (context.complexity) {
        case 'high':
          contextualPrompt += '\n\nProvide comprehensive solution with error handling and edge cases.';
          break;
        case 'low':
          contextualPrompt += '\n\nKeep the solution simple and straightforward.';
          break;
        // medium is default, no additional shaping needed
      }
    }

    return contextualPrompt;
  }

  /**
   * Update prompt shaping weights in shared intelligence
   */
  async updateWeights(updates: Partial<PromptWeights>): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/intel/shaping`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const result = await response.json();
      
      // Update cached weights
      this.weights = result.updated_weights;
      this.lastFetch = Date.now();
      
      console.log('✅ Updated prompt weights:', updates);
      return true;
      
    } catch (error) {
      console.error('❌ Failed to update prompt weights:', error);
      return false;
    }
  }

  /**
   * Get current prompt shaping weights
   */
  async getCurrentWeights(): Promise<PromptWeights> {
    return await this.loadWeights();
  }

  /**
   * Auto-tune weights based on feedback score
   */
  async tuneForFeedback(feedbackScore: number, modelUsed: string): Promise<boolean> {
    const updates: Partial<PromptWeights> = {};

    // Adjust based on score
    if (feedbackScore < 70) {
      // Poor performance - increase shaping strength
      const currentWeights = await this.loadWeights();
      const currentStrength = currentWeights.shaping_strength || 0.5;
      updates.shaping_strength = Math.min(currentStrength + 0.1, 1.0);
      
      // Try more formal tone for better results
      if (currentWeights.tone === 'default') {
        updates.tone = 'formal';
      }
    } else if (feedbackScore > 90) {
      // Excellent performance - record successful model
      updates.model_hint = modelUsed.toLowerCase();
    }

    if (Object.keys(updates).length > 0) {
      return await this.updateWeights(updates);
    }

    return false;
  }

  /**
   * Clear cached weights to force refresh
   */
  clearCache(): void {
    this.weights = null;
    this.lastFetch = 0;
  }
}

// Global instance for extension usage
let globalShaper: ExtensionPromptShaper | null = null;

/**
 * Get the global prompt shaper instance
 */
export function getPromptShaper(apiBaseUrl?: string): ExtensionPromptShaper {
  if (!globalShaper) {
    globalShaper = new ExtensionPromptShaper(apiBaseUrl);
  }
  return globalShaper;
}

/**
 * Convenience function to shape a prompt using shared intelligence
 */
export async function shapePrompt(basePrompt: string, context?: ShapingContext): Promise<string> {
  const shaper = getPromptShaper();
  return await shaper.shapePrompt(basePrompt, context);
}

/**
 * Convenience function to update prompt weights
 */
export async function tuneWeights(updates: Partial<PromptWeights>): Promise<boolean> {
  const shaper = getPromptShaper();
  return await shaper.updateWeights(updates);
}
