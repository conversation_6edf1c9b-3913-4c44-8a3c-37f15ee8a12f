"""
Status command for CodeCrusher.

This module provides the status command for CodeCrusher, which displays
a real-time snapshot of system health, model usage, fallback rates,
cache hits, and recent anomalies.
"""

import typer
import json as json_lib
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict, Counter
from rich.console import Console

# Import telemetry logger and anomaly engine
from codecrusher.telemetry_logger import get_telemetry_entries, TELEMETRY_FILE
from codecrusher.anomaly_engine import detect_anomalies as detect_anomalies_engine

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

def parse_time_period(since: str) -> Optional[datetime]:
    """
    Parse a time period string into a datetime object.

    Args:
        since: Time period string (e.g., "24h", "7d", "30d")

    Returns:
        datetime: Start time for filtering
    """
    now = datetime.now()

    if not since:
        # Default to 24 hours
        return now - timedelta(hours=24)

    try:
        # Parse time units
        if since.endswith('h'):
            hours = int(since[:-1])
            return now - timedelta(hours=hours)
        elif since.endswith('d'):
            days = int(since[:-1])
            return now - timedelta(days=days)
        elif since.endswith('w'):
            weeks = int(since[:-1])
            return now - timedelta(weeks=weeks)
        elif since.endswith('m'):
            months = int(since[:-1])
            # Approximate months as 30 days
            return now - timedelta(days=30 * months)
        else:
            # Try to parse as ISO format
            return datetime.fromisoformat(since)
    except (ValueError, TypeError):
        console.print(f"[bold red]❌ Error:[/bold red] Invalid time period format: {since}")
        console.print("[yellow]Using default of 24 hours[/yellow]")
        return now - timedelta(hours=24)

def filter_entries_by_time(entries: List[Dict[str, Any]], since: datetime) -> List[Dict[str, Any]]:
    """
    Filter entries by time period.

    Args:
        entries: List of telemetry entries
        since: Start time for filtering

    Returns:
        List[Dict[str, Any]]: Filtered entries
    """
    filtered_entries = []

    for entry in entries:
        timestamp = entry.get("timestamp", "")
        try:
            dt = datetime.fromisoformat(timestamp)
            if dt >= since:
                filtered_entries.append(entry)
        except (ValueError, TypeError):
            # Skip entries with invalid timestamps
            pass

    return filtered_entries

def analyze_models_used(entries: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
    """
    Analyze model usage from telemetry entries.

    Args:
        entries: List of telemetry entries

    Returns:
        Dict: Model usage statistics
    """
    models = defaultdict(lambda: {"provider": "", "count": 0, "success": 0, "errors": 0})

    for entry in entries:
        model = entry.get("model", "unknown")
        provider = entry.get("provider", "unknown")

        # Create a unique key for model+provider
        model_key = f"{model} ({provider})"

        # Update model stats
        models[model_key]["provider"] = provider
        models[model_key]["count"] += 1

        # Check if successful
        if not entry.get("error") and not entry.get("fallback", False):
            models[model_key]["success"] += 1
        else:
            models[model_key]["errors"] += 1

    # Calculate success rates
    for model_key, stats in models.items():
        if stats["count"] > 0:
            stats["success_rate"] = (stats["success"] / stats["count"]) * 100
        else:
            stats["success_rate"] = 0

    # Sort by count (descending)
    return dict(sorted(models.items(), key=lambda x: x[1]["count"], reverse=True))

def analyze_fallback_events(entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze fallback events from telemetry entries.

    Args:
        entries: List of telemetry entries

    Returns:
        Dict: Fallback statistics
    """
    fallback_stats = {
        "total": 0,
        "auto": 0,
        "forced": 0,
        "causes": Counter()
    }

    for entry in entries:
        if entry.get("fallback", False):
            fallback_stats["total"] += 1

            # Check if forced or auto
            tags = entry.get("tags", [])
            if "@fallback:forced" in tags:
                fallback_stats["forced"] += 1
            else:
                fallback_stats["auto"] += 1

            # Extract error cause
            error = entry.get("error", "")
            if error:
                # Simplify error message to a category
                if "timeout" in error.lower():
                    fallback_stats["causes"]["timeout"] += 1
                elif "empty" in error.lower() or not error.strip():
                    fallback_stats["causes"]["response_empty"] += 1
                elif "token" in error.lower():
                    fallback_stats["causes"]["token_limit"] += 1
                elif "rate" in error.lower():
                    fallback_stats["causes"]["rate_limit"] += 1
                elif "api" in error.lower():
                    fallback_stats["causes"]["api_error"] += 1
                else:
                    fallback_stats["causes"]["other"] += 1

    # Get top fallback cause
    if fallback_stats["causes"]:
        fallback_stats["top_cause"] = fallback_stats["causes"].most_common(1)[0][0]
    else:
        fallback_stats["top_cause"] = "none"

    return fallback_stats

def analyze_caching(entries: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze cache usage from telemetry entries.

    Args:
        entries: List of telemetry entries

    Returns:
        Dict: Cache statistics
    """
    cache_stats = {
        "hits": 0,
        "misses": 0,
        "hit_rate": 0
    }

    for entry in entries:
        if entry.get("cached", False):
            cache_stats["hits"] += 1
        else:
            cache_stats["misses"] += 1

    total = cache_stats["hits"] + cache_stats["misses"]
    if total > 0:
        cache_stats["hit_rate"] = (cache_stats["hits"] / total) * 100

    return cache_stats

def analyze_tags(entries: List[Dict[str, Any]]) -> List[Tuple[str, int]]:
    """
    Analyze tag usage from telemetry entries.

    Args:
        entries: List of telemetry entries

    Returns:
        List[Tuple[str, int]]: Top tags with counts
    """
    tag_counter = Counter()

    for entry in entries:
        for tag in entry.get("tags", []):
            tag_counter[tag] += 1

    # Return top 5 tags
    return tag_counter.most_common(5)

def detect_anomalies(entries: List[Dict[str, Any]]) -> List[str]:
    """
    Detect anomalies in telemetry data.

    Args:
        entries: List of telemetry entries

    Returns:
        List[str]: Detected anomalies
    """
    # Skip if not enough data
    if len(entries) < 10:
        return []

    # Use the anomaly engine to detect anomalies
    # Set a 6-hour timeframe for status display
    timeframe = timedelta(hours=6)
    anomaly_objects = detect_anomalies_engine(entries, since=timeframe)

    # Convert anomaly objects to strings for display
    anomaly_strings = []
    for anomaly in anomaly_objects:
        anomaly_strings.append(f"{anomaly.icon} {anomaly.description}")

    return anomaly_strings[:3]  # Limit to top 3 anomalies

@app.command("run")
def run_status(
    since: Optional[str] = typer.Option("24h", "--since", "-s", help="Show data since this time period (e.g., 24h, 7d, 30d)"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Filter by model"),
    raw: bool = typer.Option(False, "--raw", help="Output raw JSON data"),
    json: bool = typer.Option(False, "--json", help="Output clean, structured JSON for programmatic consumption"),
    compact: bool = typer.Option(False, "--compact", help="Show compact summary only"),
):
    """
    Display a real-time snapshot of CodeCrusher system health.

    This command analyzes telemetry data and provides a summary of
    model usage, fallback events, caching, tags, and anomalies.

    Examples:
        codecrusher status
        codecrusher status --since 24h
        codecrusher status --model mistral
        codecrusher status --raw
        codecrusher status --json
        codecrusher status --compact

    The --json flag outputs clean, structured JSON suitable for programmatic consumption,
    while --raw outputs pretty-printed JSON for human readability.
    """
    # Parse time period
    since_dt = parse_time_period(since)

    # Get all telemetry entries (high limit to get everything)
    entries = get_telemetry_entries(limit=10000)

    # Filter by time period
    filtered_entries = filter_entries_by_time(entries, since_dt)

    # Filter by model if specified
    if model:
        filtered_entries = [e for e in filtered_entries if model.lower() in e.get("model", "").lower()]

    # Check if we have data
    if not filtered_entries:
        console.print("[yellow]No telemetry data found for the specified filters[/yellow]")
        return True

    # Analyze data
    models_used = analyze_models_used(filtered_entries)
    fallback_stats = analyze_fallback_events(filtered_entries)
    cache_stats = analyze_caching(filtered_entries)
    top_tags = analyze_tags(filtered_entries)
    anomalies = detect_anomalies(filtered_entries)

    # Format time period for display
    time_period = f"Last {since}" if since else "Last 24h"

    # Prepare JSON data
    json_data = {
        "time_period": time_period,
        "entry_count": len(filtered_entries),
        "models_used": models_used,
        "fallback_stats": fallback_stats,
        "cache_stats": cache_stats,
        "top_tags": dict(top_tags),
        "anomalies": anomalies
    }

    # Output JSON if requested
    if raw or json:
        if json:
            # Clean JSON output for programmatic consumption
            print(json_lib.dumps(json_data))
        else:
            # Pretty-printed JSON for human consumption
            console.print_json(json_lib.dumps(json_data, indent=2))
        return True

    # Display status header
    console.print(f"\n[bold cyan]📡 CODECRUSHER STATUS – {time_period}[/bold cyan]")
    console.print("────────────────────────────────────────────\n")

    # Display models used
    console.print("[bold cyan]⚙ MODELS USED[/bold cyan]")
    for model_key, stats in models_used.items():
        success_rate = stats["success_rate"]
        success_color = "green" if success_rate >= 90 else "yellow" if success_rate >= 70 else "red"
        console.print(f"- {model_key} ........ {stats['count']} injections, [bold {success_color}]{success_rate:.1f}% success[/bold {success_color}]")

    # Display fallback events
    if not compact:
        console.print("\n[bold yellow]🔥 FALLBACK EVENTS[/bold yellow]")
        console.print(f"- {fallback_stats['total']} total ({fallback_stats['auto']} auto, {fallback_stats['forced']} forced)")
        if fallback_stats['total'] > 0:
            console.print(f"- Top fallback cause: {fallback_stats['top_cause']}")

    # Display caching
    console.print("\n[bold blue]⚡ CACHING[/bold blue]")
    console.print(f"- {cache_stats['hits']} hits, {cache_stats['misses']} misses ({cache_stats['hit_rate']:.1f}% hit rate)")

    # Display top tags
    if not compact:
        console.print("\n[bold magenta]🎯 TOP TAGS[/bold magenta]")
        for tag, count in top_tags:
            console.print(f"- {tag} ({count})")

    # Display anomalies
    if anomalies and not compact:
        console.print("\n[bold red]🧠 ANOMALIES DETECTED[/bold red]")
        for anomaly in anomalies:
            console.print(f"- {anomaly}")
        console.print("[yellow]Run 'codecrusher scan' for more details and suggested actions[/yellow]")

    # Display telemetry file info
    console.print(f"\n[dim]📁 Logs: {TELEMETRY_FILE}[/dim]")

    return True
