import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, FileText, Clock } from 'lucide-react';

interface InjectionRequest {
  prompt: string;
  file_path: string;
  model: string;
  apply: boolean;
  tags: string[];
}

interface InjectionResponse {
  success: boolean;
  message: string;
  modified: string[];
  logs: string[];
  total_files: number;
  successful_files: number;
  failed_files: number;
  total_injections: number;
  execution_time: number;
  timestamp: string;
  error?: string;
}

export const InjectionExample: React.FC = () => {
  const [request, setRequest] = useState<InjectionRequest>({
    prompt: "Replace all console.log statements with structured logger",
    file_path: "/home/<USER>/projects/myapp/src",
    model: "mixtral",
    apply: false,
    tags: ["logging", "cleanup"]
  });

  const [response, setResponse] = useState<InjectionResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Example using fetch API
  const handleFetchInjection = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const response = await fetch("http://localhost:8001/inject-frontend", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(request)
      });

      const data: InjectionResponse = await response.json();

      if (data.success) {
        console.log("✅ Injection complete:", data.modified);
        setResponse(data);
      } else {
        console.error("⚠️ Injection failed:", data.error || data.logs);
        setError(data.error || "Injection failed");
        setResponse(data);
      }
    } catch (err) {
      console.error("❌ Network error:", err);
      setError(`Network error: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  // Example using Axios (if you prefer)
  const handleAxiosInjection = async () => {
    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      // Uncomment if using axios:
      // import axios from 'axios';
      // const { data } = await axios.post<InjectionResponse>(
      //   "http://localhost:8001/inject-frontend",
      //   request
      // );

      // For now, using fetch as fallback
      await handleFetchInjection();
    } catch (err) {
      console.error("❌ Axios error:", err);
      setError(`Request error: ${err}`);
      setLoading(false);
    }
  };

  const updateTags = (tagString: string) => {
    const tags = tagString.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0);
    setRequest(prev => ({ ...prev, tags }));
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Code Injection Example
          </CardTitle>
          <CardDescription>
            Frontend integration example for the CodeCrusher injection API
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Request Form */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="prompt">Injection Prompt</Label>
              <Textarea
                id="prompt"
                placeholder="Describe what you want to inject..."
                value={request.prompt}
                onChange={(e) => setRequest(prev => ({ ...prev, prompt: e.target.value }))}
                className="min-h-[100px]"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="file_path">File/Directory Path</Label>
              <Input
                id="file_path"
                placeholder="/path/to/your/code"
                value={request.file_path}
                onChange={(e) => setRequest(prev => ({ ...prev, file_path: e.target.value }))}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="model">AI Model</Label>
              <select
                id="model"
                value={request.model}
                onChange={(e) => setRequest(prev => ({ ...prev, model: e.target.value }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="mixtral">Mixtral</option>
                <option value="groq/mixtral">Groq Mixtral</option>
                <option value="llama3-8b">Llama 3 8B</option>
                <option value="llama3-70b">Llama 3 70B</option>
                <option value="auto">Auto Select</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="apply">Mode</Label>
              <select
                id="apply"
                value={request.apply ? "apply" : "preview"}
                onChange={(e) => setRequest(prev => ({ ...prev, apply: e.target.value === "apply" }))}
                className="w-full p-2 border rounded-md"
              >
                <option value="preview">Preview Only</option>
                <option value="apply">Apply Changes</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags">Tags (comma-separated)</Label>
              <Input
                id="tags"
                placeholder="logging, cleanup, refactor"
                value={request.tags.join(', ')}
                onChange={(e) => updateTags(e.target.value)}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={handleFetchInjection} 
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <FileText className="h-4 w-4" />}
              Run Injection (Fetch)
            </Button>
            
            <Button 
              onClick={handleAxiosInjection} 
              disabled={loading}
              variant="outline"
              className="flex items-center gap-2"
            >
              {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <FileText className="h-4 w-4" />}
              Run Injection (Axios)
            </Button>
          </div>

          {/* Current Request Preview */}
          <div className="mt-4">
            <Label>Request Payload Preview:</Label>
            <pre className="mt-2 p-3 bg-gray-100 rounded-md text-sm overflow-x-auto">
              {JSON.stringify(request, null, 2)}
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Response Display */}
      {response && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {response.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              Injection Response
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{response.total_files}</div>
                <div className="text-sm text-gray-500">Total Files</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{response.successful_files}</div>
                <div className="text-sm text-gray-500">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{response.failed_files}</div>
                <div className="text-sm text-gray-500">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{response.total_injections}</div>
                <div className="text-sm text-gray-500">Injections</div>
              </div>
            </div>

            {/* Execution Time */}
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              <span>Execution Time: {response.execution_time?.toFixed(2)}s</span>
            </div>

            {/* Modified Files */}
            {response.modified.length > 0 && (
              <div>
                <Label>Modified Files:</Label>
                <div className="mt-2 space-y-1">
                  {response.modified.map((file, index) => (
                    <Badge key={index} variant="secondary">{file}</Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Logs */}
            {response.logs.length > 0 && (
              <div>
                <Label>Logs:</Label>
                <div className="mt-2 p-3 bg-gray-100 rounded-md max-h-40 overflow-y-auto">
                  {response.logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono">{log}</div>
                  ))}
                </div>
              </div>
            )}

            {/* Full Response */}
            <details className="mt-4">
              <summary className="cursor-pointer font-medium">Full Response JSON</summary>
              <pre className="mt-2 p-3 bg-gray-100 rounded-md text-sm overflow-x-auto">
                {JSON.stringify(response, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
