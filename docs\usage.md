# Usage Guide

Learn how to use CodeCrusher effectively for AI-powered code injection and improvement.

## 🚀 Quick Start

### Basic Injection Workflow

```bash
# 1. Inject AI-generated code
codecrusher inject ./src --prompt "Add error handling" --preview

# 2. Review and apply changes
codecrusher inject ./src --prompt "Add error handling" --apply

# 3. Rate the results to improve future responses
codecrusher rate ./src --rating 4 --comment "Good but needs more logging"

# 4. Trigger intelligence learning
codecrusher learn --apply
```

---

## 🎯 Core Commands

### `codecrusher inject` - AI Code Injection

The primary command for generating and injecting AI code.

#### Basic Usage
```bash
# Inject code with preview
codecrusher inject ./src/main.py --prompt "Add input validation" --preview

# Apply changes directly
codecrusher inject ./src/main.py --prompt "Add logging" --apply

# Process entire directory recursively
codecrusher inject ./src --recursive --prompt "Add error handling"
```

#### Advanced Options
```bash
# Specify model
codecrusher inject ./src --prompt "Optimize performance" --model llama3-70b

# Force fresh generation (ignore cache)
codecrusher inject ./src --prompt "Add tests" --force

# Custom injection tag
codecrusher inject ./src --prompt "Add validation" --tag custom_validation
```

### `codecrusher rate` - Quality Feedback

Rate injection results to improve future responses.

```bash
# Rate specific file
codecrusher rate ./src/main.py --rating 4 --comment "Good structure, needs more comments"

# Rate entire directory
codecrusher rate ./src --recursive --rating 3 --comment "Functional but verbose"

# Quick rating without comment
codecrusher rate ./src --rating 5
```

#### Rating Scale
- **5 stars**: Excellent - Perfect code quality
- **4 stars**: Good - Minor improvements needed
- **3 stars**: Average - Some issues to address
- **2 stars**: Poor - Significant problems
- **1 star**: Very Poor - Major issues or incorrect code

### `codecrusher learn` - Intelligence Learning

Trigger the intelligence learning system to improve future responses.

```bash
# Apply learning from feedback
codecrusher learn --apply

# Preview learning changes
codecrusher learn --dry-run

# Verbose learning output
codecrusher learn --apply --verbose
```

### `codecrusher status` - System Status

Check system configuration and health.

```bash
# Basic status
codecrusher status

# Detailed status with configuration
codecrusher status --verbose

# Check API connectivity
codecrusher status --test-apis
```

---

## 🏷️ Injection Tags

CodeCrusher uses comment tags to identify where to inject code.

### Supported Languages

#### Python
```python
# AI_INJECT:function_name
# Generated code will appear here
# AI_INJECT:function_name:end
```

#### JavaScript/TypeScript
```javascript
// AI_INJECT:function_name
// Generated code will appear here
// AI_INJECT:function_name:end
```

#### Java/C++
```java
// AI_INJECT:function_name
// Generated code will appear here
// AI_INJECT:function_name:end
```

#### HTML
```html
<!-- AI_INJECT:component_name -->
<!-- Generated code will appear here -->
<!-- AI_INJECT:component_name:end -->
```

### Tag Examples

```python
# Example Python file with injection tags

class Calculator:
    def __init__(self):
        self.history = []
    
    # AI_INJECT:add_method
    # AI_INJECT:add_method:end
    
    # AI_INJECT:error_handling
    # AI_INJECT:error_handling:end
```

---

## 📊 Command Options Reference

### Global Options

| Option | Description | Example |
|--------|-------------|---------|
| `--verbose` | Detailed output | `codecrusher inject --verbose` |
| `--quiet` | Minimal output | `codecrusher inject --quiet` |
| `--help` | Show help | `codecrusher inject --help` |

### Injection Options

| Option | Description | Example |
|--------|-------------|---------|
| `--prompt` | AI prompt text | `--prompt "Add error handling"` |
| `--preview` | Show changes before applying | `--preview` |
| `--apply` | Apply changes directly | `--apply` |
| `--recursive` | Process directories recursively | `--recursive` |
| `--model` | Specify AI model | `--model llama3-70b` |
| `--force` | Ignore cache, generate fresh | `--force` |
| `--tag` | Custom injection tag | `--tag validation_logic` |

### Rating Options

| Option | Description | Example |
|--------|-------------|---------|
| `--rating` | Quality rating (1-5) | `--rating 4` |
| `--comment` | Feedback comment | `--comment "Needs more error handling"` |
| `--recursive` | Rate entire directory | `--recursive` |

---

## 🔄 Workflow Examples

### Single File Workflow

```bash
# 1. Add injection tag to your file
echo "# AI_INJECT:validation_function" >> src/utils.py

# 2. Generate code with preview
codecrusher inject src/utils.py --prompt "Add input validation function" --preview

# 3. Apply if satisfied
codecrusher inject src/utils.py --prompt "Add input validation function" --apply

# 4. Rate the result
codecrusher rate src/utils.py --rating 4 --comment "Good validation, could use more edge cases"
```

### Project-Wide Workflow

```bash
# 1. Add injection tags throughout project
find src/ -name "*.py" -exec grep -l "AI_INJECT" {} \;

# 2. Process entire project
codecrusher inject src/ --recursive --prompt "Add comprehensive error handling" --preview

# 3. Apply changes
codecrusher inject src/ --recursive --prompt "Add comprehensive error handling" --apply

# 4. Rate results
codecrusher rate src/ --recursive --rating 3 --comment "Good coverage, some functions too verbose"

# 5. Learn from feedback
codecrusher learn --apply
```

### Iterative Improvement Workflow

```bash
# Round 1: Initial injection
codecrusher inject src/ --prompt "Add basic error handling" --apply
codecrusher rate src/ --rating 2 --comment "Too basic, needs more comprehensive handling"

# Trigger learning
codecrusher learn --apply

# Round 2: Improved injection
codecrusher inject src/ --prompt "Add comprehensive error handling" --apply
codecrusher rate src/ --rating 4 --comment "Much better, good error recovery"

# Round 3: Fine-tuning
codecrusher inject src/ --prompt "Add error handling with logging" --apply
codecrusher rate src/ --rating 5 --comment "Perfect! Comprehensive with proper logging"
```

---

## 🎛️ Model Selection

### Available Models

| Model | Provider | Use Case | Speed | Quality |
|-------|----------|----------|-------|---------|
| `llama3-8b` | Groq | Fast, simple tasks | ⚡⚡⚡ | ⭐⭐⭐ |
| `mixtral-8x7b` | Groq | Complex reasoning | ⚡⚡ | ⭐⭐⭐⭐ |
| `llama3-70b` | Groq | Deep analysis | ⚡ | ⭐⭐⭐⭐⭐ |
| `gpt-4-turbo` | OpenAI | Premium quality | ⚡ | ⭐⭐⭐⭐⭐ |

### Model Selection Examples

```bash
# Use fast model for simple tasks
codecrusher inject src/ --prompt "Add docstrings" --model llama3-8b

# Use powerful model for complex logic
codecrusher inject src/ --prompt "Implement complex algorithm" --model llama3-70b

# Let CodeCrusher choose automatically (recommended)
codecrusher inject src/ --prompt "Add error handling"
```

---

## 🔧 Advanced Usage

### Environment Variables

```bash
# API Keys
export GROQ_API_KEY="your_groq_key"
export OPENAI_API_KEY="your_openai_key"

# Configuration
export CODECRUSHER_DEFAULT_MODEL="llama3-70b"
export CODECRUSHER_CACHE_DIR="~/.codecrusher/cache"
export CODECRUSHER_LOG_LEVEL="INFO"
```

### Configuration File

Create `~/.codecrusher/config.yaml`:

```yaml
# CodeCrusher Configuration
default_model: "llama3-70b"
fallback_sensitivity: 0.7
prompt_style: "comprehensive"
verbosity: "medium"

# Model preferences
model_preferences:
  - llama3-8b
  - mixtral-8x7b
  - llama3-70b
  - gpt-4-turbo

# Cache settings
cache:
  enabled: true
  directory: "~/.codecrusher/cache"
  max_size: "1GB"
```

### Batch Processing

```bash
# Process multiple files with same prompt
find src/ -name "*.py" | xargs -I {} codecrusher inject {} --prompt "Add type hints"

# Process with different prompts
codecrusher inject src/models/ --prompt "Add data validation" --apply
codecrusher inject src/views/ --prompt "Add error handling" --apply
codecrusher inject src/utils/ --prompt "Add logging" --apply
```

---

## 📈 Intelligence Learning

### Understanding the Learning Loop

1. **Injection**: AI generates code based on prompt
2. **Rating**: You provide feedback (1-5 stars + comments)
3. **Learning**: System analyzes patterns and updates parameters
4. **Improvement**: Future injections use enhanced configuration

### Learning Parameters

The system automatically adjusts:

- **Prompt Style**: `basic` → `detailed` → `comprehensive`
- **Verbosity**: `low` → `medium` → `high`
- **Error Handling**: `basic` → `comprehensive` → `extensive`
- **Fallback Sensitivity**: `0.9` → `0.7` → `0.5`

### Monitoring Learning Progress

```bash
# Check learning status
codecrusher learn --status

# View learning analytics
codecrusher learn --analytics

# Reset learning (if needed)
codecrusher learn --reset
```

---

## 🔗 Integration with Other Tools

### Git Integration

```bash
# Inject code and commit
codecrusher inject src/ --prompt "Add error handling" --apply
git add .
git commit -m "feat: Add comprehensive error handling via CodeCrusher"
```

### CI/CD Integration

```yaml
# GitHub Actions example
- name: AI Code Enhancement
  run: |
    codecrusher inject src/ --prompt "Add error handling" --apply
    codecrusher rate src/ --rating 4 --comment "Automated enhancement"
```

### IDE Integration

```bash
# VS Code task example
{
  "label": "CodeCrusher Inject",
  "type": "shell",
  "command": "codecrusher",
  "args": ["inject", "${file}", "--prompt", "${input:prompt}", "--preview"]
}
```

---

## 🔗 Next Steps

- **[Configure Tuning](tuning.md)** - Optimize CodeCrusher for your needs
- **[Explore Dashboard](dashboard.md)** - Use the web interface
- **[Troubleshooting](troubleshooting.md)** - Solve common issues

---

**[← Installation](installation.md)** | **[Next: Tuning Guide →](tuning.md)**
