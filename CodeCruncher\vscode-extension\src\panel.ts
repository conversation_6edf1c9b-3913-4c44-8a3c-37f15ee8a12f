import * as vscode from 'vscode';
import { Code<PERSON>rusher<PERSON>pi } from './api';

/**
 * CodeCrusher sidebar panel provider
 */
export class CodeCrusherPanel implements vscode.WebviewViewProvider {
    public static readonly viewType = 'codecrusher.sidebar';
    private _view?: vscode.WebviewView;
    private _refreshInterval?: NodeJS.Timeout;

    constructor(private readonly _extensionUri: vscode.Uri) {}

    /**
     * Resolves the webview view
     */
    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // Set up refresh interval (every 60 seconds)
        this._refreshInterval = setInterval(() => this.refresh(), 60000);

        // Handle messages from the webview
        webviewView.webview.onDidReceiveMessage(async (message) => {
            switch (message.command) {
                case 'refresh':
                    await this.refresh();
                    break;
                case 'rescan':
                    await this.rescanProject();
                    break;
                case 'openTrends':
                    vscode.commands.executeCommand('codecrusher.openTrends');
                    break;
                case 'openScan':
                    vscode.commands.executeCommand('codecrusher.openScan');
                    break;
            }
        });

        // Initial refresh
        this.refresh();
    }

    /**
     * Refresh the panel data
     */
    public async refresh() {
        if (!this._view) {
            return;
        }

        try {
            // Check if server is running
            const isServerRunning = await CodeCrusherApi.isServerRunning();
            if (!isServerRunning) {
                this._view.webview.postMessage({
                    type: 'serverStatus',
                    running: false
                });
                return;
            }

            // Get status data
            const status = await CodeCrusherApi.getStatus();

            // Get scan data
            const scan = await CodeCrusherApi.runScan();

            // Get trends data (last 7 days)
            const trends = await CodeCrusherApi.getTrends();

            // Send data to webview
            this._view.webview.postMessage({
                type: 'refresh',
                data: {
                    status,
                    scan,
                    trends
                }
            });
        } catch (error) {
            console.error('Error refreshing panel:', error);

            if (this._view) {
                this._view.webview.postMessage({
                    type: 'error',
                    message: 'Failed to refresh data. Make sure the CodeCrusher API server is running.'
                });
            }
        }
    }

    /**
     * Rescan the project for anomalies
     */
    private async rescanProject() {
        if (!this._view) {
            return;
        }

        try {
            // Show scanning message
            this._view.webview.postMessage({
                type: 'scanning',
                scanning: true
            });

            // Run scan
            const scan = await CodeCrusherApi.runScan();

            // Update webview with scan results
            this._view.webview.postMessage({
                type: 'scanComplete',
                data: scan
            });

            // Show notification
            vscode.window.showInformationMessage(
                `CodeCrusher scan complete: ${scan.anomalies_count} anomalies detected`
            );
        } catch (error) {
            console.error('Error scanning project:', error);

            if (this._view) {
                this._view.webview.postMessage({
                    type: 'error',
                    message: 'Failed to scan project. Make sure the CodeCrusher API server is running.'
                });
            }
        }
    }

    /**
     * Get HTML for the webview
     */
    private _getHtmlForWebview(webview: vscode.Webview): string {
        // Get the local path to script and css for the webview
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'panel.js')
        );

        const styleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'panel.css')
        );

        // Use a nonce to only allow specific scripts to be run
        const nonce = getNonce();

        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src ${webview.cspSource}; script-src 'nonce-${nonce}';">
            <link href="${styleUri}" rel="stylesheet">
            <title>CodeCrusher</title>
        </head>
        <body>
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <div>Loading CodeCrusher data...</div>
            </div>

            <div id="server-error" class="error-container hidden">
                <h3>⚠️ CodeCrusher API Server Not Running</h3>
                <p>Start the server with:</p>
                <pre>codecrusher serve run</pre>
                <button id="refresh-button">Refresh</button>
            </div>

            <div id="content" class="hidden">
                <div class="header">
                    <h2>🚀 CodeCrusher</h2>
                    <button id="refresh-btn" title="Refresh data">🔄</button>
                </div>

                <div class="section">
                    <h3>Model Status</h3>
                    <div id="models-container" class="models-container"></div>
                </div>

                <div class="section">
                    <h3>Cache Performance</h3>
                    <div id="cache-stats" class="stats-container"></div>
                </div>

                <div class="section">
                    <h3>Anomalies</h3>
                    <div id="anomalies-container" class="anomalies-container"></div>
                    <button id="scan-btn" class="action-button">🔍 Rescan Project</button>
                </div>

                <div class="section">
                    <h3>Recent Activity</h3>
                    <div id="trends-container" class="trends-container"></div>
                    <button id="trends-btn" class="action-button">📊 View Full Trends</button>
                </div>
            </div>

            <script nonce="${nonce}" src="${scriptUri}"></script>
        </body>
        </html>`;
    }

    /**
     * Dispose of resources
     */
    public dispose() {
        if (this._refreshInterval) {
            clearInterval(this._refreshInterval);
        }
    }
}

/**
 * Generate a nonce string
 */
function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}

/**
 * Show the CodeCrusher panel in a webview
 */
export function showCrusherPanel(context: vscode.ExtensionContext) {
    // Create and show webview panel
    const panel = vscode.window.createWebviewPanel(
        'codecrusherPanel',
        'CodeCrusher',
        vscode.ViewColumn.One,
        {
            enableScripts: true,
            localResourceRoots: [context.extensionUri]
        }
    );

    // Show loading message
    panel.webview.html = `
        <html>
            <body style="font-family: var(--vscode-font-family); padding: 20px;">
                <h1>Loading CodeCrusher data...</h1>
            </body>
        </html>
    `;

    // Load data and update panel
    updatePanelContent(panel, context.extensionUri);
}

/**
 * Update the panel content with data from the API
 */
async function updatePanelContent(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
    try {
        // Check if server is running
        const isServerRunning = await CodeCrusherApi.isServerRunning();
        if (!isServerRunning) {
            panel.webview.html = `
                <html>
                    <body style="font-family: var(--vscode-font-family); padding: 20px;">
                        <h1>⚠️ CodeCrusher API Server Not Running</h1>
                        <p>Start the server with:</p>
                        <pre>codecrusher serve run</pre>
                        <button onclick="window.location.reload()">Refresh</button>
                    </body>
                </html>
            `;
            return;
        }

        // Get data from API
        const status = await CodeCrusherApi.getStatus();
        const scan = await CodeCrusherApi.runScan();
        const telemetry = await CodeCrusherApi.getTelemetry(10);

        // Create HTML content
        const html = `
            <html>
                <head>
                    <style>
                        body {
                            font-family: var(--vscode-font-family);
                            padding: 20px;
                        }
                        h1, h2, h3 {
                            color: var(--vscode-editor-foreground);
                        }
                        .section {
                            margin-bottom: 20px;
                            padding-bottom: 15px;
                            border-bottom: 1px solid var(--vscode-panel-border);
                        }
                        .model-item {
                            display: flex;
                            justify-content: space-between;
                            padding: 8px;
                            margin-bottom: 8px;
                            background-color: var(--vscode-editor-background);
                            border-radius: 4px;
                        }
                        .anomaly-item {
                            padding: 8px;
                            margin-bottom: 8px;
                            background-color: var(--vscode-editor-background);
                            border-radius: 4px;
                            border-left: 3px solid var(--vscode-errorForeground);
                        }
                        .button {
                            background-color: var(--vscode-button-background);
                            color: var(--vscode-button-foreground);
                            border: none;
                            padding: 8px 12px;
                            border-radius: 3px;
                            cursor: pointer;
                            margin-top: 10px;
                        }
                        .telemetry-item {
                            padding: 8px;
                            margin-bottom: 8px;
                            background-color: var(--vscode-editor-background);
                            border-radius: 4px;
                        }
                    </style>
                </head>
                <body>
                    <h1>CodeCrusher Dashboard</h1>

                    <div class="section">
                        <h2>Status Summary</h2>
                        <p>Time Period: ${status.time_period}</p>
                        <p>Entries: ${status.entry_count}</p>
                        <p>Cache Hit Rate: ${status.cache_stats.hit_rate}%</p>
                    </div>

                    <div class="section">
                        <h2>Models (${Object.keys(status.models_used).length})</h2>
                        ${Object.entries(status.models_used).map(([model, stats]) => `
                            <div class="model-item">
                                <div>${model}</div>
                                <div>${stats.count} calls (${stats.success_rate}% success)</div>
                            </div>
                        `).join('')}
                    </div>

                    <div class="section">
                        <h2>Anomalies (${scan.anomalies_count})</h2>
                        ${scan.anomalies.slice(0, 5).map(anomaly => `
                            <div class="anomaly-item">
                                <div><strong>${anomaly.type}</strong>: ${anomaly.description}</div>
                                <div>Model: ${anomaly.model || 'N/A'}</div>
                            </div>
                        `).join('')}
                        <button class="button" onclick="window.location.reload()">Rescan Project</button>
                    </div>

                    <div class="section">
                        <h2>Recent Telemetry (${telemetry.count})</h2>
                        ${telemetry.entries.slice(0, 5).map(entry => `
                            <div class="telemetry-item">
                                <div><strong>Model:</strong> ${entry.model}</div>
                                <div><strong>Time:</strong> ${new Date(entry.timestamp).toLocaleString()}</div>
                                <div><strong>Tags:</strong> ${entry.tags.join(', ') || 'None'}</div>
                                ${entry.file_path ? `<div><strong>File:</strong> ${entry.file_path}:${entry.line_number || 0}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                </body>
            </html>
        `;

        // Update panel content
        panel.webview.html = html;
    } catch (error) {
        console.error('Error updating panel content:', error);
        panel.webview.html = `
            <html>
                <body style="font-family: var(--vscode-font-family); padding: 20px;">
                    <h1>Error Loading CodeCrusher Data</h1>
                    <p>Failed to load data from the CodeCrusher API server.</p>
                    <p>Error: ${error instanceof Error ? error.message : String(error)}</p>
                    <button onclick="window.location.reload()">Retry</button>
                </body>
            </html>
        `;
    }
}
