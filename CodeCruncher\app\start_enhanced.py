#!/usr/bin/env python3
"""
Startup script for Enhanced CodeCrusher Backend
"""

import uvicorn
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 Starting Enhanced CodeCrusher Backend...")
    print("📡 WebSocket endpoint: ws://localhost:8000/ws/logs")
    print("🔗 API endpoint: http://localhost:8000/api/inject")
    print("📋 Documentation: http://localhost:8000/docs")
    print("=" * 50)

    uvicorn.run(
        "app_enhanced:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
