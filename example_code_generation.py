#!/usr/bin/env python3
"""
Example script demonstrating the improved code generation functionality
with race_models, scoring, and caching.
"""

import asyncio
import time
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.syntax import Syntax

from source.ai_engine import generate_code, MODEL_TIERS

# Initialize rich console
console = Console()

def display_result(prompt, output, model_id, elapsed_time):
    """Display the result in a nicely formatted way"""
    # Create a table for metadata
    table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    table.add_row("[cyan]Prompt:[/cyan]", f"[yellow]{prompt}[/yellow]")
    table.add_row("[cyan]Model Used:[/cyan]", f"[bold]{model_id}[/bold]")
    table.add_row("[cyan]Time Taken:[/cyan]", f"[bold]{elapsed_time:.2f}s[/bold]")
    
    # Create a syntax highlighted code block
    code_block = Syntax(output, "python", theme="monokai", line_numbers=True)
    
    # Display the result
    console.print(Panel(table, title="[bold green]✅ Code Generation Result[/bold green]", border_style="green"))
    console.print(Panel(code_block, title="[bold blue]Generated Code[/bold blue]", border_style="blue"))

def main():
    """Main function demonstrating code generation"""
    # Display available models
    console.print("[bold]Available Models:[/bold]")
    for name, model_id in MODEL_TIERS:
        console.print(f"  [cyan]•[/cyan] {name} ([dim]{model_id}[/dim])")
    
    # Example prompts
    prompts = [
        "Write a Python function to calculate the factorial of a number using recursion.",
        "Create a function to check if a string is a palindrome.",
        "Implement a binary search algorithm in Python."
    ]
    
    # Test with different configurations
    console.print("\n[bold]Testing with different configurations:[/bold]")
    
    # 1. Single model
    prompt = prompts[0]
    console.print(f"\n[bold magenta]1. Single Model Test:[/bold magenta] {prompt}")
    start_time = time.time()
    output, model_id = generate_code(
        prompt=prompt,
        tag="factorial",
        provider="groq",
        auto_route=False,
        use_cache=True
    )
    elapsed = time.time() - start_time
    display_result(prompt, output, model_id, elapsed)
    
    # 2. Auto-routing with multiple models
    prompt = prompts[1]
    console.print(f"\n[bold magenta]2. Auto-Routing Test:[/bold magenta] {prompt}")
    start_time = time.time()
    output, model_id = generate_code(
        prompt=prompt,
        tag="palindrome",
        auto_route=True,
        use_cache=True
    )
    elapsed = time.time() - start_time
    display_result(prompt, output, model_id, elapsed)
    
    # 3. Refresh cache test
    prompt = prompts[2]
    console.print(f"\n[bold magenta]3. Refresh Cache Test:[/bold magenta] {prompt}")
    
    # First run to populate cache
    console.print("[yellow]First run (populating cache)...[/yellow]")
    output, model_id = generate_code(
        prompt=prompt,
        tag="binary_search",
        auto_route=True,
        use_cache=True
    )
    
    # Second run with cache
    console.print("[yellow]Second run (using cache)...[/yellow]")
    start_time = time.time()
    output, model_id = generate_code(
        prompt=prompt,
        tag="binary_search",
        auto_route=True,
        use_cache=True
    )
    elapsed = time.time() - start_time
    display_result(prompt, output, model_id, elapsed)
    
    # Third run with refresh_cache
    console.print("[yellow]Third run (bypassing cache)...[/yellow]")
    start_time = time.time()
    output, model_id = generate_code(
        prompt=prompt,
        tag="binary_search",
        auto_route=True,
        use_cache=True,
        refresh_cache=True
    )
    elapsed = time.time() - start_time
    display_result(prompt, output, model_id, elapsed)

if __name__ == "__main__":
    main()
