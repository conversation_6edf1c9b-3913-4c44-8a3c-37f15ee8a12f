import React, { useEffect, useRef } from 'react';
import { LogMessage } from '@/hooks/useLogStream';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Trash2, Wifi, WifiOff, TestTube } from 'lucide-react';

interface LogDisplayProps {
  logs: LogMessage[];
  isConnected: boolean;
  connectionError: string | null;
  onClearLogs: () => void;
  onSendTestMessage: (message: string) => void;
  className?: string;
}

export function LogDisplay({
  logs,
  isConnected,
  connectionError,
  onClearLogs,
  onSendTestMessage,
  className = ""
}: LogDisplayProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const shouldAutoScroll = useRef(true);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (shouldAutoScroll.current && scrollAreaRef.current) {
      const scrollElement = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollElement) {
        scrollElement.scrollTop = scrollElement.scrollHeight;
      }
    }
  }, [logs]);

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 10;
    shouldAutoScroll.current = isAtBottom;
  };

  const formatTimestamp = (timestamp: string) => {
    try {
      return new Date(timestamp).toLocaleTimeString();
    } catch {
      return timestamp;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'success':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      case 'warning':
        return 'text-yellow-400';
      default:
        return 'text-green-300';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'injection':
        return '🚀';
      case 'system':
        return '🔧';
      case 'error':
        return '❌';
      case 'progress':
        return '📊';
      case 'stats':
        return '📈';
      case 'test':
        return '🧪';
      default:
        return '📝';
    }
  };

  const handleTestMessage = () => {
    const testMessages = [
      "Hello from React frontend!",
      "Testing WebSocket connection",
      "Real-time logging is working!",
      "Dashboard integration successful"
    ];
    const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
    onSendTestMessage(randomMessage);
  };

  return (
    <div className={`bg-black text-green-300 rounded-lg border ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-700">
        <div className="flex items-center space-x-2">
          <span className="font-mono text-sm font-medium">Terminal Logs</span>
          <div className="flex items-center space-x-1">
            {isConnected ? (
              <Wifi className="h-4 w-4 text-green-400" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-400" />
            )}
            <Badge 
              variant={isConnected ? "default" : "destructive"}
              className="text-xs"
            >
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTestMessage}
            className="text-xs bg-gray-800 border-gray-600 hover:bg-gray-700"
          >
            <TestTube className="h-3 w-3 mr-1" />
            Test
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onClearLogs}
            className="text-xs bg-gray-800 border-gray-600 hover:bg-gray-700"
          >
            <Trash2 className="h-3 w-3 mr-1" />
            Clear
          </Button>
        </div>
      </div>

      {/* Connection Error */}
      {connectionError && (
        <div className="p-2 bg-red-900/20 border-b border-red-700">
          <span className="text-red-400 text-xs">⚠️ {connectionError}</span>
        </div>
      )}

      {/* Logs */}
      <ScrollArea 
        className="h-64 p-4" 
        ref={scrollAreaRef}
        onScrollCapture={handleScroll}
      >
        {logs.length === 0 ? (
          <div className="text-gray-500 text-sm italic">
            {isConnected ? 'Waiting for logs...' : 'Connect to see logs...'}
          </div>
        ) : (
          <div className="space-y-1">
            {logs.map((log, index) => (
              <div key={index} className="font-mono text-sm">
                <span className="text-gray-500">
                  [{formatTimestamp(log.timestamp)}]
                </span>
                <span className="text-blue-400 ml-2">
                  {getTypeIcon(log.type)}
                </span>
                <span className={`ml-2 ${getLevelColor(log.level)}`}>
                  {log.message}
                </span>
                
                {/* Progress indicator for progress messages */}
                {log.type === 'progress' && typeof log.progress === 'number' && (
                  <span className="text-cyan-400 ml-2">
                    ({log.progress}%)
                  </span>
                )}
                
                {/* Stats display for stats messages */}
                {log.type === 'stats' && log.stats && (
                  <div className="ml-8 text-xs text-gray-400 mt-1">
                    📊 Total: {log.stats.totalFiles}, 
                    Processed: {log.stats.processedFiles}, 
                    Failed: {log.stats.failedFiles}, 
                    Success: {log.stats.successRate}%
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </ScrollArea>

      {/* Footer */}
      <div className="p-2 border-t border-gray-700 text-xs text-gray-500">
        {logs.length} messages • {isConnected ? 'Live' : 'Offline'}
      </div>
    </div>
  );
}
