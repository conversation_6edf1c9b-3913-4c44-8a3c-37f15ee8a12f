# CodeCrusher FastAPI Backend - Simplified Version

A streamlined FastAPI backend for CodeCrusher's AI-powered code injection capabilities.

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- CodeCrusher CLI installed
- Virtual environment (recommended)

### Installation

1. **Navigate to app directory:**
```bash
cd app
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Start the server:**
```bash
python start.py
```

Or manually:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 📋 API Endpoints

### Core Endpoints
- **GET /** - Health check and status
- **POST /inject** - Main code injection endpoint
- **GET /health** - Detailed health status
- **GET /models** - Available AI models
- **GET /extensions** - Supported file extensions
- **POST /validate** - Source path validation

## 🔧 Usage Examples

### Basic Code Injection
```bash
curl -X POST "http://localhost:8000/inject" \
  -H "Content-Type: application/json" \
  -d '{
    "source": "./src/main.py",
    "prompt_text": "Add error handling",
    "apply": false,
    "auto_model_routing": true
  }'
```

### Recursive Directory Processing
```bash
curl -X POST "http://localhost:8000/inject" \
  -H "Content-Type: application/json" \
  -d '{
    "source": "./src",
    "prompt_text": "Add logging",
    "recursive": true,
    "ext": "py,js",
    "apply": true,
    "tag": "logging-v1"
  }'
```

## 📊 Request Model

```json
{
  "source": "string",           // Required: Path to file or folder
  "prompt_text": "string",      // Required: AI prompt
  "recursive": false,           // Optional: Scan subfolders
  "ext": "py",                  // Optional: File extensions
  "tag": "default",             // Optional: Version tag
  "apply": false,               // Optional: Apply changes
  "auto_model_routing": true,   // Optional: Auto model selection
  "refresh_cache": false,       // Optional: Bypass cache
  "force": false,               // Optional: Force without tags
  "summary": true               // Optional: Include summary
}
```

## 📈 Response Model

```json
{
  "success": true,
  "message": "Code injection completed successfully",
  "output": "CLI output text...",
  "error": null,
  "execution_time": 2.34,
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🔍 Features

### Smart CodeCrusher Detection
The backend automatically finds CodeCrusher in multiple locations:
- System PATH
- Virtual environment (./codecrushervenv or ../codecrushervenv)
- Python module execution

### Robust Error Handling
- Path validation before execution
- Comprehensive error messages
- Graceful fallback mechanisms
- Detailed logging

### Async Processing
- Non-blocking request handling
- Proper subprocess management
- Timeout handling

## 🌐 API Documentation

Once the server is running, visit:
- **Swagger UI:** http://localhost:8000/docs
- **ReDoc:** http://localhost:8000/redoc

## 🧪 Testing

Run the test suite:
```bash
python ../test_app_simple.py
```

## 🔧 Configuration

### Environment Variables
- `HOST`: Server host (default: 0.0.0.0)
- `PORT`: Server port (default: 8000)
- `DEBUG`: Enable debug mode (default: False)

### CORS Configuration
For production, update the CORS settings in `main.py`:
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Specific domains
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

## 🚀 Deployment

### Local Development
```bash
python start.py
```

### Production with Gunicorn
```bash
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Docker (Optional)
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 🔗 Integration

This simplified backend provides:
- Full CLI integration with all parameters
- Automatic executable detection
- Comprehensive error handling
- Production-ready structure
- Easy frontend integration

Perfect for web frontends, mobile apps, or any system needing CodeCrusher functionality via HTTP API.

## 📞 Support

- Check `/health` endpoint for system status
- Review logs for detailed error information
- Ensure CodeCrusher CLI is properly installed
- Verify virtual environment setup
