from fastapi import <PERSON><PERSON>out<PERSON>, Request, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import asyncio
import subprocess
import os
import logging

router = APIRouter()
logger = logging.getLogger("codecrusher")

# ---- MODELS ----
class InjectRequest(BaseModel):
    prompt_text: str
    model: str
    fallback_enabled: bool

# ---- WEBSOCKET MANAGEMENT ----
# Note: WebSocket endpoints moved to backend_main.py for centralized management
# This avoids duplicate endpoints and ensures consistent ping/pong behavior

async def broadcast_log(message: str):
    """Broadcast log message to all connected WebSocket clients."""
    # Import clients from backend_main to avoid circular imports
    from app.backend_main import clients

    if not clients:
        return

    to_remove = set()
    for client in clients:
        try:
            await client.send_text(message)
        except WebSocketDisconnect:
            to_remove.add(client)
        except Exception as e:
            logger.warning(f"Failed to send message to client: {e}")
            to_remove.add(client)

    # Remove disconnected clients
    clients.difference_update(to_remove)

# ---- INJECT ROUTE ----
@router.post("/api/inject")
async def run_injection(payload: InjectRequest):
    """Run CodeCrusher injection with real-time log streaming."""
    prompt = payload.prompt_text
    model = payload.model
    fallback = payload.fallback_enabled

    # Build command
    cmd = [
        "codecrusher",
        "--source", "./src",
        "--recursive",
        "--ext", "py",
        "--prompt-text", prompt,
        "--tag", "web-ui",
        "--apply"
    ]

    # Add model selection
    if fallback:
        cmd.extend(["--auto-model-routing"])
    else:
        cmd.extend(["--model", model])

    # Flatten command (handle any nested lists)
    cmd = [str(item) for sublist in cmd for item in (item if isinstance(item, list) else [item])]

    # Set up environment
    env = os.environ.copy()
    venv_path = env.get('VIRTUAL_ENV') or './.venv'
    env["PATH"] = f"{venv_path}/Scripts;{venv_path}/bin;" + env.get("PATH", "")

    try:
        # Broadcast start message
        await broadcast_log("🚀 Starting CodeCrusher injection...")
        await broadcast_log(f"📋 Command: {' '.join(cmd)}")
        await broadcast_log(f"🧠 Model: {model} (fallback: {'enabled' if fallback else 'disabled'})")

        # Start process
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
            env=env,
        )

        # Stream output in real-time
        line_count = 0
        async for line in process.stdout:
            decoded_line = line.decode().strip()
            if decoded_line:
                line_count += 1

                # Add progress indicators
                if line_count % 10 == 0:
                    await broadcast_log(f"Progress: {min(line_count * 2, 100)}%")

                await broadcast_log(decoded_line)

        # Wait for process completion
        return_code = await process.wait()

        if return_code == 0:
            await broadcast_log("✅ CodeCrusher injection completed successfully!")
            await broadcast_log("Progress: 100%")
            return JSONResponse({"status": "complete", "success": True})
        else:
            await broadcast_log(f"❌ CodeCrusher injection failed with return code: {return_code}")
            return JSONResponse({"status": "complete", "success": False, "error": f"Process failed with code {return_code}"})

    except FileNotFoundError:
        error_msg = "❌ CodeCrusher CLI not found. Please ensure it's installed and in PATH."
        await broadcast_log(error_msg)
        return JSONResponse({"status": "error", "success": False, "error": "CodeCrusher CLI not found"})

    except Exception as e:
        error_msg = f"💥 Error during injection: {str(e)}"
        await broadcast_log(error_msg)
        logger.error(f"Injection error: {e}")
        return JSONResponse({"status": "error", "success": False, "error": str(e)})

# ---- UTILITY ROUTES ----
@router.get("/api/models")
async def get_models():
    """Get available AI models."""
    return {
        "models": [
            {"id": "auto", "name": "Auto Selection"},
            {"id": "mistral", "name": "Mistral"},
            {"id": "mixtral", "name": "Mixtral"},
            {"id": "gemma", "name": "Gemma"},
            {"id": "llama3", "name": "LLaMA 3"}
        ]
    }

@router.get("/api/status")
async def get_status():
    """Get system status."""
    # Import clients from backend_main to avoid circular imports
    from app.backend_main import clients

    return {
        "status": "running",
        "connected_clients": len(clients),
        "websocket_endpoint": "/ws/logs"
    }
