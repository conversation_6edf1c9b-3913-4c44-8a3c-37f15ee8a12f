"""
Intent Detection Module for CodeCrusher

This module provides intelligent classification of user prompts into specific
intent categories to enhance the injection process with contextual understanding.
"""

import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass


@dataclass
class IntentMatch:
    """Represents an intent classification result with confidence score."""
    intent: str
    confidence: float
    matched_keywords: List[str]
    description: str


class IntentClassifier:
    """Advanced intent classifier with keyword matching and confidence scoring."""
    
    # Intent definitions with keywords and descriptions
    INTENT_PATTERNS = {
        "refactor": {
            "keywords": ["refactor", "clean", "restructure", "reorganize", "simplify", "improve structure", "code cleanup"],
            "description": "Restructure and clean up existing code without changing functionality",
            "weight": 1.0
        },
        "optimize": {
            "keywords": ["optimize", "faster", "performance", "efficient", "speed up", "improve performance", "bottleneck"],
            "description": "Improve code performance and efficiency",
            "weight": 1.0
        },
        "document": {
            "keywords": ["document", "docstring", "comment", "documentation", "explain code", "add comments"],
            "description": "Add documentation, comments, or explanations to code",
            "weight": 1.0
        },
        "localize": {
            "keywords": ["localize", "translate", "i18n", "l10n", "language", "internationalization", "multilingual"],
            "description": "Add internationalization and localization support",
            "weight": 1.0
        },
        "test": {
            "keywords": ["test", "unittest", "coverage", "testing", "test case", "unit test", "integration test"],
            "description": "Add or improve test coverage and testing infrastructure",
            "weight": 1.0
        },
        "explain": {
            "keywords": ["explain", "why", "how", "what does", "understand", "clarify", "breakdown"],
            "description": "Provide explanations and analysis of existing code",
            "weight": 0.8  # Lower weight as it's more analytical
        },
        "debug": {
            "keywords": ["debug", "fix", "bug", "error", "issue", "problem", "troubleshoot", "resolve"],
            "description": "Identify and fix bugs or issues in code",
            "weight": 1.0
        },
        "transform": {
            "keywords": ["convert", "transform", "migrate", "port", "change to", "rewrite", "modernize"],
            "description": "Convert code between different formats, languages, or frameworks",
            "weight": 1.0
        },
        "security": {
            "keywords": ["security", "secure", "vulnerability", "sanitize", "validate", "auth", "encryption"],
            "description": "Improve code security and handle vulnerabilities",
            "weight": 1.0
        },
        "feature": {
            "keywords": ["add", "implement", "create", "build", "new feature", "functionality", "enhance"],
            "description": "Add new features or functionality to existing code",
            "weight": 1.0
        }
    }
    
    def __init__(self):
        """Initialize the intent classifier."""
        self.compiled_patterns = self._compile_patterns()
    
    def _compile_patterns(self) -> Dict[str, List[re.Pattern]]:
        """Compile regex patterns for efficient matching."""
        compiled = {}
        for intent, config in self.INTENT_PATTERNS.items():
            patterns = []
            for keyword in config["keywords"]:
                # Create flexible pattern that matches word boundaries
                pattern = re.compile(r'\b' + re.escape(keyword.lower()) + r'\b', re.IGNORECASE)
                patterns.append(pattern)
            compiled[intent] = patterns
        return compiled
    
    def classify_intent(self, prompt: str) -> str:
        """
        Classify the intent of a user prompt into categories.
        
        Args:
            prompt: The user's natural language prompt
            
        Returns:
            The detected intent category as a string
        """
        result = self.classify_intent_detailed(prompt)
        return result.intent
    
    def classify_intent_detailed(self, prompt: str) -> IntentMatch:
        """
        Classify intent with detailed confidence scoring and matched keywords.
        
        Args:
            prompt: The user's natural language prompt
            
        Returns:
            IntentMatch object with detailed classification results
        """
        if not prompt or not prompt.strip():
            return IntentMatch("unknown", 0.0, [], "No prompt provided")
        
        prompt_lower = prompt.lower().strip()
        intent_scores = {}
        intent_matches = {}
        
        # Score each intent based on keyword matches
        for intent, patterns in self.compiled_patterns.items():
            matches = []
            score = 0.0
            
            for pattern in patterns:
                if pattern.search(prompt_lower):
                    keyword = pattern.pattern.replace(r'\b', '').replace('\\', '')
                    matches.append(keyword)
                    # Weight score by intent importance and keyword specificity
                    score += self.INTENT_PATTERNS[intent]["weight"]
            
            if matches:
                # Normalize score by number of total keywords for this intent
                normalized_score = score / len(self.INTENT_PATTERNS[intent]["keywords"])
                intent_scores[intent] = normalized_score
                intent_matches[intent] = matches
        
        # Find the highest scoring intent
        if intent_scores:
            best_intent = max(intent_scores.keys(), key=lambda x: intent_scores[x])
            confidence = min(intent_scores[best_intent], 1.0)  # Cap at 1.0
            
            return IntentMatch(
                intent=best_intent,
                confidence=confidence,
                matched_keywords=intent_matches[best_intent],
                description=self.INTENT_PATTERNS[best_intent]["description"]
            )
        
        return IntentMatch("unknown", 0.0, [], "No matching intent patterns found")
    
    def get_intent_suggestions(self, prompt: str, threshold: float = 0.1) -> List[IntentMatch]:
        """
        Get multiple intent suggestions above a confidence threshold.
        
        Args:
            prompt: The user's natural language prompt
            threshold: Minimum confidence threshold for suggestions
            
        Returns:
            List of IntentMatch objects sorted by confidence
        """
        if not prompt or not prompt.strip():
            return []
        
        prompt_lower = prompt.lower().strip()
        suggestions = []
        
        for intent, patterns in self.compiled_patterns.items():
            matches = []
            score = 0.0
            
            for pattern in patterns:
                if pattern.search(prompt_lower):
                    keyword = pattern.pattern.replace(r'\b', '').replace('\\', '')
                    matches.append(keyword)
                    score += self.INTENT_PATTERNS[intent]["weight"]
            
            if matches:
                normalized_score = score / len(self.INTENT_PATTERNS[intent]["keywords"])
                if normalized_score >= threshold:
                    suggestions.append(IntentMatch(
                        intent=intent,
                        confidence=min(normalized_score, 1.0),
                        matched_keywords=matches,
                        description=self.INTENT_PATTERNS[intent]["description"]
                    ))
        
        # Sort by confidence (highest first)
        return sorted(suggestions, key=lambda x: x.confidence, reverse=True)


# Global classifier instance
_classifier = IntentClassifier()


def classify_intent(prompt: str) -> str:
    """
    Classify the intent of a user prompt into categories like refactor, optimize, document, etc.
    
    Args:
        prompt: The user's natural language prompt
        
    Returns:
        The detected intent category as a string
        
    Examples:
        >>> classify_intent("refactor this code to make it cleaner")
        'refactor'
        >>> classify_intent("optimize the performance of this function")
        'optimize'
        >>> classify_intent("add documentation to this module")
        'document'
    """
    return _classifier.classify_intent(prompt)


def classify_intent_detailed(prompt: str) -> IntentMatch:
    """
    Classify intent with detailed confidence scoring and matched keywords.
    
    Args:
        prompt: The user's natural language prompt
        
    Returns:
        IntentMatch object with detailed classification results
    """
    return _classifier.classify_intent_detailed(prompt)


def get_intent_suggestions(prompt: str, threshold: float = 0.1) -> List[IntentMatch]:
    """
    Get multiple intent suggestions above a confidence threshold.
    
    Args:
        prompt: The user's natural language prompt
        threshold: Minimum confidence threshold for suggestions
        
    Returns:
        List of IntentMatch objects sorted by confidence
    """
    return _classifier.get_intent_suggestions(prompt, threshold)


def get_available_intents() -> List[str]:
    """
    Get list of all available intent categories.
    
    Returns:
        List of intent category names
    """
    return list(_classifier.INTENT_PATTERNS.keys())


def get_intent_description(intent: str) -> Optional[str]:
    """
    Get description for a specific intent category.
    
    Args:
        intent: The intent category name
        
    Returns:
        Description string or None if intent not found
    """
    return _classifier.INTENT_PATTERNS.get(intent, {}).get("description")
