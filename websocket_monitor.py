#!/usr/bin/env python3
"""
Real-time WebSocket connection monitor for debugging dashboard issues.
"""

import asyncio
import websockets
import json
from datetime import datetime

async def monitor_websocket():
    """Monitor WebSocket connections in real-time."""
    
    url = "ws://127.0.0.1:8001/ws/logs"
    
    print("🔍 Real-Time WebSocket Monitor")
    print("=" * 50)
    print(f"📡 Monitoring: {url}")
    print(f"🕒 Started: {datetime.now().strftime('%H:%M:%S')}")
    print("💡 This will show all WebSocket activity...")
    print("=" * 50)
    
    connection_count = 0
    
    while True:
        try:
            connection_count += 1
            print(f"\n🔌 Connection attempt #{connection_count} at {datetime.now().strftime('%H:%M:%S')}")
            
            async with websockets.connect(url) as websocket:
                print("✅ Connected successfully!")
                
                # Monitor for messages
                message_count = 0
                while True:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        message_count += 1
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"📨 [{timestamp}] Message #{message_count}: {message}")
                        
                    except asyncio.TimeoutError:
                        # No message received, check if connection is still alive
                        try:
                            await websocket.ping()
                            print(f"💓 [{datetime.now().strftime('%H:%M:%S')}] Connection alive (ping successful)")
                        except:
                            print(f"💔 [{datetime.now().strftime('%H:%M:%S')}] Connection lost (ping failed)")
                            break
                        
        except websockets.exceptions.ConnectionClosed as e:
            print(f"🔌 Connection closed: {e}")
            print("🔄 Reconnecting in 3 seconds...")
            await asyncio.sleep(3)
            
        except Exception as e:
            print(f"❌ Connection error: {e}")
            print("🔄 Retrying in 5 seconds...")
            await asyncio.sleep(5)

if __name__ == "__main__":
    try:
        print("🚀 Starting WebSocket Monitor...")
        print("💡 Press Ctrl+C to stop")
        asyncio.run(monitor_websocket())
    except KeyboardInterrupt:
        print("\n⚠️ Monitor stopped by user")
    except Exception as e:
        print(f"\n💥 Monitor failed: {e}")
