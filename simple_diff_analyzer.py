#!/usr/bin/env python3
"""
Simple Diff Analyzer for CodeCrusher E2E Results
Analyzes existing before/after files and generates diff report
"""

import os
import difflib
from datetime import datetime
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

def analyze_file_diff(before_file: Path, after_file: Path) -> dict:
    """Analyze differences between before and after files."""
    try:
        with open(before_file, 'r', encoding='utf-8') as f:
            before_content = f.read()
        
        with open(after_file, 'r', encoding='utf-8') as f:
            after_content = f.read()
        
        # Generate unified diff
        before_lines = before_content.splitlines()
        after_lines = after_content.splitlines()
        
        diff = list(difflib.unified_diff(
            before_lines, 
            after_lines, 
            fromfile=f"before/{before_file.name}",
            tofile=f"after/{after_file.name}",
            lineterm=""
        ))
        
        if not diff:
            return {
                'rating': '⚠️',
                'status': 'Neutral',
                'impact': 'No changes detected',
                'diff_lines': 0,
                'diff_content': '',
                'before_content': before_content,
                'after_content': after_content
            }
        
        diff_content = '\n'.join(diff)
        diff_lines = len([line for line in diff if line.startswith(('+', '-')) and not line.startswith(('+++', '---'))])
        
        # Analyze improvement patterns
        improvements = 0
        regressions = 0
        
        # Check for improvement indicators
        improvement_patterns = [
            'try:', 'except:', 'raise', 'Error', 'logging',
            'comprehensive', 'detailed', 'extensive',
            'validation', 'check', 'assert', 'import logging',
            'logger', 'traceback'
        ]
        
        regression_patterns = [
            'TODO', 'pass', 'placeholder', 'generic', '# Add'
        ]
        
        for line in after_lines:
            line_lower = line.lower()
            if any(pattern.lower() in line_lower for pattern in improvement_patterns):
                improvements += 1
            if any(pattern.lower() in line_lower for pattern in regression_patterns):
                regressions += 1
        
        # Determine rating
        if improvements > regressions and improvements > 2:
            rating = '✅'
            status = 'Improved'
            impact = f"Enhanced with {improvements} improvement indicators (logging, error handling, validation)"
        elif regressions > improvements:
            rating = '❌'
            status = 'Worse'
            impact = f"Regression with {regressions} generic patterns"
        else:
            rating = '⚠️'
            status = 'Neutral'
            impact = f"Minor changes ({diff_lines} lines modified)"
        
        return {
            'rating': rating,
            'status': status,
            'impact': impact,
            'diff_lines': diff_lines,
            'diff_content': diff_content,
            'improvements': improvements,
            'regressions': regressions,
            'before_content': before_content,
            'after_content': after_content
        }
        
    except Exception as e:
        return {
            'rating': '❌',
            'status': 'Error',
            'impact': f"Analysis failed: {str(e)}",
            'diff_lines': 0,
            'diff_content': '',
            'improvements': 0,
            'regressions': 0,
            'before_content': '',
            'after_content': ''
        }

def main():
    """Main diff analysis function."""
    console.print(Panel(
        "[bold]CodeCrusher Simple Diff Analysis[/bold]\n"
        "Analyzing existing before/after files",
        title="[bold cyan]🔍 Diff Analysis[/bold cyan]",
        border_style="cyan"
    ))
    
    before_dir = Path("./e2e-logs/before")
    after_dir = Path("./e2e-logs/after")
    
    if not before_dir.exists() or not after_dir.exists():
        console.print("[red]❌ Before/after directories not found[/red]")
        return
    
    # Find matching files
    before_files = {f.name: f for f in before_dir.glob("*.diff")}
    after_files = {f.name: f for f in after_dir.glob("*.diff")}
    
    common_files = set(before_files.keys()).intersection(set(after_files.keys()))
    
    if not common_files:
        console.print("[yellow]⚠️ No matching files found for comparison[/yellow]")
        return
    
    console.print(f"[green]Found {len(common_files)} files to analyze[/green]")
    
    results = {
        'improvements': 0,
        'neutral': 0,
        'regressions': 0,
        'analyses': []
    }
    
    # Analyze each file
    for filename in common_files:
        console.print(f"[cyan]Analyzing: {filename}[/cyan]")
        
        analysis = analyze_file_diff(before_files[filename], after_files[filename])
        analysis['filename'] = filename
        results['analyses'].append(analysis)
        
        if analysis['status'] == 'Improved':
            results['improvements'] += 1
        elif analysis['status'] == 'Worse':
            results['regressions'] += 1
        else:
            results['neutral'] += 1
    
    # Display results table
    table = Table(title="Diff Analysis Results")
    table.add_column("File", style="cyan")
    table.add_column("Rating", style="green")
    table.add_column("Status", style="yellow")
    table.add_column("Impact", style="white")
    
    for analysis in results['analyses']:
        table.add_row(
            analysis['filename'],
            analysis['rating'],
            analysis['status'],
            analysis['impact'][:50] + "..." if len(analysis['impact']) > 50 else analysis['impact']
        )
    
    console.print(table)
    
    # Summary
    total = len(results['analyses'])
    console.print(Panel(
        f"[bold]Analysis Summary[/bold]\n\n"
        f"[green]✅ Improved: {results['improvements']} ({results['improvements']/total*100:.1f}%)[/green]\n"
        f"[yellow]⚠️ Neutral: {results['neutral']} ({results['neutral']/total*100:.1f}%)[/yellow]\n"
        f"[red]❌ Worse: {results['regressions']} ({results['regressions']/total*100:.1f}%)[/red]\n\n"
        f"[cyan]Learning Effectiveness: {'🚀 Excellent' if results['improvements']/total > 0.7 else '✅ Good' if results['improvements']/total > 0.5 else '⚠️ Fair'}[/cyan]",
        title="[bold green]📊 Results[/bold green]",
        border_style="green"
    ))
    
    # Show detailed diff for first improved file
    improved_files = [a for a in results['analyses'] if a['status'] == 'Improved']
    if improved_files:
        first_improved = improved_files[0]
        console.print(Panel(
            f"[bold]Sample Improvement: {first_improved['filename']}[/bold]\n\n"
            f"[cyan]Before:[/cyan]\n{first_improved['before_content'][:200]}...\n\n"
            f"[cyan]After:[/cyan]\n{first_improved['after_content'][:200]}...\n\n"
            f"[green]Impact:[/green] {first_improved['impact']}",
            title="[bold blue]🔍 Sample Diff[/bold blue]",
            border_style="blue"
        ))

if __name__ == "__main__":
    main()
