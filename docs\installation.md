# Installation Guide

This guide covers all the ways to install and set up CodeCrusher on your system.

## 🚀 Quick Installation

### Method 1: Install from PyPI (Recommended)

```bash
# Install the latest stable version
pip install codecrusher

# Verify installation
codecrusher --version
codecrusher status
```

### Method 2: Install from Source

```bash
# Clone the repository
git clone https://github.com/Codegx-Technology/CodeCruncher.git
cd CodeCruncher

# Install in development mode
pip install -e .

# Verify installation
codecrusher --version
```

### Method 3: Download CLI Binary

1. **Download from GitHub Releases:**
   - Go to [GitHub Releases](https://github.com/Codegx-Technology/CodeCruncher/releases)
   - Download `codecrusher-vX.X.X-cli.zip`
   - Extract the archive

2. **Install dependencies:**
   ```bash
   # Unix/Linux/Mac
   cd codecrusher-cli
   ./install.sh
   
   # Windows
   cd codecrusher-cli
   install.bat
   ```

3. **Run CodeCrusher:**
   ```bash
   python3 codecrusher_cli.py --help
   ```

---

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **Operating System**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Memory**: 512 MB RAM
- **Storage**: 100 MB free space

### Recommended Requirements
- **Python**: 3.11 or higher
- **Memory**: 2 GB RAM
- **Storage**: 1 GB free space
- **Network**: Stable internet connection for AI model access

---

## 🔧 Detailed Installation

### Python Environment Setup

#### Using Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv codecrusher-env

# Activate virtual environment
# On Windows:
codecrusher-env\Scripts\activate
# On Unix/Linux/Mac:
source codecrusher-env/bin/activate

# Install CodeCrusher
pip install codecrusher
```

#### Using Conda
```bash
# Create conda environment
conda create -n codecrusher python=3.11
conda activate codecrusher

# Install CodeCrusher
pip install codecrusher
```

### Development Installation

For contributors and developers:

```bash
# Clone repository
git clone https://github.com/Codegx-Technology/CodeCruncher.git
cd CodeCruncher

# Install development dependencies
pip install -r requirements-dev.txt

# Install in editable mode
pip install -e .

# Run tests to verify installation
pytest tests/
```

---

## ⚙️ Configuration

### API Keys Setup

CodeCrusher requires API keys for AI model access:

#### 1. Groq API (Primary)
```bash
# Get API key from: https://console.groq.com/keys
export GROQ_API_KEY="gsk_your_api_key_here"
```

#### 2. OpenAI API (Optional)
```bash
# Get API key from: https://platform.openai.com/api-keys
export OPENAI_API_KEY="sk-your_api_key_here"
```

#### 3. Persistent Configuration
Create a `.env` file in your project directory:
```bash
# .env file
GROQ_API_KEY=gsk_your_api_key_here
OPENAI_API_KEY=sk-your_api_key_here
```

### CLI Entry Points

After installation, CodeCrusher provides the `codecrusher` command:

```bash
# Main CLI command
codecrusher --help

# Available subcommands
codecrusher inject --help
codecrusher rate --help
codecrusher learn --help
codecrusher status --help
```

---

## 🧪 Verify Installation

### Basic Verification
```bash
# Check version
codecrusher --version

# Check system status
codecrusher status

# Test basic functionality
codecrusher inject --help
```

### Advanced Verification
```bash
# Test AI injection (dry run)
echo "# AI_INJECT:test_function" > test.py
codecrusher inject test.py --prompt "Create a hello world function" --preview

# Test intelligence system
codecrusher learn --dry-run

# Clean up
rm test.py
```

---

## 🔧 Platform-Specific Instructions

### Windows

#### Using PowerShell
```powershell
# Install Python if not available
# Download from: https://python.org/downloads/

# Install CodeCrusher
pip install codecrusher

# Add to PATH if needed
$env:PATH += ";C:\Python311\Scripts"
```

#### Using Command Prompt
```cmd
REM Install CodeCrusher
pip install codecrusher

REM Verify installation
codecrusher --version
```

### macOS

#### Using Homebrew
```bash
# Install Python via Homebrew
brew install python

# Install CodeCrusher
pip3 install codecrusher

# Add to shell profile if needed
echo 'export PATH="/usr/local/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc
```

### Linux (Ubuntu/Debian)

```bash
# Update package list
sudo apt update

# Install Python and pip
sudo apt install python3 python3-pip

# Install CodeCrusher
pip3 install codecrusher

# Add to PATH if needed
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

### Linux (CentOS/RHEL)

```bash
# Install Python and pip
sudo yum install python3 python3-pip

# Install CodeCrusher
pip3 install codecrusher
```

---

## 🚨 Troubleshooting Installation

### Common Issues

#### 1. Command Not Found
```bash
# Error: codecrusher: command not found

# Solution: Add to PATH
export PATH="$HOME/.local/bin:$PATH"

# Or use full path
python -m codecrusher --help
```

#### 2. Permission Denied
```bash
# Error: Permission denied

# Solution: Use --user flag
pip install --user codecrusher

# Or use virtual environment
python -m venv venv
source venv/bin/activate
pip install codecrusher
```

#### 3. Python Version Issues
```bash
# Error: Python 3.8+ required

# Check Python version
python --version

# Install newer Python or use pyenv
pyenv install 3.11.0
pyenv global 3.11.0
```

#### 4. Dependency Conflicts
```bash
# Error: Dependency conflicts

# Solution: Use fresh virtual environment
python -m venv fresh-env
source fresh-env/bin/activate
pip install codecrusher
```

### Getting Help

If you encounter issues:

1. **Check our [Troubleshooting Guide](troubleshooting.md)**
2. **Search [GitHub Issues](https://github.com/Codegx-Technology/CodeCruncher/issues)**
3. **Create a new issue** with:
   - Operating system and version
   - Python version (`python --version`)
   - Installation method used
   - Complete error message
   - Steps to reproduce

---

## 🔄 Updating CodeCrusher

### Update from PyPI
```bash
# Update to latest version
pip install --upgrade codecrusher

# Verify update
codecrusher --version
```

### Update from Source
```bash
# Pull latest changes
git pull origin main

# Reinstall
pip install -e .
```

---

## 🗑️ Uninstalling CodeCrusher

### Remove Package
```bash
# Uninstall CodeCrusher
pip uninstall codecrusher

# Remove configuration (optional)
rm -rf ~/.codecrusher/
```

### Clean Virtual Environment
```bash
# Deactivate environment
deactivate

# Remove environment directory
rm -rf codecrusher-env/
```

---

## ✅ Next Steps

After successful installation:

1. **[Learn Basic Usage](usage.md)** - Understand core commands and workflows
2. **[Configure Tuning](tuning.md)** - Optimize for your specific needs
3. **[Explore Dashboard](dashboard.md)** - Set up web interface monitoring
4. **[Read Troubleshooting](troubleshooting.md)** - Prepare for common issues

---

**[← Back to Index](index.md)** | **[Next: Usage Guide →](usage.md)**
