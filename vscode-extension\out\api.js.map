{"version": 3, "file": "api.js", "sourceRoot": "", "sources": ["../src/api.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,iCAA0B;AAC1B,iCAAiC;AAEjC,mCAAmC;AACnC,MAAM,YAAY,GAAG,2BAA2B,CAAC;AAqGjD,mBAAmB;AACnB,MAAa,cAAc;IACvB;;OAEG;IACH,MAAM,CAAO,SAAS,CAAC,QAAgB,KAAK,EAAE,KAAc;;YACxD,IAAI;gBACA,MAAM,MAAM,GAAQ,EAAE,KAAK,EAAE,CAAC;gBAC9B,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;iBACxB;gBAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,YAAY,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBACvE,OAAO,QAAQ,CAAC,IAAI,CAAC;aACxB;YAAC,OAAO,KAAK,EAAE;gBACZ,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBACrD,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAED;;OAEG;IACH,MAAM,CAAO,OAAO,CAChB,QAAgB,KAAK,EACrB,KAAc,EACd,GAAY,EACZ,QAAgB,EAAE,EAClB,UAAmB,KAAK;;YAExB,IAAI;gBACA,MAAM,MAAM,GAAQ;oBAChB,KAAK;oBACL,KAAK;oBACL,OAAO;iBACV,CAAC;gBAEF,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;iBACxB;gBAED,IAAI,GAAG,EAAE;oBACL,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;iBACpB;gBAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,YAAY,OAAO,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBACrE,OAAO,QAAQ,CAAC,IAAI,CAAC;aACxB;YAAC,OAAO,KAAK,EAAE;gBACZ,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;gBACjD,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAED;;OAEG;IACH,MAAM,CAAO,SAAS,CAClB,QAAgB,IAAI,EACpB,QAAgB,KAAK,EACrB,KAAc,EACd,GAAY;;YAEZ,IAAI;gBACA,MAAM,MAAM,GAAQ;oBAChB,KAAK;oBACL,KAAK;iBACR,CAAC;gBAEF,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;iBACxB;gBAED,IAAI,GAAG,EAAE;oBACL,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;iBACpB;gBAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,YAAY,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBACvE,OAAO,QAAQ,CAAC,IAAI,CAAC;aACxB;YAAC,OAAO,KAAK,EAAE;gBACZ,IAAI,CAAC,cAAc,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;gBACrD,MAAM,KAAK,CAAC;aACf;QACL,CAAC;KAAA;IAED;;OAEG;IACH,MAAM,CAAO,YAAY,CACrB,QAAgB,GAAG,EACnB,KAAc,EACd,KAAc,EACd,GAAY,EACZ,MAAgB,EAChB,QAAkB,EAClB,KAAe;;YAEf,IAAI;gBACA,MAAM,MAAM,GAAQ,EAAE,KAAK,EAAE,CAAC;gBAE9B,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;iBACxB;gBAED,IAAI,KAAK,EAAE;oBACP,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;iBACxB;gBAED,IAAI,GAAG,EAAE;oBACL,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;iBACpB;gBAED,IAAI,MAAM,KAAK,SAAS,EAAE;oBACtB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;iBAC1B;gBAED,IAAI,QAAQ,KAAK,SAAS,EAAE;oBACxB,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;iBAC9B;gBAED,IAAI,KAAK,KAAK,SAAS,EAAE;oBACrB,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;iBACxB;gBAED,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,YAAY,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC1E,OAAO,QAAQ,CAAC,IAAI,CAAC;aACxB;YAAC,OAAO,KAAK,EAAE;gBACZ,IAAI,CAAC,cAAc,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;gBACxD,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;aACpC;QACL,CAAC;KAAA;IAED;;OAEG;IACH,MAAM,CAAO,eAAe;;YACxB,IAAI;gBACA,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,YAAY,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7D,OAAO,IAAI,CAAC;aACf;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,KAAK,CAAC;aAChB;QACL,CAAC;KAAA;IAED;;OAEG;IACK,MAAM,CAAC,cAAc,CAAC,OAAe,EAAE,KAAU;QACrD,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;QAEpC,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;YAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE;gBAC/B,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC1B,+FAA+F,CAClG,CAAC;aACL;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAC1B,0BAA0B,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAM,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,eAAe,EAAE,CACvG,CAAC;aACL;iBAAM;gBACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;aAC7E;SACJ;aAAM;YACH,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,0BAA0B,KAAK,EAAE,CAAC,CAAC;SACrE;IACL,CAAC;CACJ;AApKD,wCAoKC"}