"""
Git hooks command for CodeCrusher.

This module provides the githooks command for CodeCrusher, which installs
and manages Git hooks for automating injection tagging, telemetry metadata
collection, and telemetry log syncing.
"""

import typer
import os
import sys
import shutil
import subprocess
import json
from pathlib import Path
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

# Constants
HOOKS_DIR = Path(__file__).parent.parent.parent / "git-hooks"
CONFIG_DIR = Path.home() / ".codecrusher"
CONFIG_FILE = CONFIG_DIR / "githooks.json"

def get_git_hooks_dir(repo_path: Optional[str] = None) -> Optional[Path]:
    """
    Get the Git hooks directory for the current or specified repository.
    
    Args:
        repo_path: Path to the Git repository (optional)
        
    Returns:
        Path to the Git hooks directory or None if not found
    """
    cmd = ["git", "rev-parse", "--git-dir"]
    
    if repo_path:
        cwd = repo_path
    else:
        cwd = os.getcwd()
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=cwd
        )
        
        if result.returncode != 0:
            console.print(f"[bold red]Error:[/bold red] {result.stderr}")
            console.print("[yellow]Are you in a Git repository?[/yellow]")
            return None
        
        git_dir = result.stdout.strip()
        
        # Handle relative paths
        if not os.path.isabs(git_dir):
            git_dir = os.path.join(cwd, git_dir)
        
        hooks_dir = os.path.join(git_dir, "hooks")
        return Path(hooks_dir)
    
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")
        return None

def save_config(config: Dict[str, Any]) -> None:
    """
    Save Git hooks configuration.
    
    Args:
        config: Configuration dictionary
    """
    CONFIG_DIR.mkdir(exist_ok=True)
    
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2)

def load_config() -> Dict[str, Any]:
    """
    Load Git hooks configuration.
    
    Returns:
        Configuration dictionary
    """
    if not CONFIG_FILE.exists():
        return {
            "block_on_errors": False,
            "auto_insert_tags": False,
            "remote_sync": False,
            "remote_sync_url": "",
            "installed_repos": []
        }
    
    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {
            "block_on_errors": False,
            "auto_insert_tags": False,
            "remote_sync": False,
            "remote_sync_url": "",
            "installed_repos": []
        }

def install_hook(hooks_dir: Path, hook_name: str, config: Dict[str, Any]) -> bool:
    """
    Install a Git hook.
    
    Args:
        hooks_dir: Path to the Git hooks directory
        hook_name: Name of the hook to install
        config: Configuration dictionary
        
    Returns:
        True if successful, False otherwise
    """
    # Source hook path
    source_path = HOOKS_DIR / hook_name
    
    if not source_path.exists():
        console.print(f"[bold red]Error:[/bold red] Hook {hook_name} not found at {source_path}")
        return False
    
    # Destination hook path
    dest_path = hooks_dir / hook_name
    
    # Check if hook already exists
    if dest_path.exists():
        backup_path = dest_path.with_suffix(".bak")
        console.print(f"Hook {hook_name} already exists. Creating backup at {backup_path}")
        shutil.copy2(dest_path, backup_path)
    
    # Copy hook
    shutil.copy2(source_path, dest_path)
    
    # Make hook executable
    os.chmod(dest_path, 0o755)
    
    # Create hook config file
    hook_config_path = hooks_dir / f"{hook_name}.config.json"
    with open(hook_config_path, "w", encoding="utf-8") as f:
        json.dump({
            "block_on_errors": config["block_on_errors"],
            "auto_insert_tags": config["auto_insert_tags"],
            "remote_sync": config["remote_sync"],
            "remote_sync_url": config["remote_sync_url"]
        }, f, indent=2)
    
    console.print(f"[green]Installed {hook_name} hook.[/green]")
    return True

def uninstall_hook(hooks_dir: Path, hook_name: str) -> bool:
    """
    Uninstall a Git hook.
    
    Args:
        hooks_dir: Path to the Git hooks directory
        hook_name: Name of the hook to uninstall
        
    Returns:
        True if successful, False otherwise
    """
    # Hook path
    hook_path = hooks_dir / hook_name
    
    # Check if hook exists
    if not hook_path.exists():
        console.print(f"[yellow]Hook {hook_name} not found.[/yellow]")
        return False
    
    # Check if backup exists
    backup_path = hook_path.with_suffix(".bak")
    
    if backup_path.exists():
        # Restore backup
        shutil.copy2(backup_path, hook_path)
        os.remove(backup_path)
        console.print(f"[green]Restored backup of {hook_name} hook.[/green]")
    else:
        # Remove hook
        os.remove(hook_path)
        console.print(f"[green]Removed {hook_name} hook.[/green]")
    
    # Remove hook config file
    hook_config_path = hooks_dir / f"{hook_name}.config.json"
    if hook_config_path.exists():
        os.remove(hook_config_path)
    
    return True

@app.command("install")
def install_githooks(
    repo_path: Optional[str] = typer.Option(
        None, "--repo-path", "-r",
        help="Path to the Git repository (default: current directory)"
    ),
    block_on_errors: bool = typer.Option(
        False, "--block-on-errors", "-b",
        help="Block commits if critical injection errors are detected"
    ),
    auto_insert_tags: bool = typer.Option(
        False, "--auto-insert-tags", "-a",
        help="Automatically insert missing tags based on context"
    ),
    remote_sync: bool = typer.Option(
        False, "--remote-sync", "-s",
        help="Sync telemetry metadata to a remote server"
    ),
    remote_sync_url: str = typer.Option(
        "", "--remote-sync-url", "-u",
        help="URL of the remote server for telemetry sync"
    ),
):
    """
    Install CodeCrusher Git hooks.
    
    This command installs Git hooks for automating injection tagging,
    telemetry metadata collection, and telemetry log syncing.
    
    Examples:
        codecrusher githooks install
        codecrusher githooks install --block-on-errors
        codecrusher githooks install --auto-insert-tags
        codecrusher githooks install --remote-sync --remote-sync-url https://example.com/api/telemetry
    """
    # Get Git hooks directory
    hooks_dir = get_git_hooks_dir(repo_path)
    
    if not hooks_dir:
        return False
    
    # Create hooks directory if it doesn't exist
    hooks_dir.mkdir(exist_ok=True)
    
    # Load config
    config = load_config()
    
    # Update config
    config["block_on_errors"] = block_on_errors
    config["auto_insert_tags"] = auto_insert_tags
    config["remote_sync"] = remote_sync
    config["remote_sync_url"] = remote_sync_url
    
    # Get absolute path of repo
    if repo_path:
        repo_abs_path = os.path.abspath(repo_path)
    else:
        repo_abs_path = os.path.abspath(os.getcwd())
    
    # Add repo to installed repos if not already present
    if repo_abs_path not in config["installed_repos"]:
        config["installed_repos"].append(repo_abs_path)
    
    # Save config
    save_config(config)
    
    # Install hooks
    success = True
    if not install_hook(hooks_dir, "pre-commit", config):
        success = False
    if not install_hook(hooks_dir, "post-commit", config):
        success = False
    
    if success:
        console.print(Panel(
            "[bold green]CodeCrusher Git hooks installed successfully![/bold green]\n\n"
            f"[cyan]Repository:[/cyan] {repo_abs_path}\n"
            f"[cyan]Hooks Directory:[/cyan] {hooks_dir}\n"
            f"[cyan]Block on Errors:[/cyan] {'Enabled' if block_on_errors else 'Disabled'}\n"
            f"[cyan]Auto Insert Tags:[/cyan] {'Enabled' if auto_insert_tags else 'Disabled'}\n"
            f"[cyan]Remote Sync:[/cyan] {'Enabled' if remote_sync else 'Disabled'}\n"
            f"[cyan]Remote Sync URL:[/cyan] {remote_sync_url or 'N/A'}",
            title="Installation Complete",
            border_style="green"
        ))
    else:
        console.print("[bold red]Installation failed. Please check the errors above.[/bold red]")
    
    return success

@app.command("uninstall")
def uninstall_githooks(
    repo_path: Optional[str] = typer.Option(
        None, "--repo-path", "-r",
        help="Path to the Git repository (default: current directory)"
    ),
):
    """
    Uninstall CodeCrusher Git hooks.
    
    This command uninstalls Git hooks from the current or specified repository.
    
    Examples:
        codecrusher githooks uninstall
        codecrusher githooks uninstall --repo-path /path/to/repo
    """
    # Get Git hooks directory
    hooks_dir = get_git_hooks_dir(repo_path)
    
    if not hooks_dir:
        return False
    
    # Uninstall hooks
    success = True
    if not uninstall_hook(hooks_dir, "pre-commit"):
        success = False
    if not uninstall_hook(hooks_dir, "post-commit"):
        success = False
    
    # Load config
    config = load_config()
    
    # Get absolute path of repo
    if repo_path:
        repo_abs_path = os.path.abspath(repo_path)
    else:
        repo_abs_path = os.path.abspath(os.getcwd())
    
    # Remove repo from installed repos
    if repo_abs_path in config["installed_repos"]:
        config["installed_repos"].remove(repo_abs_path)
    
    # Save config
    save_config(config)
    
    if success:
        console.print(Panel(
            "[bold green]CodeCrusher Git hooks uninstalled successfully![/bold green]\n\n"
            f"[cyan]Repository:[/cyan] {repo_abs_path}\n"
            f"[cyan]Hooks Directory:[/cyan] {hooks_dir}",
            title="Uninstallation Complete",
            border_style="green"
        ))
    else:
        console.print("[bold red]Uninstallation failed. Please check the errors above.[/bold red]")
    
    return success

@app.command("status")
def githooks_status(
    repo_path: Optional[str] = typer.Option(
        None, "--repo-path", "-r",
        help="Path to the Git repository (default: current directory)"
    ),
):
    """
    Check the status of CodeCrusher Git hooks.
    
    This command checks if Git hooks are installed and displays their configuration.
    
    Examples:
        codecrusher githooks status
        codecrusher githooks status --repo-path /path/to/repo
    """
    # Get Git hooks directory
    hooks_dir = get_git_hooks_dir(repo_path)
    
    if not hooks_dir:
        return False
    
    # Check if hooks are installed
    pre_commit_installed = (hooks_dir / "pre-commit").exists()
    post_commit_installed = (hooks_dir / "post-commit").exists()
    
    # Load hook configs
    pre_commit_config = {}
    post_commit_config = {}
    
    pre_commit_config_path = hooks_dir / "pre-commit.config.json"
    post_commit_config_path = hooks_dir / "post-commit.config.json"
    
    if pre_commit_config_path.exists():
        try:
            with open(pre_commit_config_path, "r", encoding="utf-8") as f:
                pre_commit_config = json.load(f)
        except Exception:
            pre_commit_config = {}
    
    if post_commit_config_path.exists():
        try:
            with open(post_commit_config_path, "r", encoding="utf-8") as f:
                post_commit_config = json.load(f)
        except Exception:
            post_commit_config = {}
    
    # Display status
    console.print(Panel(
        f"[bold]CodeCrusher Git Hooks Status[/bold]\n\n"
        f"[cyan]Repository:[/cyan] {os.path.abspath(repo_path or os.getcwd())}\n"
        f"[cyan]Hooks Directory:[/cyan] {hooks_dir}\n\n"
        f"[bold]Pre-commit Hook:[/bold] {'[green]Installed[/green]' if pre_commit_installed else '[red]Not Installed[/red]'}\n"
        f"[cyan]Block on Errors:[/cyan] {'Enabled' if pre_commit_config.get('block_on_errors', False) else 'Disabled'}\n"
        f"[cyan]Auto Insert Tags:[/cyan] {'Enabled' if pre_commit_config.get('auto_insert_tags', False) else 'Disabled'}\n\n"
        f"[bold]Post-commit Hook:[/bold] {'[green]Installed[/green]' if post_commit_installed else '[red]Not Installed[/red]'}\n"
        f"[cyan]Remote Sync:[/cyan] {'Enabled' if post_commit_config.get('remote_sync', False) else 'Disabled'}\n"
        f"[cyan]Remote Sync URL:[/cyan] {post_commit_config.get('remote_sync_url', 'N/A')}",
        title="Git Hooks Status",
        border_style="blue"
    ))
    
    return True

@app.command("help")
def githooks_help():
    """
    Show help for CodeCrusher Git hooks.
    
    This command displays detailed information about the Git hooks and how to use them.
    
    Examples:
        codecrusher githooks help
    """
    help_text = """
    # CodeCrusher Git Hooks

    Git hooks for CodeCrusher integration.

    ## Features

    ### Pre-commit Hook
    - Automatically scans staged code for injection points
    - Suggests tags based on function names and TODO comments
    - Warns about possible anomalies
    - Can block commits if critical errors are detected

    ### Post-commit Hook
    - Pushes telemetry summaries to a central log
    - Adds branch information to telemetry data
    - Tracks injections per branch for better team collaboration
    - Can sync telemetry data to a remote server

    ## Commands

    ### Install Git Hooks
    ```
    codecrusher githooks install
    ```

    Options:
    - `--repo-path, -r`: Path to the Git repository (default: current directory)
    - `--block-on-errors, -b`: Block commits if critical injection errors are detected
    - `--auto-insert-tags, -a`: Automatically insert missing tags based on context
    - `--remote-sync, -s`: Sync telemetry metadata to a remote server
    - `--remote-sync-url, -u`: URL of the remote server for telemetry sync

    ### Uninstall Git Hooks
    ```
    codecrusher githooks uninstall
    ```

    Options:
    - `--repo-path, -r`: Path to the Git repository (default: current directory)

    ### Check Git Hooks Status
    ```
    codecrusher githooks status
    ```

    Options:
    - `--repo-path, -r`: Path to the Git repository (default: current directory)
    """
    
    console.print(Markdown(help_text))
    
    return True
