#!/usr/bin/env python3
"""
CodeCrusher E2E Diff Analyzer
Automated diff analysis between original and learned injections
"""

import os
import sys
import json
import subprocess
import difflib
from datetime import datetime
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()

class E2EDiffAnalyzer:
    """Automated diff analyzer for CodeCrusher intelligence improvements."""
    
    def __init__(self):
        self.logs_dir = Path("./e2e-logs")
        self.before_dir = self.logs_dir / "before"
        self.after_dir = self.logs_dir / "after"
        self.report_file = self.logs_dir / "diff-report.md"
        
        # Ensure directories exist
        self.logs_dir.mkdir(exist_ok=True)
        self.before_dir.mkdir(exist_ok=True)
        self.after_dir.mkdir(exist_ok=True)
        
        self.test_prompt = "Add comprehensive error handling and logging"
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'files_analyzed': 0,
            'improvements': 0,
            'neutral_changes': 0,
            'regressions': 0,
            'comparisons': []
        }
    
    def run_injection_and_capture(self, output_dir: Path, phase: str) -> dict:
        """Run injection and capture output to files."""
        console.print(Panel(
            f"[bold]Capturing {phase} Injection Results[/bold]\n"
            f"Output directory: {output_dir}",
            title=f"[bold cyan]📸 {phase} Capture[/bold cyan]",
            border_style="cyan"
        ))
        
        # Run injection command
        command = [
            "python", "codecrusher_cli.py", "inject", "./test-cases", 
            "--recursive", "--preview", "--prompt", self.test_prompt
        ]
        
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode != 0:
                console.print(f"[red]❌ Injection failed: {result.stderr}[/red]")
                return {'success': False, 'error': result.stderr}
            
            # Parse output to extract file changes
            output_lines = result.stdout.split('\n')
            current_file = None
            file_content = []
            files_captured = {}
            
            in_preview = False
            for line in output_lines:
                if "Preview of Changes:" in line:
                    in_preview = True
                    continue
                
                if in_preview:
                    if line.startswith("File: "):
                        # Save previous file if exists
                        if current_file and file_content:
                            files_captured[current_file] = '\n'.join(file_content)
                        
                        # Start new file
                        current_file = line.replace("File: ", "").strip()
                        file_content = []
                    elif line.startswith("Use --apply"):
                        # End of preview
                        if current_file and file_content:
                            files_captured[current_file] = '\n'.join(file_content)
                        break
                    else:
                        file_content.append(line)
            
            # Save captured files to disk
            for filename, content in files_captured.items():
                # Clean filename for filesystem
                safe_filename = filename.replace("\\", "_").replace("/", "_")
                output_file = output_dir / f"{safe_filename}.diff"
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)
            
            console.print(f"[green]✅ Captured {len(files_captured)} files[/green]")
            return {
                'success': True,
                'files_captured': len(files_captured),
                'files': list(files_captured.keys())
            }
            
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': 'Command timed out'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def analyze_diff(self, before_content: str, after_content: str, filename: str) -> dict:
        """Analyze differences between before and after content."""
        before_lines = before_content.splitlines()
        after_lines = after_content.splitlines()
        
        # Generate unified diff
        diff = list(difflib.unified_diff(
            before_lines, 
            after_lines, 
            fromfile=f"before/{filename}",
            tofile=f"after/{filename}",
            lineterm=""
        ))
        
        if not diff:
            return {
                'rating': '⚠️',
                'status': 'Neutral',
                'impact': 'No changes detected',
                'diff_lines': 0,
                'diff_content': ''
            }
        
        diff_content = '\n'.join(diff)
        diff_lines = len([line for line in diff if line.startswith(('+', '-')) and not line.startswith(('+++', '---'))])
        
        # Analyze improvement patterns
        improvements = 0
        regressions = 0
        
        # Check for improvement indicators
        improvement_patterns = [
            'try:', 'except:', 'raise', 'Error', 'logging',
            'comprehensive', 'detailed', 'extensive',
            'validation', 'check', 'assert'
        ]
        
        regression_patterns = [
            'TODO', 'pass', 'placeholder', 'generic'
        ]
        
        for line in after_lines:
            line_lower = line.lower()
            if any(pattern in line_lower for pattern in improvement_patterns):
                improvements += 1
            if any(pattern in line_lower for pattern in regression_patterns):
                regressions += 1
        
        # Determine rating
        if improvements > regressions and improvements > 2:
            rating = '✅'
            status = 'Improved'
            impact = f"Enhanced with {improvements} improvement indicators"
        elif regressions > improvements:
            rating = '❌'
            status = 'Worse'
            impact = f"Regression with {regressions} generic patterns"
        else:
            rating = '⚠️'
            status = 'Neutral'
            impact = f"Minor changes ({diff_lines} lines modified)"
        
        return {
            'rating': rating,
            'status': status,
            'impact': impact,
            'diff_lines': diff_lines,
            'diff_content': diff_content,
            'improvements': improvements,
            'regressions': regressions
        }
    
    def generate_markdown_report(self, comparisons: list) -> str:
        """Generate markdown diff report."""
        report = f"""# CodeCrusher E2E Diff Analysis Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Test Prompt:** `{self.test_prompt}`  
**Files Analyzed:** {self.results['files_analyzed']}

## Summary

| Status | Count | Percentage |
|--------|-------|------------|
| ✅ Improved | {self.results['improvements']} | {(self.results['improvements']/max(self.results['files_analyzed'], 1)*100):.1f}% |
| ⚠️ Neutral | {self.results['neutral_changes']} | {(self.results['neutral_changes']/max(self.results['files_analyzed'], 1)*100):.1f}% |
| ❌ Worse | {self.results['regressions']} | {(self.results['regressions']/max(self.results['files_analyzed'], 1)*100):.1f}% |

## Detailed Analysis

"""
        
        for comp in comparisons:
            report += f"""### File: `{comp['filename']}`

**Change Rating:** {comp['analysis']['rating']} {comp['analysis']['status']}

**Before:**
```diff
{comp['before_preview']}
```

**After:**
```diff
{comp['after_preview']}
```

**Impact:** {comp['analysis']['impact']}

**Diff Summary:**
- Lines changed: {comp['analysis']['diff_lines']}
- Improvement indicators: {comp['analysis']['improvements']}
- Regression indicators: {comp['analysis']['regressions']}

---

"""
        
        report += f"""## Intelligence Learning Assessment

The CodeCrusher intelligence system processed {self.results['files_analyzed']} files and showed:

- **{self.results['improvements']} improvements** - Enhanced code quality, error handling, or functionality
- **{self.results['neutral_changes']} neutral changes** - Minor modifications without significant impact  
- **{self.results['regressions']} regressions** - Potential quality decreases or generic outputs

### Learning Effectiveness: {self._calculate_effectiveness()}

"""
        return report
    
    def _calculate_effectiveness(self) -> str:
        """Calculate learning effectiveness score."""
        total = self.results['files_analyzed']
        if total == 0:
            return "No data"
        
        score = (self.results['improvements'] * 2 - self.results['regressions']) / total
        
        if score >= 1.0:
            return "🚀 Excellent (High improvement rate)"
        elif score >= 0.5:
            return "✅ Good (Positive improvements)"
        elif score >= 0:
            return "⚠️ Fair (Mixed results)"
        else:
            return "❌ Poor (More regressions than improvements)"
    
    def run_full_analysis(self):
        """Run complete E2E diff analysis."""
        console.print(Panel(
            "[bold]CodeCrusher E2E Diff Analysis[/bold]\n\n"
            "[cyan]This analysis will:[/cyan]\n"
            "1. Capture baseline injection results\n"
            "2. Trigger intelligence learning\n"
            "3. Capture improved injection results\n"
            "4. Generate detailed diff report\n\n"
            "[yellow]Results will be saved to ./e2e-logs/diff-report.md[/yellow]",
            title="[bold cyan]🔍 E2E Diff Analysis[/bold cyan]",
            border_style="cyan"
        ))
        
        try:
            # Step 1: Capture baseline (before learning)
            console.print("\n[bold blue]Step 1: Capturing Baseline Results[/bold blue]")
            before_result = self.run_injection_and_capture(self.before_dir, "Baseline")
            
            if not before_result['success']:
                console.print(f"[red]❌ Failed to capture baseline: {before_result['error']}[/red]")
                return False
            
            # Step 2: Trigger learning (simulate poor rating)
            console.print("\n[bold blue]Step 2: Triggering Intelligence Learning[/bold blue]")
            rate_command = [
                "python", "codecrusher_cli.py", "rate", "./test-cases", 
                "--recursive", "--rating", "2", "--comment", "Generic output, needs improvement"
            ]
            
            rate_result = subprocess.run(rate_command, capture_output=True, text=True)
            if rate_result.returncode != 0:
                console.print(f"[red]❌ Rating failed: {rate_result.stderr}[/red]")
                return False
            
            learn_command = ["python", "codecrusher_cli.py", "learn", "--apply"]
            learn_result = subprocess.run(learn_command, capture_output=True, text=True)
            if learn_result.returncode != 0:
                console.print(f"[red]❌ Learning failed: {learn_result.stderr}[/red]")
                return False
            
            console.print("[green]✅ Intelligence learning completed[/green]")
            
            # Step 3: Capture improved results (after learning)
            console.print("\n[bold blue]Step 3: Capturing Improved Results[/bold blue]")
            after_result = self.run_injection_and_capture(self.after_dir, "Improved")
            
            if not after_result['success']:
                console.print(f"[red]❌ Failed to capture improved results: {after_result['error']}[/red]")
                return False
            
            # Step 4: Analyze differences
            console.print("\n[bold blue]Step 4: Analyzing Differences[/bold blue]")
            comparisons = []
            
            # Compare files that exist in both before and after
            before_files = set(os.listdir(self.before_dir))
            after_files = set(os.listdir(self.after_dir))
            common_files = before_files.intersection(after_files)
            
            for filename in common_files:
                before_file = self.before_dir / filename
                after_file = self.after_dir / filename
                
                with open(before_file, 'r', encoding='utf-8') as f:
                    before_content = f.read()
                
                with open(after_file, 'r', encoding='utf-8') as f:
                    after_content = f.read()
                
                analysis = self.analyze_diff(before_content, after_content, filename)
                
                # Create preview snippets
                before_preview = before_content[:300] + "..." if len(before_content) > 300 else before_content
                after_preview = after_content[:300] + "..." if len(after_content) > 300 else after_content
                
                comparison = {
                    'filename': filename,
                    'analysis': analysis,
                    'before_preview': before_preview,
                    'after_preview': after_preview
                }
                
                comparisons.append(comparison)
                
                # Update results
                if analysis['status'] == 'Improved':
                    self.results['improvements'] += 1
                elif analysis['status'] == 'Worse':
                    self.results['regressions'] += 1
                else:
                    self.results['neutral_changes'] += 1
            
            self.results['files_analyzed'] = len(comparisons)
            self.results['comparisons'] = comparisons
            
            # Step 5: Generate report
            console.print("\n[bold blue]Step 5: Generating Diff Report[/bold blue]")
            report_content = self.generate_markdown_report(comparisons)
            
            with open(self.report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            # Display summary
            table = Table(title="E2E Diff Analysis Results")
            table.add_column("Status", style="cyan")
            table.add_column("Count", style="green")
            table.add_column("Files", style="yellow")
            
            improved_files = [c['filename'] for c in comparisons if c['analysis']['status'] == 'Improved']
            neutral_files = [c['filename'] for c in comparisons if c['analysis']['status'] == 'Neutral']
            worse_files = [c['filename'] for c in comparisons if c['analysis']['status'] == 'Worse']
            
            table.add_row("✅ Improved", str(self.results['improvements']), ", ".join(improved_files[:3]))
            table.add_row("⚠️ Neutral", str(self.results['neutral_changes']), ", ".join(neutral_files[:3]))
            table.add_row("❌ Worse", str(self.results['regressions']), ", ".join(worse_files[:3]))
            
            console.print(table)
            
            console.print(Panel(
                f"[bold]Analysis Complete![/bold]\n\n"
                f"[cyan]Files Analyzed:[/cyan] {self.results['files_analyzed']}\n"
                f"[cyan]Learning Effectiveness:[/cyan] {self._calculate_effectiveness()}\n"
                f"[cyan]Report Saved:[/cyan] {self.report_file}\n\n"
                f"[dim]View the detailed report for complete diff analysis[/dim]",
                title="[bold green]🎉 Diff Analysis Complete[/bold green]",
                border_style="green"
            ))
            
            return True
            
        except Exception as e:
            console.print(Panel(
                f"[red]Analysis failed with exception: {str(e)}[/red]",
                title="[bold red]❌ Analysis Error[/bold red]",
                border_style="red"
            ))
            return False

def main():
    """Main entry point for E2E diff analysis."""
    analyzer = E2EDiffAnalyzer()
    success = analyzer.run_full_analysis()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
