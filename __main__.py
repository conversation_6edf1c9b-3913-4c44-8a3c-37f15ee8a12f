#!/usr/bin/env python3
"""
CodeCrusher CLI Main Entry Point
This module serves as the main entry point for the codecrusher command
when installed via pyproject.toml [project.scripts]
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """
    Main entry point for the codecrusher CLI command.
    This function is called when 'codecrusher' is executed from the command line.
    """
    try:
        # Import and run the CLI from cli.py
        from cli import main as cli_main
        cli_main()
    except ImportError as e:
        print(f"Error importing CLI module: {e}")
        print("Make sure all dependencies are installed.")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
