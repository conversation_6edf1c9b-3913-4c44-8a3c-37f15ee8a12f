import React, { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Terminal, Play, Square, Brain, CheckCircle, XCircle } from 'lucide-react';
import { EnterpriseFooter } from './EnterpriseFooter';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface StreamlinedDashboardProps {
  viewMode?: string;
  setViewMode?: (mode: any) => void;
  healthStatus?: any;
  wsConnected?: boolean;
  getStatusIcon?: () => React.ReactElement;
  getStatusText?: () => string;
}

const StreamlinedDashboard = ({
  viewMode,
  setViewMode,
  healthStatus,
  wsConnected,
  getStatusIcon,
  getStatusText
}: StreamlinedDashboardProps = {}) => {
  const [logs, setLogs] = useState<string[]>([]);
  const [progress, setProgress] = useState(0);
  const [model, setModel] = useState('mixtral');
  const [fallback, setFallback] = useState(true);
  const [isRunning, setIsRunning] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [jobWebSocket, setJobWebSocket] = useState<WebSocket | null>(null);
  const [filePath, setFilePath] = useState('./src');
  const [prompt, setPrompt] = useState('Optimize code for better performance and readability');

  useEffect(() => {
    const ws = new WebSocket('ws://localhost:8001/ws/logs');

    ws.onopen = () => {
      setIsConnected(true);
      console.log('🔗 WebSocket connected');
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        // Handle different message types
        if (data.type === 'progress' && typeof data.progress === 'number') {
          setProgress(data.progress);
        }

        // Add all messages to logs with timestamp
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${data.message || event.data}`;
        setLogs((prev) => [...prev.slice(-50), logMessage]); // Keep last 50 logs

      } catch (error) {
        // Handle non-JSON messages
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${event.data}`;
        setLogs((prev) => [...prev.slice(-50), logMessage]);
      }
    };

    ws.onclose = () => {
      setIsConnected(false);
      console.log('🔌 WebSocket disconnected');
    };

    ws.onerror = (error) => {
      console.error('🚨 WebSocket error:', error);
      setIsConnected(false);
    };

    return () => ws.close();
  }, []);

  const handleRun = async () => {
    setIsRunning(true);
    setProgress(0);
    setLogs([]); // Clear previous logs

    try {
      // Start the injection job
      const response = await fetch('http://localhost:8001/inject', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: prompt,
          file_path: filePath,
          model: model,
          apply: true,
          tags: ['streamlined-dashboard']
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Success handling
        console.log('✅ Injection completed successfully!', result);

        // Update progress from result
        setProgress(100);

        // Add success message to logs
        const timestamp = new Date().toLocaleTimeString();
        const successMessage = `[${timestamp}] ✅ Injection completed successfully! Files processed: ${result.total_files}`;
        setLogs((prev) => [...prev.slice(-50), successMessage]);

        setIsRunning(false);
      } else {
        // Error handling
        console.error('❌ Injection failed:', result);

        // Add error message to logs
        const timestamp = new Date().toLocaleTimeString();
        const errorMessage = `[${timestamp}] ❌ Injection failed: ${result.error || result.message}`;
        setLogs((prev) => [...prev.slice(-50), errorMessage]);

        setIsRunning(false);
      }

    } catch (error) {
      console.error('💥 Request failed:', error);
      setIsRunning(false);
    }
  };

  const handleStop = () => {
    setIsRunning(false);

    // Close job WebSocket if active
    if (jobWebSocket) {
      jobWebSocket.close();
      setJobWebSocket(null);
    }

    setCurrentJobId(null);
    setProgress(0);
  };

  const handleTestMessage = async () => {
    try {
      await fetch('http://localhost:8001/test-log', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'message=🧪 Test message from Streamlined Dashboard&level=info'
      });
    } catch (error) {
      console.error('Failed to send test message:', error);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-100">
      {/* Navigation Bar */}
      {setViewMode && (
        <div className="bg-white/95 backdrop-blur-xl border-b border-gray-200/30 shadow-lg">
          <div className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3">
            {/* Top Row: Logo + Primary Navigation */}
            <div className="flex items-center justify-between mb-2">
              {/* Logo Section - Compact Left */}
              <div className="flex items-center min-w-0">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-1.5 text-gray-800">
                  <Brain className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-blue-600 flex-shrink-0" />
                  <span className="hidden sm:inline truncate">CodeCrusher Dashboard</span>
                  <span className="sm:hidden truncate">CodeCrusher</span>
                </h1>
              </div>

              {/* Primary Navigation Row - Full Text */}
              <div className="flex-1 flex justify-center mx-4">
                <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-4xl">
                  <Button
                    variant={viewMode === 'main' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('main')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Main Dashboard
                  </Button>
                  <Button
                    variant={viewMode === 'simple' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('simple')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Full Dashboard
                  </Button>
                  <Button
                    variant={viewMode === 'streamlined' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('streamlined')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Streamlined
                  </Button>
                  <Button
                    variant={viewMode === 'clean' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('clean')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Clean
                  </Button>
                  <Button
                    variant={viewMode === 'enhanced' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('enhanced')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Enhanced
                  </Button>
                  <Button
                    variant={viewMode === 'backend' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('backend')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Backend
                  </Button>
                </div>
              </div>

              {/* Right Side Balance */}
              <div className="w-16 sm:w-20 lg:w-24 flex-shrink-0"></div>
            </div>

            {/* Bottom Row: Secondary Navigation */}
            <div className="flex justify-center">
              <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-3xl">
                <Button
                  variant={viewMode === 'ui' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('ui')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  UI
                </Button>
                <Button
                  variant={viewMode === 'status' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('status')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Status
                </Button>
                <Button
                  variant={viewMode === 'stable' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('stable')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Stable
                </Button>
                <Button
                  variant={viewMode === 'enterprise' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('enterprise')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 hover:from-blue-700 hover:to-indigo-700"
                >
                  Enterprise
                </Button>
                <Button
                  variant={viewMode === 'demo' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('demo')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Log Panel Demo
                </Button>
                <Button
                  variant={viewMode === 'styletest' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('styletest')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-yellow-500 text-white border-0 hover:bg-yellow-600"
                >
                  Style Test
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Health Status */}
      {getStatusIcon && getStatusText && (
        <div className="bg-white/90 backdrop-blur-sm border-b border-gray-100 py-2">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
            </div>
            <div className="flex items-center space-x-2">
              {wsConnected ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm font-medium">
                WebSocket {wsConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Health Alert */}
      {healthStatus && !healthStatus.codecrusher_available && (
        <div className="px-4 py-2">
          <Alert>
            <AlertDescription>
              CodeCrusher CLI not found. Please ensure it's installed and available in your PATH or virtual environment.
              {healthStatus.codecrusher_path && ` Detected path: ${healthStatus.codecrusher_path}`}
            </AlertDescription>
          </Alert>
        </div>
      )}

      <div className="flex-1 p-6 grid gap-4 max-w-4xl mx-auto">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">CodeCrusher Dashboard</h1>
          <div className="text-sm space-y-1">
            <div>WebSocket: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}</div>
            {currentJobId && (
              <div className="text-xs text-blue-600">
                Job: {currentJobId.slice(0, 8)}...
              </div>
            )}
          </div>
        </div>

      {/* Controls */}
      <Card>
        <CardContent className="space-y-4 p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">File Path</label>
              <Input
                value={filePath}
                onChange={(e) => setFilePath(e.target.value)}
                placeholder="./src"
                className="w-full"
              />
            </div>
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">AI Model</label>
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">🤖 Auto Selection</SelectItem>
                  <SelectItem value="mixtral">🧠 Mixtral</SelectItem>
                  <SelectItem value="gemma">💎 Gemma</SelectItem>
                  <SelectItem value="llama3-8b">🦙 LLaMA 3 8B</SelectItem>
                  <SelectItem value="llama3-70b">🦙 LLaMA 3 70B</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Optimization Prompt</label>
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Enter your optimization prompt..."
              rows={3}
              className="w-full"
            />
          </div>

          <div className="flex items-center justify-between gap-4">
            <div className="flex flex-col gap-2">
              <label className="text-sm font-medium">Enable Fallback</label>
              <Switch checked={fallback} onCheckedChange={setFallback} />
            </div>

            <div className="flex gap-2">
              <Button
                disabled={isRunning || !isConnected}
                onClick={handleRun}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                {isRunning ? 'Running...' : 'Run CodeCrusher'}
              </Button>

              {isRunning && (
                <Button
                  variant="destructive"
                  onClick={handleStop}
                  className="flex items-center gap-2"
                >
                  <Square className="h-4 w-4" />
                  Stop
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium">Progress</label>
            <span className="text-sm text-gray-600">{progress}%</span>
          </div>
          <Progress value={progress} className="h-3" />
        </CardContent>
      </Card>

      {/* Live Logs */}
      <Card>
        <CardContent className="p-4 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Terminal className="h-5 w-5 text-muted-foreground" />
              <span className="font-medium">Live Logs</span>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleTestMessage}>
                Test Message
              </Button>
              <Button variant="outline" size="sm" onClick={clearLogs}>
                Clear
              </Button>
            </div>
          </div>
          <div className="bg-black text-green-500 font-mono text-sm p-3 h-[300px] overflow-y-auto rounded-md border">
            {logs.length === 0 ? (
              <div className="text-gray-500 italic">
                {isConnected ? 'Waiting for logs...' : 'Connect to see logs...'}
              </div>
            ) : (
              logs.map((log, i) => (
                <div key={i} className="mb-1">{log}</div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
      </div>

      {/* Enterprise Footer */}
      <EnterpriseFooter />
    </div>
  );
};

export default StreamlinedDashboard;
