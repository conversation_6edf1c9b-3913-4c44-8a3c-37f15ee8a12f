#!/usr/bin/env powershell
<#
.SYNOPSIS
    Simple build script for CodeCrusher VS Code Extension
.DESCRIPTION
    Compiles TypeScript files and prepares the extension for testing
#>

Write-Host "🔨 Building CodeCrusher VS Code Extension..." -ForegroundColor Cyan

# Step 1: Check if TypeScript is available
Write-Host "1️⃣ Checking TypeScript..." -ForegroundColor Yellow

$tscPath = ""
if (Test-Path "node_modules\.bin\tsc.cmd") {
    $tscPath = "node_modules\.bin\tsc.cmd"
    Write-Host "✅ Found local TypeScript: $tscPath" -ForegroundColor Green
} elseif (Get-Command tsc -ErrorAction SilentlyContinue) {
    $tscPath = "tsc"
    Write-Host "✅ Found global TypeScript" -ForegroundColor Green
} else {
    Write-Host "❌ TypeScript not found!" -ForegroundColor Red
    Write-Host "💡 Installing TypeScript locally..." -ForegroundColor Yellow
    npm install typescript --save-dev
    if ($LASTEXITCODE -eq 0) {
        $tscPath = "node_modules\.bin\tsc.cmd"
        Write-Host "✅ TypeScript installed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to install TypeScript" -ForegroundColor Red
        exit 1
    }
}

# Step 2: Clean output directory
Write-Host "`n2️⃣ Cleaning output directory..." -ForegroundColor Yellow

if (Test-Path "out") {
    Remove-Item -Recurse -Force "out"
    Write-Host "✅ Cleaned out/ directory" -ForegroundColor Green
}

if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
    Write-Host "✅ Cleaned dist/ directory" -ForegroundColor Green
}

# Step 3: Compile TypeScript
Write-Host "`n3️⃣ Compiling TypeScript..." -ForegroundColor Yellow

try {
    if ($tscPath -eq "tsc") {
        & tsc -p .
    } else {
        & $tscPath -p .
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ TypeScript compilation successful!" -ForegroundColor Green
    } else {
        Write-Host "❌ TypeScript compilation failed!" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error running TypeScript compiler: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 4: Check output
Write-Host "`n4️⃣ Checking build output..." -ForegroundColor Yellow

if (Test-Path "out/extension.js") {
    $fileSize = (Get-Item "out/extension.js").Length
    Write-Host "✅ Extension built successfully: out/extension.js ($fileSize bytes)" -ForegroundColor Green
    
    # List all compiled files
    Write-Host "`n📁 Compiled files:" -ForegroundColor Gray
    Get-ChildItem "out" -Recurse -File | ForEach-Object {
        Write-Host "   $($_.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ Extension build failed - out/extension.js not found" -ForegroundColor Red
    exit 1
}

# Step 5: Validate package.json
Write-Host "`n5️⃣ Validating package.json..." -ForegroundColor Yellow

$packageJson = Get-Content "package.json" | ConvertFrom-Json
if ($packageJson.main -eq "./out/extension.js") {
    Write-Host "✅ package.json main entry point is correct" -ForegroundColor Green
} else {
    Write-Host "⚠️ package.json main entry point: $($packageJson.main)" -ForegroundColor Yellow
    Write-Host "💡 Expected: ./out/extension.js" -ForegroundColor Yellow
}

Write-Host "`n🎉 Build Complete!" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "   1. Open VS Code: code ." -ForegroundColor White
Write-Host "   2. Press F5 to launch Extension Development Host" -ForegroundColor White
Write-Host "   3. Test your extension in the new window" -ForegroundColor White
