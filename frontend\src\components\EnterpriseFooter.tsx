import React from 'react';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Brain,
  Shield,
  Globe,
  Lock,
  Heart,
  Github,
  Twitter,
  Linkedin,
  Mail,
  Phone,
  MapPin,
  ExternalLink,
  Zap,
  Database,
  Cloud,
  Users,
  Award,
  BookOpen
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnterpriseFooterProps {
  className?: string;
}

export function EnterpriseFooter({ className }: EnterpriseFooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={cn(
      "relative bg-gradient-to-br from-slate-950 via-gray-950 to-black text-white mt-20 overflow-hidden",
      className
    )}
    style={{
      boxShadow: 'inset 0 4px 20px rgba(0,0,0,0.5), 0 -4px 20px rgba(59,130,246,0.1)'
    }}>
      {/* Premium 3D Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.15),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.15),transparent_50%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.08),transparent_70%)]"></div>

      {/* 3D Glossy Top Border */}
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-transparent via-blue-500/40 to-transparent"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
      <div className="absolute top-0 left-0 w-full h-32 bg-gradient-to-b from-white/8 via-white/3 to-transparent backdrop-blur-sm"></div>

      {/* 3D Depth Layers */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-gray-600/50 to-transparent"></div>

      {/* Main Footer Content */}
      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-10">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 lg:gap-6">

          {/* Premium Brand Section - Optimized */}
          <div className="lg:col-span-4 space-y-4">
            {/* Brand Identity */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="relative group">
                  <div className="p-2.5 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-xl shadow-2xl transition-all duration-500 group-hover:scale-110 group-hover:shadow-blue-500/25">
                    <Brain className="h-6 w-6 text-white" />
                  </div>
                  <div className="absolute -top-1 -right-1 w-2.5 h-2.5 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-700/20 rounded-xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
                <div>
                  <h3 className="text-xl font-black bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                    CodeCrusher
                  </h3>
                  <p className="text-blue-300 font-semibold tracking-wide text-xs">AI-POWERED DEVELOPMENT</p>
                </div>
              </div>

              <p className="text-gray-300 text-sm leading-relaxed">
                An innovative AI-powered development platform designed to optimize and enhance your code.
                Built for developers who want smarter, faster development workflows.
              </p>

              {/* Feature Badges - Compact Grid */}
              <div className="grid grid-cols-2 gap-1.5">
                <div className="px-2 py-1 bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30 rounded-full backdrop-blur-sm">
                  <div className="flex items-center space-x-1.5 text-emerald-300">
                    <Zap className="w-2.5 h-2.5" />
                    <span className="text-xs font-semibold">AI-Powered</span>
                  </div>
                </div>
                <div className="px-2 py-1 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border border-blue-500/30 rounded-full backdrop-blur-sm">
                  <div className="flex items-center space-x-1.5 text-blue-300">
                    <Github className="w-2.5 h-2.5" />
                    <span className="text-xs font-semibold">Open Source</span>
                  </div>
                </div>
                <div className="px-2 py-1 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-full backdrop-blur-sm">
                  <div className="flex items-center space-x-1.5 text-purple-300">
                    <Heart className="w-2.5 h-2.5" />
                    <span className="text-xs font-semibold">Developer First</span>
                  </div>
                </div>
                <div className="px-2 py-1 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border border-blue-500/30 rounded-full backdrop-blur-sm">
                  <div className="flex items-center space-x-1.5 text-blue-300">
                    <Globe className="w-2.5 h-2.5" />
                    <span className="text-xs font-semibold">Global Infrastructure</span>
                  </div>
                </div>
                <div className="px-2 py-1 bg-gradient-to-r from-emerald-500/20 to-green-500/20 border border-emerald-500/30 rounded-full backdrop-blur-sm">
                  <div className="flex items-center space-x-1.5 text-emerald-300">
                    <Shield className="w-2.5 h-2.5" />
                    <span className="text-xs font-semibold">Security</span>
                  </div>
                </div>
                <div className="px-2 py-1 bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-500/30 rounded-full backdrop-blur-sm">
                  <div className="flex items-center space-x-1.5 text-red-300">
                    <Heart className="w-2.5 h-2.5" />
                    <span className="text-xs font-semibold">Built for Developers</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Developer Resources - Optimized */}
          <div className="lg:col-span-3 space-y-4">
            <h4 className="text-lg font-bold text-white flex items-center space-x-2">
              <div className="w-1 h-5 bg-gradient-to-b from-emerald-500 to-green-500 rounded-full"></div>
              <span>Developer Resources</span>
            </h4>
            <div className="space-y-3">
              <a href="#" className="group relative flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-blue-500/40 transition-all duration-500 transform hover:scale-105 overflow-hidden"
                 style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.1), 0 4px 12px rgba(0,0,0,0.3)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-indigo-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <BookOpen className="w-4 h-4 text-blue-400 group-hover:scale-110 transition-transform duration-300" />
                <span className="text-gray-300 group-hover:text-white font-semibold text-sm">Documentation</span>
                <ExternalLink className="w-3 h-3 text-gray-500 group-hover:text-blue-400 transition-colors duration-300 ml-auto" />
              </a>
              <a href="#" className="group relative flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-emerald-500/40 transition-all duration-500 transform hover:scale-105 overflow-hidden"
                 style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.1), 0 4px 12px rgba(0,0,0,0.3)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-transparent to-green-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <Github className="w-4 h-4 text-emerald-400 group-hover:scale-110 transition-transform duration-300" />
                <span className="text-gray-300 group-hover:text-white font-semibold text-sm">API Reference</span>
                <ExternalLink className="w-3 h-3 text-gray-500 group-hover:text-emerald-400 transition-colors duration-300 ml-auto" />
              </a>
              <a href="#" className="group relative flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-purple-500/40 transition-all duration-500 transform hover:scale-105 overflow-hidden"
                 style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.1), 0 4px 12px rgba(0,0,0,0.3)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-pink-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <Users className="w-4 h-4 text-purple-400 group-hover:scale-110 transition-transform duration-300" />
                <span className="text-gray-300 group-hover:text-white font-semibold text-sm">Community</span>
                <ExternalLink className="w-3 h-3 text-gray-500 group-hover:text-purple-400 transition-colors duration-300 ml-auto" />
              </a>
            </div>
          </div>

          {/* Solutions - Optimized */}
          <div className="lg:col-span-3 space-y-4">
            <h4 className="text-lg font-bold text-white flex items-center space-x-2">
              <div className="w-1 h-5 bg-gradient-to-b from-blue-500 to-purple-500 rounded-full"></div>
              <span>Solutions</span>
            </h4>
            <div className="space-y-3">
              <a href="#" className="group relative flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-blue-500/40 transition-all duration-500 transform hover:scale-105 overflow-hidden"
                 style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.1), 0 4px 12px rgba(0,0,0,0.3)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-indigo-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg group-hover:scale-110 transition-transform duration-300 shadow-lg"
                     style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.3), 0 4px 8px rgba(59,130,246,0.3)'}}>
                  <Zap className="w-4 h-4 text-white" />
                </div>
                <div className="relative">
                  <div className="text-white font-semibold text-sm">AI Code Optimization</div>
                  <div className="text-gray-400 text-xs">Intelligent code enhancement</div>
                </div>
              </a>
              <a href="#" className="group relative flex items-center space-x-3 p-3 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-emerald-500/40 transition-all duration-500 transform hover:scale-105 overflow-hidden"
                 style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.1), 0 4px 12px rgba(0,0,0,0.3)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/10 via-transparent to-green-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg group-hover:scale-110 transition-transform duration-300 shadow-lg"
                     style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.3), 0 4px 8px rgba(16,185,129,0.3)'}}>
                  <Database className="w-4 h-4 text-white" />
                </div>
                <div className="relative">
                  <div className="text-white font-semibold text-sm">Enterprise Analytics</div>
                  <div className="text-gray-400 text-xs">Advanced insights & metrics</div>
                </div>
              </a>
              {/* Social Links */}
              <div className="group relative p-3 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-purple-500/40 transition-all duration-500 transform hover:scale-105 overflow-hidden"
                   style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.1), 0 4px 12px rgba(0,0,0,0.3)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-transparent to-pink-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative">
                  <div className="text-white font-semibold text-sm mb-2">Connect With Us</div>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" className="relative p-1.5 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 hover:from-blue-700 hover:via-blue-800 hover:to-indigo-900 border-0 rounded-lg transition-all duration-500 transform hover:scale-110 overflow-hidden"
                            style={{boxShadow: 'inset 0 2px 4px rgba(255,255,255,0.2), 0 8px 16px rgba(59,130,246,0.4)'}}>
                      <div className="absolute inset-0 bg-gradient-to-t from-blue-900/30 to-transparent"></div>
                      <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>
                      <Twitter className="w-3 h-3 relative z-10" />
                    </Button>
                    <Button size="sm" className="relative p-1.5 bg-gradient-to-br from-blue-700 via-indigo-700 to-purple-800 hover:from-blue-800 hover:via-indigo-800 hover:to-purple-900 border-0 rounded-lg transition-all duration-500 transform hover:scale-110 overflow-hidden"
                            style={{boxShadow: 'inset 0 2px 4px rgba(255,255,255,0.2), 0 8px 16px rgba(99,102,241,0.4)'}}>
                      <div className="absolute inset-0 bg-gradient-to-t from-indigo-900/30 to-transparent"></div>
                      <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>
                      <Linkedin className="w-3 h-3 relative z-10" />
                    </Button>
                    <Button size="sm" className="relative p-1.5 bg-gradient-to-br from-gray-700 via-gray-800 to-slate-900 hover:from-gray-800 hover:via-gray-900 hover:to-black border-0 rounded-lg transition-all duration-500 transform hover:scale-110 overflow-hidden"
                            style={{boxShadow: 'inset 0 2px 4px rgba(255,255,255,0.2), 0 8px 16px rgba(75,85,99,0.4)'}}>
                      <div className="absolute inset-0 bg-gradient-to-t from-gray-900/30 to-transparent"></div>
                      <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>
                      <Github className="w-3 h-3 relative z-10" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Section - Responsive */}
          <div className="lg:col-span-2 space-y-4">
            <h4 className="text-lg font-bold text-white flex items-center space-x-2">
              <div className="w-1 h-5 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
              <span>Get in Touch</span>
            </h4>
            <div className="space-y-3">
              <div className="relative p-4 rounded-lg bg-gradient-to-br from-blue-500/15 to-indigo-500/15 border border-blue-500/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-500 group overflow-hidden"
                   style={{boxShadow: 'inset 0 2px 4px rgba(255,255,255,0.1), 0 8px 24px rgba(59,130,246,0.2)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-transparent to-indigo-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-blue-900/10 to-transparent"></div>
                <div className="relative flex items-center space-x-3 mb-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-lg group-hover:scale-110 transition-transform duration-300"
                       style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.3), 0 4px 8px rgba(59,130,246,0.4)'}}>
                    <Mail className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-white font-semibold text-sm">Contact Us</span>
                </div>
                <p className="text-blue-200 font-medium text-sm relative break-all"><EMAIL></p>
              </div>

              <div className="relative p-4 rounded-lg bg-gradient-to-br from-emerald-500/15 to-green-500/15 border border-emerald-500/30 backdrop-blur-sm transform hover:scale-105 transition-all duration-500 group overflow-hidden"
                   style={{boxShadow: 'inset 0 2px 4px rgba(255,255,255,0.1), 0 8px 24px rgba(16,185,129,0.2)'}}>
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/20 via-transparent to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
                <div className="absolute inset-0 bg-gradient-to-t from-emerald-900/10 to-transparent"></div>
                <div className="relative flex items-center space-x-3 mb-2">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-lg group-hover:scale-110 transition-transform duration-300"
                       style={{boxShadow: 'inset 0 1px 2px rgba(255,255,255,0.3), 0 4px 8px rgba(16,185,129,0.4)'}}>
                    <Users className="w-4 h-4 text-white" />
                  </div>
                  <span className="text-white font-semibold text-sm">Support</span>
                </div>
                <p className="text-emerald-200 font-medium text-sm relative break-all"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Premium 3D Bottom Bar */}
      <div className="relative border-t border-white/20 bg-gradient-to-r from-slate-950/80 via-gray-950/80 to-black/80 backdrop-blur-xl"
           style={{boxShadow: 'inset 0 2px 8px rgba(0,0,0,0.5), inset 0 1px 2px rgba(255,255,255,0.1)'}}>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/8 via-purple-500/8 to-emerald-500/8"></div>
        <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>

        <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 items-center">

            {/* Copyright */}
            <div className="text-center lg:text-left">
              <div className="text-gray-200 font-semibold text-base">
                © {currentYear} <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-emerald-400 bg-clip-text text-transparent font-black text-lg">CodeCrusher</span>
              </div>
              <div className="text-gray-400 text-sm mt-1">All rights reserved.</div>
              <div className="text-gray-500 text-xs mt-2 flex items-center justify-center lg:justify-start space-x-1">
                <span>Developed by</span>
                <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent font-semibold">CodeGX Technologies</span>
              </div>
            </div>

            {/* Legal Links */}
            <div className="flex items-center justify-center space-x-6">
              <a href="#" className="relative text-gray-400 hover:text-blue-400 transition-all duration-500 hover:scale-110 text-sm font-semibold group">
                <span className="relative z-10">Privacy Policy</span>
                <div className="absolute inset-0 bg-blue-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -m-2"></div>
              </a>
              <div className="w-px h-5 bg-gradient-to-b from-transparent via-gray-500 to-transparent"></div>
              <a href="#" className="relative text-gray-400 hover:text-emerald-400 transition-all duration-500 hover:scale-110 text-sm font-semibold group">
                <span className="relative z-10">Terms of Service</span>
                <div className="absolute inset-0 bg-emerald-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -m-2"></div>
              </a>
              <div className="w-px h-5 bg-gradient-to-b from-transparent via-gray-500 to-transparent"></div>
              <a href="#" className="relative text-gray-400 hover:text-purple-400 transition-all duration-500 hover:scale-110 text-sm font-semibold group">
                <span className="relative z-10">Security</span>
                <div className="absolute inset-0 bg-purple-500/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -m-2"></div>
              </a>
            </div>

            {/* Version & Status */}
            <div className="text-center lg:text-right">
              <div className="text-gray-400 text-sm font-medium">Version 2.1.0</div>
              <div className="flex items-center justify-center lg:justify-end space-x-2 mt-1">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span className="text-emerald-400 text-xs font-semibold">All Systems Operational</span>
              </div>
            </div>

          </div>
        </div>
      </div>
    </footer>
  );
}
