"""
Stats command for CodeCrusher.

This module provides the stats command for CodeCrusher, which displays
summary statistics of injections grouped by key metadata fields.
"""

import typer
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any, Counter
from collections import defaultdict, Counter
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.box import ROUNDED

# Import tag manager
from codecrusher.tag_manager import parse_user_tags

# Import cache manager
from codecrusher.cache_manager import load_cache

# Import filter utilities
from codecrusher.filter_utils import (
    parse_filter,
    apply_filters,
    MODEL_FIELD,
    FALLBACK_FIELD,
    TAG_FIELD,
    ERROR_FIELD
)

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

# Constants for group-by fields
GROUP_BY_MODEL = "model"
GROUP_BY_TAG = "tag"
GROUP_BY_FALLBACK = "fallback"
GROUP_BY_ERROR = "error"

# Valid group-by fields
VALID_GROUP_BY = [GROUP_BY_MODEL, GROUP_BY_TAG, GROUP_BY_FALLBACK, GROUP_BY_ERROR]

def extract_model_from_entry(entry: Dict[str, Any]) -> str:
    """
    Extract the model from an entry.
    
    Args:
        entry: The entry to extract the model from
        
    Returns:
        The model name
    """
    # Check if model field exists
    model = entry.get("model", "")
    if model:
        return model
    
    # Try to extract from tags
    tags = entry.get("tags", [])
    for tag in tags:
        if tag.startswith("@model:"):
            return tag[7:]  # Remove "@model:" prefix
    
    return "unknown"

def extract_fallback_from_entry(entry: Dict[str, Any]) -> str:
    """
    Extract the fallback status from an entry.
    
    Args:
        entry: The entry to extract the fallback status from
        
    Returns:
        The fallback status ("true", "false", or "unknown")
    """
    # Check if fallback field exists
    fallback = entry.get("fallback", "")
    if fallback:
        return str(fallback).lower()
    
    # Try to extract from tags
    tags = entry.get("tags", [])
    for tag in tags:
        if tag.startswith("@fallback:"):
            return tag[10:].lower()  # Remove "@fallback:" prefix
    
    return "false"  # Default to false if not specified

def extract_error_from_entry(entry: Dict[str, Any]) -> str:
    """
    Extract the error type from an entry.
    
    Args:
        entry: The entry to extract the error from
        
    Returns:
        The error type ("none" if no error)
    """
    # Check if error field exists
    error = entry.get("error", "")
    if error:
        # Try to extract the error type (e.g., "timeout", "invalid_response")
        if "timeout" in error.lower():
            return "timeout"
        elif "invalid" in error.lower():
            return "invalid_response"
        else:
            return "other"
    
    return "none"  # No error

def process_cache_entry(key: str, entry: Dict[str, Any], stats: Dict[str, Dict[str, int]], group_by: str, filters: Dict[str, str], filter_tags: List[str]) -> None:
    """
    Process a cache entry and update the stats.
    
    Args:
        key: The cache key
        entry: The cache entry
        stats: The stats dictionary to update
        group_by: The field to group by
        filters: Dictionary of field-value pairs to filter by
        filter_tags: List of tags to filter by
    """
    # Handle nested model entries
    if isinstance(entry, dict) and any(isinstance(v, dict) for v in entry.values()):
        for model_id, model_entry in entry.items():
            if not isinstance(model_entry, dict):
                continue
            
            # Extract tags
            tags = model_entry.get("tags", [])
            
            # Create a combined entry for filtering
            combined_entry = {
                "model": model_id,
                "tags": tags,
                **model_entry
            }
            
            # Filter by tags
            if filter_tags and not all(tag.lower() in [t.lower() for t in tags] for tag in filter_tags):
                continue
            
            # Filter by fields
            if filters and not apply_filters(combined_entry, filters):
                continue
            
            # Update stats based on group_by
            if group_by == GROUP_BY_MODEL:
                model = extract_model_from_entry(combined_entry)
                stats[GROUP_BY_MODEL][model] += 1
            elif group_by == GROUP_BY_TAG:
                for tag in tags:
                    stats[GROUP_BY_TAG][tag] += 1
            elif group_by == GROUP_BY_FALLBACK:
                fallback = extract_fallback_from_entry(combined_entry)
                stats[GROUP_BY_FALLBACK][fallback] += 1
            elif group_by == GROUP_BY_ERROR:
                error = extract_error_from_entry(combined_entry)
                stats[GROUP_BY_ERROR][error] += 1
    else:
        # Handle direct entries
        if isinstance(entry, dict):
            # Extract tags
            tags = entry.get("tags", [])
            
            # Create a combined entry for filtering
            combined_entry = {
                "model": entry.get("model", "unknown"),
                "tags": tags,
                **entry
            }
            
            # Filter by tags
            if filter_tags and not all(tag.lower() in [t.lower() for t in tags] for tag in filter_tags):
                return
            
            # Filter by fields
            if filters and not apply_filters(combined_entry, filters):
                return
            
            # Update stats based on group_by
            if group_by == GROUP_BY_MODEL:
                model = extract_model_from_entry(combined_entry)
                stats[GROUP_BY_MODEL][model] += 1
            elif group_by == GROUP_BY_TAG:
                for tag in tags:
                    stats[GROUP_BY_TAG][tag] += 1
            elif group_by == GROUP_BY_FALLBACK:
                fallback = extract_fallback_from_entry(combined_entry)
                stats[GROUP_BY_FALLBACK][fallback] += 1
            elif group_by == GROUP_BY_ERROR:
                error = extract_error_from_entry(combined_entry)
                stats[GROUP_BY_ERROR][error] += 1

@app.command("run")
def run_stats(
    group_by: str = typer.Option("model", "--group-by", "-g", help="Field to group by (model, tag, fallback, error)"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Filter by tag (space-separated)"),
    filter: Optional[str] = typer.Option(None, "--filter", help="Filter by fields (field=value,...)"),
    limit: int = typer.Option(10, "--limit", "-l", help="Limit the number of results"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Show detailed information"),
):
    """
    Display summary statistics of injections grouped by key metadata fields.
    
    This command provides summary statistics of injections grouped by model,
    tag, fallback status, or error type. You can filter the results by tags
    and fields.
    
    Supported group-by fields:
        model — Group injections by model used
        tag — Group by each tag separately (count of injections containing the tag)
        fallback — Group by fallback status (true/false)
        error — Group by error type (e.g., timeout, none)
    
    Filter options:
        --filter model=mistral
        --filter fallback=true
        --filter tag=teamA
        --filter error=timeout
    
    Examples:
        codecrusher stats run
        codecrusher stats run --group-by tag
        codecrusher stats run --group-by fallback --filter model=mistral
        codecrusher stats run --group-by error --tag bugfix
    """
    # Validate group_by
    if group_by not in VALID_GROUP_BY:
        console.print(f"[bold red]❌ Error:[/bold red] Invalid group-by field: {group_by}")
        console.print(f"[yellow]Valid group-by fields: {', '.join(VALID_GROUP_BY)}[/yellow]")
        raise typer.Exit(code=1)
    
    # Parse filter tags
    filter_tags = parse_user_tags(tag)
    
    # Parse field filters
    field_filters = parse_filter(filter)
    
    # Format filter tags for display
    filter_tags_display = ", ".join(filter_tags) if filter_tags else "None"
    
    # Format field filters for display
    field_filters_display = ", ".join([f"{k}={v}" for k, v in field_filters.items()]) if field_filters else "None"
    
    console.print(Panel(
        f"[bold]CodeCrusher Stats[/bold]\n\n"
        f"[cyan]Group By:[/cyan] {group_by}\n"
        f"[cyan]Filter Tags:[/cyan] {filter_tags_display}\n"
        f"[cyan]Field Filters:[/cyan] {field_filters_display}\n"
        f"[cyan]Limit:[/cyan] {limit}\n"
        f"[cyan]Verbose:[/cyan] {'Enabled' if verbose else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))
    
    # Load the cache
    cache = load_cache()
    
    # Initialize stats counters
    stats = {
        GROUP_BY_MODEL: defaultdict(int),
        GROUP_BY_TAG: defaultdict(int),
        GROUP_BY_FALLBACK: defaultdict(int),
        GROUP_BY_ERROR: defaultdict(int)
    }
    
    # Process each cache entry
    for key, entry in cache.items():
        process_cache_entry(key, entry, stats, group_by, field_filters, filter_tags)
    
    # Create a table for the results
    table = Table(title=f"Injection Statistics (Grouped by {group_by.capitalize()})", box=ROUNDED)
    table.add_column(group_by.capitalize(), style="cyan")
    table.add_column("Count", style="green", justify="right")
    
    # Get the stats for the selected group_by
    group_stats = stats[group_by]
    
    # Sort by count (descending)
    sorted_stats = sorted(group_stats.items(), key=lambda x: x[1], reverse=True)
    
    # Add rows to the table (limited by limit)
    for name, count in sorted_stats[:limit]:
        table.add_row(name, str(count))
    
    # Display the results
    if sorted_stats:
        console.print(table)
        
        # Display total count
        total_count = sum(count for _, count in sorted_stats)
        console.print(f"[bold green]Total: {total_count} entries[/bold green]")
        
        # Display additional stats if verbose
        if verbose:
            console.print("\n[bold]Additional Statistics:[/bold]")
            
            # Display top models if not already shown
            if group_by != GROUP_BY_MODEL:
                console.print("\n[bold cyan]Top Models:[/bold cyan]")
                for model, count in sorted(stats[GROUP_BY_MODEL].items(), key=lambda x: x[1], reverse=True)[:5]:
                    console.print(f"  {model}: {count}")
            
            # Display top tags if not already shown
            if group_by != GROUP_BY_TAG:
                console.print("\n[bold cyan]Top Tags:[/bold cyan]")
                for tag, count in sorted(stats[GROUP_BY_TAG].items(), key=lambda x: x[1], reverse=True)[:5]:
                    console.print(f"  {tag}: {count}")
            
            # Display fallback stats if not already shown
            if group_by != GROUP_BY_FALLBACK:
                console.print("\n[bold cyan]Fallback Usage:[/bold cyan]")
                for fallback, count in sorted(stats[GROUP_BY_FALLBACK].items(), key=lambda x: x[1], reverse=True):
                    console.print(f"  {fallback}: {count}")
            
            # Display error stats if not already shown
            if group_by != GROUP_BY_ERROR:
                console.print("\n[bold cyan]Error Types:[/bold cyan]")
                for error, count in sorted(stats[GROUP_BY_ERROR].items(), key=lambda x: x[1], reverse=True):
                    console.print(f"  {error}: {count}")
    else:
        console.print("[bold yellow]No entries found matching the filter criteria[/bold yellow]")
    
    return True
