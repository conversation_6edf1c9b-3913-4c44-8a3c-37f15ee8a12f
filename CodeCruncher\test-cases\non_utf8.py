# -*- coding: latin-1 -*-
"""
Python file with non-UTF8 encoding for testing CodeCrusher encoding handling
This file uses latin-1 encoding and contains special characters
"""

def greet_user(name):
    """Greet user with special characters."""
    # These characters are encoded in latin-1
    greeting = f"<PERSON><PERSON> {name}! ¿Cómo estás?"
    return greeting

class SpecialCharacterHandler:
    """Handle special characters in different encodings."""
    
    def __init__(self):
        # Latin-1 encoded strings
        self.spanish_text = "Niño pequeño"
        self.french_text = "Café français"
        self.german_text = "Größe"
    
    # AI_INJECT:encoding_methods
    # Add methods to handle different text encodings
    # AI_INJECT:encoding_methods:end
    
    def process_text(self, text):
        """Process text with special characters."""
        return text.upper()

def main():
    """Main function with special characters."""
    handler = SpecialCharacterHandler()
    
    # Test with special characters
    print(greet_user("<PERSON>"))
    print(handler.process_text("Niño"))
    
    # AI_INJECT:encoding_tests
    # Add more encoding tests here
    # AI_INJECT:encoding_tests:end

if __name__ == "__main__":
    main()
