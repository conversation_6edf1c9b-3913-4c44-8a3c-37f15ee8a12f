#!/usr/bin/env python3
"""
CodeCrusher CLI Entry Point
Main entry point for the codecrusher command
"""

import sys
import os
import typer
from typing import Optional
from rich.console import Console
from rich.panel import Panel

# Add the parent directory to the path so we can import cli_main
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

console = Console()

# Create the main app
app = typer.Typer(
    help="🚀 CodeCrusher: AI-powered code injection and optimization tool",
    no_args_is_help=True,
    rich_markup_mode="rich"
)

@app.command()
def inject(
    source: str = typer.Argument(..., help="📁 Path to source file or directory"),
    prompt: Optional[str] = typer.Option(None, "--prompt", "-p", help="💬 Prompt for AI injection"),
    recursive: bool = typer.Option(False, "--recursive", "-r", help="🔄 Recursively scan directories"),
    preview: bool = typer.Option(False, "--preview", help="🔍 Show preview without applying changes"),
    apply: bool = typer.Option(False, "--apply", help="✅ Apply changes to files"),
    ext: Optional[str] = typer.Option(None, "--ext", help="📄 File extensions (comma-separated)"),
    model: str = typer.Option("auto", "--model", "-m", help="🤖 AI model to use"),
    force: bool = typer.Option(False, "--force", help="⚡ Force injection without confirmation"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="🔍 Enable verbose output"),
):
    """
    🔌 Inject AI-generated code into source files

    Examples:
      codecrusher inject ./test-cases --recursive --preview
      codecrusher inject ./src/main.py --prompt "Add error handling" --apply
      codecrusher inject ./project --ext py,js --recursive --preview
    """
    # Validate arguments
    if not prompt:
        console.print(Panel(
            "[red]❌ Prompt is required[/red]\n\n"
            "[yellow]Usage examples:[/yellow]\n"
            "  codecrusher inject ./test-cases --prompt 'Add logging' --preview\n"
            "  codecrusher inject ./src --prompt 'Optimize performance' --recursive",
            title="[bold red]Missing Prompt[/bold red]",
            border_style="red"
        ))
        raise typer.Exit(code=1)

    if not preview and not apply:
        console.print(Panel(
            "[red]❌ Either --preview or --apply is required[/red]\n\n"
            "[yellow]Usage examples:[/yellow]\n"
            "  codecrusher inject ./test-cases --prompt 'Add logging' --preview\n"
            "  codecrusher inject ./src --prompt 'Add logging' --apply",
            title="[bold red]Missing Mode[/bold red]",
            border_style="red"
        ))
        raise typer.Exit(code=1)

    # Display welcome message
    console.print(Panel(
        "[bold]CodeCrusher: AI-powered code injection[/bold]\n"
        f"Processing: [yellow]{source}[/yellow]",
        title="[bold cyan]🚀 CodeCrusher Injection[/bold cyan]",
        border_style="cyan"
    ))

    # Validate input file
    from pathlib import Path
    input_path = Path(source)
    if not input_path.exists():
        console.print(Panel(
            f"[red]❌ Path does not exist: {source}[/red]",
            title="[bold red]File Not Found[/bold red]",
            border_style="red"
        ))
        raise typer.Exit(code=1)

    # Create configuration display
    from rich.table import Table
    config_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    config_table.add_row("[cyan]Source:[/cyan]", f"[yellow]{source}[/yellow]")
    config_table.add_row("[cyan]Prompt:[/cyan]", f"[yellow]{prompt[:100]}{'...' if len(prompt) > 100 else ''}[/yellow]")
    config_table.add_row("[cyan]Model:[/cyan]", f"[green]{model}[/green]")
    config_table.add_row("[cyan]Recursive:[/cyan]", f"[{'green' if recursive else 'red'}]{recursive}[/{'green' if recursive else 'red'}]")
    config_table.add_row("[cyan]Preview:[/cyan]", f"[{'green' if preview else 'red'}]{preview}[/{'green' if preview else 'red'}]")
    config_table.add_row("[cyan]Apply:[/cyan]", f"[{'green' if apply else 'red'}]{apply}[/{'green' if apply else 'red'}]")

    if ext:
        config_table.add_row("[cyan]Extensions:[/cyan]", f"[yellow]{ext}[/yellow]")

    console.print(Panel(config_table, title="[bold]Configuration[/bold]", border_style="blue"))

    # Real AI injection process
    try:
        from halo import Halo

        # Import the actual injection logic
        from codecrusher.ai_injector import AIInjector

        with Halo(text="Connecting to AI and processing files...", spinner="dots"):
            # Create AI injector instance
            injector = AIInjector(model=model, verbose=verbose)

            # Process the files
            result = injector.process_files(
                source_path=source,
                prompt=prompt,
                recursive=recursive,
                extensions=ext.split(',') if ext else None,
                preview_mode=preview
            )

        # Display results
        if result['success']:
            console.print(Panel(
                f"[green]✅ Successfully processed {result['files_processed']} files[/green]\n"
                f"[yellow]Mode: {'Preview' if preview else 'Applied changes'}[/yellow]\n"
                f"[cyan]Changes made: {result['changes_made']}[/cyan]",
                title="[bold green]Injection Complete[/bold green]",
                border_style="green"
            ))

            # Show diff if in preview mode
            if preview and result.get('preview_diff'):
                console.print("\n[bold cyan]Preview of Changes:[/bold cyan]")
                console.print(result['preview_diff'])
                console.print("\n[dim]Use --apply to make these changes permanent[/dim]")

        else:
            console.print(Panel(
                f"[red]❌ Processing failed: {result.get('error', 'Unknown error')}[/red]",
                title="[bold red]Injection Failed[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)

    except ImportError:
        console.print(Panel(
            f"[yellow]⚠️ AI injection module not found[/yellow]\n"
            f"[dim]Simulating processing for now...[/dim]",
            title="[bold yellow]Simulation Mode[/bold yellow]",
            border_style="yellow"
        ))

        # Fallback simulation
        import time
        time.sleep(2)
        console.print(Panel(
            f"[green]✅ Simulation completed for {source}[/green]\n"
            f"[yellow]Mode: {'Preview' if preview else 'Apply' if apply else 'Dry run'}[/yellow]\n"
            f"[dim]Install AI dependencies to enable real processing[/dim]",
            title="[bold green]Simulation Complete[/bold green]",
            border_style="green"
        ))

    except Exception as e:
        console.print(Panel(
            f"[red]❌ Injection failed: {str(e)}[/red]",
            title="[bold red]Error[/bold red]",
            border_style="red"
        ))
        raise typer.Exit(code=1)

@app.command()
def auth():
    """🔐 Authentication commands"""
    console.print(Panel(
        "[bold]Authentication Commands[/bold]\n\n"
        "[yellow]Available commands:[/yellow]\n"
        "  codecrusher auth login   - Login to your account\n"
        "  codecrusher auth logout  - Logout from your account\n"
        "  codecrusher auth status  - Check authentication status",
        title="[bold cyan]🔐 Authentication[/bold cyan]",
        border_style="cyan"
    ))

@app.command()
def version():
    """📋 Show version information"""
    console.print(Panel(
        "[bold]CodeCrusher v2.0.0[/bold]\n"
        "[cyan]AI-powered code injection and optimization tool[/cyan]\n"
        "[dim]Developed by CodeGX Technologies[/dim]",
        title="[bold cyan]Version Info[/bold cyan]",
        border_style="cyan"
    ))

@app.command()
def status():
    """📊 Show system status"""
    console.print(Panel(
        "[bold]CodeCrusher System Status[/bold]\n\n"
        "[green]✅ CLI Entry Point:[/green] Working\n"
        "[green]✅ Command Interface:[/green] Available\n"
        "[yellow]⚠️ Backend Connection:[/yellow] Not configured\n\n"
        "[cyan]Available Commands:[/cyan]\n"
        "  inject   - Code injection with AI\n"
        "  auth     - Authentication\n"
        "  version  - Version information\n"
        "  status   - System status",
        title="[bold blue]📊 Status[/bold blue]",
        border_style="blue"
    ))

@app.callback(invoke_without_command=True)
def main_callback(
    ctx: typer.Context,
    version_flag: bool = typer.Option(False, "--version", "-v", help="Show version"),
):
    """
    🚀 CodeCrusher: AI-powered code injection and optimization tool

    Use 'codecrusher inject ./path --prompt "description" --preview' for direct injection.
    """
    if version_flag:
        console.print(Panel(
            "[bold]CodeCrusher v2.0.0[/bold]\n"
            "[cyan]AI-powered code injection and optimization tool[/cyan]\n"
            "[dim]Developed by CodeGX Technologies[/dim]",
            title="[bold cyan]Version Info[/bold cyan]",
            border_style="cyan"
        ))
        raise typer.Exit()

    # If no subcommand is provided, show help
    if ctx.invoked_subcommand is None:
        console.print(Panel(
            "[bold]Welcome to CodeCrusher![/bold]\n\n"
            "[yellow]Quick Start Examples:[/yellow]\n"
            "  codecrusher inject ./test-cases --recursive --preview\n"
            "  codecrusher inject ./src/main.py --prompt 'Add error handling' --apply\n"
            "  codecrusher auth\n"
            "  codecrusher status\n\n"
            "[dim]Use --help for more information[/dim]",
            title="[bold cyan]🚀 CodeCrusher CLI[/bold cyan]",
            border_style="cyan"
        ))
        raise typer.Exit()

def main():
    """Main entry point for the CLI"""
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️ Operation cancelled by user[/yellow]")
        raise typer.Exit(code=1)
    except Exception as e:
        console.print(f"[red]❌ Unexpected error: {e}[/red]")
        raise typer.Exit(code=1)

if __name__ == "__main__":
    main()
