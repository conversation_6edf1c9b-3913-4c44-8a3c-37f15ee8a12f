import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectItem, SelectContent, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { EnterpriseFooter } from './EnterpriseFooter';
import { Brain, CheckCircle, XCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

const models = [
  { value: 'mixtral', label: '🧠 Mixtral' },
  { value: 'llama3-8b', label: '🦙 LLaMA 3 8B' },
  { value: 'llama3-70b', label: '🦙 LLaMA 3 70B' },
  { value: 'gemma', label: '💎 Gemma' }
];

interface CleanDashboardProps {
  viewMode?: string;
  setViewMode?: (mode: any) => void;
  healthStatus?: any;
  wsConnected?: boolean;
  getStatusIcon?: () => React.ReactElement;
  getStatusText?: () => string;
}

export default function CleanDashboard({
  viewMode,
  setViewMode,
  healthStatus,
  wsConnected,
  getStatusIcon,
  getStatusText
}: CleanDashboardProps = {}) {
  const [prompt, setPrompt] = useState('Refactor this code for clarity');
  const [model, setModel] = useState('mixtral');
  const [fallback, setFallback] = useState(true);
  const [logs, setLogs] = useState('');
  const [progress, setProgress] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);
  const [filePath, setFilePath] = useState('./src');

  const wsRef = useRef<WebSocket | null>(null);
  const jobWsRef = useRef<WebSocket | null>(null);

  const connectWebSocket = () => {
    // Connect to global WebSocket for general logs
    wsRef.current = new WebSocket('ws://localhost:8001/ws/logs');

    wsRef.current.onopen = () => {
      setIsConnected(true);
      console.log('🔗 Global WebSocket connected');
    };

    wsRef.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        const timestamp = new Date().toLocaleTimeString();
        const logMessage = `[${timestamp}] ${data.message || event.data}`;
        setLogs((prev) => prev + logMessage + '\n');
      } catch {
        setLogs((prev) => prev + event.data + '\n');
      }
    };

    wsRef.current.onclose = () => {
      setIsConnected(false);
      console.log('🔌 Global WebSocket closed');
    };

    wsRef.current.onerror = (error) => {
      console.error('🚨 Global WebSocket error:', error);
      setIsConnected(false);
    };
  };

  useEffect(() => {
    connectWebSocket();
    return () => {
      wsRef.current?.close();
      jobWsRef.current?.close();
    };
  }, []);

  const runInjection = async () => {
    setLogs('');
    setProgress(0);
    setIsRunning(true);

    try {
      // Start the injection job
      const response = await fetch('http://localhost:8001/inject', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          file_path: filePath,
          model,
          apply: true,
          tags: ['clean-dashboard']
        }),
      });

      const result = await response.json();

      if (result.status === 'started' && result.job_id) {
        setCurrentJobId(result.job_id);

        // Connect to job-specific WebSocket
        jobWsRef.current = new WebSocket(`ws://localhost:8000/ws/${result.job_id}`);

        jobWsRef.current.onopen = () => {
          console.log(`🔗 Job WebSocket connected: ${result.job_id}`);
          const timestamp = new Date().toLocaleTimeString();
          setLogs(prev => prev + `[${timestamp}] 🚀 Job started: ${result.job_id}\n`);
        };

        jobWsRef.current.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            // Handle progress updates
            if (data.type === 'progress' && typeof data.progress === 'number') {
              setProgress(data.progress);
            }

            // Handle log messages
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${data.message || event.data}`;
            setLogs((prev) => prev + logMessage + '\n');

          } catch {
            const timestamp = new Date().toLocaleTimeString();
            setLogs((prev) => prev + `[${timestamp}] ${event.data}\n`);
          }
        };

        jobWsRef.current.onclose = () => {
          console.log(`🔌 Job WebSocket closed: ${result.job_id}`);
          setIsRunning(false);
          setCurrentJobId(null);
          const timestamp = new Date().toLocaleTimeString();
          setLogs(prev => prev + `[${timestamp}] 🏁 Job completed\n`);
        };

        jobWsRef.current.onerror = (error) => {
          console.error(`🚨 Job WebSocket error: ${result.job_id}`, error);
          setIsRunning(false);
        };

      } else {
        console.error('❌ Failed to start injection job');
        setIsRunning(false);
        setLogs(prev => prev + '[ERROR] Failed to start injection job\n');
      }

    } catch (error) {
      console.error('💥 Request failed:', error);
      setIsRunning(false);
      setLogs(prev => prev + `[ERROR] Request failed: ${error}\n`);
    }
  };

  const stopInjection = () => {
    setIsRunning(false);

    if (jobWsRef.current) {
      jobWsRef.current.close();
      jobWsRef.current = null;
    }

    setCurrentJobId(null);
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => prev + `[${timestamp}] 🛑 Job stopped by user\n`);
  };

  const clearLogs = () => {
    setLogs('');
    setProgress(0);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-100">
      {/* Navigation Bar */}
      {setViewMode && (
        <div className="bg-white/95 backdrop-blur-xl border-b border-gray-200/30 shadow-lg">
          <div className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3">
            {/* Top Row: Logo + Primary Navigation */}
            <div className="flex items-center justify-between mb-2">
              {/* Logo Section - Compact Left */}
              <div className="flex items-center min-w-0">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-1.5 text-gray-800">
                  <Brain className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-blue-600 flex-shrink-0" />
                  <span className="hidden sm:inline truncate">CodeCrusher Dashboard</span>
                  <span className="sm:hidden truncate">CodeCrusher</span>
                </h1>
              </div>

              {/* Primary Navigation Row - Full Text */}
              <div className="flex-1 flex justify-center mx-4">
                <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-4xl">
                  <Button
                    variant={viewMode === 'main' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('main')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Main Dashboard
                  </Button>
                  <Button
                    variant={viewMode === 'simple' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('simple')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Full Dashboard
                  </Button>
                  <Button
                    variant={viewMode === 'streamlined' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('streamlined')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Streamlined
                  </Button>
                  <Button
                    variant={viewMode === 'clean' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('clean')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Clean
                  </Button>
                  <Button
                    variant={viewMode === 'enhanced' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('enhanced')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Enhanced
                  </Button>
                  <Button
                    variant={viewMode === 'backend' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('backend')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Backend
                  </Button>
                </div>
              </div>

              {/* Right Side Balance */}
              <div className="w-16 sm:w-20 lg:w-24 flex-shrink-0"></div>
            </div>

            {/* Bottom Row: Secondary Navigation */}
            <div className="flex justify-center">
              <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-3xl">
                <Button
                  variant={viewMode === 'ui' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('ui')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  UI
                </Button>
                <Button
                  variant={viewMode === 'status' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('status')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Status
                </Button>
                <Button
                  variant={viewMode === 'stable' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('stable')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Stable
                </Button>
                <Button
                  variant={viewMode === 'enterprise' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('enterprise')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 hover:from-blue-700 hover:to-indigo-700"
                >
                  Enterprise
                </Button>
                <Button
                  variant={viewMode === 'demo' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('demo')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Log Panel Demo
                </Button>
                <Button
                  variant={viewMode === 'styletest' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('styletest')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-yellow-500 text-white border-0 hover:bg-yellow-600"
                >
                  Style Test
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Health Status */}
      {getStatusIcon && getStatusText && (
        <div className="bg-white/90 backdrop-blur-sm border-b border-gray-100 py-2">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
            </div>
            <div className="flex items-center space-x-2">
              {wsConnected ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm font-medium">
                WebSocket {wsConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Health Alert */}
      {healthStatus && !healthStatus.codecrusher_available && (
        <div className="px-4 py-2">
          <Alert>
            <AlertDescription>
              CodeCrusher CLI not found. Please ensure it's installed and available in your PATH or virtual environment.
              {healthStatus.codecrusher_path && ` Detected path: ${healthStatus.codecrusher_path}`}
            </AlertDescription>
          </Alert>
        </div>
      )}

      <div className="flex-1 p-6 grid gap-4 max-w-4xl mx-auto">
        <Card className="p-4 grid gap-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold">🧠 CodeCrusher Dashboard</h1>
            <div className="text-sm space-y-1">
              <div>Status: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}</div>
              {currentJobId && (
                <div className="text-xs text-blue-600">
                  Job: {currentJobId.slice(0, 8)}...
                </div>
              )}
            </div>
          </div>

        <div>
          <label className="text-sm font-medium mb-2 block">File Path</label>
          <Input
            value={filePath}
            onChange={(e) => setFilePath(e.target.value)}
            placeholder="./src"
            className="mb-4"
          />
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Prompt</label>
          <Textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Enter your prompt for code injection"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
          <div>
            <label className="text-sm font-medium mb-2 block">AI Model</label>
            <Select value={model} onValueChange={setModel}>
              <SelectTrigger>
                <SelectValue placeholder="Select model" />
              </SelectTrigger>
              <SelectContent>
                {models.map((m) => (
                  <SelectItem key={m.value} value={m.value}>
                    {m.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center gap-2">
            <Switch
              checked={fallback}
              onCheckedChange={setFallback}
              id="fallback-switch"
            />
            <label htmlFor="fallback-switch" className="text-sm font-medium">
              Enable Fallback Routing
            </label>
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={runInjection}
            disabled={isRunning || !isConnected}
            className="flex-1"
          >
            {isRunning ? '⏳ Running...' : '🚀 Run Code Injection'}
          </Button>

          {isRunning && (
            <Button
              onClick={stopInjection}
              variant="destructive"
              className="px-6"
            >
              🛑 Stop
            </Button>
          )}

          <Button
            onClick={clearLogs}
            variant="outline"
            className="px-6"
          >
            🗑️ Clear
          </Button>
        </div>
      </Card>

      <Card className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold">📡 Live Logs</h2>
          <div className="text-xs text-gray-500">
            {logs.split('\n').length - 1} messages
          </div>
        </div>
        <div className="h-64 overflow-y-auto bg-black text-green-400 font-mono p-3 rounded-lg text-sm whitespace-pre-wrap border">
          {logs || 'Waiting for logs...'}
        </div>
      </Card>

      <Card className="p-4">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-semibold">📊 Progress</h2>
          <span className="text-sm font-medium">{progress}%</span>
        </div>
        <Progress value={progress} className="h-3" />
        {isRunning && (
          <div className="text-xs text-gray-600 mt-2">
            Processing... Please wait for completion.
          </div>
        )}
      </Card>
      </div>

      {/* Enterprise Footer */}
      <EnterpriseFooter />
    </div>
  );
}
