"""
Team Tuning Routes for CodeCrusher
Handles team-wide prompt tuning preferences and AI behavior configuration
"""

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from auth import get_current_user
from database import get_db
from team_events import team_event_manager

router = APIRouter(prefix="/teams", tags=["team-tuning"])

# Pydantic models
class TeamTuningConfig(BaseModel):
    fallback_sensitivity: float = 0.7
    model_order: List[str] = ["mixtral-8x7b", "llama3-70b", "llama3-8b"]
    tone_style: str = "professional"
    escalation_strategy: str = "balanced"
    auto_apply_threshold: float = 0.85
    retry_attempts: int = 3
    admin_only: bool = False

class TeamTuningResponse(BaseModel):
    fallback_sensitivity: float
    model_order: List[str]
    tone_style: str
    escalation_strategy: str
    auto_apply_threshold: float
    retry_attempts: int
    admin_only: bool
    last_updated_by: str
    last_updated_at: str

def require_team_tuning_access(current_user: dict, team_id: int, write_access: bool = False):
    """Check if user has access to team tuning settings."""
    db = get_db()
    
    # Check team membership
    role = db.check_team_membership(current_user["id"], team_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this team"
        )
    
    # If write access is required, check admin permissions
    if write_access:
        # Get current tuning config to check admin_only setting
        tuning_config = get_team_tuning_config(team_id)
        if tuning_config.get("admin_only", False) and role != "owner":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only team owners can modify tuning settings"
            )
    
    return role

def get_team_tuning_config(team_id: int) -> Dict[str, Any]:
    """Get team tuning configuration from database."""
    db = get_db()
    
    # Get all team settings
    settings = db.get_team_settings(team_id)
    
    # Default configuration
    default_config = {
        "fallback_sensitivity": 0.7,
        "model_order": ["mixtral-8x7b", "llama3-70b", "llama3-8b"],
        "tone_style": "professional",
        "escalation_strategy": "balanced",
        "auto_apply_threshold": 0.85,
        "retry_attempts": 3,
        "admin_only": False,
        "last_updated_by": "",
        "last_updated_at": ""
    }
    
    # Override with stored settings
    for key, value in settings.items():
        if key.startswith("tuning_"):
            setting_key = key.replace("tuning_", "")
            if setting_key == "model_order":
                try:
                    default_config[setting_key] = json.loads(value)
                except:
                    pass
            elif setting_key in ["fallback_sensitivity", "auto_apply_threshold"]:
                try:
                    default_config[setting_key] = float(value)
                except:
                    pass
            elif setting_key == "retry_attempts":
                try:
                    default_config[setting_key] = int(value)
                except:
                    pass
            elif setting_key == "admin_only":
                default_config[setting_key] = value.lower() == "true"
            else:
                default_config[setting_key] = value
    
    return default_config

def save_team_tuning_config(team_id: int, config: TeamTuningConfig, user_id: int, user_email: str) -> bool:
    """Save team tuning configuration to database."""
    db = get_db()
    
    try:
        # Save each setting
        settings_to_save = {
            "tuning_fallback_sensitivity": str(config.fallback_sensitivity),
            "tuning_model_order": json.dumps(config.model_order),
            "tuning_tone_style": config.tone_style,
            "tuning_escalation_strategy": config.escalation_strategy,
            "tuning_auto_apply_threshold": str(config.auto_apply_threshold),
            "tuning_retry_attempts": str(config.retry_attempts),
            "tuning_admin_only": str(config.admin_only).lower(),
            "tuning_last_updated_by": user_email,
            "tuning_last_updated_at": datetime.now().isoformat()
        }
        
        for key, value in settings_to_save.items():
            db.set_team_setting(team_id, key, value, user_id)
        
        return True
    except Exception as e:
        print(f"Error saving team tuning config: {e}")
        return False

@router.get("/{team_id}/tuning", response_model=TeamTuningResponse)
async def get_team_tuning(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get team tuning configuration."""
    # Check access
    require_team_tuning_access(current_user, team_id, write_access=False)
    
    # Get configuration
    config = get_team_tuning_config(team_id)
    
    return TeamTuningResponse(**config)

@router.post("/{team_id}/tuning", response_model=dict)
async def update_team_tuning(
    team_id: int,
    config: TeamTuningConfig,
    current_user: dict = Depends(get_current_user)
):
    """Update team tuning configuration."""
    # Check write access
    require_team_tuning_access(current_user, team_id, write_access=True)
    
    # Validate configuration
    if not (0.0 <= config.fallback_sensitivity <= 1.0):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Fallback sensitivity must be between 0.0 and 1.0"
        )
    
    if not (0.5 <= config.auto_apply_threshold <= 1.0):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Auto-apply threshold must be between 0.5 and 1.0"
        )
    
    if not (1 <= config.retry_attempts <= 10):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Retry attempts must be between 1 and 10"
        )
    
    if not config.model_order:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Model order cannot be empty"
        )
    
    # Save configuration
    success = save_team_tuning_config(team_id, config, current_user["id"], current_user["email"])
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to save tuning configuration"
        )
    
    # Emit tuning updated event
    tuning_event = team_event_manager.create_setting_changed_event(
        team_id=team_id,
        user_id=current_user["id"],
        user_email=current_user["email"],
        setting_key="prompt_tuning",
        setting_value="updated"
    )
    await team_event_manager.broadcast_event(tuning_event)
    
    return {
        "message": "Team tuning configuration updated successfully",
        "updated_by": current_user["email"],
        "updated_at": datetime.now().isoformat()
    }

@router.get("/{team_id}/tuning/export", response_model=dict)
async def export_team_tuning(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Export team tuning configuration for backup or sharing."""
    # Check access
    require_team_tuning_access(current_user, team_id, write_access=False)
    
    # Get configuration
    config = get_team_tuning_config(team_id)
    
    # Add export metadata
    export_data = {
        "team_id": team_id,
        "exported_by": current_user["email"],
        "exported_at": datetime.now().isoformat(),
        "config": config
    }
    
    return export_data

@router.post("/{team_id}/tuning/import", response_model=dict)
async def import_team_tuning(
    team_id: int,
    import_data: dict,
    current_user: dict = Depends(get_current_user)
):
    """Import team tuning configuration from backup."""
    # Check write access
    require_team_tuning_access(current_user, team_id, write_access=True)
    
    try:
        # Extract config from import data
        if "config" not in import_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid import data: missing config"
            )
        
        config_data = import_data["config"]
        
        # Create TeamTuningConfig object
        config = TeamTuningConfig(
            fallback_sensitivity=config_data.get("fallback_sensitivity", 0.7),
            model_order=config_data.get("model_order", ["mixtral-8x7b", "llama3-70b", "llama3-8b"]),
            tone_style=config_data.get("tone_style", "professional"),
            escalation_strategy=config_data.get("escalation_strategy", "balanced"),
            auto_apply_threshold=config_data.get("auto_apply_threshold", 0.85),
            retry_attempts=config_data.get("retry_attempts", 3),
            admin_only=config_data.get("admin_only", False)
        )
        
        # Save configuration
        success = save_team_tuning_config(team_id, config, current_user["id"], current_user["email"])
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to import tuning configuration"
            )
        
        # Emit tuning updated event
        tuning_event = team_event_manager.create_setting_changed_event(
            team_id=team_id,
            user_id=current_user["id"],
            user_email=current_user["email"],
            setting_key="prompt_tuning",
            setting_value="imported"
        )
        await team_event_manager.broadcast_event(tuning_event)
        
        return {
            "message": "Team tuning configuration imported successfully",
            "imported_by": current_user["email"],
            "imported_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to import configuration: {str(e)}"
        )

@router.post("/{team_id}/tuning/reset", response_model=dict)
async def reset_team_tuning(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Reset team tuning configuration to defaults."""
    # Check write access
    require_team_tuning_access(current_user, team_id, write_access=True)
    
    # Create default configuration
    default_config = TeamTuningConfig()
    
    # Save default configuration
    success = save_team_tuning_config(team_id, default_config, current_user["id"], current_user["email"])
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset tuning configuration"
        )
    
    # Emit tuning updated event
    tuning_event = team_event_manager.create_setting_changed_event(
        team_id=team_id,
        user_id=current_user["id"],
        user_email=current_user["email"],
        setting_key="prompt_tuning",
        setting_value="reset_to_defaults"
    )
    await team_event_manager.broadcast_event(tuning_event)
    
    return {
        "message": "Team tuning configuration reset to defaults",
        "reset_by": current_user["email"],
        "reset_at": datetime.now().isoformat()
    }
