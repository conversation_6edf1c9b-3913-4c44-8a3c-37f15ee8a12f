"""
AugmentCode STEP 6: Manual Shaping Configuration System
Provides user-configurable tuning parameters for intelligent prompt shaping.
"""

import json
import os
import logging
from typing import Dict, Any

# Configuration file location - now using shared intelligence layer
try:
    from intel_paths import get_config_path
    CONFIG_FILE = get_config_path()
except ImportError:
    # Fallback to legacy path if intel_paths not available
    CONFIG_FILE = os.path.expanduser("~/.codecrusher_config.json")

# Default configuration values
DEFAULT_CONFIG = {
    "tone": "neutral",  # options: neutral, friendly, assertive, formal
    "fallback_sensitivity": "medium",  # options: low, medium, high
    "shaping_strength": "smart"  # options: off, soft, smart, aggressive
}

# Valid configuration options for validation
VALID_OPTIONS = {
    "tone": ["neutral", "friendly", "assertive", "formal"],
    "fallback_sensitivity": ["low", "medium", "high"],
    "shaping_strength": ["off", "soft", "smart", "aggressive"]
}

# Tone modifier templates
TONE_MODIFIERS = {
    "neutral": "",
    "friendly": "Please ",
    "assertive": "Ensure you ",
    "formal": "It is required that you "
}

# Fallback sensitivity thresholds
FALLBACK_THRESHOLDS = {
    "low": 0.1,    # Very permissive - shape with minimal confidence
    "medium": 0.2,  # Balanced - default threshold
    "high": 0.4     # Conservative - require high confidence to shape
}

# Shaping strength multipliers
SHAPING_STRENGTH = {
    "off": 0.0,      # Disable shaping completely
    "soft": 0.5,     # Gentle modifications
    "smart": 1.0,    # Default intelligent shaping
    "aggressive": 2.0 # Strong modifications
}


def load_config() -> Dict[str, Any]:
    """
    Load configuration from file, creating defaults if missing or corrupt.

    Returns:
        Dict[str, Any]: Configuration dictionary with validated values
    """
    try:
        if not os.path.exists(CONFIG_FILE):
            logging.info(f"Config file not found, creating default: {CONFIG_FILE}")
            save_config(DEFAULT_CONFIG)
            return DEFAULT_CONFIG.copy()

        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            config = json.load(f)

        # Validate and merge with defaults
        validated_config = validate_config(config)

        # Save back if any corrections were made
        if validated_config != config:
            logging.info("Config file had invalid values, correcting...")
            save_config(validated_config)

        return validated_config

    except (json.JSONDecodeError, IOError) as e:
        logging.warning(f"Failed to load config file: {e}")
        logging.info("Using default configuration")
        save_config(DEFAULT_CONFIG)
        return DEFAULT_CONFIG.copy()


def save_config(cfg: Dict[str, Any]) -> None:
    """
    Save configuration to file.

    Args:
        cfg (Dict[str, Any]): Configuration dictionary to save
    """
    try:
        # Validate before saving
        validated_config = validate_config(cfg)

        # Ensure directory exists
        config_dir = os.path.dirname(CONFIG_FILE)
        if config_dir and not os.path.exists(config_dir):
            os.makedirs(config_dir, exist_ok=True)

        with open(CONFIG_FILE, "w", encoding="utf-8") as f:
            json.dump(validated_config, f, indent=2)

        logging.info(f"Configuration saved to: {CONFIG_FILE}")

    except (IOError, OSError) as e:
        logging.error(f"Failed to save config file: {e}")


def validate_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate configuration values and merge with defaults.

    Args:
        config (Dict[str, Any]): Configuration to validate

    Returns:
        Dict[str, Any]: Validated configuration with defaults for missing values
    """
    validated = DEFAULT_CONFIG.copy()

    for key, value in config.items():
        if key in VALID_OPTIONS:
            if value in VALID_OPTIONS[key]:
                validated[key] = value
            else:
                logging.warning(f"Invalid config value for {key}: {value}. Using default: {DEFAULT_CONFIG[key]}")
        else:
            logging.warning(f"Unknown config key: {key}. Ignoring.")

    return validated


def get_config_value(key: str, default: Any = None) -> Any:
    """
    Get a specific configuration value.

    Args:
        key (str): Configuration key to retrieve
        default (Any, optional): Default value if key not found

    Returns:
        Any: Configuration value or default
    """
    config = load_config()
    return config.get(key, default)


def update_config(updates: Dict[str, Any]) -> bool:
    """
    Update configuration with new values.

    Args:
        updates (Dict[str, Any]): Dictionary of updates to apply

    Returns:
        bool: True if update was successful, False otherwise
    """
    try:
        config = load_config()
        config.update(updates)
        validated_config = validate_config(config)
        save_config(validated_config)
        return True
    except Exception as e:
        logging.error(f"Failed to update config: {e}")
        return False


def get_tone_modifier(tone: str = None) -> str:
    """
    Get tone modifier string for the specified tone.

    Args:
        tone (str, optional): Tone to get modifier for. Uses config if None.

    Returns:
        str: Tone modifier string
    """
    if tone is None:
        tone = get_config_value("tone", "neutral")

    return TONE_MODIFIERS.get(tone, TONE_MODIFIERS["neutral"])


def get_fallback_threshold(sensitivity: str = None) -> float:
    """
    Get fallback threshold for the specified sensitivity level.

    Args:
        sensitivity (str, optional): Sensitivity level. Uses config if None.

    Returns:
        float: Threshold value for fallback decisions
    """
    if sensitivity is None:
        sensitivity = get_config_value("fallback_sensitivity", "medium")

    return FALLBACK_THRESHOLDS.get(sensitivity, FALLBACK_THRESHOLDS["medium"])


def get_shaping_multiplier(strength: str = None) -> float:
    """
    Get shaping strength multiplier for the specified strength level.

    Args:
        strength (str, optional): Strength level. Uses config if None.

    Returns:
        float: Multiplier for shaping aggressiveness
    """
    if strength is None:
        strength = get_config_value("shaping_strength", "smart")

    return SHAPING_STRENGTH.get(strength, SHAPING_STRENGTH["smart"])


def print_config_info() -> None:
    """Print current configuration information."""
    config = load_config()

    print("CodeCrusher Configuration")
    print("=" * 40)
    print(f"Config file: {CONFIG_FILE}")
    print(f"Tone: {config['tone']}")
    print(f"Fallback sensitivity: {config['fallback_sensitivity']}")
    print(f"Shaping strength: {config['shaping_strength']}")
    print()
    print("Available options:")
    for key, options in VALID_OPTIONS.items():
        print(f"  {key}: {', '.join(options)}")


def reset_config() -> bool:
    """
    Reset configuration to defaults.

    Returns:
        bool: True if reset was successful, False otherwise
    """
    try:
        save_config(DEFAULT_CONFIG)
        logging.info("Configuration reset to defaults")
        return True
    except Exception as e:
        logging.error(f"Failed to reset config: {e}")
        return False


# Helper function for testing
def get_config_file_path() -> str:
    """Get the path to the configuration file."""
    return CONFIG_FILE


# Initialize logging for this module
logging.basicConfig(level=logging.INFO)
