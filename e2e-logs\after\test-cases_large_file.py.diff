# AI_INJECT:search_functionality
import logging
import traceback

# Configure comprehensive logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def search_functionality_with_error_handling(self, query, search_type="name"):
    """Enhanced search functionality with comprehensive error handling."""
    try:
        if not query or not isinstance(query, str):
            raise ValueError("Query must be a non-empty string")
        
        logger.info(f"Performing {search_type} search for: {query}")
        
        # Implement search logic with validation
        results = []
        for user in self.users:
            try:
                if search_type == "name" and query.lower() in user.name.lower():
                    results.append(user)
                elif search_type == "email" and query.lower() in user.email.lower():
                    results.append(user)
            except AttributeError as e:
                logger.warning(f"Invalid user object: {e}")
                continue
        
        logger.info(f"Search completed. Found {len(results)} results")
        return results
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        logger.debug(traceback.format_exc())
        raise
# AI_INJECT:search_functionality:end
