import * as vscode from 'vscode';
import { CodeCrusherApi, TelemetryEntry } from './api';

/**
 * Register CodeLens provider for CodeCrusher injections
 */
export function registerLenses(context: vscode.ExtensionContext) {
    const lensProvider = new CodeCrusherLensProvider();

    context.subscriptions.push(
        vscode.languages.registerCodeLensProvider(
            { scheme: 'file' },
            lensProvider
        )
    );

    // Refresh telemetry data every 5 minutes
    const refreshInterval = setInterval(() => lensProvider.refreshTelemetryData(), 5 * 60 * 1000);
    context.subscriptions.push({ dispose: () => clearInterval(refreshInterval) });

    // Initial refresh
    lensProvider.refreshTelemetryData();

    return lensProvider;
}

/**
 * CodeLens for CodeCrusher injections
 */
export class CodeCrusherLens extends vscode.CodeLens {
    constructor(
        range: vscode.Range,
        public readonly telemetryEntry: TelemetryEntry,
        command?: vscode.Command
    ) {
        super(range, command);
    }
}

/**
 * CodeLens provider for CodeCrusher injections
 */
export class CodeCrusherLensProvider implements vscode.CodeLensProvider {
    private _onDidChangeCodeLenses: vscode.EventEmitter<void> = new vscode.EventEmitter<void>();
    public readonly onDidChangeCodeLenses: vscode.Event<void> = this._onDidChangeCodeLenses.event;
    private _telemetryEntries: TelemetryEntry[] = [];

    /**
     * Refresh telemetry data
     */
    public async refreshTelemetryData(): Promise<void> {
        try {
            // Check if server is running
            const isServerRunning = await CodeCrusherApi.isServerRunning();
            if (!isServerRunning) {
                this._telemetryEntries = [];
                this._onDidChangeCodeLenses.fire();
                return;
            }

            // Get telemetry data
            const telemetryData = await CodeCrusherApi.getTelemetry(500);
            this._telemetryEntries = telemetryData.entries;
            this._onDidChangeCodeLenses.fire();
        } catch (error) {
            console.error('Error refreshing telemetry data:', error);
            this._telemetryEntries = [];
            this._onDidChangeCodeLenses.fire();
        }
    }

    /**
     * Provide CodeLenses for the given document
     */
    public async provideCodeLenses(
        document: vscode.TextDocument,
        token: vscode.CancellationToken
    ): Promise<vscode.CodeLens[]> {
        if (this._telemetryEntries.length === 0) {
            return [];
        }

        const codeLenses: CodeCrusherLens[] = [];
        const filePath = document.uri.fsPath;

        // Find telemetry entries for this file
        const fileEntries = this._telemetryEntries.filter(entry =>
            entry.file_path && entry.line_number &&
            (entry.file_path === filePath || filePath.endsWith(entry.file_path))
        );

        for (const entry of fileEntries) {
            if (!entry.line_number) {
                continue;
            }

            // Line numbers in telemetry are 1-based, VS Code is 0-based
            const lineNumber = entry.line_number - 1;

            if (lineNumber < 0 || lineNumber >= document.lineCount) {
                continue;
            }

            const line = document.lineAt(lineNumber);
            const range = new vscode.Range(
                new vscode.Position(lineNumber, 0),
                new vscode.Position(lineNumber, line.text.length)
            );

            // Create command for the CodeLens
            const command: vscode.Command = {
                title: this.getCodeLensTitle(entry),
                command: 'codecrusher.showInjectionDetails',
                arguments: [entry]
            };

            codeLenses.push(new CodeCrusherLens(range, entry, command));
        }

        return codeLenses;
    }

    /**
     * Get title for CodeLens based on telemetry entry
     */
    private getCodeLensTitle(entry: TelemetryEntry): string {
        const parts: string[] = [];

        // Add model info
        parts.push(`Model: ${entry.model}`);

        // Add fallback info if applicable
        if (entry.fallback) {
            parts.push('⚠️ Fallback used');
        }

        // Add error info if applicable
        if (entry.error) {
            parts.push(`❌ Error: ${entry.error.substring(0, 30)}...`);
        }

        // Add tag info if available
        if (entry.tags && entry.tags.length > 0) {
            parts.push(`Tags: ${entry.tags.join(', ')}`);
        } else {
            parts.push('⚠️ No tags');
        }

        return parts.join(' | ');
    }
}
