# CodeCrusher Git Hooks Integration

This document summarizes the Git hooks integration for CodeCrusher and provides instructions for testing.

## What's Been Implemented

### 1. Git Hooks Command Module
- Created `codecrusher/commands/githooks.py` with a Typer app
- Implemented commands for installing, uninstalling, and checking status of Git hooks
- Added options for customizing hook behavior

### 2. Enhanced Pre-commit Hook
- Improved scanning for injection markers
- Added support for multiple file types and comment styles
- Added auto-insertion of missing tags
- Added anomaly detection for suspicious code
- Added option to block commits on critical errors

### 3. Enhanced Post-commit Hook
- Improved telemetry metadata extraction
- Added support for remote telemetry sync
- Enhanced branch-aware tagging
- Added extraction of injection tags from committed files

### 4. CLI Integration
- Registered the githooks command in cc_li.py
- Updated documentation

## How to Test

### 1. Install the Git Hooks

```bash
python cc_li.py githooks install
```

Options:
- `--block-on-errors`: Block commits if critical errors are detected
- `--auto-insert-tags`: Automatically insert missing tags
- `--remote-sync`: Sync telemetry to a remote server
- `--remote-sync-url`: URL for remote telemetry sync

### 2. Check the Status

```bash
python cc_li.py githooks status
```

### 3. Test the Pre-commit Hook

1. Create a Python file with a function but no injection tag:

```python
def calculate_factorial(n):
    if n <= 1:
        return 1
    return n * calculate_factorial(n - 1)
```

2. Stage the file:

```bash
git add your_file.py
```

3. Commit the file:

```bash
git commit -m "Add factorial function"
```

4. The pre-commit hook should detect the function and suggest a tag.
5. If auto-insert tags is enabled, it should automatically insert the tag.

### 4. Test the Post-commit Hook

1. Add an injection tag to a file:

```python
# AI_INJECT: factorial
def calculate_factorial(n):
    if n <= 1:
        return 1
    return n * calculate_factorial(n - 1)
```

2. Stage and commit the file:

```bash
git add your_file.py
git commit -m "Add injection tag"
```

3. The post-commit hook should detect the injection tag and add branch information to telemetry.
4. If remote sync is enabled, it should sync telemetry to the remote server.

### 5. Uninstall the Git Hooks

```bash
python cc_li.py githooks uninstall
```

## Troubleshooting

If you encounter any issues:

1. Check that the hooks are executable:

```bash
chmod +x .git/hooks/pre-commit .git/hooks/post-commit
```

2. Check the hook configuration files:

```bash
cat .git/hooks/pre-commit.config.json
cat .git/hooks/post-commit.config.json
```

3. Run the hooks manually to see any errors:

```bash
.git/hooks/pre-commit
.git/hooks/post-commit
```

4. Make sure all required Python packages are installed:

```bash
pip install requests
```

## Next Steps

1. Add more file type support
2. Improve anomaly detection
3. Add more options for customizing hook behavior
4. Add support for more Git hooks (e.g., pre-push, post-checkout)
5. Add support for team-wide configuration
