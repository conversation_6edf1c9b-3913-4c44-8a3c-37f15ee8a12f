import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, Trophy, TrendingUp, Zap, Settings, 
  RefreshCw, Download, Filter 
} from 'lucide-react';

// Import our enhanced components
import ModelAnalyticsPanel from './ModelAnalyticsPanel';
import LeaderboardPanel from './LeaderboardPanel';

interface AnalyticsDashboardProps {
  className?: string;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  const handleRefreshAll = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Dashboard Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-6 w-6 text-blue-600" />
              <span>CodeCrusher Analytics Dashboard</span>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefreshAll}
                disabled={refreshing}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh All
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-1" />
                Settings
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Quick Stats */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <Trophy className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm text-blue-600 font-medium">Top Model</div>
                  <div className="text-lg font-bold text-blue-800">Mixtral</div>
                </div>
              </div>
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-sm text-green-600 font-medium">Satisfaction</div>
                  <div className="text-lg font-bold text-green-800">87.5%</div>
                </div>
              </div>
            </div>
            
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="text-sm text-purple-600 font-medium">Avg Latency</div>
                  <div className="text-lg font-bold text-purple-800">1.2s</div>
                </div>
              </div>
            </div>
            
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="text-sm text-orange-600 font-medium">Models</div>
                  <div className="text-lg font-bold text-orange-800">4 Active</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="leaderboard" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Leaderboard
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Insights
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Leaderboard Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-500" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-yellow-50 border border-yellow-200 rounded">
                    <div className="flex items-center gap-2">
                      <Trophy className="h-4 w-4 text-yellow-600" />
                      <span className="font-medium">Mixtral</span>
                    </div>
                    <span className="text-green-600 font-bold">87.5%</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-gray-50 border border-gray-200 rounded">
                    <div className="flex items-center gap-2">
                      <Trophy className="h-4 w-4 text-gray-600" />
                      <span className="font-medium">Claude</span>
                    </div>
                    <span className="text-green-600 font-bold">66.7%</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-amber-50 border border-amber-200 rounded">
                    <div className="flex items-center gap-2">
                      <Trophy className="h-4 w-4 text-amber-600" />
                      <span className="font-medium">GPT-4</span>
                    </div>
                    <span className="text-yellow-600 font-bold">40.0%</span>
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  className="w-full mt-4" 
                  onClick={() => setActiveTab('leaderboard')}
                >
                  View Full Leaderboard
                </Button>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-blue-500" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 p-2 border-l-4 border-green-500 bg-green-50">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">Mixtral improved rating</div>
                      <div className="text-xs text-gray-500">2 minutes ago</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-2 border-l-4 border-blue-500 bg-blue-50">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">New feedback received</div>
                      <div className="text-xs text-gray-500">5 minutes ago</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-2 border-l-4 border-yellow-500 bg-yellow-50">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">Auto-fix suggestion applied</div>
                      <div className="text-xs text-gray-500">8 minutes ago</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">87.5%</div>
                  <div className="text-sm text-gray-600">Average Satisfaction</div>
                  <div className="text-xs text-green-600">↑ 5.2% from last week</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">1.2s</div>
                  <div className="text-sm text-gray-600">Average Response Time</div>
                  <div className="text-xs text-blue-600">↓ 0.3s from last week</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">65%</div>
                  <div className="text-sm text-gray-600">Retry Success Rate</div>
                  <div className="text-xs text-purple-600">↑ 12% from last week</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="leaderboard">
          <LeaderboardPanel />
        </TabsContent>

        <TabsContent value="analytics">
          <ModelAnalyticsPanel />
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-purple-500" />
                AI-Powered Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <div className="font-medium text-blue-800">Performance Recommendation</div>
                      <div className="text-sm text-blue-700 mt-1">
                        Mixtral shows consistently high performance for refactoring tasks. 
                        Consider routing more refactoring requests to this model.
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                    <div>
                      <div className="font-medium text-yellow-800">Optimization Opportunity</div>
                      <div className="text-sm text-yellow-700 mt-1">
                        GPT-4 has high latency but good quality for complex tasks. 
                        Consider using it only for high-complexity prompts.
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <div className="font-medium text-green-800">Success Pattern</div>
                      <div className="text-sm text-green-700 mt-1">
                        Auto-fix suggestions are working well with 65% success rate. 
                        This is improving overall user satisfaction.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsDashboard;
