"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeCrusherLensProvider = exports.CodeCrusherLens = exports.registerLenses = void 0;
const vscode = require("vscode");
const api_1 = require("./api");
/**
 * Register CodeLens provider for CodeCrusher injections
 */
function registerLenses(context) {
    const lensProvider = new CodeCrusherLensProvider();
    context.subscriptions.push(vscode.languages.registerCodeLensProvider({ scheme: 'file' }, lensProvider));
    // Refresh telemetry data every 5 minutes
    const refreshInterval = setInterval(() => lensProvider.refreshTelemetryData(), 5 * 60 * 1000);
    context.subscriptions.push({ dispose: () => clearInterval(refreshInterval) });
    // Initial refresh
    lensProvider.refreshTelemetryData();
    return lensProvider;
}
exports.registerLenses = registerLenses;
/**
 * CodeLens for CodeCrusher injections
 */
class CodeCrusherLens extends vscode.CodeLens {
    constructor(range, telemetryEntry, command) {
        super(range, command);
        this.telemetryEntry = telemetryEntry;
    }
}
exports.CodeCrusherLens = CodeCrusherLens;
/**
 * CodeLens provider for CodeCrusher injections
 */
class CodeCrusherLensProvider {
    constructor() {
        this._onDidChangeCodeLenses = new vscode.EventEmitter();
        this.onDidChangeCodeLenses = this._onDidChangeCodeLenses.event;
        this._telemetryEntries = [];
    }
    /**
     * Refresh telemetry data
     */
    refreshTelemetryData() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if server is running
                const isServerRunning = yield api_1.CodeCrusherApi.isServerRunning();
                if (!isServerRunning) {
                    this._telemetryEntries = [];
                    this._onDidChangeCodeLenses.fire();
                    return;
                }
                // Get telemetry data
                const telemetryData = yield api_1.CodeCrusherApi.getTelemetry(500);
                this._telemetryEntries = telemetryData.entries;
                this._onDidChangeCodeLenses.fire();
            }
            catch (error) {
                console.error('Error refreshing telemetry data:', error);
                this._telemetryEntries = [];
                this._onDidChangeCodeLenses.fire();
            }
        });
    }
    /**
     * Provide CodeLenses for the given document
     */
    provideCodeLenses(document, token) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this._telemetryEntries.length === 0) {
                return [];
            }
            const codeLenses = [];
            const filePath = document.uri.fsPath;
            // Find telemetry entries for this file
            const fileEntries = this._telemetryEntries.filter(entry => entry.file_path && entry.line_number &&
                (entry.file_path === filePath || filePath.endsWith(entry.file_path)));
            for (const entry of fileEntries) {
                if (!entry.line_number) {
                    continue;
                }
                // Line numbers in telemetry are 1-based, VS Code is 0-based
                const lineNumber = entry.line_number - 1;
                if (lineNumber < 0 || lineNumber >= document.lineCount) {
                    continue;
                }
                const line = document.lineAt(lineNumber);
                const range = new vscode.Range(new vscode.Position(lineNumber, 0), new vscode.Position(lineNumber, line.text.length));
                // Create command for the CodeLens
                const command = {
                    title: this.getCodeLensTitle(entry),
                    command: 'codecrusher.showInjectionDetails',
                    arguments: [entry]
                };
                codeLenses.push(new CodeCrusherLens(range, entry, command));
            }
            return codeLenses;
        });
    }
    /**
     * Get title for CodeLens based on telemetry entry
     */
    getCodeLensTitle(entry) {
        const parts = [];
        // Add model info
        parts.push(`Model: ${entry.model}`);
        // Add fallback info if applicable
        if (entry.fallback) {
            parts.push('⚠️ Fallback used');
        }
        // Add error info if applicable
        if (entry.error) {
            parts.push(`❌ Error: ${entry.error.substring(0, 30)}...`);
        }
        // Add tag info if available
        if (entry.tags && entry.tags.length > 0) {
            parts.push(`Tags: ${entry.tags.join(', ')}`);
        }
        else {
            parts.push('⚠️ No tags');
        }
        return parts.join(' | ');
    }
}
exports.CodeCrusherLensProvider = CodeCrusherLensProvider;
//# sourceMappingURL=lensProvider.js.map