"""
Team management routes for CodeCrusher
Handles team creation, membership, and collaboration features
"""

from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel, EmailStr
from typing import List, Optional
from datetime import datetime

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from auth import get_current_user
from database import get_db

router = APIRouter(prefix="/teams", tags=["teams"])

# Pydantic models
class TeamCreate(BaseModel):
    name: str
    description: Optional[str] = None

class TeamResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    created_by: int
    created_at: str
    role: str  # User's role in this team
    joined_at: str

class TeamMemberResponse(BaseModel):
    id: int
    email: str
    team_role: str
    joined_at: str
    user_created_at: str

class TeamInvite(BaseModel):
    email: EmailStr
    role: str = "member"

class InviteResponse(BaseModel):
    message: str
    invitation_token: str

class JoinTeam(BaseModel):
    invitation_token: str

# Permission decorators
def require_team_member(team_id: int, user_id: int):
    """Check if user is a member of the team."""
    db = get_db()
    role = db.check_team_membership(user_id, team_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this team"
        )
    return role

def require_team_owner(team_id: int, user_id: int):
    """Check if user is an owner of the team."""
    db = get_db()
    role = db.check_team_membership(user_id, team_id)
    if role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You must be a team owner to perform this action"
        )
    return role

# Team routes
@router.post("/", response_model=dict)
async def create_team(
    team_data: TeamCreate,
    current_user: dict = Depends(get_current_user)
):
    """Create a new team."""
    db = get_db()

    team_id = db.create_team(
        name=team_data.name,
        description=team_data.description or "",
        created_by=current_user["id"]
    )

    if not team_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create team"
        )

    return {
        "message": "Team created successfully",
        "team_id": team_id,
        "name": team_data.name
    }

@router.get("/", response_model=List[TeamResponse])
async def get_user_teams(
    current_user: dict = Depends(get_current_user)
):
    """Get all teams for the current user."""
    db = get_db()
    teams = db.get_user_teams(current_user["id"])

    return [
        TeamResponse(
            id=team["id"],
            name=team["name"],
            description=team["description"],
            created_by=team["created_by"],
            created_at=team["created_at"],
            role=team["role"],
            joined_at=team["joined_at"]
        )
        for team in teams
    ]

@router.get("/{team_id}", response_model=dict)
async def get_team_details(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get team details."""
    # Check membership
    require_team_member(team_id, current_user["id"])

    db = get_db()
    team = db.get_team_by_id(team_id)

    if not team:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Team not found"
        )

    return team

@router.get("/{team_id}/members", response_model=List[TeamMemberResponse])
async def get_team_members(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get all members of a team."""
    # Check membership
    require_team_member(team_id, current_user["id"])

    db = get_db()
    members = db.get_team_members(team_id)

    return [
        TeamMemberResponse(
            id=member["id"],
            email=member["email"],
            team_role=member["team_role"],
            joined_at=member["joined_at"],
            user_created_at=member["user_created_at"]
        )
        for member in members
    ]

@router.post("/{team_id}/invite", response_model=InviteResponse)
async def invite_to_team(
    team_id: int,
    invite_data: TeamInvite,
    current_user: dict = Depends(get_current_user)
):
    """Invite a user to join the team."""
    # Check if user is owner
    require_team_owner(team_id, current_user["id"])

    # Validate role
    if invite_data.role not in ["member", "viewer"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid role. Must be 'member' or 'viewer'"
        )

    db = get_db()

    # Check if team exists
    team = db.get_team_by_id(team_id)
    if not team:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Team not found"
        )

    # Check if user is already invited or member
    existing_user = db.get_user_by_email(invite_data.email)
    if existing_user:
        existing_role = db.check_team_membership(existing_user["id"], team_id)
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User is already a member of this team"
            )

    # Create invitation
    invitation_token = db.create_team_invitation(
        team_id=team_id,
        invited_email=invite_data.email,
        invited_by=current_user["id"],
        role=invite_data.role
    )

    if not invitation_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to create invitation"
        )

    return InviteResponse(
        message=f"Invitation sent to {invite_data.email}",
        invitation_token=invitation_token
    )

@router.post("/{team_id}/join", response_model=dict)
async def join_team(
    team_id: int,
    join_data: JoinTeam,
    current_user: dict = Depends(get_current_user)
):
    """Join a team using an invitation token."""
    db = get_db()

    # Check if team exists
    team = db.get_team_by_id(team_id)
    if not team:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Team not found"
        )

    # Check if user is already a member
    existing_role = db.check_team_membership(current_user["id"], team_id)
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You are already a member of this team"
        )

    # Accept invitation
    success = db.accept_team_invitation(join_data.invitation_token, current_user["id"])

    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired invitation token"
        )

    return {
        "message": f"Successfully joined team '{team['name']}'",
        "team_id": team_id,
        "team_name": team["name"]
    }
