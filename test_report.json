[{"test": "GET /health", "status": "PASS", "details": "Status: 200", "timestamp": "2025-05-29T02:40:54.356047"}, {"test": "GET /", "status": "PASS", "details": "Status: 200", "timestamp": "2025-05-29T02:40:54.911155"}, {"test": "GET /api/status", "status": "PASS", "details": "Status: 200", "timestamp": "2025-05-29T02:40:55.337077"}, {"test": "GET /api/intel/status", "status": "PASS", "details": "Status: 200", "timestamp": "2025-05-29T02:40:55.709370"}, {"test": "IPv4 Binding (127.0.0.1)", "status": "PASS", "details": "IPv4 connection successful", "timestamp": "2025-05-29T02:40:56.393637"}, {"test": "Localhost Resolution", "status": "PASS", "details": "Localhost resolves correctly", "timestamp": "2025-05-29T02:40:59.144202"}, {"test": "IPv6 Blocking (::1)", "status": "PASS", "details": "IPv6 correctly blocked", "timestamp": "2025-05-29T02:41:01.210294"}, {"test": "POST /api/inject", "status": "FAIL", "details": "Status: 422", "timestamp": "2025-05-29T02:41:01.357082"}, {"test": "POST /api/intel/analyze", "status": "FAIL", "details": "Status: 404", "timestamp": "2025-05-29T02:41:01.390294"}, {"test": "WS /ws/logs", "status": "FAIL", "details": "BaseEventLoop.create_connection() got an unexpected keyword argument 'timeout'", "timestamp": "2025-05-29T02:41:01.605605"}, {"test": "WS /ws/intel", "status": "FAIL", "details": "BaseEventLoop.create_connection() got an unexpected keyword argument 'timeout'", "timestamp": "2025-05-29T02:41:01.616647"}, {"test": "File Analysis", "status": "FAIL", "details": "Status: 404", "timestamp": "2025-05-29T02:41:01.691108"}]