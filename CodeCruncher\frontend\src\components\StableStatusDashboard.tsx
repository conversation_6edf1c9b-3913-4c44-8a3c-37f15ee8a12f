import React, { useState, useRef, use<PERSON>allback, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// import { Separator } from "@/components/ui/separator";
import {
  Loader2,
  Sparkles,
  XCircle,
  CheckCircle,
  Play,
  Square,
  Trash2,
  Wifi,
  WifiOff,
  RefreshCw,
  Settings,
  Terminal,
  Activity
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useWebSocket, WebSocketMessage } from "@/hooks/useWebSocket";
import { ConnectionOverlay, ConnectionStatusBadge } from "@/components/ConnectionOverlay";
import { FrozenStreamOverlay, StreamTimeoutBadge } from "@/components/FrozenStreamOverlay";
import { getWebSocketUrl } from "@/utils/client";

export default function StableStatusDashboard() {
  // Configuration state
  const [source, setSource] = useState("./src");
  const [promptText, setPromptText] = useState("Optimize code for clarity and performance");
  const [tag, setTag] = useState("stable-dashboard");
  const [model] = useState("mixtral");

  // UI state
  const [status, setStatus] = useState("idle");
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const logContainerRef = useRef<HTMLDivElement>(null);

  // Stable WebSocket connection with timeout detection
  const {
    connectionStatus,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    retry,
    retryCount,
    isConnected,
    isFrozen,
    lastMessageTime,
    restartStream
  } = useWebSocket({
    url: getWebSocketUrl('/logs'),
    maxRetries: 3, // Reduced for faster loading
    maxReconnectDelay: 5000, // Reduced from 10s to 5s
    timeoutMs: 10000, // Reduced timeout for faster failure detection
    autoConnect: true, // ✅ Enable auto-connect for immediate connection
    autoRestartOnTimeout: false, // Disable auto-restart for manual control
    onMessage: handleWebSocketMessage,
    onStatusChange: useCallback((newStatus) => {
      console.log(`Connection status: ${newStatus}`);
    }, [])
  });

  // Stable message handler
  function handleWebSocketMessage(message: WebSocketMessage) {
    if (message.type === "progress" && message.value !== undefined) {
      setProgress(message.value);
    } else if (message.type === "error") {
      setError(message.message || "Unknown error");
      setStatus("error");
      setIsRunning(false);
    } else if (message.type === "done" || message.message?.includes("completed successfully")) {
      setStatus("done");
      setProgress(100);
      setIsRunning(false);
    } else if (message.message) {
      setLogs(prev => {
        const newLogs = [...prev, message.message!];
        // Limit logs to prevent memory issues
        return newLogs.slice(-100);
      });

      // Extract progress from plain text if not already handled
      if (message.type !== "progress" && message.value !== undefined) {
        setProgress(message.value);
      }

      // Check for completion in plain text
      if (message.message.includes("completed successfully")) {
        setStatus("done");
        setProgress(100);
        setIsRunning(false);
      } else if (message.message.includes("injection failed") || message.message.includes("Error:")) {
        setStatus("error");
        setError(message.message);
        setIsRunning(false);
      }
    }
  }

  // Auto-scroll logs with performance optimization
  React.useEffect(() => {
    if (logContainerRef.current && logs.length > 0) {
      const container = logContainerRef.current;
      const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 1;

      if (isScrolledToBottom) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [logs]);

  const startInjection = useCallback(async () => {
    if (!isConnected) {
      setError("WebSocket not connected");
      return;
    }

    setIsRunning(true);
    setStatus("running");
    setProgress(0);
    setError(null);
    setLogs([]);

    try {
      const response = await fetch("http://127.0.0.1:8001/inject", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: promptText,
          file_path: source,
          model,
          apply: false,
          tags: [tag]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        setError(result.error || "Injection failed");
        setStatus("error");
        setIsRunning(false);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Request failed";
      setError(errorMessage);
      setStatus("error");
      setIsRunning(false);
    }
  }, [isConnected, source, promptText, model, tag]);

  const stopInjection = useCallback(() => {
    setIsRunning(false);
    setStatus("connected");
    setLogs(prev => [...prev, "🛑 Injection stopped by user"]);
  }, []);

  const clearLogs = useCallback(() => {
    setLogs([]);
    setProgress(0);
    setError(null);
    setStatus(isConnected ? "connected" : "idle");
  }, [isConnected]);

  // Memoized status badge to prevent unnecessary re-renders
  const statusBadge = useMemo(() => {
    switch (status) {
      case "running":
        return (
          <Badge className="bg-purple-100 text-purple-800 border-purple-200">
            <Activity className="w-3 h-3 mr-1 animate-pulse" />
            Running
          </Badge>
        );
      case "done":
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <Sparkles className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      case "error":
        return (
          <Badge variant="destructive">
            <XCircle className="w-3 h-3 mr-1" />
            Error
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            <CheckCircle className="w-3 h-3 mr-1" />
            Ready
          </Badge>
        );
    }
  }, [status]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Stable Connection Overlay */}
      <ConnectionOverlay
        status={connectionStatus}
        retryCount={retryCount}
        maxRetries={5}
        onRetry={retry}
        onRefresh={() => window.location.reload()}
      />

      {/* Frozen Stream Overlay */}
      <FrozenStreamOverlay
        isFrozen={isFrozen}
        lastMessageTime={lastMessageTime}
        timeoutMs={15000}
        onRestart={restartStream}
        autoRestartEnabled={true}
      />

      <div className="container mx-auto p-6 space-y-6 max-w-7xl">
        {/* Enhanced Header */}
        <div className="bg-white rounded-xl shadow-sm border p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Sparkles className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">CodeCrusher Dashboard</h1>
                <p className="text-sm text-gray-600">AI-powered code optimization platform</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <ConnectionStatusBadge
                status={connectionStatus}
                retryCount={retryCount}
                className="shadow-sm"
              />
              <StreamTimeoutBadge
                isFrozen={isFrozen}
                lastMessageTime={lastMessageTime}
                timeoutMs={15000}
              />
              {statusBadge}
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="shadow-sm">
            <XCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Configuration Panel */}
          <Card className="lg:col-span-1 shadow-sm border-0 bg-white">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5 text-blue-600" />
                <span>Configuration</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="source" className="text-sm font-medium">Source Path</Label>
                <Input
                  id="source"
                  value={source}
                  onChange={(e) => setSource(e.target.value)}
                  placeholder="./src"
                  className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="prompt" className="text-sm font-medium">AI Prompt</Label>
                <Textarea
                  id="prompt"
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  rows={4}
                  placeholder="Enter your optimization prompt..."
                  className="transition-all duration-200 focus:ring-2 focus:ring-blue-500 resize-none"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tag" className="text-sm font-medium">Tag</Label>
                <Input
                  id="tag"
                  value={tag}
                  onChange={(e) => setTag(e.target.value)}
                  placeholder="stable-dashboard"
                  className="transition-all duration-200 focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="h-px w-full bg-gray-200 my-2" />

              <div className="space-y-3">
                {!isConnected ? (
                  <Button
                    onClick={connect}
                    disabled={connectionStatus === 'connecting'}
                    className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 transition-all duration-200"
                    size="lg"
                  >
                    {connectionStatus === 'connecting' ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Connecting...
                      </>
                    ) : (
                      <>
                        <Wifi className="w-4 h-4 mr-2" />
                        Connect to Server
                      </>
                    )}
                  </Button>
                ) : (
                  <Button
                    onClick={startInjection}
                    disabled={isRunning || !isConnected || !promptText.trim()}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                    size="lg"
                  >
                    {isRunning ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Running...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4 mr-2" />
                        Start Injection
                      </>
                    )}
                  </Button>
                )}

                <div className="flex space-x-2">
                  {isRunning && (
                    <Button
                      onClick={stopInjection}
                      variant="destructive"
                      className="flex-1"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Stop
                    </Button>
                  )}

                  <Button
                    onClick={clearLogs}
                    variant="outline"
                    className="flex-1"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear
                  </Button>
                </div>
              </div>

              <div className="pt-2 text-xs text-gray-500 space-y-1">
                <div className="flex justify-between">
                  <span>Model:</span>
                  <span className="font-medium">{model}</span>
                </div>
                <div className="flex justify-between">
                  <span>Mode:</span>
                  <span className="font-medium">Preview</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Logs & Progress Panel */}
          <Card className="lg:col-span-2 shadow-sm border-0 bg-white">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Terminal className="h-5 w-5 text-green-600" />
                  <span>Live Output</span>
                </div>
                <div className="text-sm text-gray-600">
                  {logs.length} messages
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Progress Section */}
              <div className="space-y-3">
                <div className="flex justify-between text-sm font-medium">
                  <span>Progress</span>
                  <span className="text-blue-600">{progress}%</span>
                </div>
                <Progress
                  value={progress}
                  className={cn(
                    "h-3 transition-all duration-300",
                    status === "done" && "bg-green-100"
                  )}
                />
              </div>

              {/* Log Output */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Terminal Output</Label>
                <div
                  ref={logContainerRef}
                  className="bg-gray-900 text-green-400 font-mono p-4 rounded-lg h-80 overflow-y-auto text-sm border shadow-inner"
                  style={{ fontFamily: 'Consolas, Monaco, "Courier New", monospace' }}
                >
                  {logs.length === 0 ? (
                    <div className="text-gray-500 italic flex items-center">
                      <Terminal className="w-4 h-4 mr-2" />
                      {isConnected ? 'Ready for injection...' : 'Waiting for connection...'}
                    </div>
                  ) : (
                    logs.map((log, index) => (
                      <div key={index} className="mb-1 leading-relaxed">
                        <span className="text-gray-600 text-xs mr-2">
                          {new Date().toLocaleTimeString()}
                        </span>
                        {log}
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Status Footer */}
              <div className="flex justify-between items-center text-xs text-gray-500 pt-2 border-t">
                <div className="flex items-center space-x-4">
                  <span>Status: {status}</span>
                  <span>Connection: {connectionStatus}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {isConnected ? (
                    <Wifi className="w-3 h-3 text-green-500" />
                  ) : (
                    <WifiOff className="w-3 h-3 text-red-500" />
                  )}
                  <span>{isConnected ? 'Online' : 'Offline'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
