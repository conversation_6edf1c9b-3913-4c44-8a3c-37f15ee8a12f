import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, AlertCircle, Sparkles, Play, Square, Trash2 } from 'lucide-react';
import { EnterpriseFooter } from './EnterpriseFooter';

export default function DashboardUI() {
  // Configuration state
  const [source, setSource] = useState('./src');
  const [promptText, setPromptText] = useState('Optimize clarity and add comprehensive error handling');
  const [model, setModel] = useState('mixtral');
  const [tag, setTag] = useState('clarity-pass');

  // UI state
  const [logLines, setLogLines] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [isConnected, setIsConnected] = useState(false);

  const wsRef = useRef<WebSocket | null>(null);
  const logContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll logs
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logLines]);

  // Connect to WebSocket
  const connectWebSocket = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    wsRef.current = new WebSocket('ws://localhost:8000/ws/logs');

    wsRef.current.onopen = () => {
      setIsConnected(true);
      console.log('🔗 WebSocket connected');
    };

    wsRef.current.onmessage = (event) => {
      const line = event.data;
      setLogLines(prev => [...prev, line]);

      // Extract progress from messages
      const match = line.match(/Progress: (\d+)%/);
      if (match) {
        setProgress(parseInt(match[1]));
      }

      // Check for completion
      if (line.includes('completed successfully') || line.includes('injection failed')) {
        setLoading(false);
      }
    };

    wsRef.current.onclose = () => {
      setIsConnected(false);
      console.log('🔌 WebSocket disconnected');

      // Auto-reconnect after 3 seconds
      setTimeout(() => {
        if (!isConnected) {
          connectWebSocket();
        }
      }, 3000);
    };

    wsRef.current.onerror = (e) => {
      console.error('🚨 WebSocket error:', e);
      setError('WebSocket connection failed.');
      setIsConnected(false);
      setLoading(false);
    };
  };

  // Initialize WebSocket
  useEffect(() => {
    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  const startInjection = async () => {
    if (!isConnected) {
      setError('WebSocket not connected. Please wait for connection.');
      return;
    }

    setLoading(true);
    setLogLines([]);
    setError(null);
    setProgress(0);

    try {
      const response = await fetch('http://localhost:8000/api/inject', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          source,
          prompt_text: promptText,
          model,
          tag,
          auto_model: true,
          apply: false, // Preview mode for safety
          use_fallback: true,
          recursive: true,
          ext: 'py'
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        setError(result.error || 'Injection failed');
        setLoading(false);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Request failed';
      setError(errorMessage);
      setLoading(false);
      setLogLines(prev => [...prev, `❌ Error: ${errorMessage}`]);
    }
  };

  const stopInjection = () => {
    setLoading(false);
    setLogLines(prev => [...prev, '🛑 Injection stopped by user']);
  };

  const clearLogs = () => {
    setLogLines([]);
    setProgress(0);
    setError(null);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 p-6 space-y-6 max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Sparkles className="h-6 w-6 text-purple-500" />
            CodeCrusher Dashboard
          </h1>
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </Badge>
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Configuration Panel */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-lg">⚙️ Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="source">Source Path</Label>
              <Input
                id="source"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                placeholder="./src"
              />
            </div>

            <div>
              <Label htmlFor="prompt">AI Prompt</Label>
              <Textarea
                id="prompt"
                value={promptText}
                onChange={(e) => setPromptText(e.target.value)}
                rows={3}
                placeholder="Enter your optimization prompt..."
              />
            </div>

            <div>
              <Label htmlFor="tag">Tag</Label>
              <Input
                id="tag"
                value={tag}
                onChange={(e) => setTag(e.target.value)}
                placeholder="clarity-pass"
              />
            </div>

            <div className="flex items-center gap-3 pt-2">
              <Button
                onClick={startInjection}
                disabled={loading || !isConnected || !promptText.trim()}
                className="flex-1"
              >
                {loading ? (
                  <>
                    <Loader2 className="animate-spin h-4 w-4 mr-2" />
                    Running...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Run Injection
                  </>
                )}
              </Button>

              {loading && (
                <Button
                  onClick={stopInjection}
                  variant="destructive"
                  size="sm"
                >
                  <Square className="h-4 w-4" />
                </Button>
              )}
            </div>

            <Badge variant="secondary" className="flex items-center gap-2 w-fit">
              <Sparkles className="h-4 w-4 text-purple-500" />
              {model} (Auto)
            </Badge>
          </CardContent>
        </Card>

        {/* Logs & Progress Panel */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="text-lg">📊 Live Output</span>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">
                  {logLines.length} messages
                </span>
                <Button
                  onClick={clearLogs}
                  variant="outline"
                  size="sm"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Progress Bar */}
            {loading && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{progress}%</span>
                </div>
                <Progress value={progress} className="w-full h-3" />
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="flex items-center text-red-600 gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
                <AlertCircle className="h-5 w-5" />
                <span>{error}</span>
              </div>
            )}

            {/* Log Output */}
            <div
              ref={logContainerRef}
              className="bg-black text-green-400 font-mono p-4 rounded-lg h-80 overflow-auto shadow-inner border"
            >
              {logLines.length === 0 ? (
                <div className="text-gray-500 italic">
                  {isConnected ? 'Waiting for logs...' : 'Connecting to WebSocket...'}
                </div>
              ) : (
                logLines.map((line, idx) => (
                  <div key={idx} className="mb-1">
                    {line}
                  </div>
                ))
              )}
            </div>

            {/* Status Info */}
            <div className="text-xs text-gray-600 flex justify-between">
              <span>WebSocket: {isConnected ? 'Connected' : 'Disconnected'}</span>
              <span>Mode: Preview (Safe)</span>
            </div>
          </CardContent>
        </Card>
      </div>
      </div>

      {/* Enterprise Footer */}
      <EnterpriseFooter />
    </div>
  );
}
