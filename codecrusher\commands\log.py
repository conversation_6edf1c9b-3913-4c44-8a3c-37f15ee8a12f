"""
Log command for CodeCrusher.

This module provides the log command for CodeCrusher, which displays
a list of cached injections with filtering by tags and fields.
"""

import typer
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.syntax import Syntax
from rich.text import Text
from rich.box import ROUNDED
from rich.markdown import Markdown

# Import tag manager
from codecrusher.tag_manager import (
    parse_user_tags,
    tag_matches,
    highlight_matched_tags
)
from codecrusher.cache_manager import load_cache, filter_cache_by_tags

# Import filter utilities
from codecrusher.filter_utils import (
    parse_filter,
    apply_filters,
    format_entry_preview,
    PreviewMode,
    truncate_text
)

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

def tag_matches(entry_tags: List[str], filter_tags: List[str]) -> bool:
    """
    Check if all filter tags match the entry tags (case-insensitive).

    Args:
        entry_tags: List of tags in the entry
        filter_tags: List of tags to filter by

    Returns:
        True if all filter tags match, False otherwise
    """
    if not filter_tags:
        return True

    if not entry_tags:
        return False

    # Convert all tags to lowercase for case-insensitive matching
    entry_tags_lower = [tag.lower() for tag in entry_tags]

    # Check if all filter tags are in the entry tags (case-insensitive)
    for filter_tag in filter_tags:
        filter_tag_lower = filter_tag.lower()

        # Check for substring match if using contains: prefix
        if filter_tag_lower.startswith("contains:"):
            substring = filter_tag_lower[9:]  # Remove "contains:" prefix
            if not any(substring in tag for tag in entry_tags_lower):
                return False
        # Regular exact match
        elif filter_tag_lower not in entry_tags_lower:
            return False

    return True

def format_timestamp(timestamp: str) -> str:
    """
    Format a timestamp for display.

    Args:
        timestamp: ISO format timestamp

    Returns:
        Formatted timestamp string
    """
    try:
        dt = datetime.fromisoformat(timestamp)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except (ValueError, TypeError):
        return timestamp or "Unknown"

def extract_tags_from_entry(entry: Any) -> List[str]:
    """
    Extract tags from a cache entry.

    Args:
        entry: Cache entry

    Returns:
        List of tags
    """
    if isinstance(entry, dict):
        # Direct tags in entry
        if "tags" in entry:
            return entry["tags"]

        # Check for nested model entries
        for key, value in entry.items():
            if isinstance(value, dict) and "tags" in value:
                return value["tags"]

    return []

@app.command("run")
def run_log(
    tag: Optional[str] = typer.Option(None, "--tag", help="Filter by tag (space-separated, AND logic)"),
    tag_exclude: Optional[str] = typer.Option(None, "--tag-exclude", help="Exclude entries with these tags (space-separated)"),
    filter: Optional[str] = typer.Option(None, "--filter", help="Filter by fields (field=value,...)"),
    preview: str = typer.Option("full", "--preview", help="Preview mode (short, full, metadata)"),
    limit: int = typer.Option(10, "--limit", "-l", help="Limit the number of results"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Show detailed information"),
):
    """
    Display a log of cached injections with optional filtering.

    This command displays a list of cached injections, optionally filtered by tags and fields.
    If multiple tags or filters are provided, entries must match ALL criteria (AND logic).
    Tag matching is case-insensitive and exact.

    Preview modes:
        --preview short      # show ID, date, truncated input/output (1-2 lines each)
        --preview full       # default: shows everything
        --preview metadata   # only ID, model, tags, and timestamps

    Filter options:
        --tag                # Include only entries with ALL these tags (AND logic)
        --tag-exclude        # Exclude entries with ANY of these tags
        --filter model=mistral
        --filter fallback=true
        --filter tag=teamA
        --filter error=timeout

    Examples:
        codecrusher log run
        codecrusher log run --tag performance
        codecrusher log run --tag "bugfix @model:mistral"
        codecrusher log run --tag-exclude testdata
        codecrusher log run --tag performance --tag-exclude "draft testdata"
        codecrusher log run --filter model=mistral,fallback=true
        codecrusher log run --preview short --filter fallback=true
    """
    # Parse filter tags
    filter_tags = parse_user_tags(tag)

    # Parse exclude tags
    exclude_tags = parse_user_tags(tag_exclude)

    # Parse field filters
    field_filters = parse_filter(filter)

    # Determine preview mode
    try:
        preview_mode = PreviewMode(preview.lower())
    except ValueError:
        console.print(f"[bold red]❌ Error:[/bold red] Invalid preview mode: {preview}")
        console.print("[yellow]Valid preview modes: short, full, metadata[/yellow]")
        preview_mode = PreviewMode.FULL

    # Format filter tags for display
    filter_tags_display = ", ".join(filter_tags) if filter_tags else "None"

    # Format exclude tags for display
    exclude_tags_display = ", ".join(exclude_tags) if exclude_tags else "None"

    # Format field filters for display
    field_filters_display = ", ".join([f"{k}={v}" for k, v in field_filters.items()]) if field_filters else "None"

    console.print(Panel(
        f"[bold]CodeCrusher Cache Log[/bold]\n\n"
        f"[cyan]Include Tags:[/cyan] {filter_tags_display}\n"
        f"[cyan]Exclude Tags:[/cyan] {exclude_tags_display}\n"
        f"[cyan]Field Filters:[/cyan] {field_filters_display}\n"
        f"[cyan]Preview Mode:[/cyan] {preview_mode.value}\n"
        f"[cyan]Limit:[/cyan] {limit}\n"
        f"[cyan]Verbose:[/cyan] {'Enabled' if verbose else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))

    # Load the cache
    cache = load_cache()

    # Create a table for the results based on preview mode
    if preview_mode == PreviewMode.SHORT:
        table = Table(title="Cached Injections (Short Preview)", box=ROUNDED)
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Model", style="green", no_wrap=True)
        table.add_column("Date", style="yellow", no_wrap=True)
        table.add_column("Preview", style="white")
    elif preview_mode == PreviewMode.METADATA:
        table = Table(title="Cached Injections (Metadata Only)", box=ROUNDED)
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Model", style="green", no_wrap=True)
        table.add_column("Date", style="yellow", no_wrap=True)
        table.add_column("Tags", style="magenta")
    else:  # FULL
        table = Table(title="Cached Injections (Full Preview)", box=ROUNDED)
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Model", style="green", no_wrap=True)
        table.add_column("Date", style="yellow", no_wrap=True)
        table.add_column("Content", style="white")

    # Track the number of entries displayed
    displayed_count = 0
    total_entries = 0
    filtered_entries = 0

    # Process each cache entry
    for key, entry in cache.items():
        if isinstance(entry, dict):
            # Handle nested model entries
            if any(isinstance(v, dict) for v in entry.values()):
                for model_id, model_entry in entry.items():
                    if not isinstance(model_entry, dict):
                        continue

                    total_entries += 1

                    # Extract tags
                    tags = extract_tags_from_entry(model_entry)

                    # Create a combined entry for filtering
                    combined_entry = {
                        "model": model_id,
                        "tags": tags,
                        **model_entry
                    }

                    # Filter by tags
                    if not tag_matches(tags, filter_tags, exclude_tags):
                        continue

                    # Filter by fields
                    if not apply_filters(combined_entry, field_filters):
                        continue

                    filtered_entries += 1

                    # Extract timestamp
                    timestamp = model_entry.get("timestamp", "Unknown")
                    formatted_timestamp = format_timestamp(timestamp)

                    # Format entry based on preview mode
                    formatted_lines, metadata = format_entry_preview(combined_entry, preview_mode, key)

                    # Add to table if within limit
                    if displayed_count < limit:
                        if preview_mode == PreviewMode.SHORT:
                            # Short preview: ID, model, date, preview
                            preview_text = "\n".join(formatted_lines[3:])  # Skip ID, model, date
                            table.add_row(
                                truncate_text(key, 20),
                                model_id,
                                formatted_timestamp,
                                preview_text
                            )
                        elif preview_mode == PreviewMode.METADATA:
                            # Metadata only: ID, model, date, tags
                            # Highlight matched tags
                            highlighted_tags = highlight_matched_tags(tags, filter_tags, exclude_tags)
                            tags_display = ", ".join(highlighted_tags) if highlighted_tags else "None"
                            table.add_row(
                                key,
                                model_id,
                                formatted_timestamp,
                                tags_display
                            )
                        else:  # FULL
                            # Full preview: ID, model, date, content
                            content = model_entry.get("output", "")
                            if not content:
                                content = str(model_entry)
                            table.add_row(
                                key,
                                model_id,
                                formatted_timestamp,
                                content[:1000] + ("..." if len(content) > 1000 else "")
                            )

                        displayed_count += 1
            else:
                # Handle direct entries
                total_entries += 1

                # Extract tags
                tags = extract_tags_from_entry(entry)

                # Create a combined entry for filtering
                combined_entry = {
                    "model": entry.get("model", "Unknown"),
                    "tags": tags,
                    **entry
                }

                # Filter by tags
                if not tag_matches(tags, filter_tags, exclude_tags):
                    continue

                # Filter by fields
                if not apply_filters(combined_entry, field_filters):
                    continue

                filtered_entries += 1

                # Extract timestamp
                timestamp = entry.get("timestamp", "Unknown")
                formatted_timestamp = format_timestamp(timestamp)

                # Format entry based on preview mode
                formatted_lines, metadata = format_entry_preview(combined_entry, preview_mode, key)

                # Add to table if within limit
                if displayed_count < limit:
                    if preview_mode == PreviewMode.SHORT:
                        # Short preview: ID, model, date, preview
                        preview_text = "\n".join(formatted_lines[3:])  # Skip ID, model, date
                        table.add_row(
                            truncate_text(key, 20),
                            combined_entry["model"],
                            formatted_timestamp,
                            preview_text
                        )
                    elif preview_mode == PreviewMode.METADATA:
                        # Metadata only: ID, model, date, tags
                        # Highlight matched tags
                        highlighted_tags = highlight_matched_tags(tags, filter_tags, exclude_tags)
                        tags_display = ", ".join(highlighted_tags) if highlighted_tags else "None"
                        table.add_row(
                            key,
                            combined_entry["model"],
                            formatted_timestamp,
                            tags_display
                        )
                    else:  # FULL
                        # Full preview: ID, model, date, content
                        content = entry.get("output", "")
                        if not content:
                            content = str(entry)
                        table.add_row(
                            key,
                            combined_entry["model"],
                            formatted_timestamp,
                            content[:1000] + ("..." if len(content) > 1000 else "")
                        )

                    displayed_count += 1

    # Display the results
    if displayed_count > 0:
        console.print(table)
        console.print(f"[bold green]Showing {displayed_count} of {filtered_entries} filtered entries (from {total_entries} total)[/bold green]")
    else:
        console.print("[bold yellow]No entries found matching the filter criteria[/bold yellow]")

    return True
