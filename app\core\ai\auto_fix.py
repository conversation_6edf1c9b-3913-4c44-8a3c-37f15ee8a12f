"""
Auto-Fix Engine for CodeCrusher

This module provides intelligent auto-fix suggestions when users submit negative feedback.
It analyzes the original prompt, user feedback, and code diff to generate improved prompts
that address the specific issues mentioned in the feedback.

Features:
- LLM-powered prompt improvement analysis
- Context-aware suggestion generation
- Integration with existing AI models (Mixtral, GPT-4, Claude)
- Feedback pattern recognition and learning
- Automatic retry prompt generation
"""

import logging
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)

def generate_auto_fix_prompt(feedback: str, original_prompt: str, diff: str = "", 
                           model_used: str = "", intent: str = "", file_path: str = "") -> str:
    """
    Generate an LLM prompt to create an improved version of the original prompt.
    
    Args:
        feedback: User's negative feedback explaining what went wrong
        original_prompt: The original prompt that produced poor results
        diff: Code diff showing what was actually changed (optional)
        model_used: AI model that was used for the original injection
        intent: Detected intent of the original prompt
        file_path: Target file path for context
        
    Returns:
        LLM prompt for generating an improved version
    """
    
    # Build context information
    context_info = []
    if model_used:
        context_info.append(f"Model used: {model_used}")
    if intent:
        context_info.append(f"Detected intent: {intent}")
    if file_path:
        context_info.append(f"Target file: {file_path}")
    
    context_section = "\n".join(context_info) if context_info else "No additional context available."
    
    # Include diff if available
    diff_section = ""
    if diff and diff.strip():
        # Truncate very long diffs
        truncated_diff = diff[:2000] + "..." if len(diff) > 2000 else diff
        diff_section = f"""
Code diff (what was actually generated):
```diff
{truncated_diff}
```
"""
    
    # Generate the auto-fix prompt
    auto_fix_prompt = f"""You are an expert prompt engineer specializing in code generation and AI-assisted development.

A user submitted negative feedback about a code injection result. Your task is to analyze the situation and generate a BETTER, more specific prompt that addresses the user's concerns.

ORIGINAL PROMPT:
"{original_prompt}"

USER FEEDBACK (what went wrong):
"{feedback}"

CONTEXT:
{context_section}
{diff_section}

ANALYSIS GUIDELINES:
1. Identify what specifically went wrong based on the feedback
2. Determine if the original prompt was too vague, missing context, or had incorrect assumptions
3. Consider if the prompt needs more technical specificity or clearer constraints
4. Think about what additional context or examples might help

IMPROVED PROMPT REQUIREMENTS:
- Be more specific and detailed than the original
- Address the specific issues mentioned in the feedback
- Include relevant technical constraints or requirements
- Provide clear context about the desired outcome
- Use precise technical language when appropriate
- Keep it concise but comprehensive

Return ONLY the improved prompt text, nothing else. Do not include explanations or meta-commentary."""

    return auto_fix_prompt

def analyze_feedback_patterns(feedback: str) -> Dict[str, Any]:
    """
    Analyze feedback text to identify common patterns and issues.
    
    Args:
        feedback: User feedback text
        
    Returns:
        Dictionary with analysis results and suggested improvements
    """
    
    feedback_lower = feedback.lower()
    
    # Common issue patterns
    patterns = {
        "too_vague": ["vague", "unclear", "not specific", "ambiguous", "generic"],
        "wrong_context": ["wrong context", "misunderstood", "not what I wanted", "different purpose"],
        "missing_features": ["missing", "forgot", "didn't include", "lacks", "needs"],
        "performance_issues": ["slow", "inefficient", "performance", "optimization", "speed"],
        "style_issues": ["style", "formatting", "convention", "standards", "readability"],
        "functionality_broken": ["broken", "doesn't work", "error", "bug", "fails", "crash"],
        "incomplete": ["incomplete", "partial", "unfinished", "half", "not complete"],
        "over_complicated": ["too complex", "overcomplicated", "simpler", "too much", "excessive"]
    }
    
    detected_issues = []
    for issue_type, keywords in patterns.items():
        if any(keyword in feedback_lower for keyword in keywords):
            detected_issues.append(issue_type)
    
    # Generate improvement suggestions based on detected patterns
    improvement_suggestions = []
    
    if "too_vague" in detected_issues:
        improvement_suggestions.append("Add more specific technical requirements and constraints")
    
    if "wrong_context" in detected_issues:
        improvement_suggestions.append("Provide clearer context about the intended use case")
    
    if "missing_features" in detected_issues:
        improvement_suggestions.append("Explicitly list all required features and functionality")
    
    if "performance_issues" in detected_issues:
        improvement_suggestions.append("Include specific performance requirements and optimization goals")
    
    if "style_issues" in detected_issues:
        improvement_suggestions.append("Specify coding style, conventions, and formatting requirements")
    
    if "functionality_broken" in detected_issues:
        improvement_suggestions.append("Add validation requirements and error handling specifications")
    
    if "incomplete" in detected_issues:
        improvement_suggestions.append("Request complete implementation with all necessary components")
    
    if "over_complicated" in detected_issues:
        improvement_suggestions.append("Emphasize simplicity and minimal viable implementation")
    
    return {
        "detected_issues": detected_issues,
        "improvement_suggestions": improvement_suggestions,
        "feedback_sentiment": "negative",  # Since this is called for bad ratings
        "analysis_timestamp": datetime.now().isoformat()
    }

def get_improved_prompt(feedback_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate an improved prompt based on feedback data.
    
    Args:
        feedback_data: Dictionary containing feedback information
        
    Returns:
        Dictionary with improved prompt and analysis
    """
    
    try:
        # Extract data from feedback
        original_prompt = feedback_data.get("prompt", "")
        feedback_comment = feedback_data.get("comment", "")
        diff = feedback_data.get("diff", "")
        model_used = feedback_data.get("model", "")
        intent = feedback_data.get("intent", "")
        file_path = feedback_data.get("file", "")
        
        # Analyze feedback patterns
        feedback_analysis = analyze_feedback_patterns(feedback_comment)
        
        # Generate auto-fix prompt for LLM
        auto_fix_prompt = generate_auto_fix_prompt(
            feedback=feedback_comment,
            original_prompt=original_prompt,
            diff=diff,
            model_used=model_used,
            intent=intent,
            file_path=file_path
        )
        
        # Call LLM to generate improved prompt
        improved_prompt = call_auto_fix_model(auto_fix_prompt)
        
        # Validate and enhance the improved prompt
        if improved_prompt and len(improved_prompt.strip()) > 10:
            # Apply additional enhancements based on analysis
            enhanced_prompt = enhance_prompt_with_analysis(improved_prompt, feedback_analysis)
            
            return {
                "success": True,
                "improved_prompt": enhanced_prompt,
                "original_prompt": original_prompt,
                "feedback_analysis": feedback_analysis,
                "model_used_for_fix": "mixtral",  # Default model for auto-fix
                "confidence_score": calculate_confidence_score(feedback_analysis, enhanced_prompt),
                "timestamp": datetime.now().isoformat()
            }
        else:
            # Fallback to rule-based improvement
            fallback_prompt = generate_fallback_improved_prompt(original_prompt, feedback_analysis)
            
            return {
                "success": True,
                "improved_prompt": fallback_prompt,
                "original_prompt": original_prompt,
                "feedback_analysis": feedback_analysis,
                "model_used_for_fix": "rule_based_fallback",
                "confidence_score": 0.6,  # Lower confidence for fallback
                "timestamp": datetime.now().isoformat()
            }
            
    except Exception as e:
        logger.error(f"Failed to generate improved prompt: {e}")
        return {
            "success": False,
            "error": str(e),
            "original_prompt": feedback_data.get("prompt", ""),
            "timestamp": datetime.now().isoformat()
        }

def call_auto_fix_model(prompt: str) -> Optional[str]:
    """
    Call AI model to generate improved prompt.
    
    Args:
        prompt: The auto-fix prompt for the LLM
        
    Returns:
        Generated improved prompt or None if failed
    """
    
    try:
        # Import AI model calling function
        from core_injector import call_ai_model
        
        # Use Mixtral for auto-fix generation (good balance of quality and speed)
        result = call_ai_model(prompt, model="mixtral")
        
        if result and len(result.strip()) > 10:
            # Clean up the result
            cleaned_result = result.strip()
            
            # Remove any markdown formatting
            if cleaned_result.startswith('```'):
                lines = cleaned_result.split('\n')
                cleaned_result = '\n'.join(lines[1:-1]) if len(lines) > 2 else cleaned_result
            
            # Remove quotes if the entire response is quoted
            if cleaned_result.startswith('"') and cleaned_result.endswith('"'):
                cleaned_result = cleaned_result[1:-1]
            
            return cleaned_result
        
        return None
        
    except Exception as e:
        logger.error(f"Auto-fix model call failed: {e}")
        return None

def enhance_prompt_with_analysis(prompt: str, analysis: Dict[str, Any]) -> str:
    """
    Enhance the improved prompt based on feedback analysis.
    
    Args:
        prompt: The improved prompt from LLM
        analysis: Feedback analysis results
        
    Returns:
        Enhanced prompt with additional context
    """
    
    enhanced_prompt = prompt
    
    # Add specific enhancements based on detected issues
    detected_issues = analysis.get("detected_issues", [])
    
    if "performance_issues" in detected_issues and "performance" not in prompt.lower():
        enhanced_prompt += " Ensure the solution is optimized for performance and efficiency."
    
    if "style_issues" in detected_issues and "style" not in prompt.lower():
        enhanced_prompt += " Follow best practices for code style and formatting conventions."
    
    if "functionality_broken" in detected_issues and "test" not in prompt.lower():
        enhanced_prompt += " Include proper error handling and ensure the code is thoroughly tested."
    
    if "incomplete" in detected_issues and "complete" not in prompt.lower():
        enhanced_prompt += " Provide a complete implementation with all necessary components."
    
    return enhanced_prompt

def generate_fallback_improved_prompt(original_prompt: str, analysis: Dict[str, Any]) -> str:
    """
    Generate a fallback improved prompt using rule-based enhancement.
    
    Args:
        original_prompt: The original prompt that failed
        analysis: Feedback analysis results
        
    Returns:
        Rule-based improved prompt
    """
    
    # Start with the original prompt
    improved = original_prompt
    
    # Add specificity based on detected issues
    detected_issues = analysis.get("detected_issues", [])
    
    if "too_vague" in detected_issues:
        improved = f"Please provide a detailed and specific implementation for: {improved}"
    
    if "missing_features" in detected_issues:
        improved += " Include all necessary features and functionality."
    
    if "performance_issues" in detected_issues:
        improved += " Optimize for performance and efficiency."
    
    if "style_issues" in detected_issues:
        improved += " Follow coding best practices and style conventions."
    
    if "functionality_broken" in detected_issues:
        improved += " Ensure the code works correctly with proper error handling."
    
    # Add general improvements
    if len(improved) == len(original_prompt):  # No specific issues detected
        improved = f"Please provide a more detailed and robust implementation for: {improved}"
    
    return improved

def calculate_confidence_score(analysis: Dict[str, Any], improved_prompt: str) -> float:
    """
    Calculate confidence score for the improved prompt.
    
    Args:
        analysis: Feedback analysis results
        improved_prompt: The generated improved prompt
        
    Returns:
        Confidence score between 0.0 and 1.0
    """
    
    base_score = 0.7  # Base confidence
    
    # Increase confidence based on analysis quality
    detected_issues = analysis.get("detected_issues", [])
    if len(detected_issues) > 0:
        base_score += 0.1  # We identified specific issues
    
    # Increase confidence based on prompt improvements
    if len(improved_prompt) > len(analysis.get("original_prompt", "")):
        base_score += 0.1  # Prompt is more detailed
    
    # Check for specific technical terms
    technical_terms = ["implement", "optimize", "ensure", "include", "provide", "specific", "detailed"]
    if any(term in improved_prompt.lower() for term in technical_terms):
        base_score += 0.1
    
    return min(base_score, 1.0)  # Cap at 1.0
