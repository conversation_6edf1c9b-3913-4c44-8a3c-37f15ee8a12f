<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - CodeCrusher</title>
    <meta name="description" content="The page you're looking for could not be found.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/favicon.png">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="css/main.css">
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            padding: 2rem;
        }
        
        .error-content {
            text-align: center;
            max-width: 600px;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 800;
            line-height: 1;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .error-description {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .error-suggestions {
            margin-top: 3rem;
            padding: 2rem;
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--border-color);
        }
        
        .suggestions-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .suggestions-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .suggestions-list li {
            margin-bottom: 0.75rem;
        }
        
        .suggestions-list a {
            color: var(--primary-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: color 0.3s ease;
        }
        
        .suggestions-list a:hover {
            color: var(--primary-hover);
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .error-suggestions {
                margin-top: 2rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="error-content">
            <div class="error-code">404</div>
            <h1 class="error-title">Page Not Found</h1>
            <p class="error-description">
                The page you're looking for doesn't exist or has been moved. 
                Let's get you back on track with CodeCrusher!
            </p>
            
            <div class="error-actions">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    Go Home
                </a>
                <a href="https://github.com/Codegx-Technology/CodeCruncher" class="btn btn-secondary" target="_blank">
                    <i class="fab fa-github"></i>
                    View Repository
                </a>
            </div>
            
            <div class="error-suggestions">
                <h2 class="suggestions-title">Popular Pages</h2>
                <ul class="suggestions-list">
                    <li>
                        <a href="/#installation">
                            <i class="fas fa-download"></i>
                            Installation Guide
                        </a>
                    </li>
                    <li>
                        <a href="/#usage">
                            <i class="fas fa-terminal"></i>
                            Usage Examples
                        </a>
                    </li>
                    <li>
                        <a href="/#features">
                            <i class="fas fa-star"></i>
                            Features Overview
                        </a>
                    </li>
                    <li>
                        <a href="/#architecture">
                            <i class="fas fa-sitemap"></i>
                            System Architecture
                        </a>
                    </li>
                    <li>
                        <a href="/#contributing">
                            <i class="fas fa-code-branch"></i>
                            Contributing Guide
                        </a>
                    </li>
                    <li>
                        <a href="https://pypi.org/project/codecrusher/" target="_blank">
                            <i class="fas fa-cube"></i>
                            PyPI Package
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- Theme toggle functionality -->
    <script>
        // Check for saved theme preference or default to light
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
    </script>
</body>
</html>
