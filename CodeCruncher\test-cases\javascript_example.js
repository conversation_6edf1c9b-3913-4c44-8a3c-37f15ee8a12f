/**
 * JavaScript file for testing CodeCrusher injection
 * Contains AI_INJECT tags for testing multi-language support
 */

class Calculator {
    constructor() {
        this.history = [];
    }
    
    add(a, b) {
        const result = a + b;
        this.history.push(`${a} + ${b} = ${result}`);
        return result;
    }
    
    subtract(a, b) {
        const result = a - b;
        this.history.push(`${a} - ${b} = ${result}`);
        return result;
    }
    
    // AI_INJECT:multiply_method
    // Add multiplication method here
    // AI_INJECT:multiply_method:end
    
    // AI_INJECT:divide_method
    // Add division method with error handling here
    // AI_INJECT:divide_method:end
    
    getHistory() {
        return [...this.history];
    }
    
    clearHistory() {
        this.history = [];
    }
}

// AI_INJECT:utility_functions
// Add utility functions here (factorial, fibon<PERSON><PERSON>, etc.)
// AI_INJECT:utility_functions:end

function main() {
    console.log('CodeCrusher JavaScript Test');
    
    const calc = new Calculator();
    
    // Basic operations
    console.log(`2 + 3 = ${calc.add(2, 3)}`);
    console.log(`5 - 2 = ${calc.subtract(5, 2)}`);
    
    // AI_INJECT:advanced_operations
    // Add more advanced operations here
    // AI_INJECT:advanced_operations:end
    
    // Display history
    console.log('\nCalculation History:');
    calc.getHistory().forEach(entry => {
        console.log(`  ${entry}`);
    });
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Calculator };
} else {
    main();
}
