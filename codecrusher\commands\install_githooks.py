"""
Install-githooks command for CodeCrusher.

This module provides the install-githooks command for CodeCrusher, which is an alias
for the 'githooks install' command. It installs Git hooks for automating injection
tagging, telemetry metadata collection, and telemetry log syncing.
"""

import typer
from typing import Optional
from rich.console import Console
from rich.panel import Panel

# Import the githooks module to reuse its functionality
from codecrusher.commands.githooks import (
    get_git_hooks_dir,
    install_hook,
    save_config,
    load_config,
    HOOKS_DIR,
    CONFIG_DIR,
    CONFIG_FILE
)

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

@app.command("run")
def install_githooks(
    repo_path: Optional[str] = typer.Option(
        None, "--repo-path", "-r",
        help="Path to the Git repository (default: current directory)"
    ),
    block_on_errors: bool = typer.Option(
        False, "--block-on-errors", "-b",
        help="Block commits if critical injection errors are detected"
    ),
    auto_insert_tags: bool = typer.Option(
        False, "--auto-insert-tags", "-a",
        help="Automatically insert missing tags based on context"
    ),
    remote_sync: bool = typer.Option(
        False, "--remote-sync", "-s",
        help="Sync telemetry metadata to a remote server"
    ),
    remote_sync_url: str = typer.Option(
        "", "--remote-sync-url", "-u",
        help="URL of the remote server for telemetry sync"
    ),
):
    """
    Install CodeCrusher Git hooks.
    
    This command installs Git hooks for automating injection tagging,
    telemetry metadata collection, and telemetry log syncing.
    
    Examples:
        codecrusher install-githooks
        codecrusher install-githooks --block-on-errors
        codecrusher install-githooks --auto-insert-tags
        codecrusher install-githooks --remote-sync --remote-sync-url https://example.com/api/telemetry
    """
    # Get Git hooks directory
    hooks_dir = get_git_hooks_dir(repo_path)
    
    if not hooks_dir:
        return False
    
    # Create hooks directory if it doesn't exist
    hooks_dir.mkdir(exist_ok=True)
    
    # Load config
    config = load_config()
    
    # Update config
    config["block_on_errors"] = block_on_errors
    config["auto_insert_tags"] = auto_insert_tags
    config["remote_sync"] = remote_sync
    config["remote_sync_url"] = remote_sync_url
    
    # Get absolute path of repo
    import os
    if repo_path:
        repo_abs_path = os.path.abspath(repo_path)
    else:
        repo_abs_path = os.path.abspath(os.getcwd())
    
    # Add repo to installed repos if not already present
    if repo_abs_path not in config["installed_repos"]:
        config["installed_repos"].append(repo_abs_path)
    
    # Save config
    save_config(config)
    
    # Install hooks
    success = True
    if not install_hook(hooks_dir, "pre-commit", config):
        success = False
    if not install_hook(hooks_dir, "post-commit", config):
        success = False
    
    if success:
        console.print(Panel(
            "[bold green]CodeCrusher Git hooks installed successfully![/bold green]\n\n"
            f"[cyan]Repository:[/cyan] {repo_abs_path}\n"
            f"[cyan]Hooks Directory:[/cyan] {hooks_dir}\n"
            f"[cyan]Block on Errors:[/cyan] {'Enabled' if block_on_errors else 'Disabled'}\n"
            f"[cyan]Auto Insert Tags:[/cyan] {'Enabled' if auto_insert_tags else 'Disabled'}\n"
            f"[cyan]Remote Sync:[/cyan] {'Enabled' if remote_sync else 'Disabled'}\n"
            f"[cyan]Remote Sync URL:[/cyan] {remote_sync_url or 'N/A'}",
            title="Installation Complete",
            border_style="green"
        ))
    else:
        console.print("[bold red]Installation failed. Please check the errors above.[/bold red]")
    
    return success
