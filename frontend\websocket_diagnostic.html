<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Diagnostic Tool</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #00ff00;
        }
        .container {
            background: #000;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #004400; color: #00ff00; }
        .disconnected { background-color: #440000; color: #ff4444; }
        .connecting { background-color: #444400; color: #ffff00; }
        .error { background-color: #440000; color: #ff4444; }
        .log {
            background-color: #111;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        button {
            background-color: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
        }
        button:hover { background-color: #555; }
        button:disabled { background-color: #222; color: #666; cursor: not-allowed; }
        .metric {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            background: #222;
            border-radius: 4px;
            border: 1px solid #444;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #00ffff;
        }
        .metric-label {
            font-size: 12px;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 WebSocket Diagnostic Tool</h1>

        <div id="status" class="status disconnected">
            ❌ Not Connected
        </div>

        <div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
            <button onclick="testAPI()">Test API</button>
            <button onclick="runDiagnostics()">Run Diagnostics</button>
        </div>

        <div>
            <div class="metric">
                <div class="metric-value" id="connectionAttempts">0</div>
                <div class="metric-label">Connection Attempts</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="messagesReceived">0</div>
                <div class="metric-label">Messages Received</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="errors">0</div>
                <div class="metric-label">Errors</div>
            </div>
            <div class="metric">
                <div class="metric-value" id="uptime">0s</div>
                <div class="metric-label">Connection Uptime</div>
            </div>
        </div>

        <h3>📊 Connection Details:</h3>
        <div id="connectionDetails" class="log">No connection details available</div>

        <h3>📝 Diagnostic Log:</h3>
        <div id="log" class="log"></div>

        <h3>🔧 Browser Information:</h3>
        <div id="browserInfo" class="log"></div>
    </div>

    <script>
        let ws = null;
        let connectionAttempts = 0;
        let messagesReceived = 0;
        let errors = 0;
        let connectTime = null;
        let uptimeInterval = null;

        const wsUrl = `ws://${window.location.host}/ws/intelligence`;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toISOString();
            const prefix = type === 'error' ? '❌' : type === 'warn' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateMetrics() {
            document.getElementById('connectionAttempts').textContent = connectionAttempts;
            document.getElementById('messagesReceived').textContent = messagesReceived;
            document.getElementById('errors').textContent = errors;
        }

        function updateUptime() {
            if (connectTime) {
                const uptime = Math.floor((Date.now() - connectTime) / 1000);
                document.getElementById('uptime').textContent = `${uptime}s`;
            }
        }

        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');

            statusDiv.className = `status ${status}`;

            switch(status) {
                case 'connected':
                    statusDiv.innerHTML = '✅ Connected';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    connectTime = Date.now();
                    uptimeInterval = setInterval(updateUptime, 1000);
                    break;
                case 'connecting':
                    statusDiv.innerHTML = '🔄 Connecting...';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = true;
                    break;
                case 'disconnected':
                    statusDiv.innerHTML = '❌ Disconnected';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    connectTime = null;
                    if (uptimeInterval) {
                        clearInterval(uptimeInterval);
                        uptimeInterval = null;
                    }
                    document.getElementById('uptime').textContent = '0s';
                    break;
                case 'error':
                    statusDiv.innerHTML = '💥 Error';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    errors++;
                    break;
            }

            if (message) {
                log(message, status === 'error' ? 'error' : status === 'connected' ? 'success' : 'info');
            }
            updateMetrics();
        }

        function updateConnectionDetails() {
            const details = document.getElementById('connectionDetails');
            if (ws) {
                details.textContent = `
WebSocket URL: ${wsUrl}
Ready State: ${ws.readyState} (${getReadyStateText(ws.readyState)})
Protocol: ${ws.protocol || 'none'}
Extensions: ${ws.extensions || 'none'}
Binary Type: ${ws.binaryType}
Buffered Amount: ${ws.bufferedAmount} bytes
`;
            } else {
                details.textContent = 'No active WebSocket connection';
            }
        }

        function getReadyStateText(state) {
            switch(state) {
                case WebSocket.CONNECTING: return 'CONNECTING';
                case WebSocket.OPEN: return 'OPEN';
                case WebSocket.CLOSING: return 'CLOSING';
                case WebSocket.CLOSED: return 'CLOSED';
                default: return 'UNKNOWN';
            }
        }

        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected', 'warn');
                return;
            }

            connectionAttempts++;
            updateStatus('connecting', `Connection attempt #${connectionAttempts} to ${wsUrl}`);

            try {
                ws = new WebSocket(wsUrl);
                updateConnectionDetails();

                ws.onopen = function(event) {
                    updateStatus('connected', '🎉 WebSocket connection established!');
                    updateConnectionDetails();
                    log(`Connection opened. Event: ${JSON.stringify({
                        type: event.type,
                        target: event.target.constructor.name,
                        timeStamp: event.timeStamp
                    })}`, 'success');
                };

                ws.onmessage = function(event) {
                    messagesReceived++;
                    updateMetrics();
                    try {
                        const data = JSON.parse(event.data);
                        log(`📨 Message received: ${data.type} - ${JSON.stringify(data, null, 2)}`, 'success');
                    } catch (e) {
                        log(`📨 Raw message received: ${event.data}`, 'info');
                    }
                };

                ws.onclose = function(event) {
                    updateStatus('disconnected', `🔌 Connection closed (Code: ${event.code}, Reason: "${event.reason}", Clean: ${event.wasClean})`);
                    updateConnectionDetails();
                    log(`Close event details: ${JSON.stringify({
                        code: event.code,
                        reason: event.reason,
                        wasClean: event.wasClean,
                        type: event.type,
                        timeStamp: event.timeStamp
                    })}`, 'warn');
                };

                ws.onerror = function(error) {
                    updateStatus('error', `❌ WebSocket error occurred`);
                    updateConnectionDetails();
                    log(`Error event: ${JSON.stringify({
                        type: error.type,
                        target: error.target.constructor.name,
                        timeStamp: error.timeStamp,
                        readyState: error.target.readyState
                    })}`, 'error');
                };

            } catch (error) {
                updateStatus('error', `❌ Failed to create WebSocket: ${error.message}`);
                log(`Exception during WebSocket creation: ${error.stack}`, 'error');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close(1000, 'Manual disconnect');
                ws = null;
                updateConnectionDetails();
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function testAPI() {
            log('🧪 Testing API endpoint...', 'info');
            try {
                const response = await fetch('/api/intel/summary');
                if (response.ok) {
                    const data = await response.json();
                    log(`✅ API test successful: ${response.status} ${response.statusText}`, 'success');
                    log(`API response: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    log(`❌ API test failed: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                log(`❌ API test error: ${error.message}`, 'error');
            }
        }

        function runDiagnostics() {
            log('🔍 Running comprehensive diagnostics...', 'info');

            // Browser capabilities
            log(`WebSocket support: ${typeof WebSocket !== 'undefined' ? '✅ Available' : '❌ Not available'}`, 'info');
            log(`Fetch support: ${typeof fetch !== 'undefined' ? '✅ Available' : '❌ Not available'}`, 'info');

            // Network diagnostics
            log('🌐 Testing network connectivity...', 'info');
            testAPI();

            // WebSocket specific tests
            log('🔌 Testing WebSocket creation...', 'info');
            try {
                const testWs = new WebSocket(wsUrl);
                log('✅ WebSocket object created successfully', 'success');
                testWs.close();
            } catch (error) {
                log(`❌ WebSocket creation failed: ${error.message}`, 'error');
            }
        }

        function displayBrowserInfo() {
            const info = document.getElementById('browserInfo');
            info.textContent = `
User Agent: ${navigator.userAgent}
Platform: ${navigator.platform}
Language: ${navigator.language}
Online: ${navigator.onLine}
Cookie Enabled: ${navigator.cookieEnabled}
WebSocket Support: ${typeof WebSocket !== 'undefined'}
Location: ${window.location.href}
Origin: ${window.location.origin}
Protocol: ${window.location.protocol}
Host: ${window.location.host}
`;
        }

        // Initialize
        window.onload = function() {
            log('🚀 WebSocket Diagnostic Tool loaded', 'info');
            displayBrowserInfo();
            updateConnectionDetails();
            updateMetrics();
        };

        // Cleanup on page unload
        window.onbeforeunload = function() {
            if (ws) {
                ws.close(1000, 'Page unload');
            }
        };
    </script>
</body>
</html>
