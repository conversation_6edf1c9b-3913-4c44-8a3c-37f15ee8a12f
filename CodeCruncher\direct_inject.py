import logging
import asyncio
import typer

from ai_engine import run_codecrusher_injection

app = typer.Typer()

@app.command()
def main(
    input: str = typer.Option(..., "--input", "-i", help="Path to input file"),
    prompt_text: str = typer.Option(..., "--prompt-text", "-t", help="Injection prompt text"),
    provider: str = typer.Option("auto", "--provider", help="AI provider to use"),
    model: str = typer.Option("auto", "--model", help="AI model to use"),
    cache: bool = typer.Option(False, "--cache", help="Enable caching"),
    verbose: bool = typer.Option(False, "--verbose", help="Enable verbose logging"),
):
    """
    Inject task for CodeCrusher: Reads input file, sends code and prompt to AI engine,
    and prints injected code result.
    """
    if verbose:
        logging.basicConfig(level=logging.DEBUG)
    else:
        logging.basicConfig(level=logging.INFO)

    logger = logging.getLogger(__name__)

    logger.info(f"Reading from input file: {input}")
    try:
        with open(input, "r") as f:
            code = f.read()
        logger.debug(f"Read code:\n{code}")
    except FileNotFoundError:
        logger.error("❌ File not found!")
        raise typer.Exit(code=1)

    logger.info("Sending code and prompt to CodeCrusher engine...")

    # Use our existing run_codecrusher_injection function
    async def run_injection():
        return await run_codecrusher_injection(
            code=code,
            prompt_text=prompt_text,
            provider=provider,
            model=model,
            cache=cache,
            verbose=verbose,
        )

    try:
        injected_code = asyncio.run(run_injection())
        logger.info("Injection complete. Output:")
        typer.echo(injected_code)
    except Exception as e:
        logger.error(f"Injection failed: {e}")
        raise typer.Exit(code=1)

if __name__ == "__main__":
    app()
