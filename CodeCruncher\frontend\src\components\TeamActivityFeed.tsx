import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Activity,
  Star,
  Code,
  Settings,
  UserPlus,
  UserMinus,
  Filter,
  Bell,
  BellOff,
  Wifi,
  WifiOff,
  Clock,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface TeamEvent {
  event_id: string;
  team_id: number;
  event_type: string;
  user_id: number;
  user_email: string;
  timestamp: string;
  payload: any;
}

interface TeamActivityFeedProps {
  teamId: number;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function TeamActivityFeed({ teamId, isCollapsed = false, onToggleCollapse }: TeamActivityFeedProps) {
  const { user, token } = useAuth();
  const [events, setEvents] = useState<TeamEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [activeMembers, setActiveMembers] = useState<any[]>([]);
  const wsRef = useRef<WebSocket | null>(null);

  // WebSocket connection
  useEffect(() => {
    if (!teamId || !token) return;

    const connectWebSocket = () => {
      const wsUrl = `ws://127.0.0.1:8001/team-activity/ws/${teamId}?token=${token}`;
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        setIsConnected(true);
        console.log('Team activity WebSocket connected');

        // Request recent activity
        ws.send(JSON.stringify({
          type: 'get_recent_activity',
          limit: 20
        }));

        // Request active members
        ws.send(JSON.stringify({
          type: 'get_active_members'
        }));
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'team_event') {
            // New real-time event
            setEvents(prev => [data.event, ...prev].slice(0, 50));

            // Show notification if enabled
            if (notifications && data.event.user_id !== user?.id) {
              showNotification(data.event);
            }
          } else if (data.type === 'recent_activity') {
            // Historical events
            setEvents(data.events.reverse());
          } else if (data.type === 'active_members') {
            setActiveMembers(data.members);
          } else if (data.type === 'recent_event') {
            // Recent event on connection
            setEvents(prev => {
              const exists = prev.some(e => e.event_id === data.event.event_id);
              if (!exists) {
                return [data.event, ...prev].slice(0, 50);
              }
              return prev;
            });
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = () => {
        setIsConnected(false);
        console.log('Team activity WebSocket disconnected');

        // Reconnect after 3 seconds
        setTimeout(connectWebSocket, 3000);
      };

      ws.onerror = (error) => {
        console.error('Team activity WebSocket error:', error);
        setIsConnected(false);
      };

      wsRef.current = ws;
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [teamId, token, user?.id, notifications]);

  const showNotification = (event: TeamEvent) => {
    if (!('Notification' in window)) return;

    if (Notification.permission === 'granted') {
      const title = getEventTitle(event);
      const body = getEventDescription(event);

      new Notification(title, {
        body,
        icon: '/favicon.ico',
        tag: event.event_id
      });
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          showNotification(event);
        }
      });
    }
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'injection_created': return <Code className="h-4 w-4 text-blue-500" />;
      case 'injection_rated': return <Star className="h-4 w-4 text-yellow-500" />;
      case 'prompt_shaped': return <Activity className="h-4 w-4 text-purple-500" />;
      case 'team_setting_changed': return <Settings className="h-4 w-4 text-gray-500" />;
      case 'user_joined_team': return <UserPlus className="h-4 w-4 text-green-500" />;
      case 'user_left_team': return <UserMinus className="h-4 w-4 text-red-500" />;
      default: return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getEventTitle = (event: TeamEvent) => {
    switch (event.event_type) {
      case 'injection_created': return 'New Injection';
      case 'injection_rated': return 'Injection Rated';
      case 'prompt_shaped': return 'Prompt Shaped';
      case 'team_setting_changed': return 'Setting Changed';
      case 'user_joined_team': return 'User Joined';
      case 'user_left_team': return 'User Left';
      default: return 'Team Activity';
    }
  };

  const getEventDescription = (event: TeamEvent) => {
    const userName = event.user_email.split('@')[0];

    switch (event.event_type) {
      case 'injection_created':
        return `${userName} injected ${event.payload.file_path} with ${event.payload.model}`;
      case 'injection_rated':
        return `${userName} rated injection ${event.payload.rating}/5`;
      case 'prompt_shaped':
        return `${userName} shaped prompt for ${event.payload.intent}`;
      case 'team_setting_changed':
        return `${userName} changed ${event.payload.setting_key}`;
      case 'user_joined_team':
        return `${event.payload.joined_user_email} joined the team`;
      case 'user_left_team':
        return `${userName} left the team`;
      default:
        return `${userName} performed an action`;
    }
  };

  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const eventTime = new Date(timestamp);
    const diffMs = now.getTime() - eventTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const filteredEvents = events.filter(event => {
    if (filter === 'all') return true;
    if (filter === 'my') return event.user_id === user?.id;
    if (filter === 'ratings') return event.event_type === 'injection_rated';
    if (filter === 'injections') return event.event_type === 'injection_created';
    if (filter === 'shaping') return event.event_type === 'prompt_shaped';
    return true;
  });

  if (isCollapsed) {
    return (
      <Card className="w-16">
        <CardContent className="p-2">
          <div className="flex flex-col items-center space-y-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleCollapse}
              className="p-2"
            >
              <Activity className="h-4 w-4" />
            </Button>

            <div className="flex items-center">
              {isConnected ? (
                <Wifi className="h-3 w-3 text-green-500" />
              ) : (
                <WifiOff className="h-3 w-3 text-red-500" />
              )}
            </div>

            {events.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {events.length}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-80">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Activity className="h-4 w-4" />
            Team Activity
            {isConnected ? (
              <Wifi className="h-3 w-3 text-green-500" />
            ) : (
              <WifiOff className="h-3 w-3 text-red-500" />
            )}
          </CardTitle>

          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setNotifications(!notifications)}
              className="p-1"
            >
              {notifications ? (
                <Bell className="h-3 w-3" />
              ) : (
                <BellOff className="h-3 w-3" />
              )}
            </Button>

            {onToggleCollapse && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleCollapse}
                className="p-1"
              >
                <ChevronUp className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Active Members */}
        {activeMembers.length > 0 && (
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>{activeMembers.length} active</span>
            <div className="flex -space-x-1">
              {activeMembers.slice(0, 3).map((member, i) => (
                <div
                  key={member.user_id}
                  className="w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs border border-white"
                  title={member.email}
                >
                  {member.email.charAt(0).toUpperCase()}
                </div>
              ))}
              {activeMembers.length > 3 && (
                <div className="w-5 h-5 bg-gray-400 rounded-full flex items-center justify-center text-white text-xs border border-white">
                  +{activeMembers.length - 3}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Filter Controls */}
        <div className="flex items-center gap-1">
          <Filter className="h-3 w-3 text-gray-400" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="text-xs border-none bg-transparent focus:outline-none"
          >
            <option value="all">All Activity</option>
            <option value="my">My Activity</option>
            <option value="injections">Injections</option>
            <option value="ratings">Ratings</option>
            <option value="shaping">Shaping</option>
          </select>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {!isConnected && (
          <Alert className="m-3 mb-0">
            <WifiOff className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Reconnecting to team activity feed...
            </AlertDescription>
          </Alert>
        )}

        <div className="max-h-96 overflow-y-auto">
          {filteredEvents.length === 0 ? (
            <div className="p-4 text-center text-gray-500 text-sm">
              No team activity yet
            </div>
          ) : (
            <div className="space-y-1">
              {filteredEvents.map((event) => (
                <div
                  key={event.event_id}
                  className="p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start gap-2">
                    {getEventIcon(event.event_type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-xs font-medium text-gray-900 truncate">
                          {getEventTitle(event)}
                        </p>
                        <div className="flex items-center gap-1 text-xs text-gray-500">
                          <Clock className="h-3 w-3" />
                          {getTimeAgo(event.timestamp)}
                        </div>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        {getEventDescription(event)}
                      </p>

                      {/* Event-specific details */}
                      {event.event_type === 'injection_rated' && event.payload.feedback && (
                        <p className="text-xs text-gray-500 mt-1 italic">
                          "{event.payload.feedback}"
                        </p>
                      )}

                      {event.event_type === 'injection_created' && (
                        <div className="flex items-center gap-1 mt-1">
                          {event.payload.tags?.split(',').map((tag: string) => (
                            <Badge key={tag} variant="outline" className="text-xs px-1 py-0">
                              {tag.trim()}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
