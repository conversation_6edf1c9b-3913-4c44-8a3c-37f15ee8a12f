"""
Telemetry command for CodeCrusher.

This module provides the telemetry command for CodeCrusher, which displays
telemetry data and allows for basic analysis.
"""

import typer
import json
import os
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.box import ROUNDED

# Import telemetry logger
from codecrusher.telemetry_logger import get_telemetry_entries, TELEMETRY_FILE

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

@app.command("status")
def run_status(
    limit: int = typer.Option(10, "--limit", "-l", help="Limit the number of entries to display"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Filter by model"),
    provider: Optional[str] = typer.Option(None, "--provider", "-p", help="Filter by provider"),
    operation_type: Optional[str] = typer.Option(None, "--type", "-t", help="Filter by operation type (injection, replay, etc.)"),
    cached: Optional[bool] = typer.Option(None, "--cached", help="Filter by cached status"),
    fallback: Optional[bool] = typer.Option(None, "--fallback", help="Filter by fallback status"),
    error: Optional[bool] = typer.Option(None, "--error", help="Filter by error status"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Filter by tag"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Show detailed information"),
):
    """
    Display telemetry status and recent entries.
    
    This command displays telemetry status and recent entries from the telemetry file.
    You can filter the entries by model, provider, operation type, cached status, fallback status,
    error status, and tag.
    
    Examples:
        codecrusher telemetry status
        codecrusher telemetry status --model llama3
        codecrusher telemetry status --provider groq
        codecrusher telemetry status --type injection
        codecrusher telemetry status --cached
        codecrusher telemetry status --fallback
        codecrusher telemetry status --error
        codecrusher telemetry status --tag bugfix
    """
    # Parse filter tags
    filter_tags = [tag] if tag else None
    
    # Get telemetry entries
    entries = get_telemetry_entries(
        limit=limit,
        filter_tags=filter_tags,
        filter_model=model,
        filter_provider=provider,
        filter_operation_type=operation_type,
        filter_cached=cached,
        filter_fallback=fallback,
        filter_error=error
    )
    
    # Display telemetry status
    console.print(Panel(
        f"[bold]CodeCrusher Telemetry Status[/bold]\n\n"
        f"[cyan]Telemetry File:[/cyan] {TELEMETRY_FILE}\n"
        f"[cyan]File Exists:[/cyan] {'Yes' if os.path.exists(TELEMETRY_FILE) else 'No'}\n"
        f"[cyan]File Size:[/cyan] {os.path.getsize(TELEMETRY_FILE) if os.path.exists(TELEMETRY_FILE) else 0} bytes\n"
        f"[cyan]Entry Count:[/cyan] {len(entries)} (filtered from limit {limit})",
        title="Telemetry Status",
        border_style="blue"
    ))
    
    # Display filter information
    filter_info = []
    if model:
        filter_info.append(f"Model: {model}")
    if provider:
        filter_info.append(f"Provider: {provider}")
    if operation_type:
        filter_info.append(f"Operation Type: {operation_type}")
    if cached is not None:
        filter_info.append(f"Cached: {'Yes' if cached else 'No'}")
    if fallback is not None:
        filter_info.append(f"Fallback: {'Yes' if fallback else 'No'}")
    if error is not None:
        filter_info.append(f"Error: {'Yes' if error else 'No'}")
    if tag:
        filter_info.append(f"Tag: {tag}")
    
    if filter_info:
        console.print(Panel(
            "\n".join(filter_info),
            title="Active Filters",
            border_style="yellow"
        ))
    
    # Display entries
    if entries:
        # Create a table for the entries
        table = Table(title="Recent Telemetry Entries", box=ROUNDED)
        table.add_column("ID", style="cyan", no_wrap=True)
        table.add_column("Timestamp", style="green")
        table.add_column("Model", style="blue")
        table.add_column("Provider", style="magenta")
        table.add_column("Type", style="yellow")
        table.add_column("Latency (ms)", justify="right")
        table.add_column("Tokens In/Out", justify="right")
        table.add_column("Status", style="bold")
        
        # Add rows to the table
        for entry in entries:
            # Format timestamp
            timestamp = entry.get("timestamp", "")
            try:
                dt = datetime.fromisoformat(timestamp)
                formatted_timestamp = dt.strftime("%Y-%m-%d %H:%M:%S")
            except (ValueError, TypeError):
                formatted_timestamp = timestamp
            
            # Format tokens
            tokens_in = entry.get("tokens_in", None)
            tokens_out = entry.get("tokens_out", None)
            tokens_display = f"{tokens_in or '-'}/{tokens_out or '-'}"
            
            # Format status
            status_parts = []
            if entry.get("cached", False):
                status_parts.append("[green]Cached[/green]")
            if entry.get("fallback", False):
                status_parts.append("[yellow]Fallback[/yellow]")
            if entry.get("error"):
                status_parts.append("[red]Error[/red]")
            
            if not status_parts:
                status_parts.append("[green]Success[/green]")
            
            status_display = " ".join(status_parts)
            
            # Add row to table
            table.add_row(
                entry.get("id", "")[:8],
                formatted_timestamp,
                entry.get("model", ""),
                entry.get("provider", ""),
                entry.get("operation_type", ""),
                str(entry.get("latency_ms", "-")),
                tokens_display,
                status_display
            )
        
        # Display the table
        console.print(table)
        
        # Display detailed information if verbose
        if verbose:
            for i, entry in enumerate(entries):
                console.print(f"\n[bold]Entry {i+1}/{len(entries)}:[/bold]")
                console.print(f"[cyan]ID:[/cyan] {entry.get('id', '')}")
                console.print(f"[cyan]Timestamp:[/cyan] {entry.get('timestamp', '')}")
                console.print(f"[cyan]Model:[/cyan] {entry.get('model', '')}")
                console.print(f"[cyan]Provider:[/cyan] {entry.get('provider', '')}")
                console.print(f"[cyan]Operation Type:[/cyan] {entry.get('operation_type', '')}")
                console.print(f"[cyan]Latency (ms):[/cyan] {entry.get('latency_ms', '-')}")
                console.print(f"[cyan]Tokens In:[/cyan] {entry.get('tokens_in', '-')}")
                console.print(f"[cyan]Tokens Out:[/cyan] {entry.get('tokens_out', '-')}")
                console.print(f"[cyan]Cached:[/cyan] {entry.get('cached', False)}")
                console.print(f"[cyan]Fallback:[/cyan] {entry.get('fallback', False)}")
                console.print(f"[cyan]Error:[/cyan] {entry.get('error', '-')}")
                console.print(f"[cyan]Tags:[/cyan] {', '.join(entry.get('tags', []))}")
                
                if entry.get("parent_id"):
                    console.print(f"[cyan]Parent ID:[/cyan] {entry.get('parent_id', '')}")
    else:
        console.print("[yellow]No telemetry entries found[/yellow]")
    
    return True

@app.command("clear")
def run_clear(
    confirm: bool = typer.Option(False, "--confirm", help="Confirm clearing telemetry data"),
):
    """
    Clear telemetry data.
    
    This command clears all telemetry data from the telemetry file.
    You must confirm this action with the --confirm flag.
    
    Examples:
        codecrusher telemetry clear --confirm
    """
    if not confirm:
        console.print("[bold red]❌ Error:[/bold red] You must confirm clearing telemetry data with the --confirm flag")
        raise typer.Exit(code=1)
    
    # Check if telemetry file exists
    if not os.path.exists(TELEMETRY_FILE):
        console.print("[yellow]Telemetry file does not exist[/yellow]")
        return True
    
    # Clear telemetry file
    try:
        with open(TELEMETRY_FILE, "w", encoding="utf-8") as f:
            f.write("")
        
        console.print("[bold green]Telemetry data cleared successfully[/bold green]")
    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] Failed to clear telemetry data: {str(e)}")
        raise typer.Exit(code=1)
    
    return True
