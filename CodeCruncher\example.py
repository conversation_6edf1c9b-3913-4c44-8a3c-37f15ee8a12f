#!/usr/bin/env python3
"""
Employee Management System
A Python program demonstrating 5 employees and a manager with bonus functionality.
"""

from datetime import datetime
from typing import List, Dict


class Employee:
    """Represents an employee with basic information and salary."""

    def __init__(self, employee_id: int, name: str, department: str, base_salary: float, hire_date: str):
        self.employee_id = employee_id
        self.name = name
        self.department = department
        self.base_salary = base_salary
        self.hire_date = hire_date
        self.performance_rating = 3.0  # Default rating out of 5

    def set_performance_rating(self, rating: float):
        """Set employee performance rating (1-5 scale)."""
        if 1 <= rating <= 5:
            self.performance_rating = rating
        else:
            raise ValueError("Performance rating must be between 1 and 5")

    def get_annual_salary(self) -> float:
        """Calculate annual salary."""
        return self.base_salary * 12

    def __str__(self):
        return f"Employee({self.name}, {self.department}, ${self.base_salary:,.2f}/month)"

    def __repr__(self):
        return self.__str__()


class Manager:
    """Manages employees and calculates bonuses."""

    def __init__(self, name: str, department: str):
        self.name = name
        self.department = department
        self.employees: List[Employee] = []
        self.bonus_pool = 50000.0  # Annual bonus pool

    def add_employee(self, employee: Employee):
        """Add an employee to the manager's team."""
        self.employees.append(employee)
        print(f"✅ Added {employee.name} to {self.name}'s team")

    def calculate_bonus(self, employee: Employee) -> float:
        """Calculate bonus for an employee based on performance and salary."""
        # Bonus calculation factors:
        # - Base bonus: 5% of annual salary
        # - Performance multiplier: rating/3 (so 3.0 = 100%, 5.0 = 167%, 1.0 = 33%)
        # - Department bonus: Engineering gets 20% extra, Sales gets 15% extra

        base_bonus = employee.get_annual_salary() * 0.05
        performance_multiplier = employee.performance_rating / 3.0

        # Department bonus multipliers
        dept_multipliers = {
            "Engineering": 1.20,
            "Sales": 1.15,
            "Marketing": 1.10,
            "HR": 1.05,
            "Finance": 1.08
        }

        dept_multiplier = dept_multipliers.get(employee.department, 1.0)

        total_bonus = base_bonus * performance_multiplier * dept_multiplier

        return min(total_bonus, self.bonus_pool * 0.3)  # Cap at 30% of bonus pool

    def distribute_bonuses(self) -> Dict[str, float]:
        """Calculate and distribute bonuses to all employees."""
        bonuses = {}
        total_distributed = 0

        print(f"\n💰 {self.name} is calculating bonuses...")
        print("=" * 60)

        for employee in self.employees:
            bonus = self.calculate_bonus(employee)
            bonuses[employee.name] = bonus
            total_distributed += bonus

            print(f"👤 {employee.name:15} | "
                  f"Dept: {employee.department:12} | "
                  f"Rating: {employee.performance_rating:.1f}/5 | "
                  f"Bonus: ${bonus:8,.2f}")

        print("=" * 60)
        print(f"💵 Total bonuses distributed: ${total_distributed:,.2f}")
        print(f"💰 Remaining bonus pool: ${self.bonus_pool - total_distributed:,.2f}")

        return bonuses

    def get_team_summary(self):
        """Display team summary."""
        print(f"\n📋 Team Summary for Manager: {self.name}")
        print("=" * 50)
        print(f"Department: {self.department}")
        print(f"Team Size: {len(self.employees)} employees")
        print(f"Bonus Pool: ${self.bonus_pool:,.2f}")

        total_salaries = sum(emp.get_annual_salary() for emp in self.employees)
        avg_rating = sum(emp.performance_rating for emp in self.employees) / len(self.employees) if self.employees else 0

        print(f"Total Annual Salaries: ${total_salaries:,.2f}")
        print(f"Average Performance Rating: {avg_rating:.2f}/5")


def main():
    """Main function to demonstrate the employee management system."""
    print("🏢 Employee Management System Demo")
    print("=" * 50)

    # Create a manager
    manager = Manager("Sarah Johnson", "Technology")

    # Create 5 employees
    employees = [
        Employee(1, "Alice Chen", "Engineering", 8500, "2022-01-15"),
        Employee(2, "Bob Martinez", "Engineering", 7800, "2021-06-10"),
        Employee(3, "Carol Williams", "Sales", 6500, "2023-03-20"),
        Employee(4, "David Kim", "Marketing", 5900, "2022-11-05"),
        Employee(5, "Emma Thompson", "Engineering", 9200, "2020-08-12")
    ]

    # Set performance ratings
    performance_ratings = [4.2, 3.8, 4.5, 3.2, 4.8]

    # Add employees to manager's team and set their ratings
    for employee, rating in zip(employees, performance_ratings):
        manager.add_employee(employee)
        employee.set_performance_rating(rating)

    # Display team summary
    manager.get_team_summary()

    # Calculate and distribute bonuses
    bonuses = manager.distribute_bonuses()

    # Display individual employee details
    print(f"\n👥 Individual Employee Details:")
    print("=" * 60)
    for employee in employees:
        annual_salary = employee.get_annual_salary()
        bonus = bonuses[employee.name]
        total_compensation = annual_salary + bonus

        print(f"\n📝 {employee.name}")
        print(f"   Department: {employee.department}")
        print(f"   Monthly Salary: ${employee.base_salary:,.2f}")
        print(f"   Annual Salary: ${annual_salary:,.2f}")
        print(f"   Performance Rating: {employee.performance_rating}/5")
        print(f"   Annual Bonus: ${bonus:,.2f}")
        print(f"   Total Compensation: ${total_compensation:,.2f}")

    print(f"\n🎉 Bonus distribution complete!")


if __name__ == "__main__":
    main()
