"""
Parallel injection engine for CodeCrusher.

This module provides high-performance parallel processing of multiple files
with AI injection, progress tracking, caching, and comprehensive error handling.
"""

import asyncio
import hashlib
import json
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from rich.console import Console
from rich.progress import Progress, BarColumn, TextColumn, TimeElapsedColumn, SpinnerColumn
from rich.panel import Panel
from rich.table import Table

# Initialize rich console
console = Console()


class CacheAwareInjectionCache:
    """
    Thread-safe cache manager for parallel injection with per-file caching.
    """

    def __init__(self, cache_dir: Optional[Path] = None):
        """Initialize the cache manager."""
        self.cache_dir = cache_dir or Path.home() / ".codecrusher_cache"
        self.cache_dir.mkdir(exist_ok=True)
        self._lock = threading.Lock()

    def _generate_cache_key(self, file_path: Path, tag: str, prompt: str, model: str) -> str:
        """Generate cache key for (file_path, tag, prompt, model) combination."""
        # Include file modification time to invalidate cache when file changes
        try:
            mtime = file_path.stat().st_mtime
            file_content_hash = hashlib.md5(file_path.read_text(encoding='utf-8').encode()).hexdigest()[:8]
        except:
            mtime = 0
            file_content_hash = "unknown"

        key_data = f"{file_path.name}:{tag}:{prompt}:{model}:{mtime}:{file_content_hash}"
        return hashlib.sha256(key_data.encode()).hexdigest()

    def _get_cache_file_path(self, cache_key: str) -> Path:
        """Get the cache file path for a given cache key."""
        return self.cache_dir / f"{cache_key}.json"

    def check_cache(self, file_path: Path, tag: str, prompt: str, model: str) -> Optional[Dict[str, Any]]:
        """Check if a cached result exists for the given parameters."""
        with self._lock:
            try:
                cache_key = self._generate_cache_key(file_path, tag, prompt, model)
                cache_file = self._get_cache_file_path(cache_key)

                if cache_file.exists():
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        cached_data = json.load(f)

                    # Validate cache entry
                    if self._is_valid_cache_entry(cached_data):
                        return cached_data
                    else:
                        # Remove invalid cache entry
                        cache_file.unlink()

            except Exception as e:
                console.print(f"[yellow]Cache read error for {file_path.name}: {e}[/yellow]")

        return None

    def save_cache(self, file_path: Path, tag: str, prompt: str, model: str, result: Dict[str, Any]) -> None:
        """Save a successful injection result to cache."""
        with self._lock:
            try:
                cache_key = self._generate_cache_key(file_path, tag, prompt, model)
                cache_file = self._get_cache_file_path(cache_key)

                cache_entry = {
                    "file_path": str(file_path),
                    "tag": tag,
                    "prompt": prompt,
                    "model": model,
                    "result": result,
                    "timestamp": datetime.now().isoformat(),
                    "cache_version": "1.0"
                }

                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(cache_entry, f, indent=2)

            except Exception as e:
                console.print(f"[yellow]Cache save error for {file_path.name}: {e}[/yellow]")

    def _is_valid_cache_entry(self, cached_data: Dict[str, Any]) -> bool:
        """Validate that a cache entry has the required structure."""
        required_fields = ["file_path", "tag", "prompt", "model", "result", "timestamp"]
        return all(field in cached_data for field in required_fields)

    def clear_cache(self) -> None:
        """Clear all cache files."""
        with self._lock:
            try:
                for cache_file in self.cache_dir.glob("*.json"):
                    cache_file.unlink()
                console.print("[green]Cache cleared successfully[/green]")
            except Exception as e:
                console.print(f"[red]Error clearing cache: {e}[/red]")

    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics."""
        with self._lock:
            try:
                cache_files = list(self.cache_dir.glob("*.json"))
                total_size = sum(f.stat().st_size for f in cache_files)
                return {
                    "total_entries": len(cache_files),
                    "total_size_bytes": total_size,
                    "total_size_mb": round(total_size / (1024 * 1024), 2)
                }
            except Exception:
                return {"total_entries": 0, "total_size_bytes": 0, "total_size_mb": 0}


class ParallelInjectionEngine:
    """
    High-performance parallel injection engine with cache-aware processing,
    retry logic, and comprehensive error handling.
    """

    def __init__(self, max_workers: int = 4, use_cache: bool = True, max_retries: int = 1):
        """
        Initialize the parallel injection engine.

        Args:
            max_workers: Maximum number of concurrent threads
            use_cache: Whether to use caching for results
            max_retries: Maximum number of retries for failed injections
        """
        self.max_workers = max_workers
        self.use_cache = use_cache
        self.max_retries = max_retries
        self.cache = CacheAwareInjectionCache() if use_cache else None

        # Thread-safe result tracking
        self.results = {}
        self.errors = {}
        self.cached_results = {}
        self.retry_attempts = {}
        self._lock = threading.Lock()

        # Statistics tracking
        self.stats = {
            "total_files": 0,
            "injected": 0,
            "cached": 0,
            "failed": 0,
            "retries": 0
        }

    def process_files_parallel(
        self,
        file_paths: List[Path],
        prompt_text: str,
        use_ai: bool = True,
        model: str = "auto",
        auto_model_routing: bool = False,
        refresh_cache: bool = False,
        apply: bool = False,
        force: bool = False,
        preview: bool = False,
        tag: str = "default",
        inject_body: bool = False
    ) -> Dict[str, Any]:
        """
        Process multiple files in parallel with cache-aware AI injection and retry logic.

        Args:
            file_paths: List of file paths to process
            prompt_text: Prompt text for AI generation
            use_ai: Whether to use AI for code generation
            model: AI model to use
            auto_model_routing: Whether to use auto model routing
            refresh_cache: Whether to refresh cache
            apply: Whether to apply changes
            force: Whether to force apply without confirmation
            preview: Whether to preview changes
            tag: Injection tag to use
            inject_body: Whether to inject into body tag

        Returns:
            Dictionary containing processing results and statistics
        """
        total_files = len(file_paths)
        self.stats["total_files"] = total_files

        console.print(f"\n[bold blue]🔍 {total_files} files found with AI_INJECT tags[/bold blue]")
        console.print(f"[bold blue]☁️ Injecting with {'auto-routing' if auto_model_routing else model}...[/bold blue]")

        # Phase 1: Check cache for all files
        if self.use_cache and not refresh_cache:
            self._check_cache_for_files(file_paths, prompt_text, model)

        # Get files that need processing (not cached)
        files_to_process = [f for f in file_paths if f not in self.cached_results]

        if not files_to_process:
            console.print("[green]✅ All files found in cache, no processing needed![/green]")
            return self._generate_final_results()

        console.print(f"\n[bold blue]🚀 Starting parallel injection with {self.max_workers} threads...[/bold blue]")
        console.print(f"[cyan]Processing {len(files_to_process)} files (skipping {len(self.cached_results)} cached)[/cyan]")

        # Create progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TextColumn("({task.completed}/{task.total})"),
            TimeElapsedColumn(),
            console=console
        ) as progress:

            task = progress.add_task(
                f"🧠 Injecting {len(files_to_process)} files...",
                total=len(files_to_process)
            )

            # Process files using ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tasks
                future_to_file = {
                    executor.submit(
                        self._process_single_file_with_retry,
                        file_path,
                        prompt_text,
                        use_ai,
                        model,
                        auto_model_routing,
                        refresh_cache,
                        apply,
                        force,
                        preview,
                        tag,
                        inject_body
                    ): file_path
                    for file_path in files_to_process
                }

                # Process completed tasks
                completed_count = 0
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]
                    completed_count += 1

                    try:
                        result = future.result()

                        with self._lock:
                            if result.get("success"):
                                self.results[file_path] = result
                                self.stats["injected"] += 1

                                # Save to cache if successful
                                if self.use_cache and result.get("injected_content"):
                                    self._save_result_to_cache(file_path, prompt_text, model, result)

                                # Update progress
                                progress.console.print(
                                    f"[green]✔️ {file_path.name}[/green]"
                                )
                            else:
                                self.errors[file_path] = result.get("error", "Unknown error")
                                self.stats["failed"] += 1

                                # Check if this was a retry
                                if result.get("retry_attempted"):
                                    self.stats["retries"] += 1
                                    progress.console.print(
                                        f"[red]❌ {file_path.name} (error: {result.get('error', 'unknown')})[/red]"
                                    )
                                else:
                                    progress.console.print(
                                        f"[red]❌ {file_path.name} (error: timeout)[/red]"
                                    )

                    except Exception as e:
                        with self._lock:
                            self.errors[file_path] = str(e)
                            self.stats["failed"] += 1
                            progress.console.print(
                                f"[red]⚠️ {file_path.name} (error: exception)[/red]"
                            )

                    # Update progress
                    progress.update(task, advance=1)

        return self._generate_final_results()

    def _check_cache_for_files(self, file_paths: List[Path], prompt_text: str, model: str) -> None:
        """Check cache for all files and populate cached_results."""
        if not self.cache:
            return

        for file_path in file_paths:
            # For each file, check all its injection tags
            try:
                from codecrusher.file_discovery import detect_injection_tags
                tags = detect_injection_tags(file_path)

                file_fully_cached = True
                for tag in tags:
                    cached_result = self.cache.check_cache(file_path, tag, prompt_text, model)
                    if not cached_result:
                        file_fully_cached = False
                        break

                if file_fully_cached and tags:
                    self.cached_results[file_path] = {
                        "file": str(file_path),
                        "success": True,
                        "cached": True,
                        "tags": tags,
                        "timestamp": datetime.now().isoformat()
                    }
                    self.stats["cached"] += 1
                    console.print(f"[blue]✔️ {file_path.name} (cached)[/blue]")

            except Exception as e:
                # If we can't check cache, just process the file normally
                pass

    def _process_single_file_with_retry(
        self,
        file_path: Path,
        prompt_text: str,
        use_ai: bool,
        model: str,
        auto_model_routing: bool,
        refresh_cache: bool,
        apply: bool,
        force: bool,
        preview: bool,
        tag: str,
        inject_body: bool
    ) -> Dict[str, Any]:
        """Process a single file with retry logic."""

        # First attempt
        result = self._process_single_file_wrapper(
            file_path, prompt_text, use_ai, model, auto_model_routing,
            refresh_cache, apply, force, preview, tag, inject_body
        )

        # If first attempt failed and we have retries left
        if not result.get("success") and self.max_retries > 0:
            console.print(f"[yellow]✔️ {file_path.name} (error: retrying)[/yellow]")

            # Wait a bit before retry
            import time
            time.sleep(1)

            # Retry attempt
            retry_result = self._process_single_file_wrapper(
                file_path, prompt_text, use_ai, model, auto_model_routing,
                refresh_cache, apply, force, preview, tag, inject_body
            )

            if retry_result.get("success"):
                retry_result["retry_attempted"] = True
                console.print(f"[green]✔️ {file_path.name} ... success[/green]")
                return retry_result
            else:
                result["retry_attempted"] = True
                result["retry_error"] = retry_result.get("error", "Unknown retry error")

        return result

    def _save_result_to_cache(self, file_path: Path, prompt_text: str, model: str, result: Dict[str, Any]) -> None:
        """Save a successful result to cache."""
        if not self.cache:
            return

        try:
            # Get the tags that were processed
            tags = result.get("tags", [])
            for tag in tags:
                self.cache.save_cache(file_path, tag, prompt_text, model, result)
        except Exception as e:
            console.print(f"[yellow]Warning: Failed to save cache for {file_path.name}: {e}[/yellow]")

    def _generate_final_results(self) -> Dict[str, Any]:
        """Generate final results dictionary with comprehensive statistics."""
        total_files = self.stats["total_files"]
        injected = self.stats["injected"]
        cached = self.stats["cached"]
        failed = self.stats["failed"]

        # Print completion summary
        self._print_completion_summary()

        return {
            "success": (injected + cached) > 0,
            "total_files": total_files,
            "successful_files": injected,
            "failed_files": failed,
            "cached_files": cached,
            "total_injections": injected,
            "total_retries": self.stats["retries"],
            "results": list(self.results.values()),
            "cached_results": list(self.cached_results.values()),
            "errors": dict(self.errors),
            "timestamp": datetime.now().isoformat()
        }

    def _process_single_file_wrapper(
        self,
        file_path: Path,
        prompt_text: str,
        use_ai: bool,
        model: str,
        auto_model_routing: bool,
        refresh_cache: bool,
        apply: bool,
        force: bool,
        preview: bool,
        tag: str,
        inject_body: bool
    ) -> Dict[str, Any]:
        """
        Wrapper for processing a single file in a thread-safe manner.
        """
        try:
            # Import here to avoid circular imports
            from codecrusher.injector import _process_single_file

            # Read source file
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.readlines()

            # Process the file
            result = _process_single_file(
                file_path, source_code, prompt_text, use_ai, preview, model,
                auto_model_routing, refresh_cache, apply, force, tag, inject_body
            )

            return result

        except Exception as e:
            return {
                "file": str(file_path),
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _print_completion_summary(self):
        """Print a comprehensive completion summary with enhanced statistics."""

        total = self.stats["total_files"]
        injected = self.stats["injected"]
        cached = self.stats["cached"]
        failed = self.stats["failed"]
        retries = self.stats["retries"]

        # Print the summary line first
        console.print(f"\n[bold green]✅ Summary: {injected} injected, {cached} cached, {failed} failed[/bold green]")

        # Create detailed summary table
        summary_table = Table(show_header=False, box=None, padding=(0, 1))

        # Add statistics
        summary_table.add_row("[cyan]Total Files:[/cyan]", f"[bold]{total}[/bold]")
        summary_table.add_row("[green]Injected:[/green]", f"[bold green]{injected}[/bold green]")
        summary_table.add_row("[blue]Cached:[/blue]", f"[bold blue]{cached}[/bold blue]")

        if failed > 0:
            summary_table.add_row("[red]Failed:[/red]", f"[bold red]{failed}[/bold red]")

        if retries > 0:
            summary_table.add_row("[yellow]Retries:[/yellow]", f"[bold yellow]{retries}[/bold yellow]")

        # Calculate success rate
        success_rate = ((injected + cached) / total * 100) if total > 0 else 0
        summary_table.add_row("[cyan]Success Rate:[/cyan]", f"[bold]{success_rate:.1f}%[/bold]")

        # Print summary panel
        console.print(Panel(
            summary_table,
            title="[bold]🎉 Parallel Injection Complete[/bold]",
            border_style="green" if failed == 0 else "yellow"
        ))

        # Print detailed error information if any
        if self.errors:
            console.print("\n[bold red]❌ Failed Files:[/bold red]")
            for file_path, error in self.errors.items():
                console.print(f"  [red]•[/red] [yellow]{file_path.name}[/yellow]: {error}")

        # Print cache statistics if available
        if self.cache:
            cache_stats = self.cache.get_cache_stats()
            if cache_stats["total_entries"] > 0:
                console.print(f"\n[dim]💾 Cache: {cache_stats['total_entries']} entries, {cache_stats['total_size_mb']} MB[/dim]")


class AsyncParallelInjectionEngine:
    """
    Async version of the parallel injection engine for even better performance.
    """

    def __init__(self, max_workers: int = 4, use_cache: bool = True):
        self.max_workers = max_workers
        self.use_cache = use_cache
        self.results = {}
        self.errors = {}
        self.cached_results = {}

    async def process_files_async(
        self,
        file_paths: List[Path],
        prompt_text: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process files asynchronously with even better performance.

        This method uses asyncio for I/O operations and can be more efficient
        for large numbers of files.
        """
        total_files = len(file_paths)

        console.print(f"\n[bold blue]⚡ Starting async injection with {self.max_workers} concurrent tasks...[/bold blue]")

        # Create semaphore to limit concurrent operations
        semaphore = asyncio.Semaphore(self.max_workers)

        async def process_with_semaphore(file_path: Path):
            async with semaphore:
                return await self._process_file_async(file_path, prompt_text, **kwargs)

        # Process all files concurrently
        tasks = [process_with_semaphore(file_path) for file_path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        successful = 0
        failed = 0
        cached_count = 0

        for i, result in enumerate(results):
            file_path = file_paths[i]

            if isinstance(result, Exception):
                self.errors[file_path] = str(result)
                failed += 1
            elif result.get("success"):
                self.results[file_path] = result
                successful += 1
                if result.get("cached"):
                    cached_count += 1
            else:
                self.errors[file_path] = result.get("error", "Unknown error")
                failed += 1

        return {
            "success": successful > 0,
            "total_files": total_files,
            "successful_files": successful,
            "failed_files": failed,
            "cached_files": cached_count,
            "results": list(self.results.values()),
            "errors": dict(self.errors),
            "timestamp": datetime.now().isoformat()
        }

    async def _process_file_async(self, file_path: Path, prompt_text: str, **kwargs) -> Dict[str, Any]:
        """Process a single file asynchronously."""
        try:
            # Use aiofiles for async file I/O if available
            try:
                import aiofiles
                async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                    source_code = await f.readlines()
            except ImportError:
                # Fall back to sync file I/O
                with open(file_path, 'r', encoding='utf-8') as f:
                    source_code = f.readlines()

            # Process file (this would need to be made async in the future)
            from codecrusher.injector import _process_single_file
            result = _process_single_file(file_path, source_code, prompt_text, **kwargs)

            return result

        except Exception as e:
            return {
                "file": str(file_path),
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
