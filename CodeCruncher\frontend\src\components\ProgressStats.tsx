import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Clock, Target } from 'lucide-react';

interface ProgressStatsProps {
  progress: number;
  total: number;
  completed: number;
  failed?: number;
  isRunning?: boolean;
  className?: string;
}

export default function ProgressStats({ 
  progress, 
  total, 
  completed, 
  failed = 0,
  isRunning = false,
  className = ""
}: ProgressStatsProps) {
  const percent = total > 0 ? Math.round((completed / total) * 100) : 0;
  const successRate = total > 0 ? Math.round(((completed - failed) / total) * 100) : 0;
  const remaining = total - completed;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Injection Progress
          </span>
          <Badge variant={isRunning ? "default" : completed === total ? "default" : "secondary"}>
            {isRunning ? "Running" : completed === total ? "Complete" : "Ready"}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress: {completed} of {total} files</span>
            <span className="font-medium">{percent}%</span>
          </div>
          <Progress value={percent} className="h-3" />
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Target className="h-4 w-4 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{total}</div>
            <div className="text-xs text-gray-600">Total Files</div>
          </div>

          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">{completed - failed}</div>
            <div className="text-xs text-gray-600">Successful</div>
          </div>

          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <XCircle className="h-4 w-4 text-red-600" />
            </div>
            <div className="text-2xl font-bold text-red-600">{failed}</div>
            <div className="text-xs text-gray-600">Failed</div>
          </div>

          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Clock className="h-4 w-4 text-gray-600" />
            </div>
            <div className="text-2xl font-bold text-gray-600">{remaining}</div>
            <div className="text-xs text-gray-600">Remaining</div>
          </div>
        </div>

        {/* Success Rate */}
        {completed > 0 && (
          <div className="pt-2 border-t">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium">Success Rate:</span>
              <div className="flex items-center gap-2">
                <span className={`text-lg font-bold ${
                  successRate >= 90 ? 'text-green-600' : 
                  successRate >= 70 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {successRate}%
                </span>
                {successRate >= 90 ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : successRate >= 70 ? (
                  <Clock className="h-4 w-4 text-yellow-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
              </div>
            </div>
          </div>
        )}

        {/* Status Message */}
        <div className="text-center text-sm text-gray-600">
          {isRunning ? (
            <span className="flex items-center justify-center gap-2">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
              Processing files...
            </span>
          ) : completed === total ? (
            <span className="text-green-600 font-medium">
              ✅ All files processed!
            </span>
          ) : completed > 0 ? (
            <span>
              Injection paused - {remaining} files remaining
            </span>
          ) : (
            <span>
              Ready to start injection
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
