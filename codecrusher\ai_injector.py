#!/usr/bin/env python3
"""
AI Injector Module
Handles AI-powered code injection into source files
"""

import os
import re
import difflib
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from rich.console import Console
from rich.syntax import Syntax

console = Console()

class AIInjector:
    """Main AI injection class that processes files and generates code."""
    
    def __init__(self, model: str = "auto", verbose: bool = False):
        self.model = model
        self.verbose = verbose
        self.supported_extensions = [
            'py', 'js', 'ts', 'jsx', 'tsx', 'java', 'cpp', 'c', 'cs', 
            'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala', 'html', 
            'css', 'scss', 'sass', 'less', 'vue', 'svelte', 'md'
        ]
    
    def process_files(
        self, 
        source_path: str, 
        prompt: str, 
        recursive: bool = False,
        extensions: Optional[List[str]] = None,
        preview_mode: bool = True
    ) -> Dict:
        """
        Process files and inject AI-generated code.
        
        Args:
            source_path: Path to file or directory
            prompt: Description of what to inject
            recursive: Whether to scan subdirectories
            extensions: List of file extensions to process
            preview_mode: If True, show changes without applying
            
        Returns:
            Dict with processing results
        """
        try:
            # Find files to process
            files_to_process = self._find_files(source_path, recursive, extensions)
            
            if not files_to_process:
                return {
                    'success': False,
                    'error': f'No suitable files found in {source_path}',
                    'files_processed': 0,
                    'changes_made': 0
                }
            
            # Process each file
            total_changes = 0
            processed_files = 0
            preview_diffs = []
            
            for file_path in files_to_process:
                if self.verbose:
                    console.print(f"[dim]Processing: {file_path}[/dim]")
                
                result = self._process_single_file(file_path, prompt, preview_mode)
                
                if result['success']:
                    processed_files += 1
                    total_changes += result['changes_made']
                    
                    if preview_mode and result.get('diff'):
                        preview_diffs.append(f"\n[bold]File: {file_path}[/bold]")
                        preview_diffs.append(result['diff'])
            
            return {
                'success': True,
                'files_processed': processed_files,
                'changes_made': total_changes,
                'preview_diff': '\n'.join(preview_diffs) if preview_diffs else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'files_processed': 0,
                'changes_made': 0
            }
    
    def _find_files(
        self, 
        source_path: str, 
        recursive: bool, 
        extensions: Optional[List[str]]
    ) -> List[str]:
        """Find files to process based on criteria."""
        
        path = Path(source_path)
        files = []
        
        # Use provided extensions or defaults
        target_extensions = extensions or ['py']
        
        if path.is_file():
            # Single file
            if any(path.suffix.lstrip('.') == ext for ext in target_extensions):
                files.append(str(path))
        elif path.is_dir():
            # Directory
            pattern = "**/*" if recursive else "*"
            
            for ext in target_extensions:
                files.extend([
                    str(f) for f in path.glob(f"{pattern}.{ext}")
                    if f.is_file()
                ])
        
        return sorted(files)
    
    def _process_single_file(
        self, 
        file_path: str, 
        prompt: str, 
        preview_mode: bool
    ) -> Dict:
        """Process a single file for AI injection."""
        
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Find AI_INJECT tags
            injection_tags = self._find_injection_tags(original_content)
            
            if not injection_tags:
                if self.verbose:
                    console.print(f"[dim]No AI_INJECT tags found in {file_path}[/dim]")
                return {
                    'success': True,
                    'changes_made': 0,
                    'diff': None
                }
            
            # Generate new content with AI injections
            new_content = self._inject_ai_code(original_content, injection_tags, prompt, file_path)
            
            # Create diff
            diff = self._create_diff(original_content, new_content, file_path)
            
            # Apply changes if not in preview mode
            if not preview_mode and new_content != original_content:
                # Create backup
                backup_path = f"{file_path}.backup"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # Write new content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                if self.verbose:
                    console.print(f"[green]Applied changes to {file_path}[/green]")
                    console.print(f"[dim]Backup saved to {backup_path}[/dim]")
            
            return {
                'success': True,
                'changes_made': len(injection_tags) if new_content != original_content else 0,
                'diff': diff if new_content != original_content else None
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'changes_made': 0,
                'diff': None
            }
    
    def _find_injection_tags(self, content: str) -> List[Dict]:
        """Find AI_INJECT tags in file content."""
        
        # Pattern to match AI_INJECT tags
        pattern = r'#\s*AI_INJECT:(\w+)\s*\n(.*?)\n\s*#\s*AI_INJECT:\1:end'
        
        tags = []
        for match in re.finditer(pattern, content, re.DOTALL):
            tag_name = match.group(1)
            tag_content = match.group(2).strip()
            
            tags.append({
                'name': tag_name,
                'content': tag_content,
                'start': match.start(),
                'end': match.end(),
                'full_match': match.group(0)
            })
        
        return tags
    
    def _inject_ai_code(
        self, 
        content: str, 
        injection_tags: List[Dict], 
        prompt: str, 
        file_path: str
    ) -> str:
        """Generate AI code and inject it into the content."""
        
        new_content = content
        
        # Process tags in reverse order to maintain string positions
        for tag in reversed(injection_tags):
            # Generate AI code for this tag
            ai_code = self._generate_ai_code(tag, prompt, file_path)
            
            # Replace the tag with the generated code
            new_tag_content = f"# AI_INJECT:{tag['name']}\n{ai_code}\n# AI_INJECT:{tag['name']}:end"
            new_content = new_content[:tag['start']] + new_tag_content + new_content[tag['end']:]
        
        return new_content
    
    def _generate_ai_code(self, tag: Dict, prompt: str, file_path: str) -> str:
        """Generate AI code for a specific tag."""
        
        # This is where we would call actual AI APIs
        # For now, we'll generate realistic example code
        
        tag_name = tag['name']
        file_ext = Path(file_path).suffix.lstrip('.')
        
        # Generate contextual code based on tag name and prompt
        if 'multiply' in tag_name.lower():
            if file_ext == 'py':
                return '''    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        try:
            result = a * b
            self.history.append(f"{a} * {b} = {result}")
            return result
        except Exception as e:
            print(f"Error in multiplication: {e}")
            return 0.0'''
        
        elif 'divide' in tag_name.lower():
            if file_ext == 'py':
                return '''    def divide(self, a: float, b: float) -> float:
        """Divide two numbers with error handling."""
        try:
            if b == 0:
                raise ValueError("Cannot divide by zero")
            result = a / b
            self.history.append(f"{a} / {b} = {result}")
            return result
        except Exception as e:
            print(f"Error in division: {e}")
            return 0.0'''
        
        elif 'logging' in prompt.lower() or 'log' in tag_name.lower():
            if file_ext == 'py':
                return '''    import logging
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    def log_operation(self, operation: str, result: any):
        """Log operation results."""
        logger.info(f"Operation: {operation}, Result: {result}")'''
        
        else:
            # Generic code generation based on prompt
            if file_ext == 'py':
                return f'''    # Generated code for: {prompt}
    # Tag: {tag_name}
    def {tag_name}_function(self):
        """Auto-generated function based on prompt: {prompt}"""
        # TODO: Implement {tag_name} functionality
        pass'''
        
        return f"// Generated code for: {prompt}\n// Tag: {tag_name}\n// TODO: Implement {tag_name} functionality"
    
    def _create_diff(self, original: str, new: str, file_path: str) -> str:
        """Create a diff showing changes."""
        
        if original == new:
            return None
        
        diff_lines = list(difflib.unified_diff(
            original.splitlines(keepends=True),
            new.splitlines(keepends=True),
            fromfile=f"{file_path} (original)",
            tofile=f"{file_path} (modified)",
            lineterm=""
        ))
        
        return ''.join(diff_lines)
