Metadata-Version: 2.4
Name: codecrusher
Version: 0.1.0
Summary: CodeCrusher CLI – AugmentKiller with smart injection, fallback, feedback, and dashboard integration
Home-page: https://github.com/Codegx-Technology/CodeCruncher
Author: CodeCrusher Team
Author-email: Your Name <<EMAIL>>
Project-URL: Homepage, https://github.com/Codegx-Technology/CodeCruncher
Project-URL: Bug Tracker, https://github.com/Codegx-Technology/CodeCruncher/issues
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: click>=8.0.0
Requires-Dist: websockets>=10.0
Requires-Dist: requests>=2.28.0
Requires-Dist: rich>=12.0.0
Requires-Dist: halo>=0.0.31
Requires-Dist: colorama>=0.4.4
Requires-Dist: python-dotenv>=0.20.0
Requires-Dist: aiohttp>=3.8.0
Requires-Dist: typer[all]>=0.9.0
Dynamic: author
Dynamic: home-page
Dynamic: license-file
Dynamic: requires-python

# 🚀 CodeCrusher

<div align="center">

**Enterprise-grade AI-powered code injector with self-improving intelligence**

*Built to outperform any dev tool — flexible, fallback-ready, lightning-fast.*

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![CodeCrusher vs Augment](https://img.shields.io/badge/vs%20Augment-🏆%203%20Wins%20🤝%201%20Draw-green)](./e2e-comparison/)

</div>

---

## 🎯 What is CodeCrusher?

CodeCrusher is an **intelligent AI code injection system** that learns from your feedback and automatically improves code quality over time. Unlike static AI tools, CodeCrusher adapts its responses based on user ratings, creating increasingly better, more maintainable code.

### 🏆 **Proven Superior to Augment**
- **75% Win Rate** in side-by-side comparisons
- **92.5/100** average quality score vs Augment's 77.5/100
- **Self-improving intelligence** that adapts from user feedback

---

## 🧠 Key Features

### 🤖 **Intelligent AI Injection**
- **Multi-model support**: Groq, Mistral, OpenAI with automatic fallback
- **Smart model escalation**: Automatically upgrades to better models for complex tasks
- **Context-aware prompting**: Understands your codebase and coding style

### 🔄 **Self-Improving Intelligence Loop**
- **Feedback learning**: Rate injections to improve future responses
- **Adaptive parameters**: Automatically adjusts prompt style, verbosity, and error handling
- **Quality scoring**: Built-in quality assessment and improvement tracking

### 🛠️ **Professional CLI Interface**
- **Interactive preview mode**: See changes before applying
- **Rich console output**: Beautiful tables, panels, and progress indicators
- **Recursive processing**: Handle entire directories with `--recursive`
- **Comprehensive logging**: Track all injections and improvements

### 📊 **Live Web Dashboard** *(Coming Soon)*
- **Real-time injection monitoring**: Watch AI work in real-time
- **Model performance analytics**: Track success rates across different models
- **Team collaboration**: Share intelligence improvements across teams
- **Visual diff viewer**: Compare before/after code changes

### 🧪 **Enterprise-Grade Testing**
- **E2E intelligence testing**: Validate learning improvements
- **Side-by-side comparisons**: Benchmark against other AI tools
- **Quality metrics**: Comprehensive scoring and analysis
- **Automated regression testing**: Ensure consistent quality improvements

---

## 🚀 Quick Start

### Installation

```bash
git clone https://github.com/your-org/codecrusher.git
cd codecrusher
pip install -e .
```

### CLI Usage

```bash
# Inject code with AI
codecrusher inject ./src --recursive --preview --prompt "Add error handling"

# Rate injection quality to improve future responses
codecrusher rate ./src --recursive --rating 4 --comment "Good but needs more logging"

# Trigger intelligence learning
codecrusher learn --apply

# Check system status
codecrusher status
```

### Web Dashboard Usage

```bash
# 1. Start the backend API
python app/backend_main.py

# 2. Start the frontend (in a new terminal)
cd frontend
npm install
npm run dev

# 3. Open http://localhost:3000 in your browser
```

---

## 🛠️ CLI Commands

### 🎯 **Core Injection**
```bash
# Basic injection with preview
codecrusher inject ./src/main.py --prompt "Add input validation" --preview

# Recursive directory processing
codecrusher inject ./src --recursive --prompt "Add comprehensive error handling"

# Apply changes directly (skip preview)
codecrusher inject ./src/utils.py --prompt "Add logging" --apply
```

### ⭐ **Intelligence Learning**
```bash
# Rate injection results (1-5 scale)
codecrusher rate ./src --recursive --rating 3 --comment "Needs more specificity"

# Trigger learning from feedback
codecrusher learn --apply --verbose

# View learning analytics
codecrusher learn --dry-run
```

### 🔧 **System Management**
```bash
# Check system status and configuration
codecrusher status

# View version information
codecrusher version

# Authentication setup
codecrusher auth --setup
```

---

## 🧠 Model Routing & Fallback

| Tier | Model | Provider | Use Case | Fallback |
|------|-------|----------|----------|----------|
| 1 | LLaMA 3 8B | Groq | Fast, simple tasks | ✅ Auto |
| 2 | Mixtral 8x7B | Groq | Complex reasoning | ✅ Auto |
| 3 | LLaMA 3 70B | Groq | Deep analysis | ✅ Auto |
| 4 | GPT-4 Turbo | OpenAI | Premium quality | ✅ Manual |

### 🔄 **Intelligent Escalation**
- **Quality scoring**: Automatically detects low-quality responses
- **Model escalation**: Upgrades to better models when needed
- **Fallback strategies**: Multiple backup options for reliability
- **Cache optimization**: Stores high-quality responses for reuse

---

## 🏷️ Injection Tags

CodeCrusher uses simple comment tags to identify injection points:

```python
# AI_INJECT:function_name
# Your AI-generated code will appear here
# AI_INJECT:function_name:end
```

### Supported Languages
- **Python**: `# AI_INJECT:tag_name`
- **JavaScript/TypeScript**: `// AI_INJECT:tag_name`
- **Java/C++**: `// AI_INJECT:tag_name`
- **HTML**: `<!-- AI_INJECT:tag_name -->`

---

## 📊 Intelligence Learning System

### 🧠 **How It Works**
1. **Inject**: AI generates code based on your prompt
2. **Rate**: Provide feedback (1-5 stars) on code quality
3. **Learn**: System analyzes patterns and updates parameters
4. **Improve**: Future injections use enhanced configuration

### 📈 **Learning Parameters**
- **Prompt Style**: `detailed` → `comprehensive` → `extensive`
- **Verbosity**: `low` → `medium` → `high`
- **Error Handling**: `basic` → `comprehensive` → `extensive`
- **Fallback Sensitivity**: `0.9` → `0.7` → `0.5` (lower = more aggressive)

### 🎯 **Quality Metrics**
- **Improvement Indicators**: Logging, error handling, validation
- **Regression Detection**: Generic patterns, TODOs, placeholders
- **Score Calculation**: Comprehensive 100-point quality system
- **Learning Effectiveness**: Tracks improvement over time

---

## 🧪 Testing & Validation

### 🔬 **E2E Intelligence Testing**
```bash
# Run full intelligence loop test
python e2e_intelligence_test.py

# Analyze diff improvements
python simple_diff_analyzer.py

# Side-by-side comparison with other tools
python phase15_3_comparison.py
```

### 📊 **Test Results**
- **Intelligence Learning**: ✅ 4 parameters successfully updated
- **Quality Improvement**: ✅ 18 improvement indicators detected
- **Before/After Analysis**: ✅ 3-line comments → 30+ line implementations
- **Comparison vs Augment**: 🏆 **75% win rate, 92.5/100 avg score**

### 🎯 **Comparison Results**
```
CodeCrusher vs Augment: 🏆 3 Wins 🤝 1 Draw ❌ 0 Losses
Quality Score: 92.5/100 vs 77.5/100 (+15 points)
Key Advantage: Self-improving intelligence + comprehensive error handling
```

---

## 🏗️ Architecture

CodeCrusher consists of three main components:

### 🖥️ **CLI Tool (Python)**
- **Technology**: Python with Click framework
- **Purpose**: Command-line interface for code injection
- **Usage**: `codecrusher inject ./src --prompt "Add error handling"`

### 🔧 **Backend API (Python)**
- **Technology**: FastAPI + WebSocket
- **Purpose**: Web API and real-time communication
- **Port**: 8001
- **Usage**: `python app/backend_main.py`

### 🌐 **Frontend Dashboard (React)**
- **Technology**: React + TypeScript + Vite
- **Purpose**: Web-based user interface
- **Port**: 3000 (Vite configured for both dev and production)
- **Usage**: `cd frontend && npm run dev`

---

## 📦 Project Structure

```
codecrusher/
├── codecrusher_cli.py          # Main CLI interface
├── codecrusher/
│   ├── ai_injector.py          # Core injection logic
│   ├── intelligence_system.py  # Learning and feedback system
│   └── model_router.py         # Multi-model routing
├── app/
│   └── backend_main.py         # FastAPI backend server
├── frontend/                   # React/TypeScript dashboard
│   ├── src/                    # React source code
│   ├── package.json            # Node.js dependencies
│   └── vite.config.ts          # Vite configuration
├── e2e-comparison/             # Comparison reports and analysis
│   ├── side-by-side.md         # Detailed comparison results
│   ├── README.md               # Comparison documentation
│   └── comparison_tools/       # Analysis scripts
├── e2e-logs/                   # Intelligence learning logs
└── test-cases/                 # Test files for validation
```

---

## 🤝 Contributing

We welcome contributions! Here's how to get started:

### 🔧 **Development Setup**
```bash
git clone https://github.com/your-org/codecrusher.git
cd codecrusher
pip install -e .
pip install -r requirements-dev.txt
```

### 🎯 **Contribution Areas**
- **Model Plugins**: Add support for new AI providers
- **Language Support**: Extend injection tag support
- **Intelligence Improvements**: Enhance learning algorithms
- **Dashboard Features**: Build web interface components
- **Testing**: Add comprehensive test coverage

### 📝 **Code Style**
- **Python**: Follow PEP 8 with Black formatting
- **Documentation**: Comprehensive docstrings and comments
- **Testing**: Include tests for new features
- **CLI**: Use Click framework for new commands

---

## 📜 License

MIT License - see [LICENSE](LICENSE) file for details.

---

## 🎉 Why Choose CodeCrusher?

### 🏆 **vs Traditional AI Tools**
- **Self-Improving**: Learns from your feedback, gets better over time
- **Comprehensive**: Production-ready code with error handling and logging
- **Reliable**: Multi-model fallback ensures consistent results
- **Professional**: Enterprise-grade CLI and reporting

### 🚀 **vs Manual Coding**
- **Faster**: AI-powered code generation with intelligent prompting
- **Consistent**: Standardized patterns and best practices
- **Quality**: Built-in validation and improvement tracking
- **Scalable**: Handle entire codebases with recursive processing

### 🧠 **vs Static AI**
- **Adaptive**: Intelligence that improves based on usage
- **Context-Aware**: Understands your specific coding patterns
- **Quality-Focused**: Prioritizes maintainable, robust code
- **Feedback-Driven**: Real user input drives continuous improvement

---

<div align="center">

**Ready to crush your code with intelligent AI?**

[Get Started](#-quick-start) • [View Comparisons](./e2e-comparison/) • [Contribute](#-contributing)

*Built with ❤️ by Codegx Technologies*

</div>
