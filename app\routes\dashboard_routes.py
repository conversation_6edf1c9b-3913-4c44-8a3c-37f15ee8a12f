"""
Dashboard API Router for CodeCrusher Intelligence Hub
Step 13.5: Sync Dashboard Backend with intel_api.py

This module provides API endpoints for the Intelligence Hub dashboard,
integrating with the shared intelligence layer for real-time analytics.

Features:
- Real-time feedback entries from intel_api.py
- Model performance analytics
- Tone effectiveness analysis
- System status monitoring
- WebSocket broadcasting for live updates
"""

import json
import os
import sys
import logging
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field

# Add the parent directory to the path to import codecrusher modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

logger = logging.getLogger(__name__)

# Try to import the real intel_api from the root directory
try:
    # Import from the root directory where intel_api.py is located
    import intel_api
    INTEL_API_AVAILABLE = True
    logger.info("✅ Successfully imported intel_api")
except ImportError as e:
    logger.warning(f"⚠️ Failed to import intel_api: {e}")
    INTEL_API_AVAILABLE = False

router = APIRouter()

# WebSocket clients for real-time updates
dashboard_clients = set()

# Data models for dashboard API
class IntelligenceData(BaseModel):
    """Intelligence data model for dashboard."""
    total_entries: int
    models: List[str]
    files_processed: int
    average_rating: float
    average_score: float
    model_performance: Dict[str, Any]
    tone_effectiveness: Dict[str, float]
    best_model: Optional[str]
    recent_activity: int

class SystemStatus(BaseModel):
    """System status model."""
    intel_directory: str
    intel_dir_exists: bool
    files: Dict[str, Dict[str, Any]]

class FeedbackEntry(BaseModel):
    """Feedback entry model."""
    file: str
    timestamp: str
    prompt: str
    model: str
    output_score: int
    rating: int
    tone: str
    fallback_used: bool

def get_intel_api_instance():
    """Get the Intel API instance with safe import."""
    if INTEL_API_AVAILABLE:
        try:
            return intel_api.get_intel_api()
        except Exception as e:
            logger.error(f"Failed to get intel_api instance: {e}")
            return None
    return None

def load_feedback_entries_safe():
    """Load feedback entries with safe fallback."""
    if INTEL_API_AVAILABLE:
        try:
            return intel_api.load_feedback_entries()
        except Exception as e:
            logger.error(f"Failed to load feedback entries: {e}")
            return []
    else:
        logger.warning("intel_api not available, returning empty list")
        return []

def get_intelligence_summary_safe():
    """Get intelligence summary with safe fallback."""
    if INTEL_API_AVAILABLE:
        try:
            return intel_api.get_intelligence_summary()
        except Exception as e:
            logger.error(f"Failed to get intelligence summary: {e}")
            return get_mock_intelligence_data()
    else:
        logger.warning("intel_api not available, returning mock data")
        return get_mock_intelligence_data()

def get_mock_intelligence_data():
    """Get mock intelligence data for fallback."""
    return {
        "total_entries": 9,
        "models": ['GPT-4', 'TestAPI', 'TestModel', 'Mixtral'],
        "files_processed": 6,
        "average_rating": 4.0,
        "average_score": 85.2,
        "model_performance": {
            'GPT-4': {'total_entries': 2, 'average_score': 95, 'average_rating': 5.0, 'fallback_usage': 1},
            'TestAPI': {'total_entries': 1, 'average_score': 95, 'average_rating': 5.0, 'fallback_usage': 0},
            'TestModel': {'total_entries': 1, 'average_score': 88, 'average_rating': 4.0, 'fallback_usage': 0},
            'Mixtral': {'total_entries': 5, 'average_score': 78, 'average_rating': 3.4, 'fallback_usage': 2}
        },
        "tone_effectiveness": {
            'assertive': 5.0,
            'neutral': 4.5,
            'formal': 4.5,
            'friendly': 3.2
        },
        "best_model": 'GPT-4',
        "recent_activity": 3
    }

@router.websocket("/ws/intelligence")
async def websocket_intelligence(websocket: WebSocket):
    """WebSocket endpoint for real-time intelligence updates."""
    await websocket.accept()
    dashboard_clients.add(websocket)

    try:
        # Send welcome message with current data
        intelligence_data = get_intelligence_summary_safe()
        await websocket.send_text(json.dumps({
            "type": "welcome",
            "message": "Connected to Intelligence Hub",
            "data": intelligence_data
        }))

        logger.info(f"Intelligence WebSocket client connected. Total clients: {len(dashboard_clients)}")

        # Keep connection alive and send periodic updates
        while True:
            await asyncio.sleep(30)  # Send updates every 30 seconds

            # Get fresh intelligence data
            fresh_data = get_intelligence_summary_safe()
            await websocket.send_text(json.dumps({
                "type": "update",
                "data": fresh_data,
                "timestamp": datetime.now().isoformat()
            }))

    except WebSocketDisconnect:
        logger.info("Intelligence WebSocket client disconnected")
    except Exception as e:
        logger.error(f"Intelligence WebSocket error: {e}")
    finally:
        dashboard_clients.discard(websocket)
        logger.info(f"Intelligence client removed. Total clients: {len(dashboard_clients)}")

async def broadcast_intelligence_update(data: Dict[str, Any]):
    """Broadcast intelligence update to all connected dashboard clients."""
    if not dashboard_clients:
        return

    message = json.dumps({
        "type": "intelligence_update",
        "data": data,
        "timestamp": datetime.now().isoformat()
    })

    disconnected = set()
    for client in dashboard_clients:
        try:
            await client.send_text(message)
        except Exception as e:
            logger.warning(f"Failed to send to intelligence client: {e}")
            disconnected.add(client)

    # Remove disconnected clients
    dashboard_clients.difference_update(disconnected)

@router.get("/api/intel/feedback")
async def get_feedback():
    """
    Get all feedback entries from the shared intelligence layer.

    Returns:
        List of feedback entries with structured data
    """
    try:
        entries = load_feedback_entries_safe()

        # Convert to list of dictionaries if needed
        if entries and hasattr(entries[0], '__dict__'):
            entries = [entry.__dict__ for entry in entries]

        return {
            "feedback_entries": entries,
            "count": len(entries),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get feedback entries: {e}")
        raise HTTPException(status_code=500, detail="Failed to load feedback entries")

@router.get("/api/intel/summary")
async def get_intelligence_summary():
    """
    Get comprehensive intelligence summary for the dashboard.

    Returns:
        Intelligence summary with analytics and performance data
    """
    try:
        summary = get_intelligence_summary_safe()

        return {
            "intelligence_data": summary,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Failed to get intelligence summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to load intelligence summary")

@router.get("/api/intel/models")
async def get_model_performance():
    """
    Get detailed model performance analytics.

    Returns:
        Model performance data with ratings and statistics
    """
    try:
        api = get_intel_api_instance()
        if not api:
            return {"model_performance": {}, "timestamp": datetime.now().isoformat()}

        performance = api.get_model_performance_summary()

        return {
            "model_performance": performance,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Failed to get model performance: {e}")
        raise HTTPException(status_code=500, detail="Failed to load model performance")

@router.get("/api/intel/tones")
async def get_tone_effectiveness():
    """
    Get tone effectiveness analysis.

    Returns:
        Tone effectiveness data with ratings
    """
    try:
        api = get_intel_api_instance()
        if not api:
            return {"tone_effectiveness": {}, "timestamp": datetime.now().isoformat()}

        effectiveness = api.get_tone_effectiveness()

        return {
            "tone_effectiveness": effectiveness,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Failed to get tone effectiveness: {e}")
        raise HTTPException(status_code=500, detail="Failed to load tone effectiveness")

@router.get("/api/intel/status")
async def get_system_status():
    """
    Get intelligence system status.

    Returns:
        System status including file information and health
    """
    try:
        # Get basic system status
        status = {
            "intel_dir": "~/.codecrusher/intel",
            "intel_dir_exists": INTEL_API_AVAILABLE,
            "api_available": INTEL_API_AVAILABLE,
            "files": {}
        }

        if INTEL_API_AVAILABLE:
            try:
                # Get additional status from intel_api if available
                api = get_intel_api_instance()
                if api:
                    entries = api.load_feedback_entries()
                    status["total_entries"] = len(entries)
                    status["last_updated"] = datetime.now().isoformat()
            except Exception as e:
                logger.warning(f"Failed to get detailed status: {e}")

        return {
            "system_status": status,
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        raise HTTPException(status_code=500, detail="Failed to load system status")

@router.get("/api/intel/recent")
async def get_recent_activity(limit: int = 10):
    """
    Get recent intelligence activity.

    Args:
        limit: Maximum number of recent entries to return

    Returns:
        Recent activity data
    """
    try:
        api = get_intel_api_instance()
        if not api:
            return {"recent_activity": [], "count": 0, "timestamp": datetime.now().isoformat()}

        recent = api.get_recent_feedback(limit)

        return {
            "recent_activity": recent,
            "count": len(recent),
            "timestamp": datetime.now().isoformat(),
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Failed to get recent activity: {e}")
        raise HTTPException(status_code=500, detail="Failed to load recent activity")

@router.post("/api/intel/feedback/new")
async def notify_new_feedback(entry: Dict[str, Any]):
    """
    Notify dashboard of new feedback entry for real-time updates.

    This endpoint should be called when new feedback is added to trigger
    WebSocket broadcasts to connected dashboard clients.

    Args:
        entry: New feedback entry data

    Returns:
        Success confirmation
    """
    try:
        # Broadcast to connected dashboard clients
        await broadcast_intelligence_update({
            "type": "new_feedback",
            "entry": entry
        })

        logger.info(f"Broadcasted new feedback entry to {len(dashboard_clients)} clients")

        return {
            "status": "success",
            "message": "New feedback broadcasted",
            "clients_notified": len(dashboard_clients),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to broadcast new feedback: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast new feedback")

# ===== PROMPT SHAPING ENDPOINTS =====

@router.get("/api/intel/shaping")
async def get_prompt_shaping():
    """
    Get current prompt shaping configuration.

    Returns:
        Current prompt shaping weights and recommendations
    """
    try:
        if INTEL_API_AVAILABLE:
            api = get_intel_api_instance()
            if api:
                shaping_data = api.get_shaping_summary()
                return {
                    "shaping_data": shaping_data,
                    "timestamp": datetime.now().isoformat(),
                    "status": "success"
                }

        # Fallback data
        return {
            "shaping_data": {
                "current_weights": {
                    "tone": "default",
                    "fallback_level": 1,
                    "model_hint": "mixtral",
                    "shaping_strength": 0.5,
                    "fallback_sensitivity": 0.7
                },
                "usage_statistics": {},
                "recommendations": ["Intel API not available"]
            },
            "timestamp": datetime.now().isoformat(),
            "status": "fallback"
        }

    except Exception as e:
        logger.error(f"Failed to get prompt shaping: {e}")
        raise HTTPException(status_code=500, detail="Failed to load prompt shaping data")

@router.post("/api/intel/shaping")
async def update_prompt_shaping(updates: Dict[str, Any]):
    """
    Update prompt shaping configuration.

    Args:
        updates: Partial updates to apply to shaping weights

    Returns:
        Success confirmation and updated weights
    """
    try:
        if not INTEL_API_AVAILABLE:
            raise HTTPException(status_code=503, detail="Intel API not available")

        api = get_intel_api_instance()
        if not api:
            raise HTTPException(status_code=503, detail="Failed to get Intel API instance")

        # Update the weights
        success = api.update_prompt_weights(updates)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to save prompt weights")

        # Get updated weights
        updated_weights = api.load_prompt_weights()

        # Broadcast update to connected clients
        await broadcast_intelligence_update({
            "type": "shaping_update",
            "weights": updated_weights,
            "updates": updates
        })

        logger.info(f"Updated prompt shaping weights: {updates}")

        return {
            "status": "success",
            "message": "Prompt shaping updated",
            "updated_weights": updated_weights,
            "applied_updates": updates,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update prompt shaping: {e}")
        raise HTTPException(status_code=500, detail="Failed to update prompt shaping")

@router.get("/api/intel/shaping/weights")
async def get_prompt_weights():
    """
    Get current prompt weights only (lightweight endpoint).

    Returns:
        Current prompt shaping weights
    """
    try:
        if INTEL_API_AVAILABLE:
            api = get_intel_api_instance()
            if api:
                weights = api.load_prompt_weights()
                return {
                    "weights": weights,
                    "timestamp": datetime.now().isoformat(),
                    "status": "success"
                }

        # Fallback weights
        return {
            "weights": {
                "tone": "default",
                "fallback_level": 1,
                "model_hint": "mixtral",
                "shaping_strength": 0.5,
                "fallback_sensitivity": 0.7
            },
            "timestamp": datetime.now().isoformat(),
            "status": "fallback"
        }

    except Exception as e:
        logger.error(f"Failed to get prompt weights: {e}")
        raise HTTPException(status_code=500, detail="Failed to load prompt weights")

@router.post("/api/intel/shaping/tune")
async def tune_prompt_shaping(tuning_data: Dict[str, Any]):
    """
    Advanced tuning endpoint for prompt shaping.

    Args:
        tuning_data: Advanced tuning parameters

    Returns:
        Tuning results and recommendations
    """
    try:
        if not INTEL_API_AVAILABLE:
            raise HTTPException(status_code=503, detail="Intel API not available")

        api = get_intel_api_instance()
        if not api:
            raise HTTPException(status_code=503, detail="Failed to get Intel API instance")

        # Apply tuning
        current_weights = api.load_prompt_weights()

        # Intelligent tuning based on feedback history
        entries = api.load_feedback_entries()
        if entries:
            recent_entries = entries[-20:] if len(entries) >= 20 else entries
            avg_rating = sum(e.get('rating', 0) for e in recent_entries) / len(recent_entries)

            # Auto-adjust based on performance
            if avg_rating < 3.0:
                tuning_data.setdefault('tone', 'formal')
                tuning_data.setdefault('fallback_sensitivity', min(current_weights.get('fallback_sensitivity', 0.7) + 0.1, 1.0))
            elif avg_rating > 4.5:
                tuning_data.setdefault('shaping_strength', min(current_weights.get('shaping_strength', 0.5) + 0.1, 1.0))

        # Apply updates
        success = api.update_prompt_weights(tuning_data)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to apply tuning")

        # Get updated summary
        summary = api.get_shaping_summary()

        logger.info(f"Applied prompt tuning: {tuning_data}")

        return {
            "status": "success",
            "message": "Prompt tuning applied",
            "tuning_applied": tuning_data,
            "updated_summary": summary,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to tune prompt shaping: {e}")
        raise HTTPException(status_code=500, detail="Failed to apply prompt tuning")


