#!/usr/bin/env python3
"""
Python file with syntax errors for testing CodeCrusher error handling
This file intentionally contains syntax errors to test robustness
"""

import os
import sys

# Missing closing parenthesis
def broken_function(a, b:
    """Function with syntax error."""
    return a + b

class BrokenClass:
    """Class with syntax errors."""
    
    def __init__(self):
        self.value = 10
        # Missing closing quote
        self.name = "broken string
    
    def method_with_error(self):
        """Method with indentation error."""
    # Wrong indentation
    return self.value * 2
    
    # AI_INJECT:fix_syntax
    # Fix the syntax errors in this file
    # AI_INJECT:fix_syntax:end

# Missing colon
def another_broken_function()
    """Another function with syntax error."""
    # Undefined variable
    return undefined_variable + 5

# Incorrect dictionary syntax
broken_dict = {
    "key1": "value1"
    "key2": "value2"  # Missing comma
}

# AI_INJECT:error_handling
# Add proper error handling throughout this file
# AI_INJECT:error_handling:end

if __name__ == "__main__":
    # This will cause errors when run
    broken = BrokenClass()
    result = broken_function(1, 2)
    print(f"Result: {result}")
