body {
    font-family: var(--vscode-font-family);
    color: var(--vscode-foreground);
    padding: 10px;
    margin: 0;
}

.hidden {
    display: none !important;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid var(--vscode-editor-foreground);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error-container {
    padding: 20px;
    text-align: center;
}

.error-container h3 {
    color: var(--vscode-errorForeground);
    margin-bottom: 15px;
}

.error-container pre {
    background-color: var(--vscode-editor-background);
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
    overflow-x: auto;
}

.error-container button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 8px 12px;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 10px;
}

.error-container button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.header h2 {
    margin: 0;
    font-size: 1.2em;
}

.header button {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    font-size: 1.2em;
    padding: 5px;
}

.header button:hover {
    color: var(--vscode-textLink-foreground);
}

.section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.section h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1em;
    color: var(--vscode-descriptionForeground);
}

.models-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: var(--vscode-editor-background);
    border-radius: 4px;
}

.model-name {
    font-weight: bold;
}

.model-stats {
    display: flex;
    gap: 10px;
    align-items: center;
}

.model-count {
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
}

.model-status {
    font-weight: bold;
}

.stats-container {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.stat-item {
    flex: 1;
    background-color: var(--vscode-editor-background);
    padding: 8px;
    border-radius: 4px;
    text-align: center;
}

.stat-label {
    font-size: 0.8em;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 5px;
}

.stat-value {
    font-weight: bold;
    font-size: 1.1em;
}

.anomalies-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 10px;
}

.anomaly-item {
    padding: 8px;
    background-color: var(--vscode-editor-background);
    border-radius: 4px;
    border-left: 3px solid var(--vscode-errorForeground);
}

.more-anomalies {
    text-align: center;
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
    margin-top: 5px;
}

.no-anomalies {
    text-align: center;
    color: var(--vscode-descriptionForeground);
    padding: 10px;
}

.trends-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.trend-item {
    padding: 8px;
    background-color: var(--vscode-editor-background);
    border-radius: 4px;
}

.trend-label {
    font-size: 0.8em;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 5px;
}

.trend-value {
    font-weight: bold;
}

.trend-models {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.trend-model {
    display: flex;
    justify-content: space-between;
    font-size: 0.9em;
}

.no-trends {
    text-align: center;
    color: var(--vscode-descriptionForeground);
    padding: 10px;
}

.action-button {
    width: 100%;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 8px 12px;
    border-radius: 3px;
    cursor: pointer;
    margin-top: 5px;
}

.action-button:hover {
    background-color: var(--vscode-button-hoverBackground);
}
