import React, { useState, useEffect, useCallback } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import { CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { getHealthURL, getWebSocketUrl, testServerConnection } from '@/utils/client';
import { AuthProvider } from '@/contexts/AuthContext';
import { ToastProvider } from '@/components/ToastNotifications';

// Import all dashboard components
import Dashboard from './components/Dashboard';
import StreamlinedDashboard from './components/StreamlinedDashboard';
import CleanDashboard from './components/CleanDashboard';
import EnhancedDashboard from './components/EnhancedDashboard';
import BackendDashboard from './components/BackendDashboard';
import DashboardUI from './components/DashboardUI';
import StatusDashboard from './components/StatusDashboard';
import StableStatusDashboard from './components/StableStatusDashboard';
import { LogPanelDemo } from './components/LogPanelDemo';
import StyleTest from './components/StyleTest';
import EnterpriseDashboard from './components/EnterpriseDashboard';
import IntelligenceHubDashboard from './components/IntelligenceHubDashboard';
import { TeamWorkspace } from './components/TeamWorkspace';
import { NavigationWrapper } from './components/NavigationWrapper';

interface HealthStatus {
  status: string;
  codecrusher_available: boolean;
  codecrusher_path?: string;
  timestamp: string;
}

// API configuration is now imported from config/api.ts

export default function App() {
  const navigate = useNavigate();
  const location = useLocation();

  // Health and connection state
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [wsConnected, setWsConnected] = useState(false);

  // Force re-render state
  const [forceRender, setForceRender] = useState(0);

  // Get current route to determine view mode
  const getCurrentViewMode = () => {
    const path = location.pathname;
    switch (path) {
      case '/': return 'main';
      case '/dashboard': return 'simple';
      case '/streamlined': return 'streamlined';
      case '/clean': return 'clean';
      case '/enhanced': return 'enhanced';
      case '/backend': return 'backend';
      case '/ui': return 'ui';
      case '/status': return 'status';
      case '/stable': return 'stable';
      case '/demo': return 'demo';
      case '/styletest': return 'styletest';
      case '/intelligence': return 'intelligence';
      case '/teams': return 'teams';
      default: return 'main';  // Default to main instead of enterprise
    }
  };

  const setViewMode = useCallback((mode: string) => {
    const routeMap: { [key: string]: string } = {
      'main': '/',
      'simple': '/dashboard',
      'streamlined': '/streamlined',
      'clean': '/clean',
      'enhanced': '/enhanced',
      'backend': '/backend',
      'ui': '/ui',
      'status': '/status',
      'stable': '/stable',
      'enterprise': '/',  // Redirect enterprise to home
      'demo': '/demo',
      'styletest': '/styletest',
      'intelligence': '/intelligence',
      'teams': '/teams'
    };

    const route = routeMap[mode] || '/';

    console.log(`🔍 NAVIGATION DEBUG: mode=${mode}, route=${route}, currentPath=${location.pathname}`);

    // NUCLEAR OPTION: Use window.location.href for guaranteed navigation
    console.log(`🚀 FORCE NAVIGATING to: ${route}`);
    window.location.href = route;
  }, [location.pathname]);

  useEffect(() => {
    checkHealth();
    // Set up WebSocket connection monitoring with dynamic URL
    const wsUrl = getWebSocketUrl();
    const ws = new WebSocket(wsUrl);
    ws.onopen = () => setWsConnected(true);
    ws.onclose = () => setWsConnected(false);
    ws.onerror = () => setWsConnected(false);

    return () => {
      ws.close();
    };
  }, []);

  const checkHealth = async () => {
    try {
      const healthUrl = getHealthURL();
      const response = await fetch(healthUrl);
      const data = await response.json();
      setHealthStatus(data);
    } catch (error) {
      console.error('Health check failed:', error);
      // Test connection and log detailed error
      const connectionTest = await testServerConnection();
      console.error('Connection test result:', connectionTest);
    }
  };

  const getStatusIcon = () => {
    if (!healthStatus) return <AlertTriangle className="h-4 w-4 animate-pulse text-yellow-500" />;
    if (healthStatus.codecrusher_available) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusText = () => {
    if (!healthStatus) return 'Checking...';
    if (healthStatus.codecrusher_available) return 'CodeCrusher Available';
    return 'CodeCrusher Not Found';
  };

  const currentViewMode = getCurrentViewMode();

  console.log(`📊 APP RENDER: path=${location.pathname}, viewMode=${currentViewMode}, forceRender=${forceRender}`);

  return (
    <AuthProvider>
      <ToastProvider>
        <Routes>
      <Route path="/" element={
        <EnterpriseDashboard
          key={`home-${location.pathname}-${forceRender}`}
          viewMode={currentViewMode}
          setViewMode={setViewMode}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        />
      } />
      <Route path="/dashboard" element={
        <Dashboard
          key={`dashboard-${location.pathname}-${forceRender}`}
          viewMode={currentViewMode}
          setViewMode={setViewMode}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        />
      } />
      <Route path="/streamlined" element={
        <StreamlinedDashboard
          key={`streamlined-${location.pathname}-${forceRender}`}
          viewMode={currentViewMode}
          setViewMode={setViewMode}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        />
      } />
      <Route path="/clean" element={
        <CleanDashboard
          key={`clean-${location.pathname}-${forceRender}`}
          viewMode={currentViewMode}
          setViewMode={setViewMode}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        />
      } />
      <Route path="/enhanced" element={
        <NavigationWrapper
          key={`enhanced-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <EnhancedDashboard />
        </NavigationWrapper>
      } />
      <Route path="/backend" element={
        <NavigationWrapper
          key={`backend-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <BackendDashboard />
        </NavigationWrapper>
      } />
      <Route path="/ui" element={
        <NavigationWrapper
          key={`ui-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <DashboardUI />
        </NavigationWrapper>
      } />
      <Route path="/status" element={
        <NavigationWrapper
          key={`status-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <StatusDashboard />
        </NavigationWrapper>
      } />
      <Route path="/stable" element={
        <NavigationWrapper
          key={`stable-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <StableStatusDashboard />
        </NavigationWrapper>
      } />
      <Route path="/demo" element={
        <NavigationWrapper
          key={`demo-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <LogPanelDemo />
        </NavigationWrapper>
      } />
      <Route path="/styletest" element={
        <NavigationWrapper
          key={`styletest-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <StyleTest />
        </NavigationWrapper>
      } />
      <Route path="/intelligence" element={
        <IntelligenceHubDashboard
          key={`intelligence-${location.pathname}-${forceRender}`}
        />
      } />
      <Route path="/teams" element={
        <NavigationWrapper
          key={`teams-${location.pathname}-${forceRender}`}
          healthStatus={healthStatus}
          wsConnected={wsConnected}
          getStatusIcon={getStatusIcon}
          getStatusText={getStatusText}
        >
          <TeamWorkspace />
        </NavigationWrapper>
      } />
        </Routes>
      </ToastProvider>
    </AuthProvider>
  );
}
