# PyPI Configuration Template for CodeCrusher
# Copy this to ~/.pypirc and fill in your credentials

[distutils]
index-servers =
    pypi
    testpypi

[pypi]
repository = https://upload.pypi.org/legacy/
username = __token__
password = pypi-YOUR_API_TOKEN_HERE

[testpypi]
repository = https://test.pypi.org/legacy/
username = __token__
password = pypi-YOUR_TEST_API_TOKEN_HERE

# Instructions:
# 1. Create accounts on PyPI (https://pypi.org) and TestPyPI (https://test.pypi.org)
# 2. Generate API tokens in your account settings
# 3. Copy this file to ~/.pypirc (Linux/Mac) or %USERPROFILE%\.pypirc (Windows)
# 4. Replace YOUR_API_TOKEN_HERE with your actual tokens
# 5. Keep this file secure and never commit it to version control

# Alternative: Use environment variables
# export TWINE_USERNAME=__token__
# export TWINE_PASSWORD=pypi-YOUR_API_TOKEN_HERE
