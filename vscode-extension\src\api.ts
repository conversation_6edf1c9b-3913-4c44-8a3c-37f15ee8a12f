import axios from 'axios';
import * as vscode from 'vscode';

// Base URL for the CodeCrusher API
const API_BASE_URL = 'http://localhost:9000/api';

// Interface for status response
export interface StatusResponse {
    time_period: string;
    entry_count: number;
    models_used: {
        [key: string]: {
            count: number;
            errors: number;
            fallbacks: number;
        }
    };
    cache_stats: {
        hits: number;
        misses: number;
        hit_rate: number;
    };
    fallback_stats: {
        count: number;
        rate: number;
    };
    top_tags: {
        [key: string]: number;
    };
    anomalies: string[];
}

// Interface for scan response
export interface ScanResponse {
    time_period: string;
    model_filter: string;
    tag_filter: string;
    entries_analyzed: number;
    scan_duration_seconds: number;
    anomalies_count: number;
    anomalies: Array<{
        type: string;
        description: string;
        timestamp: string;
        model: string;
        value: number;
        threshold: number;
        tags: string[];
        suggested_action?: string;
    }>;
}

// Interface for trends response
export interface TrendsResponse {
    time_period: {
        since: string;
        until: string;
    };
    filters: {
        model: string;
        tag: string;
    };
    metrics: {
        [date: string]: {
            total_injections: number;
            fallbacks: number;
            fallback_rate: number;
            errors: number;
            error_types: {
                [type: string]: number;
            };
            models: {
                [model: string]: number;
            };
            tokens_in: number;
            tokens_out: number;
            tags: {
                [tag: string]: number;
            };
        };
    };
}

// Interface for telemetry entry
export interface TelemetryEntry {
    timestamp: string;
    model: string;
    provider: string;
    operation_type: string;
    tokens_in: number;
    tokens_out: number;
    cached: boolean;
    fallback: boolean;
    error: string | null;
    tags: string[];
    file_path?: string;
    line_number?: number;
}

// Interface for telemetry response
export interface TelemetryResponse {
    entries: TelemetryEntry[];
    count: number;
}

// API client class
export class CodeCrusherApi {
    /**
     * Get status information
     */
    static async getStatus(since: string = '24h', model?: string): Promise<StatusResponse> {
        try {
            const params: any = { since };
            if (model) {
                params.model = model;
            }

            const response = await axios.get(`${API_BASE_URL}/status`, { params });
            return response.data;
        } catch (error) {
            this.handleApiError('Failed to fetch status', error);
            throw error;
        }
    }

    /**
     * Run scan for anomalies
     */
    static async runScan(
        since: string = '24h',
        model?: string,
        tag?: string,
        limit: number = 10,
        verbose: boolean = false
    ): Promise<ScanResponse> {
        try {
            const params: any = {
                since,
                limit,
                verbose
            };

            if (model) {
                params.model = model;
            }

            if (tag) {
                params.tag = tag;
            }

            const response = await axios.get(`${API_BASE_URL}/scan`, { params });
            return response.data;
        } catch (error) {
            this.handleApiError('Failed to run scan', error);
            throw error;
        }
    }

    /**
     * Get trends data
     */
    static async getTrends(
        since: string = '7d',
        until: string = 'now',
        model?: string,
        tag?: string
    ): Promise<TrendsResponse> {
        try {
            const params: any = {
                since,
                until
            };

            if (model) {
                params.model = model;
            }

            if (tag) {
                params.tag = tag;
            }

            const response = await axios.get(`${API_BASE_URL}/trends`, { params });
            return response.data;
        } catch (error) {
            this.handleApiError('Failed to fetch trends', error);
            throw error;
        }
    }

    /**
     * Get telemetry data
     */
    static async getTelemetry(
        limit: number = 100,
        since?: string,
        model?: string,
        tag?: string,
        cached?: boolean,
        fallback?: boolean,
        error?: boolean
    ): Promise<TelemetryResponse> {
        try {
            const params: any = { limit };

            if (since) {
                params.since = since;
            }

            if (model) {
                params.model = model;
            }

            if (tag) {
                params.tag = tag;
            }

            if (cached !== undefined) {
                params.cached = cached;
            }

            if (fallback !== undefined) {
                params.fallback = fallback;
            }

            if (error !== undefined) {
                params.error = error;
            }

            const response = await axios.get(`${API_BASE_URL}/telemetry`, { params });
            return response.data;
        } catch (error) {
            this.handleApiError('Failed to fetch telemetry', error);
            return { entries: [], count: 0 };
        }
    }

    /**
     * Check if the API server is running
     */
    static async isServerRunning(): Promise<boolean> {
        try {
            await axios.get(`${API_BASE_URL}/status`, { timeout: 1000 });
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Handle API errors
     */
    private static handleApiError(message: string, error: any): void {
        console.error(`${message}:`, error);

        if (axios.isAxiosError(error)) {
            if (error.code === 'ECONNREFUSED') {
                vscode.window.showErrorMessage(
                    'Cannot connect to CodeCrusher API server. Make sure it\'s running with: codecrusher serve run'
                );
            } else if (error.response) {
                vscode.window.showErrorMessage(
                    `CodeCrusher API error (${error.response.status}): ${error.response.data.detail || 'Unknown error'}`
                );
            } else {
                vscode.window.showErrorMessage(`CodeCrusher API error: ${error.message}`);
            }
        } else {
            vscode.window.showErrorMessage(`CodeCrusher API error: ${error}`);
        }
    }
}
