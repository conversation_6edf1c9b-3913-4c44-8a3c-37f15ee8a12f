"""
Common functionality shared across CLI commands
"""

import logging
import os
import typer
from enum import Enum
from typing import Optional
from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Initialize rich console
console = Console()

# Define provider options as an enum
class Provider(str, Enum):
    GROQ = "groq"
    MISTRAL = "mistral"
    AUTO = "auto"

# Define model options as an enum
class Model(str, Enum):
    LLAMA3_8B = "llama3-8b-8192"
    LLAMA3_70B = "llama3-70b-8192"
    LLAMA3_3_70B = "llama-3.3-70b-versatile"
    MISTRAL_MEDIUM = "mistral-medium-2312"
    MISTRAL_LARGE = "mistral-large-2402"
    AUTO = "auto"

def setup_logging(verbose: bool = False):
    """
    Set up logging with appropriate level based on verbosity

    Args:
        verbose: Whether to enable verbose logging
    """
    log_level = logging.DEBUG if verbose else logging.INFO

    # Configure rich handler
    rich_handler = <PERSON>Handler(
        rich_tracebacks=True,
        markup=True,
        show_time=True,
        omit_repeated_times=False,
    )

    # Configure logging
    logging.basicConfig(
        level=log_level,
        format="%(message)s",
        datefmt="[%X]",
        handlers=[rich_handler]
    )

    # Log the configuration
    logging.debug("Logging initialized with level: %s", "DEBUG" if verbose else "INFO")

# Common options for all commands
def common_options(app):
    """
    Decorator factory to add common options to all commands.
    This is a different approach than using a decorator directly.
    """
    def decorator(f):
        # Add verbose option
        f = app.callback()(f)
        return f
    return decorator
