import typer
import asyncio
import difflib
import logging
from typing import Annotated
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.progress import Progress, SpinnerColumn, TextColumn

# Import utility modules
from codecrusher.logger import setup_logging, log_with_timestamp
from codecrusher.cache_manager import load_cache_result, save_cache_result
from unified_ai_engine import unified_ai_engine

# Initialize rich console
console = Console()

# Create the Typer app
app = typer.Typer()

def score_optimization(original_code: str, optimized_code: str) -> tuple[float, list[str]]:
    """
    Score the optimization based on various metrics.

    Args:
        original_code: The original code
        optimized_code: The optimized code

    Returns:
        tuple: (score, list of improvements)
    """
    score = 5.0  # Base score
    improvements = []

    # Check if code is actually different
    if original_code == optimized_code:
        return 0.0, ["No changes made"]

    # Check for length reduction
    if len(optimized_code) < len(original_code):
        reduction = (len(original_code) - len(optimized_code)) / len(original_code)
        if reduction > 0.2:
            score += 1.5
            improvements.append("Significant code reduction")
        elif reduction > 0.05:
            score += 0.8
            improvements.append("Moderate code reduction")

    # Check for list comprehensions (a common Python optimization)
    if "for" in original_code and "[" in optimized_code and "]" in optimized_code:
        if optimized_code.count("[") > original_code.count("["):
            score += 1.0
            improvements.append("Replaced for-loop with list comprehension")

    # Check for f-strings (modern Python)
    if "%" in original_code and "f\"" in optimized_code:
        score += 0.5
        improvements.append("Replaced string formatting with f-strings")

    # Check for context managers
    if "open(" in original_code and "with open" in optimized_code:
        score += 0.7
        improvements.append("Added context managers for resource handling")

    # Check for type hints
    if ": " in optimized_code and ": " not in original_code:
        score += 0.8
        improvements.append("Added type hints")

    # Check for docstrings
    if '"""' in optimized_code and '"""' not in original_code:
        score += 0.6
        improvements.append("Added docstrings")

    # Cap the score at 10
    return min(10.0, score), improvements

def generate_diff(original_code: str, optimized_code: str) -> str:
    """
    Generate a diff between the original and optimized code.

    Args:
        original_code: The original code
        optimized_code: The optimized code

    Returns:
        str: The diff as a string
    """
    original_lines = original_code.splitlines()
    optimized_lines = optimized_code.splitlines()

    diff = difflib.unified_diff(
        original_lines,
        optimized_lines,
        fromfile="original",
        tofile="optimized",
        lineterm=""
    )

    return "\n".join(diff)

def generate_cache_key(input_file: str, provider: str, model: str) -> str:
    """
    Generate a cache key for the optimization.

    Args:
        input_file: The input file path
        provider: The AI provider
        model: The model name

    Returns:
        str: The cache key
    """
    return f"optimize_{Path(input_file).name}_{provider}_{model}"

@app.command("run")
def run_optimization(
    input: Annotated[Path, typer.Option("--input", "-i", help="Path to the input file")] = None,
    provider: Annotated[str, typer.Option("--provider", help="AI provider", case_sensitive=False)] = "groq",
    model: Annotated[str, typer.Option("--model", "-m", help="Model to use")] = "llama3",
    cache: Annotated[bool, typer.Option("--cache", help="Use cached results")] = False,
    verbose: Annotated[bool, typer.Option("--verbose", help="Enable verbose logging")] = False,
):
    """
    Optimize Python code for performance, readability, and modern Python standards.

    This command reads the input file, sends the code to the AI engine with an optimization prompt,
    and displays the optimized code with a diff and quality score.
    """
    # Set up logging
    log_level = logging.DEBUG if verbose else logging.INFO
    setup_logging(log_level=log_level)
    log_with_timestamp("Starting code optimization", "info")

    # Validate input file
    if input is None:
        console.print("[bold red]Error:[/bold red] Input file is required")
        raise typer.Exit(code=1)

    if not input.exists():
        console.print(f"[bold red]Error:[/bold red] Input file not found: {input}")
        raise typer.Exit(code=1)

    # Read the input file
    try:
        original_code = input.read_text(encoding="utf-8")
        if verbose:
            log_with_timestamp(f"Read {len(original_code)} characters from {input}", "debug")
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] Failed to read file: {str(e)}")
        raise typer.Exit(code=1)

    # Display configuration
    console.print(Panel(
        f"[bold]CodeCrusher Optimization[/bold]\n\n"
        f"[cyan]Input file:[/cyan] {input}\n"
        f"[cyan]Provider:[/cyan] {provider}\n"
        f"[cyan]Model:[/cyan] {model}\n"
        f"[cyan]Cache:[/cyan] {'Enabled' if cache else 'Disabled'}\n"
        f"[cyan]Verbose:[/cyan] {'Enabled' if verbose else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))

    # Check cache if enabled
    cache_key = generate_cache_key(str(input), provider, model)
    optimized_code = None

    if cache:
        cached_result = load_cache_result(cache_key, model)
        if cached_result:
            optimized_code = cached_result
            log_with_timestamp(f"Using cached optimization result for {input}", "info")

    # If not in cache, call the AI engine
    if optimized_code is None:
        # Build the prompt
        prompt = (
            "Optimize the following Python code for performance, readability, and modern Python standards. "
            "Suggest minimal changes with clear improvements. "
            "Return the complete optimized code without explanations."
        )

        # Show progress spinner while waiting for AI
        with Progress(
            SpinnerColumn(),
            TextColumn("[bold green]Optimizing code...[/bold green]"),
            console=console,
            transient=True,
        ) as progress:
            task = progress.add_task("Optimizing...", total=None)

            # Call the AI engine
            try:
                log_with_timestamp(f"Sending code to AI engine ({provider}/{model})", "info")
                optimized_code = asyncio.run(unified_ai_engine(
                    code=original_code,
                    prompt=prompt,
                    provider=provider,
                    model=model,
                    cache=False,  # We're handling caching ourselves
                    verbose=verbose
                ))

                # Cache the result if caching is enabled
                if cache:
                    save_cache_result(cache_key, model, optimized_code)
                    log_with_timestamp(f"Cached optimization result for {input}", "debug")

            except Exception as e:
                console.print(f"[bold red]Error:[/bold red] Optimization failed: {str(e)}")
                raise typer.Exit(code=1)

            # Mark task as complete
            progress.update(task, completed=True)

    # Score the optimization
    score, improvements = score_optimization(original_code, optimized_code)

    # Generate diff
    diff_text = generate_diff(original_code, optimized_code)

    # Display results
    console.print(f"\n[bold green][SUCCESS] Optimization Complete [Score: {score:.1f}/10][/bold green]")

    # Display improvements
    if improvements:
        console.print("\n[bold][CHANGES] Detected improvements:[/bold]")
        for improvement in improvements:
            console.print(f"- {improvement}")
    else:
        console.print("\n[bold yellow]No significant improvements detected[/bold yellow]")

    # Extract a summary of the diff (first few lines)
    diff_lines = diff_text.splitlines()
    summary_lines = [line for line in diff_lines if line.startswith("+") or line.startswith("-")][:5]
    if summary_lines and not verbose:
        console.print("\n[bold]Diff Summary:[/bold]")
        for line in summary_lines:
            if line.startswith("+"):
                console.print(f"[green]{line}[/green]")
            elif line.startswith("-"):
                console.print(f"[red]{line}[/red]")
        if len(summary_lines) < len([l for l in diff_lines if l.startswith("+") or l.startswith("-")]):
            console.print("[dim]... (use --verbose to see full diff)[/dim]")

    # Display full diff if verbose
    if verbose:
        console.print("\n[bold]Full Diff:[/bold]")
        console.print(Syntax(diff_text, "diff", theme="monokai", line_numbers=True))

    # Display optimized code
    console.print("\n[bold]Optimized Code:[/bold]")
    console.print(Syntax(optimized_code, "python", theme="monokai", line_numbers=True))

    # Log completion
    log_with_timestamp("Optimization completed successfully", "info")

    return optimized_code
