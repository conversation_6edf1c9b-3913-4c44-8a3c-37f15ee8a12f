import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, WifiOff, RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';
import { ConnectionStatus } from '@/hooks/useWebSocket';
import { cn } from '@/lib/utils';

interface ConnectionOverlayProps {
  status: ConnectionStatus;
  retryCount: number;
  maxRetries: number;
  onRetry: () => void;
  onRefresh?: () => void;
  className?: string;
}

export function ConnectionOverlay({ 
  status, 
  retryCount, 
  maxRetries, 
  onRetry, 
  onRefresh,
  className 
}: ConnectionOverlayProps) {
  if (status === 'connected' || status === 'idle' || status === 'closed') {
    return null;
  }

  const getOverlayContent = () => {
    switch (status) {
      case 'connecting':
        return {
          bgColor: 'bg-blue-500',
          icon: <Loader2 className="w-4 h-4 animate-spin" />,
          title: 'Connecting to server...',
          description: 'Establishing WebSocket connection',
          showRetry: false
        };

      case 'reconnecting':
        return {
          bgColor: 'bg-yellow-500',
          icon: <RefreshCw className="w-4 h-4 animate-spin" />,
          title: `Reconnecting... (${retryCount}/${maxRetries})`,
          description: 'Connection lost, attempting to reconnect',
          showRetry: true
        };

      case 'error':
        return {
          bgColor: 'bg-red-600',
          icon: <WifiOff className="w-4 h-4" />,
          title: 'Connection lost',
          description: retryCount >= maxRetries 
            ? 'Max reconnection attempts exceeded' 
            : 'Unable to connect to server',
          showRetry: true
        };

      default:
        return null;
    }
  };

  const content = getOverlayContent();
  if (!content) return null;

  return (
    <div className={cn(
      "fixed top-0 left-0 right-0 z-50 transition-all duration-300",
      className
    )}>
      {/* Top Banner */}
      <div className={cn(
        "w-full text-white text-center py-3 px-4 shadow-lg",
        content.bgColor
      )}>
        <div className="flex items-center justify-center gap-2 max-w-4xl mx-auto">
          {content.icon}
          <span className="font-medium">{content.title}</span>
          {content.showRetry && (
            <div className="flex items-center gap-2 ml-4">
              <Button
                onClick={onRetry}
                variant="outline"
                size="sm"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Retry
              </Button>
              {onRefresh && (
                <Button
                  onClick={onRefresh}
                  variant="outline"
                  size="sm"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  Refresh Page
                </Button>
              )}
            </div>
          )}
        </div>
        <div className="text-xs opacity-90 mt-1">
          {content.description}
        </div>
      </div>

      {/* Detailed Error Modal for Critical Failures */}
      {status === 'error' && retryCount >= maxRetries && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-red-100 rounded-full">
                <AlertTriangle className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Connection Failed</h3>
                <p className="text-sm text-gray-600">Unable to connect to CodeCrusher backend</p>
              </div>
            </div>

            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Failed to establish connection after {maxRetries} attempts. 
                Please check if the backend server is running on localhost:8000.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                <strong>Troubleshooting steps:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Ensure backend server is running: <code className="bg-gray-100 px-1 rounded">python backend_main.py</code></li>
                  <li>Check if port 8000 is accessible</li>
                  <li>Verify your network connection</li>
                  <li>Try refreshing the page</li>
                </ul>
              </div>

              <div className="flex gap-2">
                <Button onClick={onRetry} className="flex-1">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button 
                  onClick={() => window.location.reload()} 
                  variant="outline"
                  className="flex-1"
                >
                  Refresh Page
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

interface ConnectionStatusBadgeProps {
  status: ConnectionStatus;
  retryCount?: number;
  className?: string;
}

export function ConnectionStatusBadge({ status, retryCount = 0, className }: ConnectionStatusBadgeProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'connected':
        return {
          variant: 'default' as const,
          className: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="w-3 h-3" />,
          text: 'Connected'
        };

      case 'connecting':
        return {
          variant: 'secondary' as const,
          className: 'bg-blue-100 text-blue-800 border-blue-200',
          icon: <Loader2 className="w-3 h-3 animate-spin" />,
          text: 'Connecting...'
        };

      case 'reconnecting':
        return {
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <RefreshCw className="w-3 h-3 animate-spin" />,
          text: `Reconnecting... (${retryCount})`
        };

      case 'error':
        return {
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 border-red-200',
          icon: <WifiOff className="w-3 h-3" />,
          text: 'Connection Error'
        };

      case 'closed':
        return {
          variant: 'outline' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <WifiOff className="w-3 h-3" />,
          text: 'Disconnected'
        };

      default:
        return {
          variant: 'outline' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: null,
          text: 'Idle'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <Badge 
      variant={config.variant}
      className={cn(
        "flex items-center gap-1.5 px-2 py-1",
        config.className,
        className
      )}
    >
      {config.icon}
      <span className="text-xs font-medium">{config.text}</span>
    </Badge>
  );
}
