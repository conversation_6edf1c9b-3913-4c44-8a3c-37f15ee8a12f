"""
Diff Intelligence Commentary Module for CodeCrusher

This module provides intelligent analysis of code changes after injection,
generating natural-language summaries of what was modified, added, or removed.

Features:
- Unified diff generation with proper formatting
- Intelligent change detection and categorization
- Natural language summaries of modifications
- Support for various programming languages
- Detailed analysis of code structure changes
"""

import re
import logging
from difflib import unified_diff
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DiffAnalysis:
    """Structured analysis of code differences."""
    additions: int
    deletions: int
    modifications: int
    total_changes: int
    added_functions: List[str]
    removed_functions: List[str]
    modified_functions: List[str]
    added_classes: List[str]
    removed_classes: List[str]
    added_imports: List[str]
    removed_imports: List[str]
    structural_changes: List[str]
    summary: str


def get_unified_diff(before: str, after: str, filename: str = "file.py") -> str:
    """
    Generate a unified diff between before and after code.
    
    Args:
        before: Original code content
        after: Modified code content
        filename: Name of the file for diff headers
        
    Returns:
        Unified diff as a string
    """
    if not before and not after:
        return ""
    
    before_lines = before.strip().splitlines(keepends=True)
    after_lines = after.strip().splitlines(keepends=True)
    
    diff = unified_diff(
        before_lines, 
        after_lines, 
        fromfile=f"{filename} (before)", 
        tofile=f"{filename} (after)",
        lineterm=""
    )
    
    return '\n'.join(diff)


def analyze_code_changes(before: str, after: str, filename: str = "file.py") -> DiffAnalysis:
    """
    Perform intelligent analysis of code changes.
    
    Args:
        before: Original code content
        after: Modified code content
        filename: Name of the file for context
        
    Returns:
        DiffAnalysis object with detailed change information
    """
    diff_text = get_unified_diff(before, after, filename)
    
    # Basic line counting
    additions = sum(1 for line in diff_text.splitlines() if line.startswith("+") and not line.startswith("+++"))
    deletions = sum(1 for line in diff_text.splitlines() if line.startswith("-") and not line.startswith("---"))
    modifications = min(additions, deletions)  # Rough estimate of modifications
    total_changes = additions + deletions
    
    # Analyze structural changes
    added_functions = _extract_added_functions(before, after)
    removed_functions = _extract_removed_functions(before, after)
    modified_functions = _extract_modified_functions(before, after)
    
    added_classes = _extract_added_classes(before, after)
    removed_classes = _extract_removed_classes(before, after)
    
    added_imports = _extract_added_imports(before, after)
    removed_imports = _extract_removed_imports(before, after)
    
    structural_changes = _analyze_structural_changes(before, after)
    
    # Generate intelligent summary
    summary = _generate_intelligent_summary(
        additions, deletions, modifications,
        added_functions, removed_functions, modified_functions,
        added_classes, removed_classes,
        added_imports, removed_imports,
        structural_changes
    )
    
    return DiffAnalysis(
        additions=additions,
        deletions=deletions,
        modifications=modifications,
        total_changes=total_changes,
        added_functions=added_functions,
        removed_functions=removed_functions,
        modified_functions=modified_functions,
        added_classes=added_classes,
        removed_classes=removed_classes,
        added_imports=added_imports,
        removed_imports=removed_imports,
        structural_changes=structural_changes,
        summary=summary
    )


def format_diff_summary(diff_text: str) -> str:
    """
    Generate a basic diff summary from diff text.
    
    Args:
        diff_text: Unified diff text
        
    Returns:
        Human-readable summary string
    """
    if not diff_text.strip():
        return "No code changes detected."
    
    additions = sum(1 for line in diff_text.splitlines() if line.startswith("+") and not line.startswith("+++"))
    deletions = sum(1 for line in diff_text.splitlines() if line.startswith("-") and not line.startswith("---"))
    
    if additions == 0 and deletions == 0:
        return "No code changes detected."
    
    summary_parts = []
    if additions:
        summary_parts.append(f"{additions} line{'s' if additions != 1 else ''} added")
    if deletions:
        summary_parts.append(f"{deletions} line{'s' if deletions != 1 else ''} removed")
    
    return "Diff summary: " + ", ".join(summary_parts) + "."


def _extract_added_functions(before: str, after: str) -> List[str]:
    """Extract function names that were added."""
    before_functions = _extract_function_names(before)
    after_functions = _extract_function_names(after)
    return list(set(after_functions) - set(before_functions))


def _extract_removed_functions(before: str, after: str) -> List[str]:
    """Extract function names that were removed."""
    before_functions = _extract_function_names(before)
    after_functions = _extract_function_names(after)
    return list(set(before_functions) - set(after_functions))


def _extract_modified_functions(before: str, after: str) -> List[str]:
    """Extract function names that were modified (rough heuristic)."""
    before_functions = _extract_function_names(before)
    after_functions = _extract_function_names(after)
    common_functions = set(before_functions) & set(after_functions)
    
    # Simple heuristic: if function exists in both but content differs
    modified = []
    for func in common_functions:
        before_content = _extract_function_content(before, func)
        after_content = _extract_function_content(after, func)
        if before_content != after_content:
            modified.append(func)
    
    return modified


def _extract_function_names(code: str) -> List[str]:
    """Extract function names from code."""
    # Support multiple languages
    patterns = [
        r'def\s+(\w+)\s*\(',  # Python
        r'function\s+(\w+)\s*\(',  # JavaScript
        r'fn\s+(\w+)\s*\(',  # Rust
        r'func\s+(\w+)\s*\(',  # Go
        r'public\s+\w+\s+(\w+)\s*\(',  # Java/C#
        r'(\w+)\s*\([^)]*\)\s*{',  # C/C++/JavaScript
    ]
    
    functions = []
    for pattern in patterns:
        matches = re.findall(pattern, code, re.MULTILINE)
        functions.extend(matches)
    
    return list(set(functions))


def _extract_function_content(code: str, func_name: str) -> str:
    """Extract the content of a specific function (simplified)."""
    # This is a simplified implementation
    lines = code.split('\n')
    in_function = False
    function_lines = []
    indent_level = 0
    
    for line in lines:
        if f'def {func_name}(' in line or f'function {func_name}(' in line:
            in_function = True
            indent_level = len(line) - len(line.lstrip())
            function_lines.append(line)
        elif in_function:
            current_indent = len(line) - len(line.lstrip())
            if line.strip() and current_indent <= indent_level and not line.strip().startswith('#'):
                break
            function_lines.append(line)
    
    return '\n'.join(function_lines)


def _extract_added_classes(before: str, after: str) -> List[str]:
    """Extract class names that were added."""
    before_classes = _extract_class_names(before)
    after_classes = _extract_class_names(after)
    return list(set(after_classes) - set(before_classes))


def _extract_removed_classes(before: str, after: str) -> List[str]:
    """Extract class names that were removed."""
    before_classes = _extract_class_names(before)
    after_classes = _extract_class_names(after)
    return list(set(before_classes) - set(after_classes))


def _extract_class_names(code: str) -> List[str]:
    """Extract class names from code."""
    patterns = [
        r'class\s+(\w+)(?:\s*\([^)]*\))?\s*:',  # Python
        r'class\s+(\w+)(?:\s+extends\s+\w+)?\s*{',  # JavaScript/Java
        r'struct\s+(\w+)\s*{',  # C/Rust
        r'public\s+class\s+(\w+)',  # Java/C#
    ]
    
    classes = []
    for pattern in patterns:
        matches = re.findall(pattern, code, re.MULTILINE)
        classes.extend(matches)
    
    return list(set(classes))


def _extract_added_imports(before: str, after: str) -> List[str]:
    """Extract import statements that were added."""
    before_imports = _extract_imports(before)
    after_imports = _extract_imports(after)
    return list(set(after_imports) - set(before_imports))


def _extract_removed_imports(before: str, after: str) -> List[str]:
    """Extract import statements that were removed."""
    before_imports = _extract_imports(before)
    after_imports = _extract_imports(after)
    return list(set(before_imports) - set(after_imports))


def _extract_imports(code: str) -> List[str]:
    """Extract import statements from code."""
    patterns = [
        r'import\s+([^\n;]+)',  # Python/JavaScript
        r'from\s+(\S+)\s+import',  # Python
        r'#include\s*[<"]([^>"]+)[>"]',  # C/C++
        r'use\s+([^;]+);',  # Rust
    ]
    
    imports = []
    for pattern in patterns:
        matches = re.findall(pattern, code, re.MULTILINE)
        imports.extend([match.strip() for match in matches])
    
    return list(set(imports))


def _analyze_structural_changes(before: str, after: str) -> List[str]:
    """Analyze structural changes in the code."""
    changes = []
    
    # Check for indentation changes
    before_indent = _analyze_indentation(before)
    after_indent = _analyze_indentation(after)
    if before_indent != after_indent:
        changes.append("indentation modified")
    
    # Check for comment changes
    before_comments = len(re.findall(r'#.*|//.*|/\*.*?\*/', before, re.DOTALL))
    after_comments = len(re.findall(r'#.*|//.*|/\*.*?\*/', after, re.DOTALL))
    if after_comments > before_comments:
        changes.append("comments added")
    elif after_comments < before_comments:
        changes.append("comments removed")
    
    # Check for docstring changes
    before_docstrings = len(re.findall(r'""".*?"""', before, re.DOTALL))
    after_docstrings = len(re.findall(r'""".*?"""', after, re.DOTALL))
    if after_docstrings > before_docstrings:
        changes.append("documentation added")
    elif after_docstrings < before_docstrings:
        changes.append("documentation removed")
    
    return changes


def _analyze_indentation(code: str) -> str:
    """Analyze the indentation style of code."""
    lines = [line for line in code.split('\n') if line.strip()]
    if not lines:
        return "none"
    
    # Check for tabs vs spaces
    has_tabs = any('\t' in line for line in lines)
    has_spaces = any(line.startswith(' ') for line in lines)
    
    if has_tabs and not has_spaces:
        return "tabs"
    elif has_spaces and not has_tabs:
        return "spaces"
    else:
        return "mixed"


def _generate_intelligent_summary(
    additions: int, deletions: int, modifications: int,
    added_functions: List[str], removed_functions: List[str], modified_functions: List[str],
    added_classes: List[str], removed_classes: List[str],
    added_imports: List[str], removed_imports: List[str],
    structural_changes: List[str]
) -> str:
    """Generate an intelligent natural language summary of changes."""
    
    if additions == 0 and deletions == 0:
        return "No code changes detected."
    
    summary_parts = []
    
    # Function changes
    if added_functions:
        if len(added_functions) == 1:
            summary_parts.append(f"added function '{added_functions[0]}'")
        else:
            summary_parts.append(f"added {len(added_functions)} functions")
    
    if removed_functions:
        if len(removed_functions) == 1:
            summary_parts.append(f"removed function '{removed_functions[0]}'")
        else:
            summary_parts.append(f"removed {len(removed_functions)} functions")
    
    if modified_functions:
        if len(modified_functions) == 1:
            summary_parts.append(f"modified function '{modified_functions[0]}'")
        else:
            summary_parts.append(f"modified {len(modified_functions)} functions")
    
    # Class changes
    if added_classes:
        if len(added_classes) == 1:
            summary_parts.append(f"added class '{added_classes[0]}'")
        else:
            summary_parts.append(f"added {len(added_classes)} classes")
    
    if removed_classes:
        if len(removed_classes) == 1:
            summary_parts.append(f"removed class '{removed_classes[0]}'")
        else:
            summary_parts.append(f"removed {len(removed_classes)} classes")
    
    # Import changes
    if added_imports:
        summary_parts.append(f"added {len(added_imports)} import{'s' if len(added_imports) != 1 else ''}")
    
    if removed_imports:
        summary_parts.append(f"removed {len(removed_imports)} import{'s' if len(removed_imports) != 1 else ''}")
    
    # Structural changes
    if structural_changes:
        summary_parts.extend(structural_changes)
    
    # Fallback to basic line counting if no structural changes detected
    if not summary_parts:
        if additions and deletions:
            summary_parts.append(f"modified {modifications} line{'s' if modifications != 1 else ''}")
        elif additions:
            summary_parts.append(f"added {additions} line{'s' if additions != 1 else ''}")
        elif deletions:
            summary_parts.append(f"removed {deletions} line{'s' if deletions != 1 else ''}")
    
    if not summary_parts:
        return "Code structure analyzed, minimal changes detected."
    
    # Capitalize first word and join with commas
    summary = ", ".join(summary_parts)
    return summary[0].upper() + summary[1:] + "."
