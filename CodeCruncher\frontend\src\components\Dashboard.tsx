import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Brain, Play, Square, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from 'sonner';

import LiveLogPanel from './LiveLogPanel';
import ModelControls from './ModelControls';
import ProgressStats from './ProgressStats';
import { useLogStream } from '@/hooks/useLogStream';
import { getInjectURL } from '@/utils/client';
import { EnterpriseFooter } from './EnterpriseFooter';
import { LabelWithTooltip } from './ui/tooltip';

interface DashboardProps {
  viewMode?: string;
  setViewMode?: (mode: any) => void;
  healthStatus?: any;
  wsConnected?: boolean;
  getStatusIcon?: () => React.ReactElement;
  getStatusText?: () => string;
}

export default function Dashboard({
  viewMode,
  setViewMode,
  healthStatus,
  wsConnected,
  getStatusIcon,
  getStatusText
}: DashboardProps = {}) {
  // Form state - Frontend Compatible Schema
  const [promptText, setPromptText] = useState('Replace all console.log statements with structured logger');
  const [selectedFile, setSelectedFile] = useState('./src');
  const [selectedModel, setSelectedModel] = useState('mixtral');
  const [selectedTags, setSelectedTags] = useState<string[]>(['optimization', 'cleanup']);
  const [loading, setLoading] = useState(false);
  const [logOutput, setLogOutput] = useState('');

  // Legacy form state (for backward compatibility)
  const [sourceFolder, setSourceFolder] = useState('./src');
  const [prompt, setPrompt] = useState('Optimize logic for clarity');
  const [tag, setTag] = useState('clarity-pass');

  // Model controls
  const [model, setModel] = useState('mixtral');
  const [fallbackEnabled, setFallbackEnabled] = useState(true);

  // Additional options
  const [recursive, setRecursive] = useState(true);
  const [apply, setApply] = useState(false);

  // Progress tracking - these could eventually be set from WebSocket updates
  const [completed, setCompleted] = useState(0);
  const [total, setTotal] = useState(10); // Example total count
  const [failed, setFailed] = useState(0);
  const [isRunning, setIsRunning] = useState(false);

  // WebSocket integration - Updated to correct port
  const {
    logs,
    isConnected,
    connectionError,
    clearLogs,
    sendTestMessage,
    latestProgress,
    latestStats
  } = useLogStream('ws://localhost:8001/ws/logs');

  // Auto-scroll logs to bottom when new logs arrive
  useEffect(() => {
    const logBox = document.getElementById("websocket-log-box");
    if (logBox) {
      logBox.scrollTop = logBox.scrollHeight;
    }
  }, [logs]);

  // Auto-scroll static output when it updates
  useEffect(() => {
    const outputBox = document.getElementById("static-output-box");
    if (outputBox) {
      outputBox.scrollTop = outputBox.scrollHeight;
    }
  }, [logOutput]);

  // Update progress from WebSocket
  useEffect(() => {
    if (latestStats) {
      setTotal(latestStats.totalFiles || 0);
      setCompleted(latestStats.processedFiles || 0);
      setFailed(latestStats.failedFiles || 0);
    }
  }, [latestStats]);

  // Production-Ready Injection Function with Real-time WebSocket Logs
  const runInjection = async () => {
    setLoading(true);
    setIsRunning(true);
    clearLogs(); // Clear WebSocket logs
    setLogOutput(''); // Clear static output

    // Reset progress
    setCompleted(0);
    setFailed(0);
    setTotal(10);

    try {
      // Frontend-compatible payload matching the exact schema
      const payload = {
        prompt: prompt, // Use current prompt from form
        file_path: sourceFolder, // Use current source folder
        model: model, // Use current model selection
        apply: apply, // Use current apply/preview mode
        tags: tag ? [tag] : [] // Convert single tag to array
      };

      console.log('🚀 Starting injection with real-time logs:', payload);

      // Send to the updated /inject endpoint
      const response = await fetch('http://localhost:8001/inject', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Success handling
        console.log('✅ Injection completed successfully!', result);

        // Update progress from result
        setTotal(result.total_files || 0);
        setCompleted(result.successful_files || 0);
        setFailed(result.failed_files || 0);

        // Set final result output
        const finalOutput = [
          `✅ INJECTION COMPLETED SUCCESSFULLY`,
          ``,
          `📊 STATISTICS:`,
          `   • Total Files: ${result.total_files}`,
          `   • Successful: ${result.successful_files}`,
          `   • Failed: ${result.failed_files}`,
          `   • Total Injections: ${result.total_injections}`,
          `   • Execution Time: ${result.execution_time?.toFixed(2)}s`,
          ``,
          `📁 MODIFIED FILES:`,
          ...(result.modified?.map((file: string) => `   ✏️ ${file}`) || ['   (No files modified)']),
          ``,
          `📝 DETAILED LOGS:`,
          ...(result.logs || [])
        ].join('\n');

        setLogOutput(finalOutput);

      } else {
        // Error handling
        console.error('❌ Injection failed:', result);

        // Set error output
        const errorOutput = [
          `❌ INJECTION FAILED`,
          ``,
          `🔍 ERROR DETAILS:`,
          `   • Message: ${result.message || 'Unknown error'}`,
          `   • Error: ${result.error || 'No specific error'}`,
          `   • Execution Time: ${result.execution_time?.toFixed(2)}s`,
          ``,
          `📝 ERROR LOGS:`,
          ...(result.logs || [])
        ].join('\n');

        setLogOutput(errorOutput);
      }
    } catch (error: any) {
      console.error('💥 Network or server error:', error);

      // Enhanced error handling with specific messages
      let errorMessage = '💥 NETWORK ERROR\n\n';

      if (error.message?.includes('fetch')) {
        errorMessage += `Connection Error: Cannot reach backend server\n\n`;
        errorMessage += `Troubleshooting:\n`;
        errorMessage += `• Check if backend is running: uvicorn main:app --host 0.0.0.0 --port 8001\n`;
        errorMessage += `• Verify URL: http://localhost:8001/inject\n`;
        errorMessage += `• Check network connectivity\n`;
        errorMessage += `• Verify CORS configuration`;
      } else if (error.response?.status === 404) {
        errorMessage += `Endpoint Not Found: /inject endpoint not available\n\n`;
        errorMessage += `Troubleshooting:\n`;
        errorMessage += `• Verify backend has /inject endpoint\n`;
        errorMessage += `• Check API documentation at http://localhost:8001/docs\n`;
        errorMessage += `• Ensure latest backend version is running`;
      } else if (error.response?.status === 500) {
        errorMessage += `Server Error: Backend internal error\n\n`;
        errorMessage += `Details: ${error.response?.data?.detail || 'Unknown server error'}\n\n`;
        errorMessage += `Troubleshooting:\n`;
        errorMessage += `• Check backend logs for detailed error information\n`;
        errorMessage += `• Verify all dependencies are installed\n`;
        errorMessage += `• Check file permissions and paths`;
      } else {
        errorMessage += `Error: ${error.message || error}\n\n`;
        errorMessage += `Troubleshooting:\n`;
        errorMessage += `• Check browser console for detailed error\n`;
        errorMessage += `• Verify request payload format\n`;
        errorMessage += `• Check network connectivity`;
      }

      setLogOutput(errorMessage);
    } finally {
      setLoading(false);
      setIsRunning(false);
    }
  };

  // Legacy function for backward compatibility
  const handleStartInjection = runInjection;

  const handleStopInjection = () => {
    setIsRunning(false);
    // In a real implementation, this would send a stop signal to the backend
  };

  const handleTestMessage = () => {
    const testMessages = [
      "🧪 Test message from Dashboard",
      "🔍 Simulating file scan...",
      "⚡ Processing test file",
      "✅ Test injection complete"
    ];
    const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
    sendTestMessage(randomMessage);
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-100">
      {/* Navigation Bar */}
      {setViewMode && (
        <div className="bg-white/95 backdrop-blur-xl border-b border-gray-200/30 shadow-lg">
          <div className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3">
            {/* Top Row: Logo + Primary Navigation */}
            <div className="flex items-center justify-between mb-2">
              {/* Logo Section - Compact Left */}
              <div className="flex items-center min-w-0">
                <h1 className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-1.5 text-gray-800">
                  <Brain className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-blue-600 flex-shrink-0" />
                  <span className="hidden sm:inline truncate">CodeCrusher Dashboard</span>
                  <span className="sm:hidden truncate">CodeCrusher</span>
                </h1>
              </div>

              {/* Primary Navigation Row - Full Text */}
              <div className="flex-1 flex justify-center mx-4">
                <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-4xl">
                  <Button
                    variant={viewMode === 'main' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('main')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Main Dashboard
                  </Button>
                  <Button
                    variant={viewMode === 'simple' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('simple')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Full Dashboard
                  </Button>
                  <Button
                    variant={viewMode === 'streamlined' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('streamlined')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Streamlined
                  </Button>
                  <Button
                    variant={viewMode === 'clean' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('clean')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Clean
                  </Button>
                  <Button
                    variant={viewMode === 'enhanced' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('enhanced')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Enhanced
                  </Button>
                  <Button
                    variant={viewMode === 'backend' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('backend')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Backend
                  </Button>
                </div>
              </div>

              {/* Right Side Balance */}
              <div className="w-16 sm:w-20 lg:w-24 flex-shrink-0"></div>
            </div>

            {/* Bottom Row: Secondary Navigation */}
            <div className="flex justify-center">
              <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-3xl">
                <Button
                  variant={viewMode === 'ui' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('ui')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  UI
                </Button>
                <Button
                  variant={viewMode === 'status' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('status')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Status
                </Button>
                <Button
                  variant={viewMode === 'stable' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('stable')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Stable
                </Button>
                <Button
                  variant={viewMode === 'enterprise' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('enterprise')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 hover:from-blue-700 hover:to-indigo-700"
                >
                  Enterprise
                </Button>
                <Button
                  variant={viewMode === 'demo' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('demo')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Log Panel Demo
                </Button>
                <Button
                  variant={viewMode === 'styletest' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('styletest')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-yellow-500 text-white border-0 hover:bg-yellow-600"
                >
                  Style Test
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Health Status */}
      {getStatusIcon && getStatusText && (
        <div className="bg-white/90 backdrop-blur-sm border-b border-gray-100 py-2">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
            </div>
            <div className="flex items-center space-x-2">
              {wsConnected ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm font-medium">
                WebSocket {wsConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Health Alert */}
      {healthStatus && !healthStatus.codecrusher_available && (
        <div className="px-4 py-2">
          <Alert>
            <AlertDescription>
              CodeCrusher CLI not found. Please ensure it's installed and available in your PATH or virtual environment.
              {healthStatus.codecrusher_path && ` Detected path: ${healthStatus.codecrusher_path}`}
            </AlertDescription>
          </Alert>
        </div>
      )}

      <div className="flex-1 p-6 space-y-6 max-w-6xl mx-auto">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Brain className="h-8 w-8 text-blue-600" />
            CodeCrusher Dashboard
          </h1>
          <div className="text-sm text-gray-600">
            WebSocket: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </div>
        </div>

      {/* Configuration Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Injection Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <LabelWithTooltip
                htmlFor="source"
                tooltip="Absolute or relative path to your project folder. This is the root directory CodeCrusher will scan for target files."
              >
                Source Folder
              </LabelWithTooltip>
              <Input
                id="source"
                value={sourceFolder}
                onChange={(e) => setSourceFolder(e.target.value)}
                placeholder="Source folder (e.g., ./src)"
              />
            </div>

            <div>
              <LabelWithTooltip
                htmlFor="prompt"
                tooltip="Describe exactly what you want the AI to do. Be surgical. Include file names, features, layout requests, or code changes."
              >
                AI Prompt
              </LabelWithTooltip>
              <Textarea
                id="prompt"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Prompt for AI injection"
                rows={3}
              />
            </div>

            <div>
              <LabelWithTooltip
                htmlFor="tag"
                tooltip="Optional label for this injection. Useful for tracking runs, organizing logs, and versioning changes."
              >
                Injection Tag
              </LabelWithTooltip>
              <Input
                id="tag"
                value={tag}
                onChange={(e) => setTag(e.target.value)}
                placeholder="Injection tag"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch
                  id="recursive"
                  checked={recursive}
                  onCheckedChange={setRecursive}
                />
                <Label htmlFor="recursive">Recursive</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="apply"
                  checked={apply}
                  onCheckedChange={setApply}
                />
                <Label htmlFor="apply">
                  {apply ? 'Apply Changes' : 'Preview Mode'}
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Model Controls */}
        <ModelControls
          model={model}
          setModel={setModel}
          fallbackEnabled={fallbackEnabled}
          setFallbackEnabled={setFallbackEnabled}
        />
      </div>

      {/* Progress Stats */}
      <ProgressStats
        progress={latestProgress}
        total={total}
        completed={completed}
        failed={failed}
        isRunning={isRunning}
      />

      {/* Control Buttons */}
      <div className="flex flex-col gap-4">
        {/* Primary Injection Button */}
        <div className="flex justify-center">
          <Button
            onClick={runInjection}
            disabled={loading || isRunning || !isConnected}
            className="px-12 py-4 text-xl bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0 shadow-lg"
          >
            {loading || isRunning ? (
              <>
                <Loader2 className="mr-3 h-6 w-6 animate-spin" />
                Injecting Code...
              </>
            ) : (
              <>
                <Play className="mr-3 h-6 w-6" />
                🚀 Run Code Injection
              </>
            )}
          </Button>
        </div>

        {/* Secondary Controls */}
        <div className="flex gap-4 justify-center">
          {isRunning && (
            <Button
              onClick={handleStopInjection}
              variant="destructive"
              className="px-6 py-2 text-sm"
            >
              <Square className="mr-2 h-4 w-4" />
              Stop
            </Button>
          )}

          <Button
            onClick={handleTestMessage}
            variant="outline"
            className="px-6 py-2 text-sm"
            disabled={!isConnected}
          >
            Test WebSocket
          </Button>

          <Button
            onClick={clearLogs}
            variant="outline"
            className="px-6 py-2 text-sm"
          >
            Clear Logs
          </Button>
        </div>
      </div>

        {/* Live Logs */}
        <Card>
          <CardHeader>
            <CardTitle>Real-time Injection Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <LiveLogPanel height="400px" />
          </CardContent>
        </Card>

        {/* Real-time WebSocket Logs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Real-time Injection Logs
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm font-normal text-gray-500">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div
              id="websocket-log-box"
              className="bg-black text-green-300 p-4 rounded-md h-64 overflow-auto font-mono text-sm"
            >
              {logs.length > 0 ? (
                <div className="space-y-1">
                  {logs.slice(-50).map((log, index) => (
                    <div key={index} className="whitespace-pre-wrap">
                      {log.timestamp && (
                        <span className="text-gray-400">[{new Date(log.timestamp).toLocaleTimeString()}] </span>
                      )}
                      <span className={
                        log.level === 'error' ? 'text-red-400' :
                        log.level === 'success' ? 'text-green-400' :
                        log.level === 'warning' ? 'text-yellow-400' :
                        'text-green-300'
                      }>
                        {log.message}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-gray-500 italic">
                  {isConnected ? 'Waiting for logs...' : 'WebSocket disconnected. Reconnecting...'}
                </div>
              )}
            </div>
            <div className="flex gap-2 mt-2">
              <Button
                onClick={clearLogs}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Clear Logs
              </Button>
              <Button
                onClick={() => sendTestMessage("🧪 Test message from Dashboard")}
                variant="outline"
                size="sm"
                className="text-xs"
              >
                Test Message
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Injection Output (Static) */}
        {logOutput && (
          <Card>
            <CardHeader>
              <CardTitle>Final Injection Result</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-100 p-4 rounded-md">
                <pre
                  id="static-output-box"
                  className="text-sm font-mono whitespace-pre-wrap overflow-x-auto max-h-96 overflow-y-auto"
                >
                  {logOutput}
                </pre>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Enterprise Footer */}
      <EnterpriseFooter />
    </div>
  );
}
