"""
Rating CLI command for CodeCrusher
AugmentCode Surgical Implementation - STEP 2
Allows users to rate the last injection and provide feedback using SQLite log store
"""

import click
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

# Add parent directory to path to import log_store
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
from log_store import InjectionLogStore

console = Console()

@click.command()
@click.option('--rating', type=int, help='Rating from 1-5 stars')
@click.option('--feedback', help='Optional feedback text')
@click.option('--log-id', type=str, help='Specific log UUID to rate (defaults to last injection)')
@click.option('--list-recent', is_flag=True, help='List recent injections to choose from')
def rate(rating, feedback, log_id, list_recent):
    """Rate the last injection or a specific injection by UUID"""

    try:
        store = InjectionLogStore()

        # List recent injections if requested
        if list_recent:
            _list_recent_injections(store)
            return

        # Get the injection to rate
        if log_id:
            # Find specific injection by UUID
            recent_logs = store.get_recent_logs(100)
            target_log = next((log for log in recent_logs if log['id'] == log_id), None)

            if not target_log:
                console.print(f"[red]❌ No injection found with UUID: {log_id}[/red]")
                return
        else:
            # Get the most recent injection (by timestamp DESC)
            recent_logs = store.get_recent_logs(1)

            if not recent_logs:
                console.print("[red]❌ No injections found to rate[/red]")
                console.print("[dim]Tip: Run an injection first, then use 'codecrusher rate'[/dim]")
                return

            target_log = recent_logs[0]

        # Display injection details
        _display_injection_details(target_log)

        # Get rating if not provided
        if rating is None:
            rating = click.prompt(
                "Rate this injection (1-5 stars)",
                type=click.IntRange(1, 5)
            )

        # Validate rating
        if not 1 <= rating <= 5:
            console.print("[red]❌ Rating must be between 1 and 5[/red]")
            return

        # Get feedback if not provided
        if feedback is None and rating <= 3:
            feedback = click.prompt(
                "Optional feedback (especially helpful for low ratings)",
                default="",
                show_default=False
            )
            if feedback == "":
                feedback = None

        # Save the rating using AugmentCode SQLite log store
        success = store.update_rating(target_log['id'], rating, feedback)

        if success:
            stars = "⭐" * rating
            console.print(f"\n[green]✅ Feedback saved for injection {target_log['id'][:8]}...[/green]")
            console.print(f"[green]Rating: {stars} ({rating}/5)[/green]")

            if feedback:
                console.print(f"[dim]Feedback: {feedback}[/dim]")

            # Show improvement suggestions for low ratings
            if rating <= 2:
                _show_improvement_suggestions(target_log, feedback)
        else:
            console.print("[red]❌ Failed to save rating[/red]")

    except Exception as e:
        console.print(f"[red]❌ Error: {e}[/red]")


def _list_recent_injections(store: InjectionLogStore):
    """List recent injections for user to choose from"""
    recent_logs = store.get_recent_logs(10)

    if not recent_logs:
        console.print("[yellow]No recent injections found[/yellow]")
        return

    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("UUID", style="dim", width=12)
    table.add_column("File", style="cyan", width=20)
    table.add_column("Prompt", style="green", width=30)
    table.add_column("Model", style="blue", width=12)
    table.add_column("Type", style="yellow", width=12)
    table.add_column("Rating", style="red", width=8)

    for log in recent_logs:
        rating_str = f"⭐{log['rating']}" if log['rating'] else "—"
        prompt_preview = log['prompt'][:27] + "..." if len(log['prompt']) > 30 else log['prompt']
        filename_preview = log['filename'][:17] + "..." if len(log['filename']) > 20 else log['filename']

        table.add_row(
            log['id'][:8] + "...",  # Show first 8 chars of UUID
            filename_preview,
            prompt_preview,
            log['model'],
            log['injection_type'],
            rating_str
        )

    console.print(Panel(table, title="[bold]Recent Injections[/bold]"))
    console.print("\n[dim]Use: codecrusher rate --log-id <UUID> --rating <1-5>[/dim]")


def _display_injection_details(log: dict):
    """Display details of the injection being rated"""
    details_table = Table(show_header=False, box=None, padding=(0, 1))
    details_table.add_column("Field", style="cyan", width=15)
    details_table.add_column("Value", style="white")

    details_table.add_row("UUID:", log['id'][:8] + "...")
    details_table.add_row("File:", log['filename'])
    details_table.add_row("Prompt:", log['prompt'][:100] + "..." if len(log['prompt']) > 100 else log['prompt'])
    details_table.add_row("Model:", log['model'])
    details_table.add_row("Type:", log['injection_type'])
    details_table.add_row("Timestamp:", log['timestamp'][:19])  # Show date/time without microseconds

    if log['tags']:
        # Tags are stored as comma-separated string in AugmentCode format
        tags = log['tags'] if isinstance(log['tags'], list) else log['tags'].split(',')
        details_table.add_row("Tags:", ", ".join(tags))

    current_rating = f"⭐{log['rating']}" if log['rating'] else "Not rated"
    details_table.add_row("Current Rating:", current_rating)

    if log['feedback']:
        details_table.add_row("Feedback:", log['feedback'][:50] + "..." if len(log['feedback']) > 50 else log['feedback'])

    console.print(Panel(details_table, title="[bold]Injection Details[/bold]"))


def _show_improvement_suggestions(log: dict, feedback: str):
    """Show improvement suggestions for low-rated injections"""
    suggestions = []

    # Analyze the injection and feedback for suggestions
    feedback_lower = (feedback or "").lower()

    if 'error' in feedback_lower or 'exception' in feedback_lower:
        suggestions.append("Consider adding more comprehensive error handling")

    if 'null' in feedback_lower or 'none' in feedback_lower:
        suggestions.append("Add null/None checks before accessing properties")

    if 'performance' in feedback_lower or 'slow' in feedback_lower:
        suggestions.append("Focus on algorithmic optimization and performance")

    if 'unclear' in feedback_lower or 'confusing' in feedback_lower:
        suggestions.append("Add more detailed comments and documentation")

    if 'incomplete' in feedback_lower or 'missing' in feedback_lower:
        suggestions.append("Ensure all edge cases and requirements are addressed")

    # Add general suggestions based on injection type
    if log['injection_type'] == 'bugfix':
        suggestions.append("Include test cases to verify the fix")
    elif log['injection_type'] == 'optimize':
        suggestions.append("Measure and document performance improvements")
    elif log['injection_type'] == 'refactor':
        suggestions.append("Maintain existing functionality while improving structure")

    if suggestions:
        console.print("\n[yellow]💡 Improvement suggestions for future similar injections:[/yellow]")
        for i, suggestion in enumerate(suggestions[:3], 1):  # Limit to top 3
            console.print(f"   {i}. {suggestion}")


@click.command()
def stats():
    """Show injection statistics and ratings summary using AugmentCode SQLite log store"""
    try:
        store = InjectionLogStore()
        stats = store.get_statistics()

        # Overall statistics
        stats_table = Table(show_header=False, box=None, padding=(0, 1))
        stats_table.add_column("Metric", style="cyan", width=20)
        stats_table.add_column("Value", style="green")

        stats_table.add_row("Total Injections:", str(stats['total_injections']))
        stats_table.add_row("Learned Entries:", str(stats['learned_count']))

        if stats['average_rating']:
            stars = "⭐" * int(stats['average_rating'])
            stats_table.add_row("Average Rating:", f"{stats['average_rating']:.1f} {stars}")

        console.print(Panel(stats_table, title="[bold]Overall Statistics[/bold]"))

        # Rating distribution
        if stats['rating_distribution']:
            rating_table = Table(show_header=True, header_style="bold yellow")
            rating_table.add_column("Rating", style="cyan")
            rating_table.add_column("Count", style="green")
            rating_table.add_column("Percentage", style="blue")

            total_rated = sum(stats['rating_distribution'].values())
            for rating, count in sorted(stats['rating_distribution'].items()):
                percentage = (count / total_rated) * 100
                stars = "⭐" * rating
                rating_table.add_row(f"{rating} {stars}", str(count), f"{percentage:.1f}%")

            console.print(Panel(rating_table, title="[bold]Rating Distribution[/bold]"))

        # Model usage
        if stats['model_usage']:
            model_table = Table(show_header=True, header_style="bold blue")
            model_table.add_column("Model", style="cyan")
            model_table.add_column("Usage Count", style="green")

            for model, count in stats['model_usage'].items():
                model_table.add_row(model, str(count))

            console.print(Panel(model_table, title="[bold]Model Usage[/bold]"))

        # Injection types
        if stats['injection_types']:
            type_table = Table(show_header=True, header_style="bold magenta")
            type_table.add_column("Injection Type", style="cyan")
            type_table.add_column("Count", style="green")

            for injection_type, count in stats['injection_types'].items():
                type_table.add_row(injection_type, str(count))

            console.print(Panel(type_table, title="[bold]Injection Types[/bold]"))

    except Exception as e:
        console.print(f"[red]❌ Error getting statistics: {e}[/red]")


if __name__ == '__main__':
    rate()
