<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Intelligence Hub</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧠 Intelligence Hub WebSocket Test</h1>
        
        <div id="status" class="status disconnected">
            ❌ Disconnected
        </div>
        
        <div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <h3>Connection Log:</h3>
        <div id="log" class="log"></div>
        
        <h3>Last Message:</h3>
        <div id="lastMessage" class="log">No messages received yet</div>
    </div>

    <script>
        let ws = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }
        
        function updateStatus(status, message) {
            const statusDiv = document.getElementById('status');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            
            statusDiv.className = `status ${status}`;
            
            switch(status) {
                case 'connected':
                    statusDiv.innerHTML = '✅ Connected';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    break;
                case 'connecting':
                    statusDiv.innerHTML = '🔄 Connecting...';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = true;
                    break;
                case 'disconnected':
                    statusDiv.innerHTML = '❌ Disconnected';
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    break;
            }
            
            if (message) {
                log(message);
            }
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected');
                return;
            }
            
            updateStatus('connecting', 'Attempting to connect to ws://localhost:8001/ws/intelligence');
            
            try {
                ws = new WebSocket('ws://localhost:8001/ws/intelligence');
                
                ws.onopen = function(event) {
                    updateStatus('connected', '🎉 WebSocket connection established!');
                    reconnectAttempts = 0;
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`📨 Message received: ${data.type}`);
                        
                        document.getElementById('lastMessage').innerHTML = 
                            `<strong>Type:</strong> ${data.type}<br>` +
                            `<strong>Data:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>`;
                        
                        if (data.type === 'welcome') {
                            log('🎊 Welcome message received with intelligence data');
                        } else if (data.type === 'update') {
                            log('🔄 Intelligence data update received');
                        }
                    } catch (e) {
                        log(`❌ Failed to parse message: ${e.message}`);
                        document.getElementById('lastMessage').innerHTML = event.data;
                    }
                };
                
                ws.onclose = function(event) {
                    updateStatus('disconnected', `🔌 Connection closed (Code: ${event.code}, Reason: ${event.reason || 'Unknown'})`);
                    
                    // Auto-reconnect logic
                    if (reconnectAttempts < maxReconnectAttempts && event.code !== 1000) {
                        reconnectAttempts++;
                        const delay = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000);
                        log(`🔄 Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts}/${maxReconnectAttempts})`);
                        setTimeout(connect, delay);
                    }
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error.message || 'Connection failed'}`);
                    updateStatus('disconnected');
                };
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`);
                updateStatus('disconnected');
            }
        }
        
        function disconnect() {
            if (ws) {
                reconnectAttempts = maxReconnectAttempts; // Prevent auto-reconnect
                ws.close(1000, 'Manual disconnect');
                ws = null;
            }
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Auto-connect on page load
        window.onload = function() {
            log('🚀 WebSocket test page loaded');
            log('🎯 Target: ws://localhost:8001/ws/intelligence');
            connect();
        };
        
        // Cleanup on page unload
        window.onbeforeunload = function() {
            if (ws) {
                ws.close(1000, 'Page unload');
            }
        };
    </script>
</body>
</html>
