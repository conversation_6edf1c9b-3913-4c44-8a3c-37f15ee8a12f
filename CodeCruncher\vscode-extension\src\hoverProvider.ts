import * as vscode from 'vscode';
import { CodeCrusherApi, TelemetryEntry } from './api';

/**
 * Register hover provider for CodeCrusher injections
 */
export function registerHoverProvider(context: vscode.ExtensionContext) {
    const hoverProvider = new CodeCrusherHoverProvider();

    context.subscriptions.push(
        vscode.languages.registerHoverProvider(
            { scheme: 'file' },
            hoverProvider
        )
    );

    return hoverProvider;
}

/**
 * Hover provider for CodeCrusher injections
 */
export class CodeCrusherHoverProvider implements vscode.HoverProvider {
    private _telemetryEntries: TelemetryEntry[] = [];
    private _refreshInterval?: NodeJS.Timeout;

    constructor() {
        // Refresh telemetry data every 5 minutes
        this._refreshInterval = setInterval(() => this.refreshTelemetryData(), 5 * 60 * 1000);

        // Initial refresh
        this.refreshTelemetryData();
    }

    /**
     * Refresh telemetry data
     */
    public async refreshTelemetryData(): Promise<void> {
        try {
            // Check if server is running
            const isServerRunning = await CodeCrusherApi.isServerRunning();
            if (!isServerRunning) {
                this._telemetryEntries = [];
                return;
            }

            // Get telemetry data
            const telemetryData = await CodeCrusherApi.getTelemetry(500);
            this._telemetryEntries = telemetryData.entries;
        } catch (error) {
            console.error('Error refreshing telemetry data:', error);
            this._telemetryEntries = [];
        }
    }

    /**
     * Provide hover information for the given position
     */
    public async provideHover(
        document: vscode.TextDocument,
        position: vscode.Position,
        token: vscode.CancellationToken
    ): Promise<vscode.Hover | null> {
        if (this._telemetryEntries.length === 0) {
            return null;
        }

        const filePath = document.uri.fsPath;
        const lineNumber = position.line + 1; // Convert to 1-based line number

        // Find telemetry entries for this file and line
        const entries = this._telemetryEntries.filter(entry =>
            entry.file_path && entry.line_number &&
            (entry.file_path === filePath || filePath.endsWith(entry.file_path)) &&
            entry.line_number === lineNumber
        );

        if (entries.length === 0) {
            return null;
        }

        // Create hover content
        const markdownContent: vscode.MarkdownString[] = [];

        for (const entry of entries) {
            const markdown = new vscode.MarkdownString();
            markdown.isTrusted = true;

            // Add header
            markdown.appendMarkdown(`## CodeCrusher Injection\n\n`);

            // Add timestamp
            const timestamp = new Date(entry.timestamp).toLocaleString();
            markdown.appendMarkdown(`**Timestamp:** ${timestamp}\n\n`);

            // Add model info
            markdown.appendMarkdown(`**Model:** ${entry.model} (${entry.provider})\n\n`);

            // Add token usage
            markdown.appendMarkdown(`**Tokens:** ${entry.tokens_in} in / ${entry.tokens_out} out\n\n`);

            // Add cache info
            markdown.appendMarkdown(`**Cached:** ${entry.cached ? 'Yes' : 'No'}\n\n`);

            // Add fallback info if applicable
            if (entry.fallback) {
                markdown.appendMarkdown(`⚠️ **Fallback Used**\n\n`);
                markdown.appendMarkdown(`This injection used a fallback model because the primary model was unavailable or encountered an error.\n\n`);
                markdown.appendMarkdown(`**Suggestion:** Consider retrying with a specific model using the command palette.\n\n`);
            }

            // Add error info if applicable
            if (entry.error) {
                markdown.appendMarkdown(`❌ **Error:** ${entry.error}\n\n`);
                markdown.appendMarkdown(`**Suggestion:** Check the error message and retry the injection.\n\n`);
            }

            // Add tag info
            if (entry.tags && entry.tags.length > 0) {
                markdown.appendMarkdown(`**Tags:** ${entry.tags.join(', ')}\n\n`);
            } else {
                markdown.appendMarkdown(`⚠️ **Missing Tags**\n\n`);
                markdown.appendMarkdown(`**Suggestion:** Add tags to this injection for better tracking and analysis.\n\n`);
            }

            // Add actions
            markdown.appendMarkdown(`---\n\n`);
            markdown.appendMarkdown(`[Resend Injection](command:codecrusher.resendInjection?${encodeURIComponent(JSON.stringify([entry]))})`);

            markdownContent.push(markdown);
        }

        return new vscode.Hover(markdownContent);
    }

    /**
     * Dispose of resources
     */
    public dispose() {
        if (this._refreshInterval) {
            clearInterval(this._refreshInterval);
        }
    }
}
