import React from 'react';
import LiveLogPanel from '../components/LiveLogPanel';

/**
 * Simple Dashboard Example
 * 
 * This demonstrates the minimal setup required to use the LiveLogPanel
 * component for real-time log streaming from CodeCrusher.
 */
function SimpleDashboard() {
  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1 style={{ marginBottom: '20px', color: '#333' }}>
        CodeCrusher Simple Dashboard
      </h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2 style={{ marginBottom: '10px', color: '#666' }}>
          Real-time Injection Logs
        </h2>
        <LiveLogPanel />
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
        <h3 style={{ marginBottom: '10px', color: '#333' }}>Usage:</h3>
        <pre style={{ backgroundColor: '#fff', padding: '10px', borderRadius: '4px', overflow: 'auto' }}>
{`import LiveLogPanel from './components/LiveLogPanel';

function Dashboard() {
  return (
    <div>
      <h2>Real-time Injection Logs</h2>
      <LiveLogPanel />
    </div>
  );
}

export default Dashboard;`}
        </pre>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#e8f4fd', borderRadius: '4px' }}>
        <h3 style={{ marginBottom: '10px', color: '#333' }}>Features:</h3>
        <ul style={{ margin: 0, paddingLeft: '20px' }}>
          <li>Auto-scroll to latest logs</li>
          <li>Connection status indicator</li>
          <li>Terminal-style appearance</li>
          <li>Minimal setup required</li>
          <li>Real-time WebSocket streaming</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#fff3cd', borderRadius: '4px' }}>
        <h3 style={{ marginBottom: '10px', color: '#333' }}>Customization:</h3>
        <pre style={{ backgroundColor: '#fff', padding: '10px', borderRadius: '4px', overflow: 'auto' }}>
{`// Custom height and styling
<LiveLogPanel 
  height="400px" 
  className="my-custom-class"
/>

// Custom WebSocket URL
<LiveLogPanel 
  wsUrl="ws://your-server:8000/ws/logs"
/>`}
        </pre>
      </div>
    </div>
  );
}

export default SimpleDashboard;
