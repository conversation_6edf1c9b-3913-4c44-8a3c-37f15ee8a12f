import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export interface TeamPermissions {
  canEditTuning: boolean;
  canInviteMembers: boolean;
  canManageTeam: boolean;
  canViewSensitiveData: boolean;
  canDeleteTeam: boolean;
  canChangeRoles: boolean;
  isReadOnly: boolean;
  roleLevel: number; // 0 = viewer, 1 = member, 2 = owner
}

export interface UseTeamPermissionsProps {
  userRole: string;
  teamAdminOnly?: boolean;
}

/**
 * Hook to determine user permissions within a team context
 */
export function useTeamPermissions({ 
  userRole, 
  teamAdminOnly = false 
}: UseTeamPermissionsProps): TeamPermissions {
  const { user } = useAuth();

  return useMemo(() => {
    const isOwner = userRole === 'owner';
    const isMember = userRole === 'member';
    const isViewer = userRole === 'viewer';

    // Role hierarchy levels
    const roleLevel = isOwner ? 2 : isMember ? 1 : 0;

    // Base permissions by role
    const basePermissions = {
      owner: {
        canEditTuning: true,
        canInviteMembers: true,
        canManageTeam: true,
        canViewSensitiveData: true,
        canDeleteTeam: true,
        canChangeRoles: true,
        isReadOnly: false,
      },
      member: {
        canEditTuning: true,
        canInviteMembers: false,
        canManageTeam: false,
        canViewSensitiveData: true,
        canDeleteTeam: false,
        canChangeRoles: false,
        isReadOnly: false,
      },
      viewer: {
        canEditTuning: false,
        canInviteMembers: false,
        canManageTeam: false,
        canViewSensitiveData: false,
        canDeleteTeam: false,
        canChangeRoles: false,
        isReadOnly: true,
      }
    };

    // Get base permissions for role
    const permissions = basePermissions[userRole as keyof typeof basePermissions] || basePermissions.viewer;

    // Apply admin-only restrictions
    if (teamAdminOnly && !isOwner) {
      return {
        ...permissions,
        canEditTuning: false,
        canInviteMembers: false,
        canManageTeam: false,
        isReadOnly: true,
        roleLevel
      };
    }

    return {
      ...permissions,
      roleLevel
    };
  }, [userRole, teamAdminOnly]);
}

/**
 * Get role display information
 */
export function getRoleInfo(role: string) {
  const roleInfo = {
    owner: {
      label: 'Owner',
      description: 'Full control over team settings and members',
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
      icon: '👑'
    },
    member: {
      label: 'Member',
      description: 'Can participate in team activities and view most data',
      color: 'text-blue-600 bg-blue-50 border-blue-200',
      icon: '👤'
    },
    viewer: {
      label: 'Viewer',
      description: 'Read-only access to team activities',
      color: 'text-gray-600 bg-gray-50 border-gray-200',
      icon: '👁️'
    }
  };

  return roleInfo[role as keyof typeof roleInfo] || roleInfo.viewer;
}

/**
 * Check if user can perform a specific action
 */
export function canPerformAction(
  permissions: TeamPermissions,
  action: keyof Omit<TeamPermissions, 'roleLevel' | 'isReadOnly'>
): boolean {
  return permissions[action] === true;
}

/**
 * Get permission level description
 */
export function getPermissionDescription(permissions: TeamPermissions): string {
  if (permissions.canManageTeam) {
    return 'Full team management access';
  } else if (permissions.canEditTuning) {
    return 'Can edit team settings';
  } else if (permissions.canViewSensitiveData) {
    return 'Can view team data';
  } else {
    return 'Read-only access';
  }
}

/**
 * Get restricted action message
 */
export function getRestrictedMessage(action: string, userRole: string): string {
  const messages = {
    editTuning: `Only team owners can modify tuning settings. Your role: ${userRole}`,
    inviteMembers: `Only team owners can invite new members. Your role: ${userRole}`,
    manageTeam: `Only team owners can manage team settings. Your role: ${userRole}`,
    deleteTeam: `Only team owners can delete teams. Your role: ${userRole}`,
    changeRoles: `Only team owners can change member roles. Your role: ${userRole}`
  };

  return messages[action as keyof typeof messages] || `This action requires higher permissions. Your role: ${userRole}`;
}
