#!/usr/bin/env python3
"""
CodeCrusher pre-commit hook

This hook scans staged files for injection points, adds recommended tags,
and warns about possible anomalies.
"""

import os
import sys
import re
import subprocess
import json
import tempfile
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Set

# Constants
CONFIG_DIR = os.path.expanduser("~/.codecrusher")
TELEMETRY_FILE = os.path.join(CONFIG_DIR, "telemetry.jsonl")
HOOK_CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "pre-commit.config.json")

# Injection markers
INJECTION_MARKERS = [
    r"# AI_INJECT: (\w+)",  # Python
    r"// AI_INJECT: (\w+)",  # JS, TS, Java, C#, etc.
    r"/\* AI_INJECT: (\w+) \*/",  # CSS, C, etc.
    r"<!-- AI_INJECT: (\w+) -->",  # HTML, XML, etc.
    r"-- AI_INJECT: (\w+)",  # SQL
    r"' AI_INJECT: (\w+)",  # VB
]

# Suspicious patterns
SUSPICIOUS_PATTERNS = [
    r"(token|api[_-]?key|secret|password|credential)",  # Sensitive data
    r"(rm -rf|DROP DATABASE|DELETE FROM.*WHERE|FORMAT C:)",  # Destructive commands
    r"(eval\(|exec\(|system\(|subprocess\.call\()",  # Code execution
]

def load_hook_config() -> Dict[str, Any]:
    """Load hook configuration."""
    if not os.path.exists(HOOK_CONFIG_FILE):
        return {
            "block_on_errors": False,
            "auto_insert_tags": False
        }

    try:
        with open(HOOK_CONFIG_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {
            "block_on_errors": False,
            "auto_insert_tags": False
        }

def get_staged_files() -> List[str]:
    """Get list of staged files."""
    result = subprocess.run(
        ["git", "diff", "--cached", "--name-only", "--diff-filter=ACMR"],
        capture_output=True,
        text=True
    )

    if result.returncode != 0:
        print(f"Error getting staged files: {result.stderr}")
        return []

    return result.stdout.strip().split("\n")

def is_supported_file(file_path: str) -> bool:
    """Check if file is supported for injection."""
    supported_extensions = [
        ".py", ".js", ".ts", ".jsx", ".tsx", ".java", ".cs", ".go",
        ".rb", ".php", ".cpp", ".c", ".h", ".hpp", ".html", ".xml",
        ".css", ".scss", ".sass", ".less", ".sql", ".md", ".rst",
        ".json", ".yaml", ".yml", ".toml", ".sh", ".bash", ".ps1"
    ]

    return any(file_path.endswith(ext) for ext in supported_extensions)

def get_file_content(file_path: str) -> str:
    """Get content of a staged file."""
    result = subprocess.run(
        ["git", "show", f":0:{file_path}"],
        capture_output=True,
        text=True
    )

    if result.returncode != 0:
        print(f"Error getting content of {file_path}: {result.stderr}")
        return ""

    return result.stdout

def get_comment_style(file_path: str) -> str:
    """Get comment style based on file extension."""
    if file_path.endswith((".py", ".rb", ".sh", ".bash", ".yaml", ".yml")):
        return "#"
    elif file_path.endswith((".js", ".ts", ".jsx", ".tsx", ".java", ".cs", ".go", ".cpp", ".c", ".h", ".hpp", ".css", ".scss", ".sass", ".less")):
        return "//"
    elif file_path.endswith((".html", ".xml")):
        return "<!--"
    elif file_path.endswith((".sql")):
        return "--"
    elif file_path.endswith((".ps1")):
        return "#"
    else:
        return "#"

def scan_for_injection_points(content: str, file_path: str) -> List[Dict[str, Any]]:
    """Scan file content for potential injection points."""
    injection_points = []

    # Determine comment style based on file extension
    comment_prefix = get_comment_style(file_path)

    # Look for existing injection tags
    existing_tags = set()
    for marker in INJECTION_MARKERS:
        for match in re.finditer(marker, content):
            existing_tags.add(match.group(1))

    # Look for function definitions without tags
    if file_path.endswith(".py"):
        # Python function definitions
        function_matches = re.finditer(
            r"def\s+(\w+)\s*\(.*\):",
            content
        )

        for match in function_matches:
            func_name = match.group(1)
            line_num = content[:match.start()].count("\n") + 1

            # Check if this function already has a tag
            has_tag = func_name in existing_tags

            if not has_tag:
                injection_points.append({
                    "type": "function",
                    "name": func_name,
                    "line": line_num,
                    "suggested_tag": func_name
                })

    elif file_path.endswith((".js", ".ts", ".jsx", ".tsx")):
        # JavaScript/TypeScript function definitions
        function_matches = re.finditer(
            r"(function\s+(\w+)|const\s+(\w+)\s*=\s*(?:function|\([^)]*\)\s*=>)|(\w+)\s*:\s*function)",
            content
        )

        for match in function_matches:
            func_name = match.group(2) or match.group(3) or match.group(4)
            if func_name:
                line_num = content[:match.start()].count("\n") + 1

                # Check if this function already has a tag
                has_tag = func_name in existing_tags

                if not has_tag:
                    injection_points.append({
                        "type": "function",
                        "name": func_name,
                        "line": line_num,
                        "suggested_tag": func_name
                    })

    # Look for TODO comments
    todo_pattern = rf"{re.escape(comment_prefix)}\s*(TODO|FIXME):\s*(.*)"
    todo_matches = re.finditer(todo_pattern, content)

    for match in todo_matches:
        todo_text = match.group(2)
        line_num = content[:match.start()].count("\n") + 1

        # Generate a tag name from the TODO text
        tag_name = re.sub(r"[^a-zA-Z0-9_]", "_", todo_text.lower())
        tag_name = re.sub(r"_+", "_", tag_name)
        tag_name = tag_name[:20].strip("_")

        injection_points.append({
            "type": "todo",
            "text": todo_text,
            "line": line_num,
            "suggested_tag": tag_name
        })

    # Look for class definitions without tags
    if file_path.endswith(".py"):
        # Python class definitions
        class_matches = re.finditer(
            r"class\s+(\w+)(?:\([^)]*\))?:",
            content
        )

        for match in class_matches:
            class_name = match.group(1)
            line_num = content[:match.start()].count("\n") + 1

            # Check if this class already has a tag
            has_tag = class_name in existing_tags

            if not has_tag:
                injection_points.append({
                    "type": "class",
                    "name": class_name,
                    "line": line_num,
                    "suggested_tag": class_name
                })

    return injection_points

def scan_for_anomalies(content: str, file_path: str) -> List[Dict[str, Any]]:
    """Scan file content for potential anomalies."""
    anomalies = []

    # Look for suspicious patterns
    for pattern in SUSPICIOUS_PATTERNS:
        for match in re.finditer(pattern, content, re.IGNORECASE):
            line_num = content[:match.start()].count("\n") + 1
            matched_text = match.group(0)

            anomalies.append({
                "type": "suspicious_pattern",
                "text": matched_text,
                "line": line_num,
                "severity": "warning"
            })

    # Look for unusually large code blocks
    lines = content.split("\n")
    if len(lines) > 1000:
        anomalies.append({
            "type": "large_file",
            "text": f"File has {len(lines)} lines",
            "line": 1,
            "severity": "info"
        })

    # Look for multiple injection tags
    injection_count = 0
    for marker in INJECTION_MARKERS:
        injection_count += len(re.findall(marker, content))

    if injection_count > 5:
        anomalies.append({
            "type": "multiple_injections",
            "text": f"File has {injection_count} injection tags",
            "line": 1,
            "severity": "info"
        })

    return anomalies

def auto_insert_tag(file_path: str, line: int, tag: str) -> bool:
    """
    Automatically insert a tag at the specified line.

    Args:
        file_path: Path to the file
        line: Line number to insert the tag
        tag: Tag to insert

    Returns:
        True if successful, False otherwise
    """
    try:
        # Get the file content
        content = get_file_content(file_path)
        lines = content.split("\n")

        # Determine comment style
        comment_style = get_comment_style(file_path)

        # Create the tag line
        if comment_style == "<!--":
            tag_line = f"{comment_style} AI_INJECT: {tag} -->"
        else:
            tag_line = f"{comment_style} AI_INJECT: {tag}"

        # Insert the tag line before the specified line
        lines.insert(line - 1, tag_line)

        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode="w", delete=False) as temp_file:
            temp_file.write("\n".join(lines))
            temp_path = temp_file.name

        # Stage the changes
        subprocess.run(["git", "add", file_path], check=True)

        # Replace the staged content
        subprocess.run(["git", "hash-object", "-w", temp_path], capture_output=True, text=True, check=True)
        result = subprocess.run(["git", "hash-object", "-w", temp_path], capture_output=True, text=True, check=True)
        hash_value = result.stdout.strip()

        subprocess.run(["git", "update-index", "--add", "--cacheinfo", f"100644,{hash_value},{file_path}"], check=True)

        # Clean up
        os.unlink(temp_path)

        return True
    except Exception as e:
        print(f"Error inserting tag: {str(e)}")
        return False

def get_current_branch() -> str:
    """Get the current Git branch name."""
    result = subprocess.run(
        ["git", "rev-parse", "--abbrev-ref", "HEAD"],
        capture_output=True,
        text=True
    )

    if result.returncode != 0:
        print(f"Error getting current branch: {result.stderr}")
        return "unknown"

    return result.stdout.strip()

def main():
    print("Running CodeCrusher pre-commit hook...")

    # Load hook configuration
    config = load_hook_config()
    block_on_errors = config.get("block_on_errors", False)
    auto_insert_tags = config.get("auto_insert_tags", False)

    # Get current branch
    branch = get_current_branch()
    print(f"Current branch: {branch}")

    # Get staged files
    staged_files = get_staged_files()
    if not staged_files:
        print("No staged files found.")
        return 0

    print(f"Found {len(staged_files)} staged files.")

    # Scan each file for injection points and anomalies
    found_injection_points = False
    found_anomalies = False
    critical_anomalies = False

    for file_path in staged_files:
        if not is_supported_file(file_path):
            continue

        print(f"Scanning {file_path}...")
        content = get_file_content(file_path)

        if not content:
            continue

        # Scan for injection points
        injection_points = scan_for_injection_points(content, file_path)

        if injection_points:
            found_injection_points = True
            print(f"\n[Potential injection points in {file_path}]")

            for point in injection_points:
                if point["type"] == "function":
                    print(f"  Line {point['line']}: Function '{point['name']}'")
                    print(f"    Suggested tag: # AI_INJECT: {point['suggested_tag']}")

                    # Auto-insert tag if enabled
                    if auto_insert_tags:
                        if auto_insert_tag(file_path, point["line"], point["suggested_tag"]):
                            print(f"    [Auto-inserted tag: # AI_INJECT: {point['suggested_tag']}]")

                elif point["type"] == "todo":
                    print(f"  Line {point['line']}: TODO '{point['text']}'")
                    print(f"    Suggested tag: # AI_INJECT: {point['suggested_tag']}")

                    # Auto-insert tag if enabled
                    if auto_insert_tags:
                        if auto_insert_tag(file_path, point["line"], point["suggested_tag"]):
                            print(f"    [Auto-inserted tag: # AI_INJECT: {point['suggested_tag']}]")

                elif point["type"] == "class":
                    print(f"  Line {point['line']}: Class '{point['name']}'")
                    print(f"    Suggested tag: # AI_INJECT: {point['suggested_tag']}")

                    # Auto-insert tag if enabled
                    if auto_insert_tags:
                        if auto_insert_tag(file_path, point["line"], point["suggested_tag"]):
                            print(f"    [Auto-inserted tag: # AI_INJECT: {point['suggested_tag']}]")

        # Scan for anomalies
        anomalies = scan_for_anomalies(content, file_path)

        if anomalies:
            found_anomalies = True
            print(f"\n[Potential anomalies in {file_path}]")

            for anomaly in anomalies:
                severity_marker = "⚠️" if anomaly["severity"] == "warning" else "ℹ️"
                print(f"  {severity_marker} Line {anomaly['line']}: {anomaly['text']}")

                if anomaly["severity"] == "warning" and anomaly["type"] == "suspicious_pattern":
                    critical_anomalies = True

    if found_injection_points:
        print("\nTo add these tags manually, run:")
        print("  codecrusher inject run -i <file_path> -t \"<prompt>\" --tag <tag_name>")

    if critical_anomalies and block_on_errors:
        print("\n❌ Critical anomalies detected. Commit blocked.")
        print("To override, run with --no-verify or disable blocking in the hook configuration.")
        return 1

    if found_injection_points or found_anomalies:
        print("\nWould you like to continue with the commit? (y/n)")
        response = input().strip().lower()

        if response != "y":
            print("Commit aborted.")
            return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
