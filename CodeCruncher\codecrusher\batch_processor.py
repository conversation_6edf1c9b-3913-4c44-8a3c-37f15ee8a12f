"""
Batch processing utilities for CodeCrusher.

This module provides functionality for asynchronous batch processing of
injection requests with caching, progress tracking, and error handling.
"""

import asyncio
import hashlib
import json
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from rich.console import Console
from rich.progress import Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.prompt import Confirm
from rich.panel import Panel
from rich.table import Table

console = Console()

# Cache configuration
CACHE_FILE = Path.home() / '.codecrusher_batch_cache.json'
CACHE_VERSION = "1.0"

class BatchCache:
    """Cache for batch injection results."""

    def __init__(self, cache_file: Path = CACHE_FILE):
        self.cache_file = cache_file
        self._cache = self._load_cache()

    def _load_cache(self) -> Dict[str, Any]:
        """Load cache from disk."""
        if not self.cache_file.exists():
            return {"version": CACHE_VERSION, "entries": {}}

        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache = json.load(f)
                if cache.get("version") != CACHE_VERSION:
                    console.print("[yellow]Cache version mismatch, clearing cache[/yellow]")
                    return {"version": CACHE_VERSION, "entries": {}}
                return cache
        except Exception as e:
            console.print(f"[yellow]Error loading cache: {e}, starting fresh[/yellow]")
            return {"version": CACHE_VERSION, "entries": {}}

    def _save_cache(self) -> None:
        """Save cache to disk."""
        try:
            self.cache_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self._cache, f, indent=2)
        except Exception as e:
            console.print(f"[yellow]Error saving cache: {e}[/yellow]")

    def _generate_key(self, file_path: Path, prompt: str, model: str, provider: str) -> str:
        """Generate cache key for a file + prompt combination."""
        # Include file modification time to invalidate cache when file changes
        try:
            mtime = file_path.stat().st_mtime
        except:
            mtime = 0

        key_data = f"{file_path}:{prompt}:{model}:{provider}:{mtime}"
        return hashlib.sha256(key_data.encode()).hexdigest()

    def get(self, file_path: Path, prompt: str, model: str, provider: str) -> Optional[Dict[str, Any]]:
        """Get cached result for file + prompt combination."""
        key = self._generate_key(file_path, prompt, model, provider)
        return self._cache["entries"].get(key)

    def set(self, file_path: Path, prompt: str, model: str, provider: str, result: Dict[str, Any]) -> None:
        """Cache result for file + prompt combination."""
        key = self._generate_key(file_path, prompt, model, provider)
        self._cache["entries"][key] = {
            "result": result,
            "timestamp": time.time(),
            "file_path": str(file_path),
            "prompt": prompt,
            "model": model,
            "provider": provider
        }
        self._save_cache()

    def clear(self) -> None:
        """Clear all cache entries."""
        self._cache = {"version": CACHE_VERSION, "entries": {}}
        self._save_cache()

    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        entries = self._cache["entries"]
        return {
            "total_entries": len(entries),
            "cache_file": str(self.cache_file),
            "cache_size_mb": self.cache_file.stat().st_size / (1024 * 1024) if self.cache_file.exists() else 0
        }


class BatchProcessor:
    """Handles batch processing of injection requests."""

    def __init__(self, max_workers: int = 3, use_cache: bool = True):
        self.max_workers = max_workers
        self.use_cache = use_cache
        self.cache = BatchCache() if use_cache else None
        self.results = {}
        self.errors = {}

    async def process_file_injection(
        self,
        file_path: Path,
        injection_tags: List[str],
        prompt: str,
        model: str,
        provider: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process injection for a single file.

        Args:
            file_path: Path to the file to process
            injection_tags: List of injection tags found in the file
            prompt: The prompt to use for injection
            model: AI model to use
            provider: AI provider to use
            **kwargs: Additional arguments for injection

        Returns:
            Dictionary containing injection results
        """
        # Check cache first
        if self.cache:
            cached_result = self.cache.get(file_path, prompt, model, provider)
            if cached_result:
                console.print(f"[dim]Cache hit for {file_path}[/dim]")
                return {
                    "file_path": file_path,
                    "cached": True,
                    "result": cached_result["result"],
                    "injection_tags": injection_tags,
                    "timestamp": cached_result["timestamp"]
                }

        # Simulate AI injection processing
        # In a real implementation, this would call the AI engine
        await asyncio.sleep(0.5)  # Simulate processing time

        # Mock result for demonstration
        result = {
            "success": True,
            "modified_lines": len(injection_tags) * 5,  # Mock: 5 lines per tag
            "tokens_used": len(prompt) + 100,  # Mock token usage
            "model_used": model,
            "provider_used": provider,
            "injection_tags": injection_tags,
            "processing_time": 0.5
        }

        # Cache the result
        if self.cache:
            self.cache.set(file_path, prompt, model, provider, result)

        return {
            "file_path": file_path,
            "cached": False,
            "result": result,
            "injection_tags": injection_tags,
            "timestamp": time.time()
        }

    async def process_batch(
        self,
        files_with_tags: Dict[Path, List[str]],
        prompt: str,
        model: str,
        provider: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a batch of files with injection tags.

        Args:
            files_with_tags: Dictionary mapping file paths to their injection tags
            prompt: The prompt to use for injection
            model: AI model to use
            provider: AI provider to use
            **kwargs: Additional arguments for injection

        Returns:
            Dictionary containing batch processing results
        """
        total_files = len(files_with_tags)

        # Create progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        ) as progress:

            task = progress.add_task(
                f"Processing {total_files} files...",
                total=total_files
            )

            # Create semaphore to limit concurrent operations
            semaphore = asyncio.Semaphore(self.max_workers)

            async def process_with_semaphore(file_path: Path, injection_tags: List[str]):
                async with semaphore:
                    try:
                        result = await self.process_file_injection(
                            file_path, injection_tags, prompt, model, provider, **kwargs
                        )
                        self.results[file_path] = result
                        progress.update(task, advance=1)
                        return result
                    except Exception as e:
                        error_msg = f"Error processing {file_path}: {str(e)}"
                        self.errors[file_path] = error_msg
                        console.print(f"[red]{error_msg}[/red]")
                        progress.update(task, advance=1)
                        return None

            # Process all files concurrently
            tasks = [
                process_with_semaphore(file_path, injection_tags)
                for file_path, injection_tags in files_with_tags.items()
            ]

            await asyncio.gather(*tasks, return_exceptions=True)

        # Calculate statistics
        successful = len(self.results)
        failed = len(self.errors)
        cached_count = sum(1 for r in self.results.values() if r.get("cached", False))

        return {
            "total_files": total_files,
            "successful": successful,
            "failed": failed,
            "cached_hits": cached_count,
            "results": self.results,
            "errors": self.errors
        }


class ParallelInjectionProcessor:
    """Handles parallel AI injection using ThreadPoolExecutor."""

    def __init__(self, max_workers: int = 4, use_cache: bool = True):
        self.max_workers = max_workers
        self.use_cache = use_cache
        self.cache = BatchCache() if use_cache else None
        self._lock = threading.Lock()  # For thread-safe operations

    def _inject_single_file(
        self,
        file_path: Path,
        injection_tags: List[str],
        prompt: str,
        model: str,
        provider: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process injection for a single file in a thread.

        Args:
            file_path: Path to the file to process
            injection_tags: List of injection tags found in the file
            prompt: The prompt to use for injection
            model: AI model to use
            provider: AI provider to use
            **kwargs: Additional arguments for injection

        Returns:
            Dictionary containing injection results
        """
        try:
            # Check cache first (thread-safe)
            if self.cache:
                with self._lock:
                    cached_result = self.cache.get(file_path, prompt, model, provider)
                    if cached_result:
                        console.print(f"[dim]Cache hit for {file_path}[/dim]")
                        return {
                            "file_path": file_path,
                            "cached": True,
                            "result": cached_result["result"],
                            "injection_tags": injection_tags,
                            "timestamp": cached_result["timestamp"],
                            "success": True,
                            "error": None
                        }

            # Simulate AI injection processing
            # In a real implementation, this would call the AI engine
            time.sleep(0.5)  # Simulate processing time

            # Mock result for demonstration
            result = {
                "success": True,
                "modified_lines": len(injection_tags) * 5,  # Mock: 5 lines per tag
                "tokens_used": len(prompt) + 100,  # Mock token usage
                "model_used": model,
                "provider_used": provider,
                "injection_tags": injection_tags,
                "processing_time": 0.5
            }

            # Cache the result (thread-safe)
            if self.cache:
                with self._lock:
                    self.cache.set(file_path, prompt, model, provider, result)

            return {
                "file_path": file_path,
                "cached": False,
                "result": result,
                "injection_tags": injection_tags,
                "timestamp": time.time(),
                "success": True,
                "error": None
            }

        except Exception as e:
            return {
                "file_path": file_path,
                "cached": False,
                "result": None,
                "injection_tags": injection_tags,
                "timestamp": time.time(),
                "success": False,
                "error": str(e)
            }

    def process_parallel_injection(
        self,
        files_with_tags: Dict[Path, List[str]],
        prompt: str,
        model: str,
        provider: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process multiple files in parallel using ThreadPoolExecutor.

        Args:
            files_with_tags: Dictionary mapping file paths to their injection tags
            prompt: The prompt to use for injection
            model: AI model to use
            provider: AI provider to use
            **kwargs: Additional arguments for injection

        Returns:
            Dictionary containing batch processing results
        """
        total_files = len(files_with_tags)
        results = {}
        errors = {}

        console.print(f"\n[bold blue]🚀 Starting parallel injection with {self.max_workers} threads...[/bold blue]")

        # Create progress bar
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=console
        ) as progress:

            task = progress.add_task(
                f"Processing {total_files} files...",
                total=total_files
            )

            # Use ThreadPoolExecutor for parallel processing
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tasks
                future_to_file = {
                    executor.submit(
                        self._inject_single_file,
                        file_path,
                        injection_tags,
                        prompt,
                        model,
                        provider,
                        **kwargs
                    ): file_path
                    for file_path, injection_tags in files_with_tags.items()
                }

                # Process completed tasks
                for future in as_completed(future_to_file):
                    file_path = future_to_file[future]

                    try:
                        result = future.result()

                        if result["success"]:
                            results[file_path] = result
                        else:
                            error_msg = f"Error processing {file_path}: {result['error']}"
                            errors[file_path] = error_msg
                            console.print(f"[red]{error_msg}[/red]")

                        progress.update(task, advance=1)

                    except Exception as e:
                        error_msg = f"Unexpected error processing {file_path}: {str(e)}"
                        errors[file_path] = error_msg
                        console.print(f"[red]{error_msg}[/red]")
                        progress.update(task, advance=1)

        # Calculate statistics
        successful = len(results)
        failed = len(errors)
        cached_count = sum(1 for r in results.values() if r.get("cached", False))

        return {
            "total_files": total_files,
            "successful": successful,
            "failed": failed,
            "cached_hits": cached_count,
            "results": results,
            "errors": errors
        }


def show_batch_confirmation(
    files_with_tags: Dict[Path, List[str]],
    prompt: str,
    model: str,
    provider: str,
    cache_enabled: bool = True
) -> bool:
    """
    Show batch processing confirmation dialog.

    Args:
        files_with_tags: Dictionary of files with their injection tags
        prompt: The prompt to use for injection
        model: AI model to use
        provider: AI provider to use
        cache_enabled: Whether caching is enabled

    Returns:
        True if user confirms, False otherwise
    """
    console.print("\n" + "="*60)
    console.print(Panel(
        f"[bold yellow]⚠️  BATCH INJECTION CONFIRMATION[/bold yellow]\n\n"
        f"[cyan]Files to process:[/cyan] {len(files_with_tags)}\n"
        f"[cyan]AI Model:[/cyan] {model} ({provider})\n"
        f"[cyan]Prompt:[/cyan] {prompt[:100]}{'...' if len(prompt) > 100 else ''}\n"
        f"[cyan]Caching:[/cyan] {'Enabled' if cache_enabled else 'Disabled'}\n\n"
        f"[yellow]This will modify the following files:[/yellow]",
        title="Confirmation Required",
        border_style="yellow"
    ))

    # Show files in a table
    table = Table(show_header=True, header_style="bold blue")
    table.add_column("File", style="cyan")
    table.add_column("Injection Tags", style="green")
    table.add_column("Tag Count", justify="right", style="yellow")

    for file_path, tags in sorted(files_with_tags.items()):
        table.add_row(
            str(file_path),
            ", ".join(tags),
            str(len(tags))
        )

    console.print(table)

    # Calculate estimated processing time
    estimated_time = len(files_with_tags) * 0.5  # Rough estimate
    console.print(f"\n[dim]Estimated processing time: ~{estimated_time:.1f} seconds[/dim]")

    # Ask for confirmation
    return Confirm.ask(
        "\n[bold]Do you want to proceed with batch injection?[/bold]",
        default=False
    )


def print_batch_results(batch_results: Dict[str, Any]) -> None:
    """
    Print batch processing results.

    Args:
        batch_results: Results from batch processing
    """
    console.print("\n" + "="*60)
    console.print(Panel(
        f"[bold green]✅ BATCH PROCESSING COMPLETED[/bold green]\n\n"
        f"[cyan]Total files:[/cyan] {batch_results['total_files']}\n"
        f"[cyan]Successful:[/cyan] {batch_results['successful']}\n"
        f"[cyan]Failed:[/cyan] {batch_results['failed']}\n"
        f"[cyan]Cache hits:[/cyan] {batch_results['cached_hits']}\n"
        f"[cyan]Success rate:[/cyan] {(batch_results['successful'] / batch_results['total_files'] * 100):.1f}%",
        title="Batch Results",
        border_style="green"
    ))

    # Show successful results
    if batch_results['results']:
        console.print("\n[bold green]✅ Successfully processed files:[/bold green]")
        for file_path, result in batch_results['results'].items():
            cached_indicator = " [dim](cached)[/dim]" if result.get('cached') else ""
            console.print(f"  [green]✓[/green] {file_path}{cached_indicator}")
            if result.get('result'):
                res = result['result']
                console.print(f"    [dim]Modified {res.get('modified_lines', 0)} lines, "
                            f"used {res.get('tokens_used', 0)} tokens[/dim]")

    # Show errors
    if batch_results['errors']:
        console.print("\n[bold red]❌ Failed files:[/bold red]")
        for file_path, error in batch_results['errors'].items():
            console.print(f"  [red]✗[/red] {file_path}")
            console.print(f"    [dim]{error}[/dim]")


def print_cache_stats(cache: BatchCache) -> None:
    """Print cache statistics."""
    stats = cache.stats()
    console.print(f"\n[bold blue]📊 Cache Statistics[/bold blue]")
    console.print(f"[cyan]Cache entries:[/cyan] {stats['total_entries']}")
    console.print(f"[cyan]Cache file:[/cyan] {stats['cache_file']}")
    console.print(f"[cyan]Cache size:[/cyan] {stats['cache_size_mb']:.2f} MB")
