#!/usr/bin/env powershell
<#
.SYNOPSIS
    CodeCrusher VS Code Extension Setup Script
.DESCRIPTION
    Sets up the VS Code extension for development and testing
#>

Write-Host "🚀 CodeCrusher VS Code Extension Setup" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Step 1: Navigate to extension directory
Write-Host "`n1️⃣ Checking Extension Directory..." -ForegroundColor Yellow

if (-not (Test-Path "vscode-extension")) {
    Write-Host "❌ Extension directory not found!" -ForegroundColor Red
    Write-Host "💡 Make sure you're in the codecrusherv2 root directory" -ForegroundColor Yellow
    exit 1
}

Set-Location "vscode-extension"
Write-Host "✅ In extension directory: $(Get-Location)" -ForegroundColor Green

# Step 2: Check Node.js and npm
Write-Host "`n2️⃣ Checking Node.js and npm..." -ForegroundColor Yellow

try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js or npm not found!" -ForegroundColor Red
    Write-Host "💡 Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Step 3: Install dependencies
Write-Host "`n3️⃣ Installing Dependencies..." -ForegroundColor Yellow

if (Test-Path "node_modules") {
    Write-Host "📦 Dependencies already installed" -ForegroundColor Gray
} else {
    Write-Host "📦 Installing npm dependencies..." -ForegroundColor Gray
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ npm install failed!" -ForegroundColor Red
        exit 1
    }
}

# Step 4: Install missing ts-loader if needed
Write-Host "`n4️⃣ Checking TypeScript Loader..." -ForegroundColor Yellow

$tsLoaderCheck = npm list ts-loader 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "📦 Installing ts-loader..." -ForegroundColor Gray
    npm install --save-dev ts-loader
    if ($LASTEXITCODE -ne 0) {
        Write-Host "⚠️ ts-loader installation failed, trying alternative build..." -ForegroundColor Yellow
    }
} else {
    Write-Host "✅ ts-loader is installed" -ForegroundColor Green
}

# Step 5: Try to build the extension
Write-Host "`n5️⃣ Building Extension..." -ForegroundColor Yellow

# Try webpack build
Write-Host "🔨 Attempting webpack build..." -ForegroundColor Gray
npx webpack --mode development
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Webpack build successful!" -ForegroundColor Green
} else {
    Write-Host "⚠️ Webpack build failed, trying TypeScript compilation..." -ForegroundColor Yellow

    # Fallback to TypeScript compilation
    npx tsc
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ TypeScript compilation successful!" -ForegroundColor Green
    } else {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        Write-Host "💡 You may need to install dependencies manually" -ForegroundColor Yellow
    }
}

# Step 6: Check output
Write-Host "`n6️⃣ Checking Build Output..." -ForegroundColor Yellow

if (Test-Path "dist/extension.js") {
    Write-Host "✅ Extension built successfully: dist/extension.js" -ForegroundColor Green
} elseif (Test-Path "out") {
    Write-Host "✅ Extension compiled to: out/" -ForegroundColor Green
} else {
    Write-Host "⚠️ No build output found" -ForegroundColor Yellow
}

# Step 7: Development instructions
Write-Host "`n7️⃣ Development Instructions..." -ForegroundColor Yellow

Write-Host "📋 To develop the extension:" -ForegroundColor Cyan
Write-Host "   1. Open VS Code in this directory: code ." -ForegroundColor White
Write-Host "   2. Press F5 to launch Extension Development Host" -ForegroundColor White
Write-Host "   3. Test extension commands in the new VS Code window" -ForegroundColor White
Write-Host "   4. Make changes and reload with Ctrl+R" -ForegroundColor White

Write-Host "`n📋 Available Commands:" -ForegroundColor Cyan
Write-Host "   • CodeCrusher: Inject with CodeCrusher" -ForegroundColor White
Write-Host "   • CodeCrusher: Show Live Logs" -ForegroundColor White
Write-Host "   • CodeCrusher: Run Injection" -ForegroundColor White
Write-Host "   • CodeCrusher: Insert Injection Tag" -ForegroundColor White

Write-Host "`n📋 Build Commands:" -ForegroundColor Cyan
Write-Host "   • npm run compile    - Build for development" -ForegroundColor White
Write-Host "   • npm run watch      - Watch for changes" -ForegroundColor White
Write-Host "   • npm run package    - Build for production" -ForegroundColor White

# Step 8: Open VS Code
Write-Host "`n8️⃣ Opening VS Code..." -ForegroundColor Yellow

if (Get-Command code -ErrorAction SilentlyContinue) {
    Write-Host "🌐 Opening VS Code in extension directory..." -ForegroundColor Gray
    code .
} else {
    Write-Host "⚠️ VS Code command not found in PATH" -ForegroundColor Yellow
    Write-Host "💡 Open VS Code manually and open this folder: $((Get-Location).Path)" -ForegroundColor Yellow
}

Write-Host "`n🎉 Extension Setup Complete!" -ForegroundColor Green
Write-Host "Press F5 in VS Code to start debugging the extension." -ForegroundColor Gray
