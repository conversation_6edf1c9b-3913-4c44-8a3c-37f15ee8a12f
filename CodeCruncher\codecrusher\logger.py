"""
Logger module for CodeCrusher
"""

import logging
import os
from datetime import datetime


def setup_logging(log_level=logging.INFO, log_file=None):
    """
    Set up logging configuration
    
    Args:
        log_level (int, optional): Logging level (default: logging.INFO)
        log_file (str, optional): Path to log file (default: None)
    """
    # Create logs directory if it doesn't exist
    if log_file:
        os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        filename=log_file
    )
    
    # Add console handler if no log file is specified
    if not log_file:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logging.getLogger().addHandler(console_handler)
    
    logging.info(f"Logging initialized at {datetime.now()}")


def log_with_timestamp(message, level="info"):
    """
    Log a message with timestamp
    
    Args:
        message (str): The message to log
        level (str, optional): The log level (info, warning, error, debug)
    """
    if level == "warning":
        logging.warning(message)
    elif level == "error":
        logging.error(message)
    elif level == "debug":
        logging.debug(message)
    else:  # info
        logging.info(message)
