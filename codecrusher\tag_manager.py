"""
Tag management module for CodeCrusher.

This module provides utilities for working with tags in CodeCrusher.
"""

import re
from typing import List, Dict, Any, Optional, Union

# Constants for internal tag prefixes
MODEL_TAG_PREFIX = "@model:"
SOURCE_TAG_PREFIX = "@source:"
CACHED_TAG_PREFIX = "@cached:"
FALLBACK_TAG_PREFIX = "@fallback:"
CONFIDENCE_TAG_PREFIX = "@confidence:"
REPLAY_TAG_PREFIX = "@replay:"
PARENT_TAG_PREFIX = "@parent:"
MIXED_VALUE = "mixed"

def generate_internal_tags(
    model: str,
    provider: str,
    cached: bool = False,
    fallback: bool = False,
    confidence: str = "medium",
    replay: bool = False
) -> List[str]:
    """
    Generate internal tags based on the injection parameters.

    Args:
        model: The model used for generation
        provider: The provider used for generation
        cached: Whether the result was retrieved from cache
        fallback: Whether a fallback model was used
        confidence: The confidence level of the result (high, medium, low)
        replay: Whether this is a replay of a previous injection

    Returns:
        A list of internal tags
    """
    tags = []

    # Add model tag
    tags.append(f"{MODEL_TAG_PREFIX}{model}")

    # Add source tag
    tags.append(f"{SOURCE_TAG_PREFIX}{provider}")

    # Add cached tag if applicable
    if cached:
        tags.append(f"{CACHED_TAG_PREFIX}true")

    # Add fallback tag if applicable
    if fallback:
        tags.append(f"{FALLBACK_TAG_PREFIX}true")

    # Add confidence tag
    tags.append(f"{CONFIDENCE_TAG_PREFIX}{confidence}")

    # Add replay tag if applicable
    if replay:
        tags.append(f"{REPLAY_TAG_PREFIX}true")

    return tags

def parse_user_tags(tag_input: Optional[str]) -> List[str]:
    """
    Parse user-provided tags from the command line.

    Args:
        tag_input: Space-separated list of tags

    Returns:
        A list of user tags
    """
    if not tag_input:
        return []

    # Split by spaces and filter out empty strings
    return [tag.strip() for tag in tag_input.split() if tag.strip()]

def combine_tags(internal_tags: List[str], user_tags: List[str]) -> List[str]:
    """
    Combine internal and user tags.

    Args:
        internal_tags: List of internal tags
        user_tags: List of user tags

    Returns:
        A combined list of tags
    """
    # Combine and deduplicate tags
    all_tags = list(set(internal_tags + user_tags))
    return all_tags

def format_tags_for_comment(tags: List[str], comment_prefix: str = "#", sort_tags: bool = True) -> str:
    """
    Format tags for inclusion in a comment.

    Args:
        tags: List of tags
        comment_prefix: The comment prefix to use
        sort_tags: Whether to sort the tags alphabetically

    Returns:
        A formatted string with tags
    """
    if not tags:
        return ""

    # Sort tags alphabetically if requested
    if sort_tags:
        tags = sorted(tags)

    # Format tags as a space-separated list
    return f"{comment_prefix} Tags: {' '.join(tags)}"

def tag_matches(entry_tags: List[str], filter_tags: List[str], exclude_tags: List[str] = None) -> bool:
    """
    Check if all filter tags match the entry tags (case-insensitive) and none of the exclude tags match.

    Args:
        entry_tags: List of tags in the entry
        filter_tags: List of tags to filter by (include)
        exclude_tags: List of tags to exclude

    Returns:
        True if all filter tags match and no exclude tags match, False otherwise
    """
    # If no entry tags, only match if no filter tags are specified
    if not entry_tags:
        return not filter_tags

    # Convert all tags to lowercase for case-insensitive matching
    entry_tags_lower = [tag.lower() for tag in entry_tags]

    # Check include filters
    if filter_tags:
        # Check if all filter tags are in the entry tags (case-insensitive)
        for filter_tag in filter_tags:
            filter_tag_lower = filter_tag.lower()

            # Check for substring match if using contains: prefix
            # FIXED: Add defensive check for None filter_tag_lower before calling .startswith()
            if filter_tag_lower and filter_tag_lower.startswith("contains:"):
                substring = filter_tag_lower[9:]  # Remove "contains:" prefix
                if not any(substring in tag for tag in entry_tags_lower):
                    return False
            # Regular exact match
            elif filter_tag_lower not in entry_tags_lower:
                return False

    # Check exclude filters
    if exclude_tags:
        # Check if any exclude tag is in the entry tags (case-insensitive)
        for exclude_tag in exclude_tags:
            exclude_tag_lower = exclude_tag.lower()

            # Check for substring match if using contains: prefix
            # FIXED: Add defensive check for None exclude_tag_lower before calling .startswith()
            if exclude_tag_lower and exclude_tag_lower.startswith("contains:"):
                substring = exclude_tag_lower[9:]  # Remove "contains:" prefix
                if any(substring in tag for tag in entry_tags_lower):
                    return False
            # Regular exact match
            elif exclude_tag_lower in entry_tags_lower:
                return False

    return True

def highlight_matched_tags(tags: List[str], filter_tags: List[str], exclude_tags: List[str] = None) -> List[str]:
    """
    Highlight tags that match filter tags or are excluded by exclude tags.

    Args:
        tags: List of tags to highlight
        filter_tags: List of tags to filter by (include)
        exclude_tags: List of tags to exclude

    Returns:
        List of tags with highlighting markup for rich console
    """
    if not tags:
        return []

    # Convert filter and exclude tags to lowercase for case-insensitive matching
    filter_tags_lower = [tag.lower() for tag in filter_tags] if filter_tags else []
    exclude_tags_lower = [tag.lower() for tag in exclude_tags] if exclude_tags else []

    # Remove "contains:" prefix from filter and exclude tags
    # FIXED: Add defensive checks for None tags before calling .startswith()
    filter_contains = [tag[9:] for tag in filter_tags_lower if tag and tag.startswith("contains:")]
    exclude_contains = [tag[9:] for tag in exclude_tags_lower if tag and tag.startswith("contains:")]

    # Filter out "contains:" prefixed tags from the original lists
    # FIXED: Add defensive checks for None tags before calling .startswith()
    filter_tags_lower = [tag for tag in filter_tags_lower if tag and not tag.startswith("contains:")]
    exclude_tags_lower = [tag for tag in exclude_tags_lower if tag and not tag.startswith("contains:")]

    highlighted_tags = []

    for tag in tags:
        tag_lower = tag.lower()

        # Check if tag matches any filter tag
        is_filter_match = tag_lower in filter_tags_lower

        # Check if tag contains any filter substring
        is_filter_contains_match = any(substring in tag_lower for substring in filter_contains)

        # Check if tag matches any exclude tag
        is_exclude_match = tag_lower in exclude_tags_lower

        # Check if tag contains any exclude substring
        is_exclude_contains_match = any(substring in tag_lower for substring in exclude_contains)

        # Apply highlighting based on match type
        if is_filter_match or is_filter_contains_match:
            # Highlight included tags in green
            highlighted_tags.append(f"[bold green]{tag}[/bold green]")
        elif is_exclude_match or is_exclude_contains_match:
            # Highlight excluded tags in red (strikethrough)
            highlighted_tags.append(f"[bold red strike]{tag}[/bold red strike]")
        # FIXED: Add defensive check for None tag before calling .startswith()
        elif tag and tag.startswith("@"):
            # Internal tag (not matched)
            highlighted_tags.append(f"[cyan]{tag}[/cyan]")
        else:
            # User tag (not matched)
            highlighted_tags.append(tag)

    return highlighted_tags

def filter_by_tags(entries: Dict[str, Any], filter_tags: List[str], exclude_tags: List[str] = None) -> Dict[str, Any]:
    """
    Filter cache entries by tags.

    Args:
        entries: Dictionary of cache entries
        filter_tags: List of tags to filter by (include)
        exclude_tags: List of tags to exclude

    Returns:
        Filtered dictionary of cache entries
    """
    if not filter_tags and not exclude_tags:
        return entries

    filtered_entries = {}

    for key, entry in entries.items():
        # Skip entries without tags
        if not isinstance(entry, dict) or "tags" not in entry:
            continue

        # Check if all filter tags are in the entry's tags and no exclude tags are in the entry's tags
        entry_tags = entry["tags"]
        if tag_matches(entry_tags, filter_tags, exclude_tags):
            filtered_entries[key] = entry

    return filtered_entries

def extract_tags_from_content(content: str) -> List[str]:
    """
    Extract tags from content.

    Args:
        content: The content to extract tags from

    Returns:
        A list of extracted tags
    """
    tags = []

    # Look for tags in the first few lines (assuming they're in comments)
    lines = content.split("\n")[:5]
    for line in lines:
        if "Tags:" in line:
            # Extract tags after "Tags:"
            tag_part = line.split("Tags:")[1].strip()
            tags.extend([tag.strip() for tag in tag_part.split() if tag.strip()])

    return tags

def inherit_tags(original_tags: List[str], is_replay: bool = False, parent_id: Optional[str] = None) -> List[str]:
    """
    Inherit tags from an original injection, with modifications for the new context.

    Args:
        original_tags: List of tags from the original injection
        is_replay: Whether this is a replay operation
        parent_id: Optional ID of the parent injection for provenance tracking

    Returns:
        A list of inherited tags with appropriate modifications
    """
    # Start with a copy of the original tags
    inherited_tags = original_tags.copy()

    # Update or add the replay tag if this is a replay
    if is_replay:
        # Remove any existing replay tag
        # FIXED: Add defensive check for None tag before calling .startswith()
        inherited_tags = [tag for tag in inherited_tags if not (tag and tag.startswith(REPLAY_TAG_PREFIX))]
        # Add the new replay tag
        inherited_tags.append(f"{REPLAY_TAG_PREFIX}true")

    # Add parent tag if provided
    if parent_id:
        # Remove any existing parent tag
        # FIXED: Add defensive check for None tag before calling .startswith()
        inherited_tags = [tag for tag in inherited_tags if not (tag and tag.startswith(PARENT_TAG_PREFIX))]
        # Add the new parent tag
        inherited_tags.append(f"{PARENT_TAG_PREFIX}{parent_id}")

    return inherited_tags

def merge_tags(tag_sets: List[List[str]]) -> List[str]:
    """
    Merge multiple sets of tags, handling special cases.

    Args:
        tag_sets: List of tag lists to merge

    Returns:
        A merged list of tags with special cases handled
    """
    if not tag_sets:
        return []

    # Flatten all tags into a single list
    all_tags = []
    for tags in tag_sets:
        all_tags.extend(tags)

    # Track special tag prefixes that need special handling
    special_prefixes = [CACHED_TAG_PREFIX, FALLBACK_TAG_PREFIX]
    special_tags = {}

    # Process all tags
    processed_tags = []
    for tag in all_tags:
        # Check if this is a special tag
        is_special = False
        for prefix in special_prefixes:
            # FIXED: Add defensive check for None tag before calling .startswith()
            if tag and tag.startswith(prefix):
                is_special = True
                prefix_key = prefix
                # Extract the value
                value = tag[len(prefix):]

                # Track this special tag
                if prefix_key not in special_tags:
                    special_tags[prefix_key] = []
                special_tags[prefix_key].append(value)
                break

        # If not a special tag, add it to the processed list
        if not is_special:
            processed_tags.append(tag)

    # Handle special tags
    for prefix, values in special_tags.items():
        # If all values are the same, use that value
        if len(set(values)) == 1:
            processed_tags.append(f"{prefix}{values[0]}")
        # Otherwise, use "mixed"
        else:
            processed_tags.append(f"{prefix}{MIXED_VALUE}")

    # Deduplicate tags
    return list(set(processed_tags))

def apply_batch_tags(item_tags: List[str], global_tags: List[str]) -> List[str]:
    """
    Apply global batch tags to item-specific tags.

    Args:
        item_tags: List of tags specific to an item
        global_tags: List of global tags to apply to all items

    Returns:
        A combined list of tags
    """
    # Combine and deduplicate
    return list(set(item_tags + global_tags))
