"""
Filter utilities for CodeCrusher.

This module provides utilities for filtering and previewing output in CodeCrusher.
"""

from typing import Dict, List, Any, Optional, Tuple
from enum import Enum, auto

# Constants for filter fields
MODEL_FIELD = "model"
FALLBACK_FIELD = "fallback"
TAG_FIELD = "tag"
ERROR_FIELD = "error"

# Constants for preview modes
class PreviewMode(Enum):
    SHORT = "short"
    FULL = "full"
    METADATA = "metadata"

def parse_filter(filter_str: Optional[str]) -> Dict[str, str]:
    """
    Parse a filter string into a dictionary of field-value pairs.
    
    Args:
        filter_str: Filter string in the format "field1=value1,field2=value2"
        
    Returns:
        Dictionary of field-value pairs
    """
    if not filter_str:
        return {}
    
    filters = {}
    
    # Split by commas
    filter_parts = filter_str.split(",")
    
    for part in filter_parts:
        # Skip empty parts
        if not part.strip():
            continue
        
        # Split by equals sign
        if "=" in part:
            field, value = part.split("=", 1)
            field = field.strip().lower()
            value = value.strip()
            
            # Add to filters
            filters[field] = value
    
    return filters

def apply_filters(entry: Dict[str, Any], filters: Dict[str, str]) -> bool:
    """
    Apply filters to an entry.
    
    Args:
        entry: The entry to filter
        filters: Dictionary of field-value pairs to filter by
        
    Returns:
        True if the entry passes all filters, False otherwise
    """
    if not filters:
        return True
    
    for field, value in filters.items():
        # Handle model filter
        if field == MODEL_FIELD:
            # Check if model field exists
            entry_model = entry.get("model", "")
            if not entry_model:
                # Try to extract from tags
                tags = entry.get("tags", [])
                for tag in tags:
                    if tag.startswith("@model:"):
                        entry_model = tag[7:]  # Remove "@model:" prefix
                        break
            
            # Case-insensitive comparison
            if value.lower() != entry_model.lower():
                return False
        
        # Handle fallback filter
        elif field == FALLBACK_FIELD:
            # Check if fallback field exists
            entry_fallback = entry.get("fallback", "")
            if not entry_fallback:
                # Try to extract from tags
                tags = entry.get("tags", [])
                for tag in tags:
                    if tag.startswith("@fallback:"):
                        entry_fallback = tag[10:]  # Remove "@fallback:" prefix
                        break
            
            # Case-insensitive comparison
            if value.lower() != entry_fallback.lower():
                return False
        
        # Handle tag filter
        elif field == TAG_FIELD:
            # Get tags
            tags = entry.get("tags", [])
            
            # Case-insensitive comparison
            if not any(value.lower() == tag.lower() for tag in tags):
                return False
        
        # Handle error filter
        elif field == ERROR_FIELD:
            # Check if error field exists
            entry_error = entry.get("error", "")
            
            # Case-insensitive comparison
            if value.lower() not in entry_error.lower():
                return False
    
    return True

def truncate_text(text: str, max_length: int = 50) -> str:
    """
    Truncate text to a maximum length.
    
    Args:
        text: The text to truncate
        max_length: Maximum length of the truncated text
        
    Returns:
        Truncated text
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - 3] + "..."

def format_entry_preview(entry: Dict[str, Any], mode: PreviewMode, key: str = "") -> Tuple[List[str], Dict[str, str]]:
    """
    Format an entry for preview based on the preview mode.
    
    Args:
        entry: The entry to format
        mode: The preview mode
        key: The cache key for the entry
        
    Returns:
        Tuple of (formatted_lines, metadata)
    """
    formatted_lines = []
    metadata = {}
    
    # Extract common fields
    entry_id = key or entry.get("id", "Unknown")
    model = entry.get("model", "Unknown")
    timestamp = entry.get("timestamp", "Unknown")
    tags = entry.get("tags", [])
    output = entry.get("output", "")
    error = entry.get("error", "")
    
    # Try to extract model from tags if not found
    if model == "Unknown":
        for tag in tags:
            if tag.startswith("@model:"):
                model = tag[7:]  # Remove "@model:" prefix
                break
    
    # Add metadata
    metadata["id"] = entry_id
    metadata["model"] = model
    metadata["timestamp"] = timestamp
    metadata["tags"] = tags
    
    # Format based on mode
    if mode == PreviewMode.SHORT:
        # Short preview: ID, date, truncated input/output (1-2 lines each)
        formatted_lines.append(f"ID: {truncate_text(entry_id, 20)}")
        formatted_lines.append(f"Model: {model}")
        formatted_lines.append(f"Date: {timestamp}")
        
        # Add truncated output (first 2 lines)
        if output:
            output_lines = output.split("\n")
            formatted_lines.append("Output:")
            for i, line in enumerate(output_lines[:2]):
                formatted_lines.append(f"  {truncate_text(line, 50)}")
            if len(output_lines) > 2:
                formatted_lines.append("  ...")
        
        # Add error if present
        if error:
            formatted_lines.append(f"Error: {truncate_text(error, 50)}")
    
    elif mode == PreviewMode.METADATA:
        # Metadata only: ID, model, tags, timestamps
        formatted_lines.append(f"ID: {entry_id}")
        formatted_lines.append(f"Model: {model}")
        formatted_lines.append(f"Date: {timestamp}")
        
        # Add tags
        if tags:
            formatted_lines.append("Tags:")
            for tag in tags:
                formatted_lines.append(f"  {tag}")
    
    elif mode == PreviewMode.FULL:
        # Full preview: everything
        formatted_lines.append(f"ID: {entry_id}")
        formatted_lines.append(f"Model: {model}")
        formatted_lines.append(f"Date: {timestamp}")
        
        # Add tags
        if tags:
            formatted_lines.append("Tags:")
            for tag in tags:
                formatted_lines.append(f"  {tag}")
        
        # Add full output
        if output:
            formatted_lines.append("Output:")
            for line in output.split("\n"):
                formatted_lines.append(f"  {line}")
        
        # Add error if present
        if error:
            formatted_lines.append(f"Error: {error}")
    
    return formatted_lines, metadata
