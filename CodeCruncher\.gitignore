# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
codecrushervenv/

# Environment variables
.env
.env.*

# Cache files
.cache/
*.cache
cache.json
.pytest_cache/

# Logs
logs/
*.log

# CodeCrusher specific
*.v0.1.py
somefile.py
custom_config.json
~/.codecrusher_cache.json
~/.codecrusher/
.codecrusher_token
*.backup
injection_results.json
phase15_injection_results.json

# Testing and development
test_*.py
demo_*.py
*_test.py
*.test.js
coverage.xml
.coverage
htmlcov/

# Node.js (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Database
*.db
*.sqlite
*.sqlite3

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.vim/
.emacs.d/

# OS specific files
.DS_Store
Thumbs.db
*.tmp
*.temp

# Frontend dependencies and build artifacts
frontend/node_modules/
frontend/.vite/
frontend/dist/assets/
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Documentation builds
docs/_build/
site/

# Jupyter Notebooks
.ipynb_checkpoints/
*.ipynb

# Security
*.pem
*.key
secrets.json
.secrets
