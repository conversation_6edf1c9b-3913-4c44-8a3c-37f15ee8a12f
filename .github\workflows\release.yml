name: 🚀 CodeCrusher Release

on:
  push:
    tags:
      - 'v*'  # Trigger on version tags like v1.0.0
  workflow_dispatch:  # Allow manual triggering
    inputs:
      version_type:
        description: 'Version bump type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
      test_pypi:
        description: 'Publish to TestPyPI instead of PyPI'
        required: false
        default: false
        type: boolean
      create_github_release:
        description: 'Create GitHub release with CLI binary'
        required: false
        default: true
        type: boolean

jobs:
  release:
    runs-on: ubuntu-latest
    permissions:
      contents: write  # Required for creating releases
      id-token: write  # Required for PyPI trusted publishing
    
    steps:
    - name: 🔄 Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Fetch full history for proper versioning
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 🐍 Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 📦 Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine setuptools wheel requests
        pip install -r requirements-dev.txt
    
    - name: 🔍 Validate environment
      run: |
        python --version
        pip --version
        twine --version
        git --version
    
    - name: 🏷️ Get version info
      id: version
      run: |
        if [[ "${{ github.event_name }}" == "push" ]]; then
          # Extract version from tag
          VERSION=${GITHUB_REF#refs/tags/v}
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "tag=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
        else
          # Get current version for manual dispatch
          VERSION=$(python -c "import re; content=open('setup.py').read(); print(re.search(r'version\s*=\s*[\"\\']([^\"\\']*)[\"\\'']', content).group(1))")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "tag=v$VERSION" >> $GITHUB_OUTPUT
        fi
    
    - name: 🔧 Configure Git
      run: |
        git config --global user.name "github-actions[bot]"
        git config --global user.email "github-actions[bot]@users.noreply.github.com"
    
    - name: 🏗️ Build package
      run: |
        python -m build
        echo "📦 Built packages:"
        ls -la dist/
    
    - name: 🧪 Validate package
      run: |
        python -m twine check dist/*
        echo "✅ Package validation passed"
    
    - name: 📤 Publish to PyPI
      if: github.event_name == 'push' || !inputs.test_pypi
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        echo "🚀 Publishing to PyPI..."
        python -m twine upload dist/*
        echo "✅ Published to PyPI successfully"
    
    - name: 📤 Publish to TestPyPI
      if: inputs.test_pypi
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.TEST_PYPI_API_TOKEN }}
      run: |
        echo "🧪 Publishing to TestPyPI..."
        python -m twine upload --repository-url https://test.pypi.org/legacy/ dist/*
        echo "✅ Published to TestPyPI successfully"
    
    - name: 📦 Create CLI binary
      id: cli_binary
      run: |
        echo "🔨 Creating CLI binary archive..."
        python release_to_github.py --dry-run --tag ${{ steps.version.outputs.tag }}
        
        # Create the actual binary
        mkdir -p cli-binary
        
        # Copy essential files
        cp codecrusher_cli.py cli-binary/
        cp -r codecrusher/ cli-binary/
        cp setup.py cli-binary/
        cp README.md cli-binary/
        cp LICENSE cli-binary/
        cp requirements.txt cli-binary/ 2>/dev/null || echo "requirements.txt not found, skipping"
        
        # Create installation script
        cat > cli-binary/install.sh << 'EOF'
        #!/bin/bash
        # CodeCrusher CLI Installation Script
        
        echo "🚀 Installing CodeCrusher CLI..."
        
        # Check Python
        if ! command -v python3 &> /dev/null; then
            echo "❌ Python 3 is required but not installed"
            exit 1
        fi
        
        # Install dependencies
        echo "📦 Installing dependencies..."
        pip3 install click rich requests groq pydantic sqlalchemy aiosqlite python-dotenv pyyaml jinja2
        
        # Make CLI executable
        chmod +x codecrusher_cli.py
        
        echo "✅ CodeCrusher CLI installed successfully!"
        echo "Usage: python3 codecrusher_cli.py --help"
        EOF
        
        chmod +x cli-binary/install.sh
        
        # Create Windows batch file
        cat > cli-binary/install.bat << 'EOF'
        @echo off
        echo 🚀 Installing CodeCrusher CLI...
        
        python --version >nul 2>&1
        if errorlevel 1 (
            echo ❌ Python is required but not installed
            exit /b 1
        )
        
        echo 📦 Installing dependencies...
        pip install click rich requests groq pydantic sqlalchemy aiosqlite python-dotenv pyyaml jinja2
        
        echo ✅ CodeCrusher CLI installed successfully!
        echo Usage: python codecrusher_cli.py --help
        EOF
        
        # Create archive
        ARCHIVE_NAME="codecrusher-v${{ steps.version.outputs.version }}-cli.zip"
        cd cli-binary
        zip -r "../$ARCHIVE_NAME" .
        cd ..
        
        echo "archive_name=$ARCHIVE_NAME" >> $GITHUB_OUTPUT
        echo "archive_path=$(pwd)/$ARCHIVE_NAME" >> $GITHUB_OUTPUT
        
        echo "📦 Created CLI binary: $ARCHIVE_NAME"
        ls -la "$ARCHIVE_NAME"
    
    - name: 🎉 Create GitHub Release
      if: github.event_name == 'push' || inputs.create_github_release
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "🎉 Creating GitHub release..."
        
        # Get changelog for this version
        VERSION="${{ steps.version.outputs.version }}"
        CHANGELOG_CONTENT=""
        
        if [[ -f "CHANGELOG.md" ]]; then
          # Extract changelog for this version
          CHANGELOG_CONTENT=$(python -c "
        import re
        content = open('CHANGELOG.md').read()
        pattern = r'## \[$VERSION\].*?(?=## \[|\Z)'
        match = re.search(pattern, content, re.DOTALL)
        if match:
            changelog = match.group(0)
            changelog = re.sub(r'## \[$VERSION\][^\n]*\n', '', changelog)
            print(changelog.strip())
        else:
            print(f'Release $VERSION - See commit history for details.')
        " 2>/dev/null || echo "Release $VERSION - See commit history for details.")
        fi
        
        # Create release using GitHub CLI
        gh release create "${{ steps.version.outputs.tag }}" \
          --title "CodeCrusher ${{ steps.version.outputs.version }}" \
          --notes "$CHANGELOG_CONTENT" \
          --latest \
          "${{ steps.cli_binary.outputs.archive_path }}"
        
        echo "✅ GitHub release created successfully"
        echo "🔗 Release URL: https://github.com/${{ github.repository }}/releases/tag/${{ steps.version.outputs.tag }}"
    
    - name: 📋 Summary
      run: |
        echo "## 🎉 Release Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**Version:** ${{ steps.version.outputs.version }}" >> $GITHUB_STEP_SUMMARY
        echo "**Tag:** ${{ steps.version.outputs.tag }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        if [[ "${{ inputs.test_pypi }}" == "true" ]]; then
          echo "**PyPI:** Published to TestPyPI" >> $GITHUB_STEP_SUMMARY
          echo "**Install:** \`pip install --index-url https://test.pypi.org/simple/ codecrusher\`" >> $GITHUB_STEP_SUMMARY
        else
          echo "**PyPI:** Published to production PyPI" >> $GITHUB_STEP_SUMMARY
          echo "**Install:** \`pip install codecrusher\`" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "**GitHub Release:** https://github.com/${{ github.repository }}/releases/tag/${{ steps.version.outputs.tag }}" >> $GITHUB_STEP_SUMMARY
        echo "**CLI Binary:** ${{ steps.cli_binary.outputs.archive_name }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "🚀 **CodeCrusher ${{ steps.version.outputs.version }} released successfully!**" >> $GITHUB_STEP_SUMMARY
