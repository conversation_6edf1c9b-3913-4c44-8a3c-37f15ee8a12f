# Contributing Guide

Welcome to Code<PERSON>rusher! We're excited to have you contribute to the future of intelligent AI code injection. This guide will help you get started with contributing to the project.

## 🚀 Quick Start for Contributors

### 1. **Fork and Clone**
```bash
# Fork the repository on GitHub
# Then clone your fork
git clone https://github.com/YOUR_USERNAME/CodeCruncher.git
cd CodeCruncher

# Add upstream remote
git remote add upstream https://github.com/Codegx-Technology/CodeCruncher.git
```

### 2. **Development Setup**
```bash
# Create virtual environment
python -m venv codecrusher-dev
source codecrusher-dev/bin/activate  # On Windows: codecrusher-dev\Scripts\activate

# Install development dependencies
pip install -r requirements-dev.txt

# Install in editable mode
pip install -e .

# Verify installation
codecrusher --version
```

### 3. **Run Tests**
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=codecrusher

# Run specific test file
pytest tests/test_intelligence.py
```

---

## 🎯 How to Contribute

### 🐛 **Bug Reports**
Found a bug? Help us fix it!

1. **Check existing issues** to avoid duplicates
2. **Create detailed bug report** with:
   - Operating system and version
   - Python version
   - CodeCrusher version
   - Steps to reproduce
   - Expected vs actual behavior
   - Error messages and logs

### ✨ **Feature Requests**
Have an idea for improvement?

1. **Check existing feature requests**
2. **Create feature request** with:
   - Clear description of the feature
   - Use case and benefits
   - Proposed implementation approach
   - Examples of similar features

### 🔧 **Code Contributions**
Ready to write code?

1. **Pick an issue** or create one
2. **Comment on the issue** to claim it
3. **Create feature branch** from main
4. **Write code** following our standards
5. **Add tests** for new functionality
6. **Submit pull request**

---

## 📁 Project Structure

### Core Components

```
CodeCruncher/
├── codecrusher_cli.py          # Main CLI entry point
├── codecrusher/                # Core package
│   ├── __init__.py
│   ├── ai_injector.py          # AI injection logic
│   ├── intelligence_system.py  # Learning and feedback
│   ├── model_router.py         # Multi-model routing
│   ├── quality_scorer.py       # Quality assessment
│   └── utils/                  # Utility modules
├── tests/                      # Test suite
│   ├── test_injection.py
│   ├── test_intelligence.py
│   └── test_models.py
├── docs/                       # Documentation
├── e2e-comparison/             # Comparison reports
├── frontend/                   # Web dashboard (future)
└── scripts/                    # Development scripts
```

### Key Modules

#### **codecrusher/ai_injector.py**
- Core injection logic
- File parsing and modification
- Tag detection and replacement

#### **codecrusher/intelligence_system.py**
- Feedback collection and analysis
- Parameter learning and adaptation
- Quality improvement tracking

#### **codecrusher/model_router.py**
- Multi-model support
- Fallback and escalation logic
- API integration

---

## 🛠️ Development Standards

### Code Style

#### **Python Standards**
- **PEP 8** compliance with Black formatting
- **Type hints** for all functions and methods
- **Docstrings** for all public functions
- **Maximum line length**: 88 characters (Black default)

```python
def inject_code(
    file_path: Path, 
    prompt: str, 
    model: str = "llama3-8b"
) -> InjectionResult:
    """Inject AI-generated code into specified file.
    
    Args:
        file_path: Path to the target file
        prompt: AI prompt for code generation
        model: AI model to use for generation
        
    Returns:
        InjectionResult containing success status and generated code
        
    Raises:
        FileNotFoundError: If target file doesn't exist
        APIError: If AI model request fails
    """
    # Implementation here
    pass
```

#### **Code Quality Tools**
```bash
# Format code with Black
black codecrusher/ tests/

# Check style with flake8
flake8 codecrusher/ tests/

# Type checking with mypy
mypy codecrusher/

# Security check with bandit
bandit -r codecrusher/
```

### Testing Standards

#### **Test Coverage**
- **Minimum 80%** test coverage for new code
- **Unit tests** for all core functions
- **Integration tests** for CLI commands
- **E2E tests** for complete workflows

#### **Test Structure**
```python
import pytest
from codecrusher.ai_injector import inject_code

class TestAIInjector:
    """Test suite for AI injection functionality."""
    
    def test_inject_code_success(self):
        """Test successful code injection."""
        # Arrange
        file_path = Path("test_file.py")
        prompt = "Add error handling"
        
        # Act
        result = inject_code(file_path, prompt)
        
        # Assert
        assert result.success is True
        assert "try:" in result.generated_code
        assert "except:" in result.generated_code
    
    def test_inject_code_file_not_found(self):
        """Test injection with non-existent file."""
        with pytest.raises(FileNotFoundError):
            inject_code(Path("nonexistent.py"), "test prompt")
```

### Git Workflow

#### **Branch Naming**
- **Feature branches**: `feature/add-new-model-support`
- **Bug fixes**: `fix/injection-tag-parsing`
- **Documentation**: `docs/update-installation-guide`
- **Refactoring**: `refactor/simplify-model-router`

#### **Commit Messages**
Follow [Conventional Commits](https://www.conventionalcommits.org/):

```bash
# Feature
feat: add support for Claude AI model

# Bug fix
fix: resolve injection tag parsing in HTML files

# Documentation
docs: update installation guide with new requirements

# Refactoring
refactor: simplify model router configuration

# Tests
test: add integration tests for intelligence learning

# Breaking change
feat!: change CLI command structure for better UX
```

---

## 🧪 Testing Guidelines

### Running Tests

```bash
# Run all tests
pytest

# Run with verbose output
pytest -v

# Run specific test file
pytest tests/test_intelligence.py

# Run specific test function
pytest tests/test_intelligence.py::test_learning_parameter_update

# Run with coverage report
pytest --cov=codecrusher --cov-report=html
```

### Writing Tests

#### **Test Categories**
1. **Unit Tests**: Test individual functions and methods
2. **Integration Tests**: Test component interactions
3. **E2E Tests**: Test complete user workflows
4. **Performance Tests**: Test speed and resource usage

#### **Test Fixtures**
```python
@pytest.fixture
def sample_python_file(tmp_path):
    """Create a sample Python file for testing."""
    file_path = tmp_path / "sample.py"
    file_path.write_text("""
def hello():
    # AI_INJECT:error_handling
    # AI_INJECT:error_handling:end
    print("Hello, World!")
""")
    return file_path

@pytest.fixture
def mock_ai_response():
    """Mock AI model response."""
    return {
        "code": "try:\n    pass\nexcept Exception as e:\n    print(f'Error: {e}')",
        "quality_score": 85,
        "model_used": "llama3-8b"
    }
```

---

## 📚 Documentation Standards

### Documentation Types

#### **Code Documentation**
- **Docstrings** for all public functions
- **Type hints** for parameters and returns
- **Examples** in docstrings when helpful
- **Inline comments** for complex logic

#### **User Documentation**
- **Clear explanations** with examples
- **Step-by-step guides** for complex procedures
- **Screenshots** for UI features
- **Troubleshooting sections** for common issues

#### **API Documentation**
- **Complete parameter descriptions**
- **Return value specifications**
- **Error condition documentation**
- **Usage examples**

### Documentation Tools

```bash
# Generate API documentation
sphinx-build -b html docs/ docs/_build/

# Check documentation links
sphinx-build -b linkcheck docs/ docs/_build/

# Live documentation server
sphinx-autobuild docs/ docs/_build/
```

---

## 🔄 Pull Request Process

### Before Submitting

1. **Update your fork**:
   ```bash
   git fetch upstream
   git checkout main
   git merge upstream/main
   ```

2. **Create feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make changes and test**:
   ```bash
   # Make your changes
   # Run tests
   pytest
   # Check code style
   black codecrusher/ tests/
   flake8 codecrusher/ tests/
   ```

4. **Commit changes**:
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

### Pull Request Template

When creating a pull request, include:

```markdown
## Description
Brief description of changes and motivation.

## Type of Change
- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Test coverage maintained or improved

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

---

## 🎯 Contribution Areas

### High-Priority Areas

#### **1. AI Model Integration**
- Add support for new AI providers
- Improve model fallback logic
- Optimize model selection algorithms

#### **2. Intelligence System**
- Enhance learning algorithms
- Improve quality scoring
- Add new feedback mechanisms

#### **3. CLI Improvements**
- Add new commands and options
- Improve error handling and messages
- Enhance user experience

#### **4. Testing and Quality**
- Increase test coverage
- Add performance tests
- Improve CI/CD pipeline

### Getting Started Areas

#### **Good First Issues**
- Documentation improvements
- Bug fixes with clear reproduction steps
- Small feature additions
- Test coverage improvements

#### **Help Wanted**
- Complex algorithm improvements
- New feature implementations
- Performance optimizations
- Architecture enhancements

---

## 🤝 Community Guidelines

### Code of Conduct

We are committed to providing a welcoming and inclusive environment:

- **Be respectful** and considerate in all interactions
- **Be collaborative** and help others learn and grow
- **Be patient** with newcomers and different skill levels
- **Be constructive** in feedback and criticism

### Communication Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Pull Requests**: Code review and collaboration
- **Documentation**: Guides and tutorials

---

## 🏆 Recognition

### Contributors

We recognize and appreciate all contributions:

- **Code contributors** listed in CONTRIBUTORS.md
- **Documentation contributors** credited in docs
- **Bug reporters** acknowledged in release notes
- **Feature requesters** mentioned in changelogs

### Becoming a Maintainer

Active contributors may be invited to become maintainers:

- **Consistent contributions** over time
- **High-quality code** and documentation
- **Helpful community participation**
- **Understanding of project goals**

---

## 🔗 Resources

### Development Resources
- **[Architecture Guide](architecture.md)** - Understand system design
- **[API Documentation](api.md)** - Detailed API reference
- **[Testing Guide](testing.md)** - Comprehensive testing information

### External Resources
- **[Python Style Guide](https://pep8.org/)** - PEP 8 standards
- **[Conventional Commits](https://www.conventionalcommits.org/)** - Commit message format
- **[Semantic Versioning](https://semver.org/)** - Version numbering

---

**[← Dashboard Guide](dashboard.md)** | **[Next: Architecture Guide →](architecture.md)**
