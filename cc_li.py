import subprocess
import sys
import json
import os
import hashlib
import typer
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table

# Import commands
from codecrusher.commands import optimize
try:
    from codecrusher.commands import inject
except ImportError:
    inject = None
try:
    from codecrusher.commands import scan
except ImportError:
    scan = None
try:
    from codecrusher.commands import replay
except ImportError:
    replay = None
try:
    from codecrusher.commands import log
except ImportError:
    log = None
try:
    from codecrusher.commands import merge
except ImportError:
    merge = None
try:
    from codecrusher.commands import analyze
except ImportError:
    analyze = None
try:
    from codecrusher.commands import stats
except ImportError:
    stats = None
try:
    from codecrusher.commands import telemetry
except ImportError:
    telemetry = None
try:
    from codecrusher.commands import dashboard
except ImportError:
    dashboard = None
try:
    from codecrusher.commands import status
except ImportError:
    status = None
try:
    from codecrusher.commands import trends
except ImportError:
    trends = None
try:
    from codecrusher.commands import server
except ImportError:
    server = None
try:
    from codecrusher.commands import githooks
except ImportError:
    githooks = None
try:
    from codecrusher.commands import install_githooks
except ImportError:
    install_githooks = None

# Initialize rich console
console = Console()

# Create the main app
app = typer.Typer(
    help="🚀 CodeCrusher CLI — Command Line Interface for CodeCrusher",
    no_args_is_help=True,
)

def check_dependencies():
    """Check if required dependencies are installed."""
    try:
        import typer
        print(f"[SUCCESS] Typer is installed (version: {typer.__version__})")
        return True
    except ImportError:
        print("❌ Error: Typer module not found.")
        print("\nPlease install the required dependencies:")
        print("    pip install typer rich")
        print("\nOr activate the virtual environment where these packages are installed:")
        print("    source codecrushervenv/bin/activate  # On Linux/Mac")
        print("    codecrushervenv\\Scripts\\activate    # On Windows")
        return False

def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"{description}...")
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode != 0:
        print(f"❌ Command failed with exit code {result.returncode}")
        # Check both stderr and stdout for error messages
        error_message = result.stderr if result.stderr else result.stdout
        print(f"Error: {error_message}")
        return False

    print(f"[SUCCESS] {description} completed successfully")
    if result.stdout:
        print(f"Output: {result.stdout}")

    return True

def run_inject():
    """Run the inject command."""
    print("🔧 Running Inject...")

    # Define the input file and prompt
    input_file = "somefile.py"
    prompt_text = "Refactor this to use async"
    provider = "groq"
    model = "llama3"

    # Check if we should try to replay from cache
    try:
        # Read the file content
        file_path = Path(input_file)
        if file_path.exists():
            file_content = file_path.read_text(encoding="utf-8")

            # Try all possible cache key formats
            cache_keys = []

            # 1. unified_ai_engine.py format (MD5 hash)
            content_hash = f"{file_content}|{prompt_text}|{provider}|{model}"
            md5_key = hashlib.md5(content_hash.encode()).hexdigest()
            cache_keys.append(md5_key)

            # 2. Simple format: file:prompt
            cache_keys.append(f"{input_file}:{prompt_text}")

            # 3. inject:: prefix format
            file_hash = hashlib.md5(file_content.encode()).hexdigest()[:8]
            cache_keys.append(f"inject::{file_hash}::{prompt_text}")

            # 4. optimize_ prefix format - only use if file content hasn't changed
            optimize_key = f"optimize_{Path(input_file).name}_{provider}_{model}"

            # Check if the optimize_key exists in the cache
            optimize_entry = get_cached_entry(optimize_key)
            if optimize_entry:
                # Check if the cached entry is for the current file content
                if isinstance(optimize_entry, dict) and model in optimize_entry:
                    model_entry = optimize_entry[model]
                    if isinstance(model_entry, dict) and "output" in model_entry:
                        # Only add the optimize_key if it's for the current file content
                        # This is a simple check - we're just making sure the output doesn't
                        # contain the old file content
                        output = model_entry["output"]
                        if "print(\"I am some file\")" not in output:
                            cache_keys.append(optimize_key)

            # Log the keys we're checking
            print(f"[DEBUG] Checking cache with keys: {cache_keys}")

            # Check for cached entry
            cached_entry = None
            used_key = None

            # Try all cache keys
            for key in cache_keys:
                cached_entry = get_cached_entry(key)
                if cached_entry:
                    used_key = key
                    break

            # If we found a cached entry, use it
            if cached_entry:
                print(f"[REPLAY] Loaded from cache for hash: {used_key}")

                # Extract the content from the cached entry
                content = None
                if isinstance(cached_entry, str):
                    content = cached_entry
                elif isinstance(cached_entry, dict):
                    if "result" in cached_entry:
                        content = cached_entry["result"]
                    elif "output" in cached_entry:
                        content = cached_entry["output"]
                    elif model in cached_entry and isinstance(cached_entry[model], dict):
                        model_entry = cached_entry[model]
                        if "output" in model_entry:
                            content = model_entry["output"]

                # If we successfully extracted content, print it and return success
                if content:
                    print("\n[SUCCESS] Injection replayed from cache")
                    print("\nInjected Code:")
                    print(content)
                    return True
    except Exception as e:
        print(f"[WARNING] Cache check failed: {str(e)}")
        print("Falling back to normal injection...")

    # If we get here, either cache check failed or no cache entry was found
    # Run the normal inject command
    result = run_command([
        sys.executable, "codecrusher_cli.py", "inject", "run",
        "-i", input_file,
        "-t", prompt_text,
        "--provider", provider,
        "--model", model,
        "--cache",
        "--verbose"
    ], "Inject")

    # If the injection was successful, manually add the result to the cache
    if result:
        try:
            # Read the file content again (it might have changed)
            file_content = file_path.read_text(encoding="utf-8")

            # Generate the cache key
            content_hash = f"{file_content}|{prompt_text}|{provider}|{model}"
            cache_key = hashlib.md5(content_hash.encode()).hexdigest()

            # Create a cache entry
            cache_entry = {
                "result": f"# [Injected by CodeCrusher using {provider}/{model}]\n{file_content}\n# [End Injection]",
                "timestamp": datetime.now().isoformat(),
                "provider": provider,
                "model": model
            }

            # Add to the cache
            cache_data = load_cache_from_json()
            cache_data[cache_key] = cache_entry

            # Save the cache
            try:
                CACHE_DIR.mkdir(exist_ok=True)
                with open(CACHE_FILE, "w", encoding="utf-8") as f:
                    json.dump(cache_data, f, indent=2)
                print(f"[DEBUG] Added result to cache with key: {cache_key}")
            except Exception as e:
                print(f"[WARNING] Failed to save cache: {str(e)}")
        except Exception as e:
            print(f"[WARNING] Failed to add result to cache: {str(e)}")

    return result

def run_scan():
    """Run the scan command."""
    print("🔍 Running Scan...")
    return run_command([
        sys.executable, "codecrusher_cli.py", "scan", "run",
        "-p", "src/",
        "--verbose"
    ], "Scan")

def run_optimize():
    """Run the optimize command."""
    print("⚙️  Running Optimize...")
    return run_command([
        sys.executable, "codecrusher_cli.py", "optimize", "run",
        "-i", "test_optimize.py",
        "--provider", "groq",
        "--model", "llama3",
        "--cache",
        "--verbose"
    ], "Optimize")

# Cache file paths
CACHE_DIR = Path.home() / ".codecrusher"
CACHE_FILE = CACHE_DIR / "cache.json"
SIMPLE_CACHE_FILE = Path.home() / ".codecrusher_cache.json"

# Try to import Redis
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

def get_redis_connection():
    """Get Redis connection if available."""
    if not REDIS_AVAILABLE:
        return None

    try:
        # Try to get Redis connection details from environment or config
        redis_host = os.environ.get("CODECRUSHER_REDIS_HOST", "localhost")
        redis_port = int(os.environ.get("CODECRUSHER_REDIS_PORT", "6379"))
        redis_db = int(os.environ.get("CODECRUSHER_REDIS_DB", "0"))
        redis_password = os.environ.get("CODECRUSHER_REDIS_PASSWORD", None)

        # Connect to Redis
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            password=redis_password,
            decode_responses=True  # Return strings instead of bytes
        )

        # Test connection
        r.ping()
        return r
    except Exception as e:
        console.print(f"[yellow]Warning: Redis connection failed: {str(e)}[/yellow]")
        return None

def load_cache_from_json():
    """Load cache from JSON files."""
    cache_data = {}

    # Try to load from the main cache file
    try:
        if CACHE_FILE.exists():
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data.update(json.load(f))
    except Exception as e:
        console.print(f"[yellow]Warning: Failed to load main cache file: {str(e)}[/yellow]")

    # Try to load from the simple cache file
    try:
        if SIMPLE_CACHE_FILE.exists():
            with open(SIMPLE_CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data.update(json.load(f))
    except Exception as e:
        console.print(f"[yellow]Warning: Failed to load simple cache file: {str(e)}[/yellow]")

    return cache_data

def get_cached_entry(key: str):
    """
    Get a cached entry by key.

    Args:
        key: The cache key to look up

    Returns:
        The cached entry or None if not found
    """
    # Try Redis first if available
    redis_conn = get_redis_connection()
    if redis_conn:
        try:
            cached_data = redis_conn.get(f"codecrusher:cache:{key}")
            if cached_data:
                return json.loads(cached_data)
        except Exception as e:
            console.print(f"[yellow]Warning: Redis cache lookup failed: {str(e)}[/yellow]")

    # Fallback to JSON file cache
    cache_data = load_cache_from_json()

    # Check if the key exists directly
    if key in cache_data:
        return cache_data[key]

    # Check if the key exists in any nested structure
    for cache_key, cache_value in cache_data.items():
        if isinstance(cache_value, dict) and key in cache_value:
            return cache_value[key]

        # Check one level deeper for model-specific caches
        if isinstance(cache_value, dict):
            for model_key, model_value in cache_value.items():
                if isinstance(model_value, dict) and key == model_key:
                    return model_value

    return None

# Direct command handlers
@app.callback()
def callback():
    """
    CodeCrusher CLI - Command Line Interface for CodeCrusher.
    """
    pass

# Register commands
if optimize:
    app.add_typer(optimize.app, name="optimize")
if inject:
    app.add_typer(inject.app, name="inject")
if scan:
    app.add_typer(scan.app, name="scan")
if replay:
    app.add_typer(replay.app, name="replay")
if log:
    app.add_typer(log.app, name="log")
if merge:
    app.add_typer(merge.app, name="merge")
if analyze:
    app.add_typer(analyze.app, name="analyze")
if stats:
    app.add_typer(stats.app, name="stats")
if telemetry:
    app.add_typer(telemetry.app, name="telemetry")
if dashboard:
    app.add_typer(dashboard.app, name="dashboard")
if status:
    app.add_typer(status.app, name="status")
if trends:
    app.add_typer(trends.app, name="trends")
if server:
    app.add_typer(server.app, name="serve")
if githooks:
    app.add_typer(githooks.app, name="githooks")
if install_githooks:
    app.add_typer(install_githooks.app, name="install-githooks")

@app.command("run")
def run_full_chain():
    """
    Run the full chain of inject, scan, and optimize.
    """
    print("🚀 Starting Full CodeCrusher Chain...")

    # Check if dependencies are installed
    if not check_dependencies():
        sys.exit(1)

    # Run the commands
    success = True

    if not run_inject():
        success = False

    if success and not run_scan():
        success = False

    if success and not run_optimize():
        success = False

    if success:
        print("[SUCCESS] All CodeCrusher commands executed successfully.")
    else:
        print("❌ Some commands failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    app()
