[project]
name = "codecrusher"
version = "0.1.0"
description = "CodeCrusher CLI – AugmentKiller with smart injection, fallback, feedback, and dashboard integration"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "websockets>=10.0",
    "requests>=2.28.0",
    "rich>=12.0.0",
    "halo>=0.0.31",
    "colorama>=0.4.4",
    "python-dotenv>=0.20.0",
    "aiohttp>=3.8.0",
    "typer[all]>=0.9.0",
]

[project.scripts]
codecrusher = "codecrusher_cli:main"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
packages = ["codecrusher", "source", "source.providers"]

[tool.setuptools.package-data]
codecrusher = ["*.json", "*.yaml"]

[project.urls]
"Homepage" = "https://github.com/Codegx-Technology/CodeCruncher"
"Bug Tracker" = "https://github.com/Codegx-Technology/CodeCruncher/issues"
