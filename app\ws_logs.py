"""
WebSocket logging utilities for CodeCrusher API
"""

import json
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import WebSocket

logger = logging.getLogger(__name__)

# Global list of connected WebSocket clients
clients: List[WebSocket] = []

async def broadcast_log(message: str, log_type: str = "log", level: str = "info", **kwargs):
    """Broadcast a log message to all connected WebSocket clients."""
    if not clients:
        return
    
    log_data = {
        "type": log_type,
        "message": message,
        "timestamp": datetime.now().isoformat(),
        "level": level,
        **kwargs
    }
    
    # Remove disconnected clients
    active_clients = []
    for client in clients:
        try:
            await client.send_text(json.dumps(log_data))
            active_clients.append(client)
        except Exception as e:
            logger.warning(f"Failed to send message to WebSocket client: {e}")
    
    # Update the clients list with only active connections
    clients.clear()
    clients.extend(active_clients)

async def stream_log(message: str, level: str = "info"):
    """Stream a log message to WebSocket clients."""
    await broadcast_log(message, log_type="log", level=level)

async def stream_progress(progress: int, message: str = ""):
    """Stream progress update to WebSocket clients."""
    await broadcast_log(
        message or f"Progress: {progress}%",
        log_type="progress",
        level="info",
        progress=progress,
        value=progress  # For compatibility
    )

async def stream_stats(stats: Dict[str, Any]):
    """Stream statistics to WebSocket clients."""
    await broadcast_log(
        "Statistics updated",
        log_type="stats",
        level="info",
        stats=stats
    )

async def stream_error(error_message: str, error_details: Optional[Dict[str, Any]] = None):
    """Stream error message to WebSocket clients."""
    await broadcast_log(
        error_message,
        log_type="error",
        level="error",
        error_details=error_details or {}
    )

async def stream_success(message: str, details: Optional[Dict[str, Any]] = None):
    """Stream success message to WebSocket clients."""
    await broadcast_log(
        message,
        log_type="success",
        level="info",
        details=details or {}
    )
