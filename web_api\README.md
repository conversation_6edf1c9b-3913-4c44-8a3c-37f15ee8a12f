# CodeCrusher Web API

Enterprise-grade FastAPI backend for CodeCrusher's AI-powered code injection capabilities.

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- CodeCrusher CLI installed and available in PATH
- Virtual environment (recommended)

### Installation

1. **Create and activate virtual environment:**

```bash
# Windows
python -m venv codecrushervenv
codecrushervenv\Scripts\activate

# Linux/macOS
python3 -m venv codecrushervenv
source codecrushervenv/bin/activate
```

2. **Install dependencies:**

```bash
cd web_api
pip install -r requirements.txt
```

3. **Start the server:**

```bash
python main.py
```

Or using uvicorn directly:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 📋 API Endpoints

### Core Endpoints

- **GET /** - Health check and API status
- **POST /inject** - Main code injection endpoint
- **POST /upload-inject** - Upload file and inject code
- **GET /health** - Detailed health check

### Configuration Endpoints

- **GET /models** - List available AI models
- **GET /extensions** - List supported file extensions
- **POST /validate-source** - Validate source path
- **GET /version** - API and CodeCrusher version info

### Utility Endpoints

- **GET /jobs/{job_id}** - Get background job status
- **GET /docs** - Interactive API documentation (Swagger UI)
- **GET /redoc** - Alternative API documentation

## 🔧 API Usage Examples

### Basic Code Injection

```bash
curl -X POST "http://localhost:8000/inject" \
  -H "Content-Type: application/json" \
  -d '{
    "source": "./src/main.py",
    "prompt_text": "Add comprehensive error handling",
    "preview": true,
    "auto_model_routing": true,
    "summary": true
  }'
```

### File Upload and Injection

```bash
curl -X POST "http://localhost:8000/upload-inject" \
  -F "file=@example.py" \
  -F "prompt_text=Add logging and error handling" \
  -F "apply=false" \
  -F "auto_model_routing=true"
```

### Recursive Directory Processing

```bash
curl -X POST "http://localhost:8000/inject" \
  -H "Content-Type: application/json" \
  -d '{
    "source": "./src",
    "prompt_text": "Add comprehensive logging",
    "recursive": true,
    "ext": "py,js",
    "apply": false,
    "tag": "logging-enhancement",
    "summary": true
  }'
```

## 📊 Request/Response Models

### InjectionRequest

```json
{
  "source": "string",           // Required: Path to file or folder
  "prompt_text": "string",      // Required: AI prompt
  "recursive": false,           // Optional: Scan subfolders
  "ext": "py",                  // Optional: File extensions
  "tag": "default",             // Optional: Version tag
  "apply": false,               // Optional: Apply changes
  "preview": true,              // Optional: Preview mode
  "auto_model_routing": true,   // Optional: Auto model selection
  "model": null,                // Optional: Specific model
  "refresh_cache": false,       // Optional: Bypass cache
  "force": false,               // Optional: Force without tags
  "summary": true               // Optional: Include summary
}
```

### InjectionResponse

```json
{
  "success": true,
  "message": "Code injection completed successfully",
  "output": "CLI output text...",
  "error": null,
  "statistics": {
    "total_files": 5,
    "injected_files": 4,
    "cached_files": 1,
    "failed_files": 0,
    "success_rate": 100.0,
    "model_used": "llama3-70b-8192"
  },
  "execution_time": 2.34,
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🔒 Security Considerations

### Production Deployment

1. **CORS Configuration**: Update CORS settings for production:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Specific domains
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

2. **Authentication**: Add authentication middleware for production use
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Input Validation**: Validate and sanitize all file paths
5. **File Upload Limits**: Configure appropriate file size limits

### Environment Variables

Create a `.env` file for configuration:

```env
# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false

# CodeCrusher Configuration
CODECRUSHER_PATH=codecrusher
MAX_FILE_SIZE=10485760  # 10MB

# Security
ALLOWED_ORIGINS=https://yourdomain.com
API_KEY_REQUIRED=true
```

## 🧪 Testing

### Run Tests

```bash
pytest tests/
```

### Test Coverage

```bash
pytest --cov=main tests/
```

### Manual Testing

1. **Health Check:**
```bash
curl http://localhost:8000/health
```

2. **Get Models:**
```bash
curl http://localhost:8000/models
```

3. **Validate Path:**
```bash
curl -X POST http://localhost:8000/validate-source \
  -F "source=./src"
```

## 🚀 Deployment

### Docker Deployment

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Server

```bash
# Using Gunicorn with Uvicorn workers
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 📝 Development

### Adding New Endpoints

1. Define Pydantic models for request/response
2. Add endpoint function with proper type hints
3. Include comprehensive error handling
4. Add documentation strings
5. Update tests

### Code Style

- Follow PEP 8 guidelines
- Use type hints for all functions
- Include comprehensive docstrings
- Handle errors gracefully

## 🔗 Integration

This API integrates seamlessly with:

- CodeCrusher CLI (all features supported)
- Parallel injection engine
- Cache-aware processing
- Tag detection system
- Multi-model AI routing

## 📞 Support

For issues and questions:

1. Check the interactive API docs at `/docs`
2. Review the CodeCrusher CLI documentation
3. Check server logs for detailed error information
4. Validate CodeCrusher CLI availability with `/health` endpoint
