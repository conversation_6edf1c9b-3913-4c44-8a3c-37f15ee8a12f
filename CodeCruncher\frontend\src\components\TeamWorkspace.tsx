import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Users,
  Plus,
  Mail,
  Crown,
  Shield,
  Eye,
  Calendar,
  Settings,
  UserPlus,
  Copy,
  Check,
  AlertCircle,
  Sliders
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthenticatedAPI } from '@/hooks/useAuthenticatedFetch';
import { TeamActivityFeed } from '@/components/TeamActivityFeed';
import { TeamTuning } from '@/components/TeamTuning';
import { ToastProvider, useToast } from '@/components/ToastNotifications';
import { useTeamPermissions, getRoleInfo } from '@/hooks/useTeamPermissions';

interface Team {
  id: number;
  name: string;
  description: string;
  created_by: number;
  created_at: string;
  role: string;
  joined_at: string;
}

interface TeamMember {
  id: number;
  email: string;
  team_role: string;
  joined_at: string;
  user_created_at: string;
}

interface CreateTeamData {
  name: string;
  description: string;
}

interface InviteUserData {
  email: string;
  role: string;
}

export function TeamWorkspace() {
  const { user } = useAuth();
  const api = useAuthenticatedAPI();

  const [teams, setTeams] = useState<Team[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [showCreateTeam, setShowCreateTeam] = useState(false);
  const [showInviteUser, setShowInviteUser] = useState(false);
  const [createTeamData, setCreateTeamData] = useState<CreateTeamData>({ name: '', description: '' });
  const [inviteUserData, setInviteUserData] = useState<InviteUserData>({ email: '', role: 'member' });
  const [inviteToken, setInviteToken] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const [activityFeedCollapsed, setActivityFeedCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'tuning'>('overview');

  // Load user teams
  useEffect(() => {
    loadTeams();
  }, []);

  // Load team members when team is selected
  useEffect(() => {
    if (selectedTeam) {
      loadTeamMembers(selectedTeam.id);
    }
  }, [selectedTeam]);

  const loadTeams = async () => {
    try {
      setLoading(true);
      const teamsData = await api.get('/teams');
      setTeams(teamsData);

      // Auto-select first team if available
      if (teamsData.length > 0 && !selectedTeam) {
        setSelectedTeam(teamsData[0]);
      }
    } catch (err) {
      setError('Failed to load teams');
      console.error('Load teams error:', err);
    } finally {
      setLoading(false);
    }
  };

  const loadTeamMembers = async (teamId: number) => {
    try {
      const members = await api.get(`/teams/${teamId}/members`);
      setTeamMembers(members);
    } catch (err) {
      console.error('Load team members error:', err);
    }
  };

  const handleCreateTeam = async () => {
    if (!createTeamData.name.trim()) {
      setError('Team name is required');
      return;
    }

    try {
      await api.post('/teams', createTeamData);
      setShowCreateTeam(false);
      setCreateTeamData({ name: '', description: '' });
      await loadTeams();
    } catch (err) {
      setError('Failed to create team');
      console.error('Create team error:', err);
    }
  };

  const handleInviteUser = async () => {
    if (!inviteUserData.email.trim() || !selectedTeam) {
      setError('Email is required');
      return;
    }

    try {
      const response = await api.post(`/teams/${selectedTeam.id}/invite`, inviteUserData);
      setInviteToken(response.invitation_token);
      setInviteUserData({ email: '', role: 'member' });
    } catch (err) {
      setError('Failed to send invitation');
      console.error('Invite user error:', err);
    }
  };

  const copyInviteLink = () => {
    if (inviteToken) {
      const inviteLink = `${window.location.origin}/join-team?token=${inviteToken}`;
      navigator.clipboard.writeText(inviteLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'member': return <Shield className="h-4 w-4 text-blue-500" />;
      case 'viewer': return <Eye className="h-4 w-4 text-gray-500" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'member': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'viewer': return 'bg-gray-100 text-gray-800 border-gray-300';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading teams...</p>
        </div>
      </div>
    );
  }

  return (
    <ToastProvider>
      <div className="flex gap-6">
        {/* Main Content */}
        <div className="flex-1 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Team Workspace</h2>
          <p className="text-gray-600">Collaborate with your team on CodeCrusher projects</p>
        </div>

        <Dialog open={showCreateTeam} onOpenChange={setShowCreateTeam}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create Team
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Team</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="team-name">Team Name</Label>
                <Input
                  id="team-name"
                  value={createTeamData.name}
                  onChange={(e) => setCreateTeamData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter team name"
                />
              </div>
              <div>
                <Label htmlFor="team-description">Description (Optional)</Label>
                <Input
                  id="team-description"
                  value={createTeamData.description}
                  onChange={(e) => setCreateTeamData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your team's purpose"
                />
              </div>
              <div className="flex gap-2 pt-4">
                <Button onClick={handleCreateTeam} className="flex-1">
                  Create Team
                </Button>
                <Button variant="outline" onClick={() => setShowCreateTeam(false)} className="flex-1">
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Teams List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Your Teams ({teams.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {teams.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No teams yet</p>
                  <p className="text-sm">Create your first team to get started</p>
                </div>
              ) : (
                teams.map((team) => (
                  <div
                    key={team.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedTeam?.id === team.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedTeam(team)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">{team.name}</h3>
                        <p className="text-sm text-gray-500 truncate">{team.description}</p>
                      </div>
                      <Badge className={getRoleBadgeColor(team.role)}>
                        {getRoleIcon(team.role)}
                        <span className="ml-1 capitalize">{team.role}</span>
                      </Badge>
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>
        </div>

        {/* Team Details */}
        <div className="lg:col-span-2">
          {selectedTeam ? (
            <div className="space-y-6">
              {/* Enhanced Tab Navigation */}
              <Card className="shadow-sm border-0 bg-gradient-to-r from-blue-50 to-purple-50">
                <CardContent className="p-0">
                  <div className="flex border-b border-gray-200">
                    <button
                      onClick={() => setActiveTab('overview')}
                      className={`group relative px-6 py-4 text-sm font-medium transition-all duration-200 ${
                        activeTab === 'overview'
                          ? 'text-blue-600 bg-white border-b-2 border-blue-500 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        Overview
                      </div>
                      {activeTab === 'overview' && (
                        <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                      )}
                    </button>

                    <button
                      onClick={() => setActiveTab('members')}
                      className={`group relative px-6 py-4 text-sm font-medium transition-all duration-200 ${
                        activeTab === 'members'
                          ? 'text-blue-600 bg-white border-b-2 border-blue-500 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        Members
                        <Badge variant="secondary" className="ml-1 text-xs">
                          {teamMembers.length}
                        </Badge>
                      </div>
                      {activeTab === 'members' && (
                        <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                      )}
                    </button>

                    <button
                      onClick={() => setActiveTab('tuning')}
                      className={`group relative px-6 py-4 text-sm font-medium transition-all duration-200 ${
                        activeTab === 'tuning'
                          ? 'text-blue-600 bg-white border-b-2 border-blue-500 shadow-sm'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/50'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <Sliders className="h-4 w-4" />
                        Prompt Tuning
                        {(() => {
                          const permissions = useTeamPermissions({ userRole: selectedTeam.role });
                          return !permissions.canEditTuning && (
                            <Lock className="h-3 w-3 text-gray-400" />
                          );
                        })()}
                      </div>
                      {activeTab === 'tuning' && (
                        <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                      )}
                    </button>
                  </div>
                </CardContent>
              </Card>

              {/* Tab Content */}
              {activeTab === 'overview' && (
                <div className="space-y-6">
              {/* Team Info */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {getRoleIcon(selectedTeam.role)}
                        {selectedTeam.name}
                      </CardTitle>
                      <CardDescription>{selectedTeam.description}</CardDescription>
                    </div>

                    {(() => {
                      const permissions = useTeamPermissions({ userRole: selectedTeam.role });
                      return permissions.canInviteMembers && (
                        <Dialog open={showInviteUser} onOpenChange={setShowInviteUser}>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm" className="flex items-center gap-2">
                              <UserPlus className="h-4 w-4" />
                              Invite Member
                            </Button>
                          </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Invite Team Member</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="invite-email">Email Address</Label>
                              <Input
                                id="invite-email"
                                type="email"
                                value={inviteUserData.email}
                                onChange={(e) => setInviteUserData(prev => ({ ...prev, email: e.target.value }))}
                                placeholder="Enter email address"
                              />
                            </div>
                            <div>
                              <Label htmlFor="invite-role">Role</Label>
                              <select
                                id="invite-role"
                                value={inviteUserData.role}
                                onChange={(e) => setInviteUserData(prev => ({ ...prev, role: e.target.value }))}
                                className="w-full p-2 border border-gray-300 rounded-md"
                              >
                                <option value="member">Member</option>
                                <option value="viewer">Viewer</option>
                              </select>
                            </div>

                            {inviteToken && (
                              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                <p className="text-sm text-green-800 mb-2">Invitation created! Share this link:</p>
                                <div className="flex items-center gap-2">
                                  <Input
                                    value={`${window.location.origin}/join-team?token=${inviteToken}`}
                                    readOnly
                                    className="text-xs"
                                  />
                                  <Button
                                    size="sm"
                                    onClick={copyInviteLink}
                                    className="flex items-center gap-1"
                                  >
                                    {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                                    {copied ? 'Copied!' : 'Copy'}
                                  </Button>
                                </div>
                              </div>
                            )}

                            <div className="flex gap-2 pt-4">
                              <Button onClick={handleInviteUser} className="flex-1">
                                Send Invitation
                              </Button>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setShowInviteUser(false);
                                  setInviteToken(null);
                                }}
                                className="flex-1"
                              >
                                Close
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                      );
                    })()}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">Your Role:</span>
                      <div className="flex items-center gap-1 mt-1">
                        {getRoleIcon(selectedTeam.role)}
                        <span className="capitalize font-medium">{selectedTeam.role}</span>
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-500">Joined:</span>
                      <div className="flex items-center gap-1 mt-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(selectedTeam.joined_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
                </div>
              )}

              {activeTab === 'members' && (
                <Card className="shadow-sm">
                  <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50">
                    <CardTitle className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      Team Members ({teamMembers.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {teamMembers.map((member) => {
                        const roleInfo = getRoleInfo(member.team_role);
                        return (
                          <div key={member.id} className="group relative p-4 border border-gray-200 rounded-xl hover:border-blue-300 hover:shadow-md transition-all duration-200 bg-white">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-4">
                                <div className="relative">
                                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-md">
                                    <span className="text-white text-lg font-semibold">
                                      {member.email.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-sm border-2 border-white">
                                    <span className="text-xs">{roleInfo.icon}</span>
                                  </div>
                                </div>
                                <div>
                                  <p className="font-semibold text-gray-900">{member.email}</p>
                                  <p className="text-sm text-gray-500 flex items-center gap-1">
                                    <Calendar className="h-3 w-3" />
                                    Joined {new Date(member.joined_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge className={`${roleInfo.color} px-3 py-1 font-medium`}>
                                  <span className="mr-1">{roleInfo.icon}</span>
                                  {roleInfo.label}
                                </Badge>
                              </div>
                            </div>

                            {/* Hover tooltip */}
                            <div className="absolute top-full left-4 mt-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                              {roleInfo.description}
                              <div className="absolute -top-1 left-4 w-2 h-2 bg-gray-900 rotate-45"></div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'tuning' && (
                <TeamTuning teamId={selectedTeam.id} userRole={selectedTeam.role} />
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a team to view details</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
        </div>

        {/* Activity Feed Sidebar */}
        <div className="flex-shrink-0">
          {selectedTeam && (
            <TeamActivityFeed
              teamId={selectedTeam.id}
              isCollapsed={activityFeedCollapsed}
              onToggleCollapse={() => setActivityFeedCollapsed(!activityFeedCollapsed)}
            />
          )}
        </div>
      </div>
    </ToastProvider>
  );
}
