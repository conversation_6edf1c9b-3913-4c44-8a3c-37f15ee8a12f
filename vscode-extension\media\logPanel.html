<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeCrusher Live Logs</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: var(--vscode-titleBar-activeBackground);
            color: var(--vscode-titleBar-activeForeground);
            padding: 12px 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .header h1 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .header-controls {
            display: flex;
            gap: 8px;
        }

        .btn {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }

        .btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .btn.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .btn.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .status-bar {
            background-color: var(--vscode-statusBar-background);
            color: var(--vscode-statusBar-foreground);
            padding: 8px 16px;
            border-bottom: 1px solid var(--vscode-panel-border);
            font-size: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #ff6b6b;
        }

        .status-dot.connected {
            background-color: #51cf66;
        }

        .status-dot.connecting {
            background-color: #ffd43b;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .log-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid transparent;
            background-color: var(--vscode-editor-background);
        }

        .log-entry.info {
            border-left-color: #339af0;
            background-color: rgba(51, 154, 240, 0.1);
        }

        .log-entry.success {
            border-left-color: #51cf66;
            background-color: rgba(81, 207, 102, 0.1);
        }

        .log-entry.warning {
            border-left-color: #ffd43b;
            background-color: rgba(255, 212, 59, 0.1);
        }

        .log-entry.error {
            border-left-color: #ff6b6b;
            background-color: rgba(255, 107, 107, 0.1);
        }

        .log-entry.model {
            border-left-color: #9775fa;
            background-color: rgba(151, 117, 250, 0.1);
        }

        .log-timestamp {
            color: var(--vscode-descriptionForeground);
            font-size: 11px;
            margin-right: 8px;
        }

        .log-level {
            font-weight: 600;
            margin-right: 8px;
            text-transform: uppercase;
            font-size: 11px;
        }

        .log-message {
            word-wrap: break-word;
        }

        .highlight {
            background-color: var(--vscode-editor-findMatchHighlightBackground);
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: 600;
        }

        .model-highlight {
            color: #9775fa;
            font-weight: 600;
        }

        .fallback-highlight {
            color: #ffd43b;
            font-weight: 600;
        }

        .time-highlight {
            color: #51cf66;
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            color: var(--vscode-descriptionForeground);
            margin-top: 40px;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .scrollbar-track {
            scrollbar-width: thin;
            scrollbar-color: var(--vscode-scrollbarSlider-background) var(--vscode-scrollbar-shadow);
        }

        .log-container::-webkit-scrollbar {
            width: 8px;
        }

        .log-container::-webkit-scrollbar-track {
            background: var(--vscode-scrollbar-shadow);
        }

        .log-container::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-background);
            border-radius: 4px;
        }

        .log-container::-webkit-scrollbar-thumb:hover {
            background: var(--vscode-scrollbarSlider-hoverBackground);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 CodeCrusher Live Logs</h1>
        <div class="header-controls">
            <button class="btn secondary" onclick="clearLogs()">Clear Logs</button>
            <button class="btn secondary" onclick="toggleAutoScroll()">Auto-scroll: ON</button>
            <button class="btn" onclick="reconnectWebSocket()">Reconnect</button>
        </div>
    </div>

    <div class="status-bar">
        <div class="status-indicator">
            <div class="status-dot" id="statusDot"></div>
            <span id="statusText">Disconnected</span>
        </div>
        <div id="logCount">0 logs</div>
    </div>

    <div class="log-container" id="logContainer">
        <div class="empty-state">
            <div class="empty-state-icon">📋</div>
            <h3>No logs yet</h3>
            <p>Waiting for CodeCrusher injection logs...</p>
            <p>Make sure the WebSocket server is running on the configured port.</p>
        </div>
    </div>

    <script>
        let ws = null;
        let logCount = 0;
        let autoScroll = true;
        let wsPort = 11434; // Default port, will be updated from extension

        // Initialize WebSocket connection
        function initWebSocket(port = wsPort) {
            wsPort = port;
            const wsUrl = `ws://localhost:${wsPort}/logs`;
            
            updateStatus('connecting', 'Connecting...');
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function() {
                    updateStatus('connected', `Connected to localhost:${wsPort}`);
                    addLogEntry('info', 'WebSocket connection established');
                };
                
                ws.onmessage = function(event) {
                    try {
                        const logData = JSON.parse(event.data);
                        addLogEntry(logData.level || 'info', logData.message, logData.timestamp);
                    } catch (e) {
                        // Fallback for plain text messages
                        addLogEntry('info', event.data);
                    }
                };
                
                ws.onclose = function() {
                    updateStatus('disconnected', 'Connection lost');
                    addLogEntry('warning', 'WebSocket connection closed');
                };
                
                ws.onerror = function(error) {
                    updateStatus('disconnected', 'Connection error');
                    addLogEntry('error', `WebSocket error: ${error.message || 'Unknown error'}`);
                };
                
            } catch (error) {
                updateStatus('disconnected', 'Failed to connect');
                addLogEntry('error', `Failed to connect to WebSocket: ${error.message}`);
            }
        }

        function updateStatus(status, text) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            statusDot.className = `status-dot ${status}`;
            statusText.textContent = text;
        }

        function addLogEntry(level, message, timestamp = null) {
            const container = document.getElementById('logContainer');
            const emptyState = container.querySelector('.empty-state');
            
            // Remove empty state on first log
            if (emptyState) {
                emptyState.remove();
            }
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            
            const time = timestamp ? new Date(timestamp) : new Date();
            const timeStr = time.toLocaleTimeString();
            
            // Highlight special content
            const highlightedMessage = highlightLogContent(message);
            
            logEntry.innerHTML = `
                <span class="log-timestamp">${timeStr}</span>
                <span class="log-level">${level}</span>
                <span class="log-message">${highlightedMessage}</span>
            `;
            
            container.appendChild(logEntry);
            logCount++;
            
            // Update log count
            document.getElementById('logCount').textContent = `${logCount} logs`;
            
            // Auto-scroll to bottom
            if (autoScroll) {
                container.scrollTop = container.scrollHeight;
            }
        }

        function highlightLogContent(message) {
            // Highlight model names
            message = message.replace(/\b(auto|mistral|gemma|mixtral|llama3-8b|llama3-70b)\b/gi, 
                '<span class="model-highlight">$1</span>');
            
            // Highlight fallback mentions
            message = message.replace(/\b(fallback|backup|retry)\b/gi, 
                '<span class="fallback-highlight">$1</span>');
            
            // Highlight time measurements
            message = message.replace(/\b(\d+(?:\.\d+)?)\s*(ms|seconds?|minutes?)\b/gi, 
                '<span class="time-highlight">$1 $2</span>');
            
            // Highlight file paths
            message = message.replace(/\b[\w\-_]+\.(py|js|ts|java|cs|go|rs|html|css)\b/gi, 
                '<span class="highlight">$&</span>');
            
            return message;
        }

        function clearLogs() {
            const container = document.getElementById('logContainer');
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">📋</div>
                    <h3>Logs cleared</h3>
                    <p>Waiting for new CodeCrusher injection logs...</p>
                </div>
            `;
            logCount = 0;
            document.getElementById('logCount').textContent = '0 logs';
        }

        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const button = event.target;
            button.textContent = `Auto-scroll: ${autoScroll ? 'ON' : 'OFF'}`;
        }

        function reconnectWebSocket() {
            if (ws) {
                ws.close();
            }
            initWebSocket(wsPort);
        }

        // Listen for messages from the extension
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'setPort':
                    wsPort = message.port;
                    break;
                case 'connect':
                    initWebSocket(message.port || wsPort);
                    break;
                case 'addLog':
                    addLogEntry(message.level, message.message, message.timestamp);
                    break;
            }
        });

        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            // Will be connected when extension sends the port
        });
    </script>
</body>
</html>
