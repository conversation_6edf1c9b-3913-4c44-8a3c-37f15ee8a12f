#!/usr/bin/env python3
"""
CLI Authentication Client for CodeCrusher
Handles user login, token storage, and authenticated API requests
"""

import os
import json
import getpass
import requests
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Configuration
DEFAULT_API_BASE = "http://localhost:8001"
TOKEN_FILE = Path.home() / ".codecrusher_token"
CONFIG_FILE = Path.home() / ".codecrusher_config"

class AuthClient:
    """CLI Authentication client for CodeCrusher."""
    
    def __init__(self, api_base: str = None):
        """Initialize auth client."""
        self.api_base = api_base or DEFAULT_API_BASE
        self.token = None
        self.user_info = None
        self.load_token()
    
    def load_token(self) -> bool:
        """Load stored authentication token."""
        try:
            if TOKEN_FILE.exists():
                with open(TOKEN_FILE, 'r') as f:
                    token_data = json.load(f)
                
                # Check if token is expired
                expires_at = datetime.fromisoformat(token_data.get('expires_at', ''))
                if datetime.now() < expires_at:
                    self.token = token_data.get('access_token')
                    self.user_info = token_data.get('user_info', {})
                    logger.debug(f"Loaded valid token for user: {self.user_info.get('email', 'unknown')}")
                    return True
                else:
                    logger.debug("Stored token has expired")
                    self.clear_token()
            
            return False
        
        except Exception as e:
            logger.warning(f"Failed to load token: {e}")
            self.clear_token()
            return False
    
    def save_token(self, token_data: Dict[str, Any], user_info: Dict[str, Any]) -> bool:
        """Save authentication token to file."""
        try:
            # Calculate expiration time
            expires_in = token_data.get('expires_in', 1800)  # Default 30 minutes
            expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            token_info = {
                'access_token': token_data.get('access_token'),
                'token_type': token_data.get('token_type', 'bearer'),
                'expires_in': expires_in,
                'expires_at': expires_at.isoformat(),
                'user_info': user_info,
                'saved_at': datetime.now().isoformat()
            }
            
            # Ensure directory exists
            TOKEN_FILE.parent.mkdir(parents=True, exist_ok=True)
            
            # Save token with restricted permissions
            with open(TOKEN_FILE, 'w') as f:
                json.dump(token_info, f, indent=2)
            
            # Set file permissions (readable only by owner)
            os.chmod(TOKEN_FILE, 0o600)
            
            self.token = token_info['access_token']
            self.user_info = user_info
            
            logger.info(f"Token saved for user: {user_info.get('email', 'unknown')}")
            return True
        
        except Exception as e:
            logger.error(f"Failed to save token: {e}")
            return False
    
    def clear_token(self):
        """Clear stored authentication token."""
        try:
            if TOKEN_FILE.exists():
                TOKEN_FILE.unlink()
            self.token = None
            self.user_info = None
            logger.info("Authentication token cleared")
        except Exception as e:
            logger.warning(f"Failed to clear token: {e}")
    
    def login(self, email: str = None, password: str = None) -> bool:
        """Login user and store authentication token."""
        try:
            # Get credentials if not provided
            if not email:
                email = input("Email: ").strip()
            
            if not password:
                password = getpass.getpass("Password: ")
            
            if not email or not password:
                print("❌ Email and password are required")
                return False
            
            print(f"🔐 Authenticating {email}...")
            
            # Send login request
            response = requests.post(
                f"{self.api_base}/auth/login",
                json={"email": email, "password": password},
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                token_data = response.json()
                
                # Get user info
                user_info = self.get_user_info(token_data.get('access_token'))
                if not user_info:
                    print("❌ Failed to get user information")
                    return False
                
                # Save token
                if self.save_token(token_data, user_info):
                    print(f"✅ Login successful! Welcome, {user_info.get('email', 'User')}")
                    print(f"🕒 Token expires in {token_data.get('expires_in', 1800)} seconds")
                    return True
                else:
                    print("❌ Failed to save authentication token")
                    return False
            
            elif response.status_code == 401:
                print("❌ Invalid email or password")
                return False
            
            else:
                print(f"❌ Login failed: {response.status_code} - {response.text}")
                return False
        
        except requests.exceptions.ConnectionError:
            print(f"❌ Cannot connect to CodeCrusher API at {self.api_base}")
            print("   Make sure the backend server is running")
            return False
        
        except requests.exceptions.Timeout:
            print("❌ Login request timed out")
            return False
        
        except Exception as e:
            logger.error(f"Login error: {e}")
            print(f"❌ Login failed: {e}")
            return False
    
    def get_user_info(self, token: str) -> Optional[Dict[str, Any]]:
        """Get user information using token."""
        try:
            response = requests.get(
                f"{self.api_base}/auth/me",
                headers={"Authorization": f"Bearer {token}"},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Failed to get user info: {response.status_code}")
                return None
        
        except Exception as e:
            logger.error(f"Get user info error: {e}")
            return None
    
    def logout(self) -> bool:
        """Logout user and clear stored token."""
        try:
            if self.token:
                # Send logout request to server
                try:
                    response = requests.post(
                        f"{self.api_base}/auth/logout",
                        headers={"Authorization": f"Bearer {self.token}"},
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        logger.info("Server logout successful")
                    else:
                        logger.warning(f"Server logout failed: {response.status_code}")
                
                except Exception as e:
                    logger.warning(f"Server logout error: {e}")
                
                # Clear local token regardless of server response
                self.clear_token()
                print("✅ Logged out successfully")
                return True
            else:
                print("ℹ️ Not currently logged in")
                return True
        
        except Exception as e:
            logger.error(f"Logout error: {e}")
            print(f"❌ Logout failed: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if user is currently authenticated."""
        return self.token is not None
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API requests."""
        if self.token:
            return {"Authorization": f"Bearer {self.token}"}
        return {}
    
    def status(self):
        """Display current authentication status."""
        if self.is_authenticated():
            user_email = self.user_info.get('email', 'Unknown')
            user_role = self.user_info.get('role', 'user')
            created_at = self.user_info.get('created_at', 'Unknown')
            
            print(f"✅ Logged in as: {user_email}")
            print(f"👤 Role: {user_role}")
            print(f"📅 Account created: {created_at}")
            print(f"🔗 API endpoint: {self.api_base}")
            
            # Check token expiration
            try:
                if TOKEN_FILE.exists():
                    with open(TOKEN_FILE, 'r') as f:
                        token_data = json.load(f)
                    
                    expires_at = datetime.fromisoformat(token_data.get('expires_at', ''))
                    time_left = expires_at - datetime.now()
                    
                    if time_left.total_seconds() > 0:
                        hours, remainder = divmod(int(time_left.total_seconds()), 3600)
                        minutes, seconds = divmod(remainder, 60)
                        print(f"⏰ Token expires in: {hours:02d}:{minutes:02d}:{seconds:02d}")
                    else:
                        print("⚠️ Token has expired - please login again")
            except Exception:
                pass
        else:
            print("❌ Not logged in")
            print(f"🔗 API endpoint: {self.api_base}")
            print("💡 Use 'codecrusher login' to authenticate")
    
    def make_authenticated_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make an authenticated API request."""
        if not self.is_authenticated():
            raise Exception("Not authenticated. Please login first.")
        
        # Add authentication headers
        headers = kwargs.get('headers', {})
        headers.update(self.get_auth_headers())
        kwargs['headers'] = headers
        
        # Make request
        url = f"{self.api_base}{endpoint}"
        response = requests.request(method, url, **kwargs)
        
        # Handle token expiration
        if response.status_code == 401:
            print("⚠️ Authentication token has expired. Please login again.")
            self.clear_token()
            raise Exception("Authentication token expired")
        
        return response

# Global auth client instance
_auth_client = None

def get_auth_client() -> AuthClient:
    """Get global authentication client instance."""
    global _auth_client
    if _auth_client is None:
        _auth_client = AuthClient()
    return _auth_client

def require_auth() -> AuthClient:
    """Require authentication and return auth client."""
    client = get_auth_client()
    if not client.is_authenticated():
        print("❌ Authentication required")
        print("💡 Please login first: codecrusher login")
        raise SystemExit(1)
    return client

# Convenience functions
def login(email: str = None, password: str = None) -> bool:
    """Login user."""
    return get_auth_client().login(email, password)

def logout() -> bool:
    """Logout user."""
    return get_auth_client().logout()

def status():
    """Show authentication status."""
    return get_auth_client().status()

def is_authenticated() -> bool:
    """Check if user is authenticated."""
    return get_auth_client().is_authenticated()

def get_auth_headers() -> Dict[str, str]:
    """Get authentication headers."""
    return get_auth_client().get_auth_headers()

if __name__ == "__main__":
    # Simple CLI test
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "login":
            login()
        elif command == "logout":
            logout()
        elif command == "status":
            status()
        else:
            print(f"Unknown command: {command}")
            print("Available commands: login, logout, status")
    else:
        status()
