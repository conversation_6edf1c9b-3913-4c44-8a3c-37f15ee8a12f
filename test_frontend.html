<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeCrusher Dashboard Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        .warning { background: #5a5a2d; }
        button {
            background: #4a9eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #3a8eef; }
        #logs {
            background: #1a1a1a;
            padding: 10px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            margin-top: 10px;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background: #3a3a3a;
            border: 1px solid #555;
            color: white;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CodeCrusher Dashboard Test</h1>
        
        <div id="connection-status" class="status warning">
            🔌 Connecting to WebSocket...
        </div>
        
        <div>
            <h3>📁 File Path</h3>
            <input type="text" id="filePath" placeholder="Enter file path..." 
                   value="C:\Users\<USER>\Documents\augment-projects\codecrusherv2\CodeCruncher\example.py">
        </div>
        
        <div>
            <h3>💬 Prompt</h3>
            <textarea id="prompt" rows="3" placeholder="Enter your optimization prompt...">Add error handling to this function</textarea>
        </div>
        
        <div>
            <button onclick="testConnection()">🧪 Test Connection</button>
            <button onclick="sendOptimization()" id="optimizeBtn" disabled>🤖 AI Optimization</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
        
        <div>
            <h3>📊 Real-time Logs</h3>
            <div id="logs"></div>
        </div>
    </div>

    <script>
        let websocket = null;
        let connectionAttempts = 0;
        const maxRetries = 999;
        
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logs.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const status = document.getElementById('connection-status');
            status.textContent = message;
            status.className = `status ${type}`;
            
            const btn = document.getElementById('optimizeBtn');
            btn.disabled = type !== 'success';
        }
        
        function connectWebSocket() {
            try {
                connectionAttempts++;
                log(`🔌 Attempting WebSocket connection (${connectionAttempts}/${maxRetries})...`);
                updateStatus(`🔌 Connecting... (attempt ${connectionAttempts})`, 'warning');
                
                websocket = new WebSocket('ws://127.0.0.1:8001/ws/logs');
                
                websocket.onopen = function(event) {
                    log('✅ WebSocket connected successfully!', 'success');
                    updateStatus('✅ Connected to CodeCrusher Backend', 'success');
                    connectionAttempts = 0;
                };
                
                websocket.onmessage = function(event) {
                    log(`📨 ${event.data}`, 'success');
                };
                
                websocket.onclose = function(event) {
                    log(`❌ WebSocket closed (code: ${event.code}, reason: ${event.reason})`, 'error');
                    updateStatus('❌ Connection Lost', 'error');
                    
                    if (connectionAttempts < maxRetries) {
                        setTimeout(connectWebSocket, 2000);
                    }
                };
                
                websocket.onerror = function(error) {
                    log(`💥 WebSocket error: ${error}`, 'error');
                    updateStatus('💥 Connection Error', 'error');
                };
                
            } catch (error) {
                log(`💥 Failed to create WebSocket: ${error}`, 'error');
                updateStatus('💥 Connection Failed', 'error');
            }
        }
        
        function testConnection() {
            log('🧪 Testing backend connection...');
            
            fetch('http://127.0.0.1:8001/health')
                .then(response => response.json())
                .then(data => {
                    log(`✅ Backend health check: ${data.status}`, 'success');
                    log(`📊 Connected clients: ${data.connected_clients}`, 'info');
                })
                .catch(error => {
                    log(`❌ Backend health check failed: ${error}`, 'error');
                });
        }
        
        function sendOptimization() {
            const filePath = document.getElementById('filePath').value;
            const prompt = document.getElementById('prompt').value;
            
            if (!filePath || !prompt) {
                log('❌ Please enter both file path and prompt', 'error');
                return;
            }
            
            log('🚀 Sending optimization request...', 'info');
            
            const data = {
                file_path: filePath,
                prompt: prompt,
                timestamp: new Date().toISOString()
            };
            
            fetch('http://127.0.0.1:8001/inject', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                log(`✅ Optimization request sent successfully`, 'success');
                log(`📝 Response: ${JSON.stringify(result)}`, 'info');
            })
            .catch(error => {
                log(`❌ Optimization request failed: ${error}`, 'error');
            });
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        // Initialize connection when page loads
        window.onload = function() {
            log('🚀 CodeCrusher Dashboard Test initialized');
            connectWebSocket();
            testConnection();
        };
    </script>
</body>
</html>
