/* Syntax Highlighting for Code Blocks */

/* Base code styling */
.code-block pre code {
  display: block;
  overflow-x: auto;
  padding: 0;
  color: #e5e7eb;
  background: transparent;
}

/* Syntax highlighting colors */
.hljs {
  color: #e5e7eb;
  background: #1e293b;
}

.hljs-comment,
.hljs-quote {
  color: #6b7280;
  font-style: italic;
}

.hljs-keyword,
.hljs-selector-tag,
.hljs-subst {
  color: #f472b6;
  font-weight: bold;
}

.hljs-number,
.hljs-literal,
.hljs-variable,
.hljs-template-variable,
.hljs-tag .hljs-attr {
  color: #fbbf24;
}

.hljs-string,
.hljs-doctag {
  color: #34d399;
}

.hljs-title,
.hljs-section,
.hljs-selector-id {
  color: #60a5fa;
  font-weight: bold;
}

.hljs-type,
.hljs-class .hljs-title {
  color: #a78bfa;
  font-weight: bold;
}

.hljs-tag,
.hljs-name,
.hljs-attribute {
  color: #f87171;
  font-weight: normal;
}

.hljs-regexp,
.hljs-link {
  color: #34d399;
}

.hljs-symbol,
.hljs-bullet {
  color: #fbbf24;
}

.hljs-built_in,
.hljs-builtin-name {
  color: #60a5fa;
}

.hljs-meta {
  color: #6b7280;
}

.hljs-deletion {
  background: #fecaca;
  color: #dc2626;
}

.hljs-addition {
  background: #bbf7d0;
  color: #059669;
}

.hljs-emphasis {
  font-style: italic;
}

.hljs-strong {
  font-weight: bold;
}

/* Language-specific highlighting */

/* Bash/Shell */
.language-bash .hljs-built_in {
  color: #34d399;
}

.language-bash .hljs-variable {
  color: #fbbf24;
}

/* Python */
.language-python .hljs-function .hljs-title {
  color: #60a5fa;
}

.language-python .hljs-decorator {
  color: #f472b6;
}

/* JavaScript */
.language-javascript .hljs-function {
  color: #f472b6;
}

.language-javascript .hljs-params {
  color: #fbbf24;
}

/* JSON */
.language-json .hljs-attr {
  color: #60a5fa;
}

.language-json .hljs-string {
  color: #34d399;
}

/* YAML */
.language-yaml .hljs-attr {
  color: #60a5fa;
}

.language-yaml .hljs-string {
  color: #34d399;
}

/* Terminal/Console styling */
.terminal-line .hljs-built_in {
  color: #34d399;
}

.terminal-line .hljs-string {
  color: #fbbf24;
}

.terminal-line .hljs-comment {
  color: #6b7280;
}

/* Code block enhancements */
.code-block {
  position: relative;
}

.code-block:hover .copy-btn {
  opacity: 1;
}

.copy-btn {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.copy-btn.copied {
  color: #34d399 !important;
}

.copy-btn.copied::after {
  content: " Copied!";
  font-size: 0.75rem;
  margin-left: 0.5rem;
}

/* Inline code styling */
code:not(.hljs) {
  background: var(--bg-tertiary);
  color: var(--primary-color);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-family: var(--font-mono);
  font-size: 0.875em;
  font-weight: 500;
}

/* Dark theme adjustments */
[data-theme="dark"] code:not(.hljs) {
  background: var(--bg-tertiary);
  color: #60a5fa;
}

/* Command prompt styling */
.command-prompt {
  color: #34d399;
  user-select: none;
}

.command-prompt::before {
  content: "$ ";
  color: #34d399;
}

/* Output styling */
.command-output {
  color: #d1d5db;
  margin-left: 1rem;
}

.command-output.success {
  color: #34d399;
}

.command-output.error {
  color: #f87171;
}

.command-output.warning {
  color: #fbbf24;
}

.command-output.info {
  color: #60a5fa;
}

/* Code block line numbers */
.code-block.line-numbers {
  counter-reset: line;
}

.code-block.line-numbers pre {
  padding-left: 3rem;
}

.code-block.line-numbers pre code {
  position: relative;
}

.code-block.line-numbers pre code::before {
  counter-increment: line;
  content: counter(line);
  position: absolute;
  left: -2.5rem;
  top: 0;
  color: #6b7280;
  font-size: 0.75rem;
  line-height: inherit;
  text-align: right;
  width: 2rem;
  user-select: none;
}

/* Scrollbar styling for code blocks */
.code-block pre::-webkit-scrollbar {
  height: 8px;
}

.code-block pre::-webkit-scrollbar-track {
  background: #374151;
}

.code-block pre::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

.code-block pre::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Firefox scrollbar */
.code-block pre {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 #374151;
}

/* Responsive code blocks */
@media (max-width: 768px) {
  .code-block pre {
    font-size: 0.8rem;
    padding: 1rem;
  }
  
  .code-header {
    padding: 0.5rem 1rem;
  }
  
  .code-lang {
    font-size: 0.8rem;
  }
}

/* Animation for copy feedback */
@keyframes copySuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.copy-btn.copied {
  animation: copySuccess 0.3s ease;
}
