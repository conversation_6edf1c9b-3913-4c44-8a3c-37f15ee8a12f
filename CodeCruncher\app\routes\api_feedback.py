"""
Feedback API Router for CodeCrusher

This module provides API endpoints for capturing user feedback on code injections,
enabling analytics and quality improvement of the AI generation system.

Features:
- Feedback capture with rating and comments
- Structured logging to JSONL format
- Metadata collection (model, intent, prompt, file)
- Timestamp tracking for analytics
- Optional Redis/SQLite integration ready
"""

import json
import os
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from fastapi import APIRouter, Request, HTTPException
from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)

router = APIRouter()

# Feedback data models
class FeedbackRequest(BaseModel):
    """Structured feedback request model."""
    rating: str = Field(..., description="User rating: 'up', 'down', or 'neutral'")
    comment: Optional[str] = Field(None, description="Optional user comment")
    model: Optional[str] = Field(None, description="AI model used for generation")
    intent: Optional[str] = Field(None, description="Detected or specified intent")
    prompt: Optional[str] = Field(None, description="Original user prompt")
    file: Optional[str] = Field(None, description="Target file path")
    injection_id: Optional[str] = Field(None, description="Unique injection identifier")
    session_id: Optional[str] = Field(None, description="User session identifier")
    diff: Optional[str] = Field(None, description="Code diff showing changes made")
    before_code: Optional[str] = Field(None, description="Original code before injection")
    after_code: Optional[str] = Field(None, description="Modified code after injection")
    additional_data: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class FeedbackResponse(BaseModel):
    """Feedback API response model."""
    status: str
    message: str
    feedback_id: Optional[str] = None
    timestamp: str

# Configuration
FEEDBACK_LOG_PATH = "logs/feedback.jsonl"
FEEDBACK_SUMMARY_PATH = "logs/feedback_summary.json"

def ensure_logs_directory():
    """Ensure the logs directory exists."""
    os.makedirs("logs", exist_ok=True)

def generate_feedback_id() -> str:
    """Generate a unique feedback ID."""
    from uuid import uuid4
    return f"feedback_{uuid4().hex[:8]}"

def log_feedback_to_file(feedback_data: Dict[str, Any]) -> str:
    """
    Log feedback data to JSONL file.

    Args:
        feedback_data: Feedback data dictionary

    Returns:
        Generated feedback ID
    """
    ensure_logs_directory()

    # Generate unique feedback ID
    feedback_id = generate_feedback_id()

    # Add metadata
    log_entry = {
        "feedback_id": feedback_id,
        "timestamp": datetime.now().isoformat(),
        "unix_timestamp": int(datetime.now().timestamp()),
        **feedback_data
    }

    try:
        # Append to JSONL file
        with open(FEEDBACK_LOG_PATH, "a", encoding="utf-8") as f:
            f.write(json.dumps(log_entry, ensure_ascii=False) + "\n")

        logger.info(f"Feedback logged: {feedback_id} - Rating: {feedback_data.get('rating', 'unknown')}")
        return feedback_id

    except Exception as e:
        logger.error(f"Failed to log feedback: {e}")
        raise

def update_feedback_summary():
    """Update feedback summary statistics."""
    try:
        ensure_logs_directory()

        # Read all feedback entries
        feedback_entries = []
        if os.path.exists(FEEDBACK_LOG_PATH):
            with open(FEEDBACK_LOG_PATH, "r", encoding="utf-8") as f:
                for line in f:
                    if line.strip():
                        try:
                            feedback_entries.append(json.loads(line))
                        except json.JSONDecodeError:
                            continue

        # Calculate summary statistics
        total_feedback = len(feedback_entries)
        ratings = [entry.get("rating", "unknown") for entry in feedback_entries]

        summary = {
            "total_feedback": total_feedback,
            "rating_counts": {
                "up": ratings.count("up"),
                "down": ratings.count("down"),
                "neutral": ratings.count("neutral"),
                "unknown": ratings.count("unknown")
            },
            "models": {},
            "intents": {},
            "last_updated": datetime.now().isoformat()
        }

        # Count by model
        for entry in feedback_entries:
            model = entry.get("model", "unknown")
            summary["models"][model] = summary["models"].get(model, 0) + 1

        # Count by intent
        for entry in feedback_entries:
            intent = entry.get("intent", "unknown")
            summary["intents"][intent] = summary["intents"].get(intent, 0) + 1

        # Calculate satisfaction rate
        positive_feedback = summary["rating_counts"]["up"]
        total_rated = positive_feedback + summary["rating_counts"]["down"]
        summary["satisfaction_rate"] = (positive_feedback / total_rated * 100) if total_rated > 0 else 0

        # Save summary
        with open(FEEDBACK_SUMMARY_PATH, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        logger.info(f"Feedback summary updated: {total_feedback} entries, {summary['satisfaction_rate']:.1f}% satisfaction")

    except Exception as e:
        logger.error(f"Failed to update feedback summary: {e}")

@router.post("/api/feedback", response_model=FeedbackResponse)
async def capture_feedback(request: Request, feedback: FeedbackRequest):
    """
    Capture user feedback on code injections.

    This endpoint logs user ratings and comments for analytics and quality improvement.

    Args:
        request: FastAPI request object
        feedback: Structured feedback data

    Returns:
        FeedbackResponse with status and feedback ID

    Example:
        POST /api/feedback
        {
            "rating": "up",
            "comment": "Great code generation!",
            "model": "mixtral",
            "intent": "refactor",
            "prompt": "Refactor to improve naming",
            "file": "utils/helper.py"
        }
    """
    try:
        # Validate rating
        valid_ratings = ["up", "down", "neutral"]
        if feedback.rating not in valid_ratings:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid rating. Must be one of: {', '.join(valid_ratings)}"
            )

        # Generate diff if before_code and after_code are provided
        diff_data = feedback.diff
        if not diff_data and feedback.before_code and feedback.after_code:
            try:
                # Import diff generation function
                from core.ai.diff_commentary import get_unified_diff
                diff_data = get_unified_diff(
                    feedback.before_code,
                    feedback.after_code,
                    feedback.file or "file"
                )
                logger.info(f"Generated diff for feedback: {len(diff_data)} characters")
            except Exception as e:
                logger.warning(f"Failed to generate diff for feedback: {e}")
                diff_data = None

        # Prepare feedback data
        feedback_data = {
            "rating": feedback.rating,
            "comment": feedback.comment,
            "model": feedback.model,
            "intent": feedback.intent,
            "prompt": feedback.prompt,
            "file": feedback.file,
            "injection_id": feedback.injection_id,
            "session_id": feedback.session_id,
            "diff": diff_data,
            "before_code": feedback.before_code,
            "after_code": feedback.after_code,
            "user_agent": request.headers.get("user-agent"),
            "ip_address": request.client.host if request.client else None,
            "additional_data": feedback.additional_data
        }

        # Log feedback to file
        feedback_id = log_feedback_to_file(feedback_data)

        # Update summary statistics
        update_feedback_summary()

        # Record analytics data (10.8.B)
        try:
            from services.analytics import record_feedback_analytics
            analytics_id = record_feedback_analytics(feedback_data)
            logger.info(f"Recorded feedback analytics: {analytics_id}")
        except Exception as e:
            logger.warning(f"Failed to record analytics: {e}")
            # Don't fail the request if analytics recording fails

        # Prepare response
        response = FeedbackResponse(
            status="success",
            message="Feedback captured successfully",
            feedback_id=feedback_id,
            timestamp=datetime.now().isoformat()
        )

        logger.info(f"Feedback captured: {feedback_id} - {feedback.rating} rating")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to capture feedback: {e}")
        raise HTTPException(status_code=500, detail="Failed to capture feedback")

@router.get("/api/feedback/summary")
async def get_feedback_summary():
    """
    Get feedback summary statistics.

    Returns:
        Summary of all feedback including counts, ratings, and satisfaction rate
    """
    try:
        if os.path.exists(FEEDBACK_SUMMARY_PATH):
            with open(FEEDBACK_SUMMARY_PATH, "r", encoding="utf-8") as f:
                summary = json.load(f)
            return summary
        else:
            # Return empty summary if no feedback yet
            return {
                "total_feedback": 0,
                "rating_counts": {"up": 0, "down": 0, "neutral": 0, "unknown": 0},
                "models": {},
                "intents": {},
                "satisfaction_rate": 0,
                "last_updated": datetime.now().isoformat()
            }
    except Exception as e:
        logger.error(f"Failed to get feedback summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get feedback summary")

@router.get("/api/feedback/recent")
async def get_recent_feedback(limit: int = 10):
    """
    Get recent feedback entries.

    Args:
        limit: Maximum number of entries to return (default: 10)

    Returns:
        List of recent feedback entries
    """
    try:
        feedback_entries = []

        if os.path.exists(FEEDBACK_LOG_PATH):
            with open(FEEDBACK_LOG_PATH, "r", encoding="utf-8") as f:
                lines = f.readlines()

                # Get last N lines
                recent_lines = lines[-limit:] if len(lines) > limit else lines

                for line in reversed(recent_lines):  # Most recent first
                    if line.strip():
                        try:
                            entry = json.loads(line)
                            # Remove sensitive data
                            entry.pop("ip_address", None)
                            entry.pop("user_agent", None)
                            feedback_entries.append(entry)
                        except json.JSONDecodeError:
                            continue

        return {
            "recent_feedback": feedback_entries,
            "count": len(feedback_entries),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get recent feedback: {e}")
        raise HTTPException(status_code=500, detail="Failed to get recent feedback")

# Legacy endpoint for backward compatibility
@router.post("/api/feedback")
async def capture_feedback_legacy(request: Request):
    """
    Legacy feedback endpoint for backward compatibility.

    Accepts raw JSON and converts to structured format.
    """
    try:
        data = await request.json()

        # Convert to structured format
        feedback = FeedbackRequest(
            rating=data.get("rating", "neutral"),
            comment=data.get("comment"),
            model=data.get("model"),
            intent=data.get("intent"),
            prompt=data.get("prompt"),
            file=data.get("file"),
            injection_id=data.get("injection_id"),
            session_id=data.get("session_id"),
            additional_data=data.get("additional_data")
        )

        return await capture_feedback(request, feedback)

    except Exception as e:
        logger.error(f"Legacy feedback capture failed: {e}")
        raise HTTPException(status_code=500, detail="Failed to capture feedback")

# Auto-Fix Models
class AutoFixRequest(BaseModel):
    """Auto-fix request model for generating improved prompts."""
    rating: str = Field(..., description="User rating that triggered auto-fix")
    comment: Optional[str] = Field(None, description="User feedback comment")
    prompt: str = Field(..., description="Original prompt that failed")
    model: Optional[str] = Field(None, description="Model used for original injection")
    intent: Optional[str] = Field(None, description="Detected intent")
    file: Optional[str] = Field(None, description="Target file path")
    diff: Optional[str] = Field(None, description="Code diff from original injection")
    injection_id: Optional[str] = Field(None, description="Original injection ID")

class AutoFixResponse(BaseModel):
    """Auto-fix response model."""
    success: bool
    improved_prompt: Optional[str] = None
    original_prompt: str
    confidence_score: float
    feedback_analysis: Optional[Dict[str, Any]] = None
    model_used_for_fix: str
    auto_fix_id: str
    timestamp: str
    error: Optional[str] = None

@router.post("/api/auto_fix", response_model=AutoFixResponse)
async def generate_auto_fix(request: AutoFixRequest):
    """
    Generate improved prompt suggestions for negative feedback.

    This endpoint analyzes negative feedback and generates improved prompts
    that address the specific issues mentioned by the user.

    Args:
        request: Auto-fix request with feedback data

    Returns:
        AutoFixResponse with improved prompt and analysis

    Example:
        POST /api/auto_fix
        {
            "rating": "down",
            "comment": "The code is too complex and hard to understand",
            "prompt": "Optimize this function",
            "model": "mixtral",
            "intent": "optimize"
        }
    """
    try:
        # Import auto-fix engine
        from core.ai.auto_fix import get_improved_prompt

        # Prepare feedback data for auto-fix analysis
        feedback_data = {
            "rating": request.rating,
            "comment": request.comment or "",
            "prompt": request.prompt,
            "model": request.model,
            "intent": request.intent,
            "file": request.file,
            "diff": request.diff,
            "injection_id": request.injection_id
        }

        logger.info(f"Generating auto-fix for negative feedback: {request.rating}")

        # Generate improved prompt
        auto_fix_result = get_improved_prompt(feedback_data)

        # Generate unique auto-fix ID
        auto_fix_id = f"autofix_{generate_feedback_id().split('_')[1]}"

        if auto_fix_result.get("success"):
            response = AutoFixResponse(
                success=True,
                improved_prompt=auto_fix_result["improved_prompt"],
                original_prompt=auto_fix_result["original_prompt"],
                confidence_score=auto_fix_result.get("confidence_score", 0.7),
                feedback_analysis=auto_fix_result.get("feedback_analysis"),
                model_used_for_fix=auto_fix_result.get("model_used_for_fix", "mixtral"),
                auto_fix_id=auto_fix_id,
                timestamp=datetime.now().isoformat()
            )

            logger.info(f"Auto-fix generated successfully: {auto_fix_id}")
            return response
        else:
            # Return error response
            response = AutoFixResponse(
                success=False,
                improved_prompt=None,
                original_prompt=request.prompt,
                confidence_score=0.0,
                model_used_for_fix="none",
                auto_fix_id=auto_fix_id,
                timestamp=datetime.now().isoformat(),
                error=auto_fix_result.get("error", "Auto-fix generation failed")
            )

            logger.error(f"Auto-fix failed: {auto_fix_result.get('error', 'Unknown error')}")
            return response

    except Exception as e:
        logger.error(f"Auto-fix endpoint error: {e}")

        # Return error response
        auto_fix_id = f"autofix_error_{datetime.now().strftime('%H%M%S')}"
        return AutoFixResponse(
            success=False,
            improved_prompt=None,
            original_prompt=request.prompt,
            confidence_score=0.0,
            model_used_for_fix="none",
            auto_fix_id=auto_fix_id,
            timestamp=datetime.now().isoformat(),
            error=f"Auto-fix generation failed: {str(e)}"
        )
