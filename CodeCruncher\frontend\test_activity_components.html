<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Activity Components Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .demo-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .activity-feed {
            width: 320px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .activity-header {
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: between;
        }
        .activity-title {
            font-weight: 600;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .activity-content {
            max-height: 400px;
            overflow-y: auto;
        }
        .activity-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: start;
            gap: 8px;
        }
        .activity-item:hover {
            background: #f8f9fa;
        }
        .activity-icon {
            width: 16px;
            height: 16px;
            margin-top: 2px;
        }
        .activity-details {
            flex: 1;
        }
        .activity-event {
            font-size: 12px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        .activity-description {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        .activity-time {
            font-size: 11px;
            color: #999;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        }
        .toast-title {
            font-weight: 600;
            margin-bottom: 4px;
        }
        .toast-message {
            font-size: 14px;
            opacity: 0.9;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        .status-connected {
            background: #10b981;
        }
        .status-disconnected {
            background: #ef4444;
        }
        .active-members {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 8px;
        }
        .member-avatar {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 600;
            border: 2px solid white;
            margin-left: -4px;
        }
        .member-avatar:first-child {
            margin-left: 0;
        }
        .filter-select {
            font-size: 12px;
            border: none;
            background: transparent;
            color: #666;
            margin-top: 8px;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Team Activity Feed Components</h1>
            <p>Live preview of real-time team collaboration features</p>
        </div>

        <div class="demo-section">
            <div class="demo-title">📡 Live Team Activity Feed</div>
            <div class="activity-feed">
                <div class="activity-header">
                    <div class="activity-title">
                        ⚡ Team Activity
                        <span class="status-indicator status-connected" title="Connected"></span>
                    </div>
                </div>
                
                <div style="padding: 12px 16px; font-size: 12px; color: #666; border-bottom: 1px solid #f0f0f0;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span>3 active</span>
                        <div class="active-members">
                            <div class="member-avatar">A</div>
                            <div class="member-avatar">B</div>
                            <div class="member-avatar">M</div>
                        </div>
                    </div>
                    <select class="filter-select">
                        <option>All Activity</option>
                        <option>My Activity</option>
                        <option>Injections</option>
                        <option>Ratings</option>
                        <option>Shaping</option>
                    </select>
                </div>

                <div class="activity-content">
                    <div class="activity-item">
                        <div class="activity-icon">💉</div>
                        <div class="activity-details">
                            <div class="activity-event">New Injection</div>
                            <div class="activity-description">alice injected ./src/main.py with mixtral-8x7b</div>
                            <div class="activity-time">2m ago</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon">⭐</div>
                        <div class="activity-details">
                            <div class="activity-event">Injection Rated</div>
                            <div class="activity-description">bob rated injection 5/5: "Excellent error handling!"</div>
                            <div class="activity-time">5m ago</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon">🧠</div>
                        <div class="activity-details">
                            <div class="activity-event">Prompt Shaped</div>
                            <div class="activity-description">maria improved prompt for optimization</div>
                            <div class="activity-time">8m ago</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon">⚙️</div>
                        <div class="activity-details">
                            <div class="activity-event">Setting Changed</div>
                            <div class="activity-description">alice updated default_model to llama3-70b</div>
                            <div class="activity-time">12m ago</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon">👋</div>
                        <div class="activity-details">
                            <div class="activity-event">New Team Member</div>
                            <div class="activity-description"><EMAIL> joined the team</div>
                            <div class="activity-time">1h ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">🔔 Toast Notifications</div>
            <p>Click buttons to simulate real-time notifications:</p>
            
            <button class="btn" onclick="showToast('injection')">💉 Injection Created</button>
            <button class="btn" onclick="showToast('rating')">⭐ Injection Rated</button>
            <button class="btn" onclick="showToast('shaping')">🧠 Prompt Shaped</button>
            <button class="btn" onclick="showToast('setting')">⚙️ Setting Changed</button>
            <button class="btn" onclick="showToast('member')">👋 Member Joined</button>
        </div>

        <div class="demo-section">
            <div class="demo-title">📊 Features Overview</div>
            <ul style="line-height: 1.6;">
                <li><strong>Real-time WebSocket connection</strong> - Live updates across team members</li>
                <li><strong>Event filtering</strong> - View all activity, personal, or specific event types</li>
                <li><strong>Active member tracking</strong> - See who's currently online</li>
                <li><strong>Toast notifications</strong> - Optional popup alerts for new activity</li>
                <li><strong>Event history</strong> - Persistent activity log with timestamps</li>
                <li><strong>Responsive design</strong> - Collapsible sidebar for space efficiency</li>
                <li><strong>Team context</strong> - Activity scoped to specific team workspaces</li>
            </ul>
        </div>
    </div>

    <script>
        function showToast(type) {
            // Remove existing toast
            const existing = document.querySelector('.toast');
            if (existing) {
                existing.remove();
            }

            const toasts = {
                injection: {
                    title: '💉 New Injection',
                    message: 'alice injected ./src/utils.py with gpt-4-turbo'
                },
                rating: {
                    title: '⭐ Injection Rated',
                    message: 'bob rated injection 4/5: "Good optimization!"'
                },
                shaping: {
                    title: '🧠 Prompt Shaped',
                    message: 'maria improved prompt for error handling'
                },
                setting: {
                    title: '⚙️ Setting Changed',
                    message: 'alice updated auto_apply_threshold to 0.9'
                },
                member: {
                    title: '👋 New Team Member',
                    message: '<EMAIL> joined the team'
                }
            };

            const toast = toasts[type];
            const toastEl = document.createElement('div');
            toastEl.className = 'toast';
            toastEl.innerHTML = `
                <div class="toast-title">${toast.title}</div>
                <div class="toast-message">${toast.message}</div>
            `;

            document.body.appendChild(toastEl);

            // Auto-remove after 4 seconds
            setTimeout(() => {
                toastEl.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => toastEl.remove(), 300);
            }, 4000);
        }

        // Simulate real-time updates
        let eventCounter = 0;
        function addRandomEvent() {
            const events = [
                { icon: '💉', event: 'New Injection', desc: 'teammate injected ./src/api.py', time: 'Just now' },
                { icon: '⭐', event: 'Injection Rated', desc: 'someone rated injection 5/5', time: 'Just now' },
                { icon: '🧠', event: 'Prompt Shaped', desc: 'AI improved prompt automatically', time: 'Just now' }
            ];

            const randomEvent = events[Math.floor(Math.random() * events.length)];
            const activityContent = document.querySelector('.activity-content');
            
            const newItem = document.createElement('div');
            newItem.className = 'activity-item';
            newItem.style.background = '#f0f9ff';
            newItem.innerHTML = `
                <div class="activity-icon">${randomEvent.icon}</div>
                <div class="activity-details">
                    <div class="activity-event">${randomEvent.event}</div>
                    <div class="activity-description">${randomEvent.desc}</div>
                    <div class="activity-time">${randomEvent.time}</div>
                </div>
            `;

            activityContent.insertBefore(newItem, activityContent.firstChild);
            
            // Remove background highlight after animation
            setTimeout(() => {
                newItem.style.background = '';
            }, 2000);

            // Keep only recent items
            const items = activityContent.querySelectorAll('.activity-item');
            if (items.length > 8) {
                items[items.length - 1].remove();
            }
        }

        // Add random events every 10 seconds
        setInterval(addRandomEvent, 10000);

        console.log('🎯 Team Activity Feed Demo Loaded');
        console.log('💡 This demonstrates the real-time collaboration features');
        console.log('🔔 Click notification buttons to see toast alerts');
    </script>
</body>
</html>
