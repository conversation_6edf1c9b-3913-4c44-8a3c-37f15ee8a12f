"""
AI Engine - Unified AI Model Interface with Smart Routing and Telemetry
"""

import asyncio
import os
import time
import traceback
import uuid
from rich import print as rich_print
from dotenv import load_dotenv
from .providers import mistral, groq
from .logger import log
from codecrusher.cache_manager import save_result, get_cached_result
from codecrusher.telemetry_logger import log_model_telemetry, count_tokens

# Load environment variables from .env file
load_dotenv()

# Model tier order: (name, id)
MODEL_TIERS = [
    # Removed decommissioned models
    # ("Gemma 7B", "gemma-7b-it"),
    # ("Mixtral", "mixtral-8x7b-instruct"),
    ("LLaMA 3 8B", "llama3-8b-8192"),
    ("LLaMA 3 70B", "llama3-70b-8192"),
    ("LLaMA 3.3 70B", "llama-3.3-70b-versatile"),
    # ("OpenAI GPT-4", "gpt-4"),  # optional later
]

# Default timeout for model calls (in seconds)
DEFAULT_MODEL_TIMEOUT = 15  # Reduced timeout to prevent long waits

# --- Score Output ---
def score_output(output: str) -> int:
    """
    Scores AI output based on presence of expected code elements and quality indicators.

    Enhanced with:
    - Better detection of code-like structures
    - Penalties for hallucinated or explanatory responses
    - Type hint detection
    - Logical structure scoring
    - Detection of question-like responses

    Returns:
        int: Score value (higher is better)
    """
    # Use the existing score_output function from ai_engine.py
    from .ai_engine import score_output as original_score_output
    return original_score_output(output)

# --- Response Validation ---
def is_valid_code_response(output: str, min_score: int = 4) -> tuple:
    """
    Validates if the AI response is actual code and not a hallucinated or explanatory response.

    Args:
        output (str): The AI-generated output to validate
        min_score (int): Minimum score threshold for valid responses

    Returns:
        tuple: (is_valid, reason) - Whether the response is valid and reason if not
    """
    # Use the existing is_valid_code_response function from ai_engine.py
    from .ai_engine import is_valid_code_response as original_is_valid_code_response
    return original_is_valid_code_response(output, min_score)

# --- Parallel Racing Logic ---
async def race_models(
    prompt: str,
    tag: str,
    timeout: int = DEFAULT_MODEL_TIMEOUT,
    early_return: bool = True,
    operation_id: str = None,
    no_telemetry: bool = False
) -> tuple:
    """
    Run all models in parallel and return best output and model used.

    Enhanced with:
    - Detailed debug logging
    - Timeouts for each model call
    - Early return option for first valid response
    - Improved error handling
    - Better async coordination
    - Telemetry logging

    Args:
        prompt (str): The prompt to send to the AI
        tag (str): The tag name
        timeout (int): Timeout in seconds for each model call (default: 30)
        early_return (bool): Whether to return immediately when a good response is received (default: True)
        operation_id (str): Optional operation ID for telemetry
        no_telemetry (bool): Whether to skip telemetry logging

    Returns:
        tuple: (output, model_id) - The best output and the model that produced it
    """
    log(f"Starting race_models with {len(MODEL_TIERS)} models, timeout={timeout}s, early_return={early_return}", "info")

    # Generate operation ID if not provided
    if not operation_id:
        operation_id = str(uuid.uuid4())

    async def call_model(model_name, model_id):
        """Call a model with timeout and detailed logging"""
        # Log the start of the model call
        log(f"[DEBUG] Starting model call for {model_name} ({model_id})", "debug")

        model_start_time = time.time()
        is_cached = False
        has_error = False
        error_msg = None

        # Check cache first
        cached_result = get_cached_result(tag, prompt, model_id)
        if cached_result:
            log(f"[DEBUG] Cache hit for {model_name}", "debug")
            rich_print(f"[gray]Cached result used from {model_id}[/gray]")
            log(f"Using cached result for {model_name}", "info")

            # Mark as cached for telemetry
            is_cached = True

            # Log telemetry for cached result
            if not no_telemetry:
                log_model_telemetry(
                    model=model_id,
                    provider="groq" if "llama" in model_id else "mistral",
                    prompt=prompt,
                    output=cached_result.get("output", ""),
                    start_time=model_start_time,
                    cached=True,
                    fallback=False,
                    tags=[tag] if tag else None,
                    operation_id=f"{operation_id}_{model_id}",
                    parent_id=operation_id,
                    operation_type="model_call",
                    no_telemetry=no_telemetry
                )

            return model_name, model_id, cached_result.get("output", ""), cached_result.get("score", 0)

        try:
            log(f"Calling {model_name}...", "info")

            # Call the model with timeout
            try:
                output = await asyncio.wait_for(
                    groq.generate_async(prompt, tag=tag, model_id=model_id),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                log(f"⏱️ {model_name} timed out after {timeout} seconds", "warning")

                # Log telemetry for timeout
                has_error = True
                error_msg = f"Timeout after {timeout} seconds"

                if not no_telemetry:
                    log_model_telemetry(
                        model=model_id,
                        provider="groq" if "llama" in model_id else "mistral",
                        prompt=prompt,
                        output="",
                        start_time=model_start_time,
                        cached=False,
                        fallback=False,
                        error=error_msg,
                        tags=[tag] if tag else None,
                        operation_id=f"{operation_id}_{model_id}",
                        parent_id=operation_id,
                        operation_type="model_call",
                        no_telemetry=no_telemetry
                    )

                return model_name, model_id, "", -500

            elapsed = time.time() - model_start_time

            # Check if we got a valid response
            if not output or not isinstance(output, str):
                log(f"❌ {model_name} returned invalid output", "warning")

                # Log telemetry for invalid output
                has_error = True
                error_msg = "Invalid or empty output"

                if not no_telemetry:
                    log_model_telemetry(
                        model=model_id,
                        provider="groq" if "llama" in model_id else "mistral",
                        prompt=prompt,
                        output="",
                        start_time=model_start_time,
                        cached=False,
                        fallback=False,
                        error=error_msg,
                        tags=[tag] if tag else None,
                        operation_id=f"{operation_id}_{model_id}",
                        parent_id=operation_id,
                        operation_type="model_call",
                        no_telemetry=no_telemetry
                    )

                return model_name, model_id, "", -200

            # Score the output
            score = score_output(output)
            log(f"{model_name} responded in {elapsed:.2f}s with score {score}", "info")

            # Create result object for cache
            result = {
                "model": model_name,
                "id": model_id,
                "output": output,
                "score": score,
                "time": elapsed,
                "timestamp": time.time()
            }

            # Save to cache
            save_result(tag, prompt, model_id, result)

            # Log telemetry for successful call
            if not no_telemetry:
                log_model_telemetry(
                    model=model_id,
                    provider="groq" if "llama" in model_id else "mistral",
                    prompt=prompt,
                    output=output,
                    start_time=model_start_time,
                    cached=False,
                    fallback=False,
                    tags=[tag] if tag else None,
                    operation_id=f"{operation_id}_{model_id}",
                    parent_id=operation_id,
                    operation_type="model_call",
                    no_telemetry=no_telemetry
                )

            return model_name, model_id, output, score

        except Exception as e:
            # Get detailed stack trace
            stack_trace = traceback.format_exc()
            log(f"[DEBUG] Exception in {model_name}: {e}\n{stack_trace}", "error")
            log(f"{model_name} failed: {e}", "warning")

            # Log telemetry for error
            has_error = True
            error_msg = str(e)

            if not no_telemetry:
                log_model_telemetry(
                    model=model_id,
                    provider="groq" if "llama" in model_id else "mistral",
                    prompt=prompt,
                    output="",
                    start_time=model_start_time,
                    cached=False,
                    fallback=False,
                    error=error_msg,
                    tags=[tag] if tag else None,
                    operation_id=f"{operation_id}_{model_id}",
                    parent_id=operation_id,
                    operation_type="model_call",
                    no_telemetry=no_telemetry
                )

            return model_name, model_id, "", -999

    # Create tasks for all models
    tasks = {
        asyncio.create_task(call_model(name, model_id), name=f"task_{model_id}"): (name, model_id)
        for name, model_id in MODEL_TIERS
    }

    valid_results = []

    # Process results as they come in
    while tasks:
        # Wait for the first task to complete or timeout
        done, _ = await asyncio.wait(
            tasks.keys(),
            return_when=asyncio.FIRST_COMPLETED
        )

        # Process completed tasks
        for task in done:
            model_name, model_id, output, score = task.result()

            # Remove the task from our tracking dict
            tasks.pop(task)

            # Check if we got a valid result
            if output and score > -100:
                log(f"Valid result from {model_name} with score {score}", "info")
                valid_results.append((model_name, model_id, output, score))

                # If early return is enabled and we have a good score, return immediately
                if early_return and score > 50:
                    log(f"Early return with {model_name} (score: {score})", "info")

                    # Cancel remaining tasks
                    for pending_task in tasks:
                        pending_task.cancel()

                    return output, model_id

    # If we get here, all tasks have completed
    log(f"All models completed. Valid results: {len(valid_results)}/{len(MODEL_TIERS)}", "info")

    # Handle the case where no valid results were found
    if not valid_results:
        error_msg = "All models failed or returned poor quality output"
        log(error_msg, "error")

        # Log telemetry for overall failure
        if not no_telemetry:
            log_model_telemetry(
                model="all",
                provider="multiple",
                prompt=prompt,
                output="",
                start_time=time.time(),  # This is not accurate but we don't have the original start time
                cached=False,
                fallback=False,
                error=error_msg,
                tags=[tag] if tag else None,
                operation_id=operation_id,
                operation_type="race_models",
                no_telemetry=no_telemetry
            )

        raise RuntimeError(error_msg)

    # Find the best result based on score
    best_result = max(valid_results, key=lambda x: x[3])  # x[3] is the score
    best_model_name, best_model_id, best_output, best_score = best_result

    log(f"Best model: {best_model_name} (score {best_score})", "success")

    # Return both the output and the model ID
    return best_output, best_model_id

# --- Main Entry ---
async def generate_code_async(
    prompt: str,
    tag: str="",
    provider="auto",
    model_ids=None,
    fallback=True,
    auto_route=False,
    use_cache=True,
    refresh_cache=False,
    timeout=DEFAULT_MODEL_TIMEOUT,
    operation_id=None,
    parent_id=None,
    no_telemetry=False
):
    """
    Async version of code generation function with improved race, scoring, caching, and telemetry.

    Args:
        prompt (str): The prompt to send to the AI
        tag (str, optional): The tag name
        provider (str, optional): The AI provider to use
        model_ids (list, optional): List of model IDs to use (overrides provider)
        fallback (bool, optional): Whether to use fallback models
        auto_route (bool, optional): Whether to use auto-routing
        use_cache (bool, optional): Whether to use the cache
        refresh_cache (bool, optional): Whether to bypass the cache
        timeout (int, optional): Timeout in seconds for each model call
        operation_id (str, optional): Operation ID for telemetry
        parent_id (str, optional): Parent operation ID for telemetry
        no_telemetry (bool, optional): Whether to skip telemetry logging

    Returns:
        tuple: (output, model_id) - The generated code and the model that produced it
    """
    log(f"Generating code for tag: {tag} using {provider}...", "info")
    print(f"DEBUG: Starting generate_code_async with provider={provider}, model_ids={model_ids}, auto_route={auto_route}")

    # Generate operation ID if not provided
    if not operation_id:
        operation_id = str(uuid.uuid4())

    # Start time for telemetry
    start_time = time.time()

    # If refresh_cache is True, we bypass the cache
    effective_use_cache = use_cache and not refresh_cache
    print(f"DEBUG: effective_use_cache={effective_use_cache}")

    # Variables for telemetry
    used_model = None
    used_provider = provider
    is_cached = False
    is_fallback = False
    error_msg = None
    output = None

    try:
        # Auto-routing or explicit model_ids - use race_models
        if auto_route or (model_ids and len(model_ids) > 1) or provider == "auto":
            log("[Auto Route] Racing multiple models...", "info")

            # Use provided model_ids or all models from MODEL_TIERS
            models_to_race = model_ids if model_ids else [model_id for _, model_id in MODEL_TIERS]

            # Log the models we're racing
            log(f"Racing models: {models_to_race}", "info")

            # Use the improved race_models with timeout and early return
            output, used_model = await race_models(
                prompt,
                tag,
                timeout=timeout,
                early_return=True,
                operation_id=operation_id,
                no_telemetry=no_telemetry
            )

            # Update provider for telemetry
            used_provider = "groq" if "llama" in used_model else "mistral"

        else:
            # Single model case
            # Determine model_id based on provider
            model_id = None
            if provider.startswith("groq/"):
                model_id = provider.split("/", 1)[1]
                used_provider = "groq"
            elif provider == "groq":
                model_id = MODEL_TIERS[0][1]  # Use first model in tier list
                used_provider = "groq"
            elif model_ids and len(model_ids) == 1:
                model_id = model_ids[0]
                used_provider = "groq" if "llama" in model_id else "mistral"

            used_model = model_id

            # Check cache if use_cache is enabled and we have a model_id
            if effective_use_cache and model_id:
                cached_result = get_cached_result(tag, prompt, model_id)
                if cached_result:
                    rich_print(f"[gray]Cached result used from {model_id}[/gray]")
                    log(f"Using cached result for {model_id}", "info")

                    # Mark as cached for telemetry
                    is_cached = True
                    output = cached_result.get("output", "")

                    # Log telemetry for cached result
                    if not no_telemetry:
                        log_model_telemetry(
                            model=model_id,
                            provider=used_provider,
                            prompt=prompt,
                            output=output,
                            start_time=start_time,
                            cached=True,
                            fallback=False,
                            tags=[tag] if tag else None,
                            operation_id=operation_id,
                            parent_id=parent_id,
                            operation_type="generate_code",
                            no_telemetry=no_telemetry
                        )

                    return output, model_id

            # Generate new result based on provider
            if provider == "mistral":
                output = mistral.generate(prompt, tag=tag)
                score = score_output(output)
                log(f"Mistral output scored: {score}", "info")

                if use_cache:
                    # Save to cache
                    result = {
                        "model": "mistral",
                        "id": "mistral",
                        "output": output,
                        "score": score,
                        "timestamp": time.time()
                    }
                    save_result(tag, prompt, "mistral", result)

                # Log telemetry
                if not no_telemetry:
                    log_model_telemetry(
                        model="mistral",
                        provider="mistral",
                        prompt=prompt,
                        output=output,
                        start_time=start_time,
                        cached=False,
                        fallback=False,
                        tags=[tag] if tag else None,
                        operation_id=operation_id,
                        parent_id=parent_id,
                        operation_type="generate_code",
                        no_telemetry=no_telemetry
                    )

                return output, "mistral"

            if provider.startswith("groq/") or provider == "groq":
                if not model_id:
                    model_id = MODEL_TIERS[0][1]  # Default to first model

                # Use async version if available
                try:
                    print(f"DEBUG: Calling groq.generate_async with model_id={model_id}, timeout={timeout}")
                    output = await asyncio.wait_for(
                        groq.generate_async(prompt, tag=tag, model_id=model_id),
                        timeout=timeout
                    )
                    print(f"DEBUG: groq.generate_async completed successfully")
                except (asyncio.TimeoutError, AttributeError, Exception) as e:
                    # Fall back to sync version if async fails or times out
                    print(f"DEBUG: Async call failed with error: {type(e).__name__}: {str(e)}")
                    log(f"Async call failed or timed out, falling back to sync for {model_id}", "warning")

                    try:
                        print(f"DEBUG: Calling groq.generate (sync) with model_id={model_id}")
                        # Set a timeout for the sync call as well (using a thread with timeout)
                        output = groq.generate(prompt, tag=tag, model_id=model_id)
                        print(f"DEBUG: groq.generate (sync) completed successfully")
                    except Exception as sync_error:
                        print(f"DEBUG: Sync call also failed: {sync_error}")
                        log(f"Sync call also failed: {sync_error}", "error")
                        # Don't hang here, raise the error to trigger fallback
                        raise

                score = score_output(output)
                log(f"Groq/{model_id} output scored: {score}", "info")

                if use_cache:
                    # Save to cache
                    result = {
                        "model": "groq",
                        "id": model_id,
                        "output": output,
                        "score": score,
                        "timestamp": time.time()
                    }
                    save_result(tag, prompt, model_id, result)

                # Log telemetry
                if not no_telemetry:
                    log_model_telemetry(
                        model=model_id,
                        provider="groq",
                        prompt=prompt,
                        output=output,
                        start_time=start_time,
                        cached=False,
                        fallback=False,
                        tags=[tag] if tag else None,
                        operation_id=operation_id,
                        parent_id=parent_id,
                        operation_type="generate_code",
                        no_telemetry=no_telemetry
                    )

                return output, model_id

            # Default to groq with first model if we get here
            model_id = MODEL_TIERS[0][1]
            used_model = model_id
            used_provider = "groq"

            output = groq.generate(prompt, tag=tag, model_id=model_id)
            score = score_output(output)
            log(f"Default model {model_id} output scored: {score}", "info")

            if use_cache:
                # Save to cache
                result = {
                    "model": "groq",
                    "id": model_id,
                    "output": output,
                    "score": score,
                    "timestamp": time.time()
                }
                save_result(tag, prompt, model_id, result)

            # Log telemetry
            if not no_telemetry:
                log_model_telemetry(
                    model=model_id,
                    provider="groq",
                    prompt=prompt,
                    output=output,
                    start_time=start_time,
                    cached=False,
                    fallback=False,
                    tags=[tag] if tag else None,
                    operation_id=operation_id,
                    parent_id=parent_id,
                    operation_type="generate_code",
                    no_telemetry=no_telemetry
                )

            return output, model_id

    except Exception as e:
        log(f"Primary provider failed: {e}", "error")
        log(traceback.format_exc(), "error")

        # Set error message for telemetry
        error_msg = str(e)

        if fallback and provider != "mistral":
            log("Falling back to mistral...", "warning")
            print(f"DEBUG: Falling back to mistral due to error: {e}")

            # Mark as fallback for telemetry
            is_fallback = True
            used_provider = "mistral"
            used_model = "mistral-fallback"

            try:
                # Check if MISTRAL_API_KEY is set
                mistral_api_key = os.getenv("MISTRAL_API_KEY")
                if not mistral_api_key:
                    log("MISTRAL_API_KEY not found in environment variables", "error")
                    print("DEBUG: MISTRAL_API_KEY not found in environment variables")
                    # Return a helpful error message instead of hanging
                    error_msg = "MISTRAL_API_KEY not found in environment variables. Please set it in your .env file."
                    output = f"ERROR: {error_msg}"

                    # Log telemetry for error
                    if not no_telemetry:
                        log_model_telemetry(
                            model="mistral-fallback",
                            provider="mistral",
                            prompt=prompt,
                            output=output,
                            start_time=start_time,
                            cached=False,
                            fallback=True,
                            error=error_msg,
                            tags=[tag] if tag else None,
                            operation_id=operation_id,
                            parent_id=parent_id,
                            operation_type="generate_code",
                            no_telemetry=no_telemetry
                        )

                    return output, "error"

                # Set a timeout for the mistral call as well
                output = mistral.generate(prompt, tag=tag)
                print(f"DEBUG: Mistral fallback successful")

                # Log telemetry for successful fallback
                if not no_telemetry:
                    log_model_telemetry(
                        model="mistral-fallback",
                        provider="mistral",
                        prompt=prompt,
                        output=output,
                        start_time=start_time,
                        cached=False,
                        fallback=True,
                        tags=[tag] if tag else None,
                        operation_id=operation_id,
                        parent_id=parent_id,
                        operation_type="generate_code",
                        no_telemetry=no_telemetry
                    )

                return output, "mistral-fallback"
            except Exception as fallback_error:
                log(f"Fallback to mistral also failed: {fallback_error}", "error")
                print(f"DEBUG: Mistral fallback also failed: {fallback_error}")

                # Update error message for telemetry
                error_msg = f"All AI providers failed. Primary error: {e}. Fallback error: {fallback_error}"
                output = f"ERROR: {error_msg}"

                # Log telemetry for fallback error
                if not no_telemetry:
                    log_model_telemetry(
                        model="mistral-fallback",
                        provider="mistral",
                        prompt=prompt,
                        output=output,
                        start_time=start_time,
                        cached=False,
                        fallback=True,
                        error=error_msg,
                        tags=[tag] if tag else None,
                        operation_id=operation_id,
                        parent_id=parent_id,
                        operation_type="generate_code",
                        no_telemetry=no_telemetry
                    )

                # Return a helpful error message instead of hanging
                return output, "error"

        # Return a helpful error message instead of hanging
        print(f"DEBUG: No fallback available, returning error message")
        output = f"ERROR: AI provider failed: {e}"

        # Log telemetry for error
        if not no_telemetry:
            log_model_telemetry(
                model=used_model or "unknown",
                provider=used_provider or "unknown",
                prompt=prompt,
                output=output,
                start_time=start_time,
                cached=is_cached,
                fallback=is_fallback,
                error=error_msg,
                tags=[tag] if tag else None,
                operation_id=operation_id,
                parent_id=parent_id,
                operation_type="generate_code",
                no_telemetry=no_telemetry
            )

        return output, "error"

def generate_code(
    prompt: str,
    tag: str="",
    provider="auto",
    model_ids=None,
    fallback=True,
    auto_route=False,
    use_cache=True,
    refresh_cache=False,
    operation_id=None,
    parent_id=None,
    no_telemetry=False
):
    """
    Main AI code generation function with fallback, escalation, and telemetry.
    Synchronous wrapper around the async generate_code_async function.

    Args:
        prompt (str): The prompt to send to the AI
        tag (str, optional): The tag name
        provider (str, optional): The AI provider to use
        model_ids (list, optional): List of model IDs to use (overrides provider)
        fallback (bool, optional): Whether to use fallback models
        auto_route (bool, optional): Whether to use auto-routing
        use_cache (bool, optional): Whether to use the cache
        refresh_cache (bool, optional): Whether to bypass the cache
        operation_id (str, optional): Operation ID for telemetry
        parent_id (str, optional): Parent operation ID for telemetry
        no_telemetry (bool, optional): Whether to skip telemetry logging

    Returns:
        tuple: (output, model_id) - The generated code and the model that produced it
    """
    return asyncio.run(generate_code_async(
        prompt=prompt,
        tag=tag,
        provider=provider,
        model_ids=model_ids,
        fallback=fallback,
        auto_route=auto_route,
        use_cache=use_cache,
        refresh_cache=refresh_cache,
        timeout=DEFAULT_MODEL_TIMEOUT,
        operation_id=operation_id,
        parent_id=parent_id,
        no_telemetry=no_telemetry
    ))
