"""
Best Model Predictor Service for CodeCrusher

This module provides intelligent model selection based on prompt analysis
and historical performance data. It uses machine learning techniques to
predict the best AI model for a given prompt.

Features:
- Prompt analysis and feature extraction
- Historical performance-based recommendations
- Rule-based fallback system
- Confidence scoring for predictions
- Alternative model suggestions
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from collections import defaultdict, Counter

logger = logging.getLogger(__name__)

class ModelPredictor:
    """
    Intelligent model predictor based on prompt analysis and historical data.
    
    This class analyzes prompts and recommends the best AI model based on
    historical performance patterns and prompt characteristics.
    """
    
    def __init__(self):
        """Initialize the model predictor."""
        self.intent_keywords = {
            "refactor": ["refactor", "clean", "improve", "restructure", "organize", "simplify"],
            "optimize": ["optimize", "performance", "speed", "efficient", "faster", "memory"],
            "debug": ["debug", "fix", "error", "bug", "issue", "problem", "broken"],
            "document": ["document", "comment", "explain", "describe", "documentation"],
            "test": ["test", "unit test", "testing", "spec", "coverage", "assert"],
            "feature": ["add", "implement", "create", "build", "new", "feature"],
            "transform": ["convert", "transform", "change", "migrate", "port"],
            "explain": ["explain", "how", "why", "what", "understand", "clarify"]
        }
        
        # Model performance profiles based on typical strengths
        self.model_profiles = {
            "mixtral": {
                "strengths": ["refactor", "optimize", "general"],
                "speed_score": 0.8,
                "quality_score": 0.85,
                "complexity_handling": 0.8
            },
            "gpt-4": {
                "strengths": ["explain", "document", "complex"],
                "speed_score": 0.6,
                "quality_score": 0.9,
                "complexity_handling": 0.95
            },
            "claude": {
                "strengths": ["document", "explain", "analysis"],
                "speed_score": 0.7,
                "quality_score": 0.85,
                "complexity_handling": 0.85
            },
            "llama3-70b": {
                "strengths": ["feature", "transform", "complex"],
                "speed_score": 0.5,
                "quality_score": 0.88,
                "complexity_handling": 0.9
            },
            "llama3-8b": {
                "strengths": ["simple", "fast"],
                "speed_score": 0.95,
                "quality_score": 0.7,
                "complexity_handling": 0.6
            }
        }
    
    def extract_prompt_features(self, prompt: str) -> Dict[str, Any]:
        """
        Extract features from a prompt for analysis.
        
        Args:
            prompt: The input prompt text
            
        Returns:
            Dictionary of extracted features
        """
        prompt_lower = prompt.lower()
        
        features = {
            "length": len(prompt),
            "word_count": len(prompt.split()),
            "has_code_keywords": bool(re.search(r'\b(function|class|def|var|let|const|import|from)\b', prompt_lower)),
            "has_file_extension": bool(re.search(r'\.(py|js|ts|html|css|java|cpp|c|rs|go)\b', prompt_lower)),
            "complexity_indicators": len(re.findall(r'\b(complex|advanced|sophisticated|detailed|comprehensive)\b', prompt_lower)),
            "urgency_indicators": len(re.findall(r'\b(quick|fast|simple|basic|easy)\b', prompt_lower)),
            "question_marks": prompt.count('?'),
            "exclamation_marks": prompt.count('!'),
            "detected_intent": self._detect_intent(prompt_lower),
            "technical_terms": self._count_technical_terms(prompt_lower),
            "specificity_score": self._calculate_specificity_score(prompt)
        }
        
        return features
    
    def _detect_intent(self, prompt_lower: str) -> str:
        """Detect the primary intent of the prompt."""
        intent_scores = {}
        
        for intent, keywords in self.intent_keywords.items():
            score = sum(1 for keyword in keywords if keyword in prompt_lower)
            if score > 0:
                intent_scores[intent] = score
        
        if intent_scores:
            return max(intent_scores, key=intent_scores.get)
        return "general"
    
    def _count_technical_terms(self, prompt_lower: str) -> int:
        """Count technical programming terms in the prompt."""
        technical_terms = [
            "algorithm", "function", "class", "method", "variable", "array", "object",
            "database", "api", "framework", "library", "module", "package", "dependency",
            "async", "await", "promise", "callback", "event", "handler", "middleware",
            "component", "service", "controller", "model", "view", "template", "schema"
        ]
        
        return sum(1 for term in technical_terms if term in prompt_lower)
    
    def _calculate_specificity_score(self, prompt: str) -> float:
        """Calculate how specific/detailed the prompt is."""
        # Factors that indicate specificity
        specificity_factors = [
            len(prompt) > 50,  # Longer prompts tend to be more specific
            bool(re.search(r'\b(exactly|specifically|precisely|particular)\b', prompt.lower())),
            bool(re.search(r'\b(should|must|need to|want to)\b', prompt.lower())),
            prompt.count(',') > 2,  # Multiple clauses
            bool(re.search(r'\b(example|instance|case|scenario)\b', prompt.lower())),
            bool(re.search(r'\b(using|with|for|in|on)\b', prompt.lower())),  # Context indicators
        ]
        
        return sum(specificity_factors) / len(specificity_factors)
    
    def predict_best_model(self, prompt: str, historical_data: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        Predict the best model for a given prompt.
        
        Args:
            prompt: The input prompt text
            historical_data: Optional historical performance data
            
        Returns:
            Prediction result with best model, confidence, and reasoning
        """
        try:
            # Extract prompt features
            features = self.extract_prompt_features(prompt)
            
            # Get historical performance if available
            if historical_data:
                historical_prediction = self._predict_from_historical_data(features, historical_data)
            else:
                historical_prediction = None
            
            # Get rule-based prediction
            rule_based_prediction = self._predict_from_rules(features)
            
            # Combine predictions
            final_prediction = self._combine_predictions(
                historical_prediction, 
                rule_based_prediction, 
                features
            )
            
            return final_prediction
            
        except Exception as e:
            logger.error(f"Failed to predict best model: {e}")
            return {
                "best_model": "mixtral",  # Safe default
                "confidence": 0.5,
                "reasoning": "Default model selected due to prediction error",
                "alternatives": []
            }
    
    def _predict_from_historical_data(self, features: Dict[str, Any], historical_data: List[Dict]) -> Optional[Dict]:
        """Predict based on historical performance data."""
        try:
            intent = features["detected_intent"]
            
            # Group historical data by model and intent
            model_performance = defaultdict(list)
            
            for record in historical_data:
                if record.get("intent") == intent:
                    model = record.get("model", "unknown")
                    rating = record.get("rating_score", 0)
                    model_performance[model].append(rating)
            
            # Calculate average performance for each model
            model_scores = {}
            for model, ratings in model_performance.items():
                if ratings:
                    avg_rating = sum(ratings) / len(ratings)
                    sample_size = len(ratings)
                    # Weight by sample size (more data = higher confidence)
                    confidence_weight = min(sample_size / 10, 1.0)
                    model_scores[model] = avg_rating * confidence_weight
            
            if model_scores:
                best_model = max(model_scores, key=model_scores.get)
                confidence = model_scores[best_model]
                
                return {
                    "model": best_model,
                    "confidence": confidence,
                    "reasoning": f"Based on historical performance for {intent} tasks",
                    "data_points": len(model_performance[best_model])
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to predict from historical data: {e}")
            return None
    
    def _predict_from_rules(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """Predict based on rule-based analysis."""
        intent = features["detected_intent"]
        complexity = features["complexity_indicators"]
        urgency = features["urgency_indicators"]
        specificity = features["specificity_score"]
        
        # Rule-based model selection
        model_scores = {}
        
        for model, profile in self.model_profiles.items():
            score = 0.5  # Base score
            
            # Intent matching
            if intent in profile["strengths"]:
                score += 0.3
            elif "general" in profile["strengths"] and intent == "general":
                score += 0.2
            
            # Complexity handling
            if complexity > 0:
                score += profile["complexity_handling"] * 0.2
            
            # Speed requirements
            if urgency > 0:
                score += profile["speed_score"] * 0.2
            
            # Quality requirements
            if specificity > 0.7:
                score += profile["quality_score"] * 0.2
            
            model_scores[model] = min(score, 1.0)
        
        best_model = max(model_scores, key=model_scores.get)
        confidence = model_scores[best_model]
        
        # Generate reasoning
        reasoning_parts = []
        if intent != "general":
            reasoning_parts.append(f"Intent: {intent}")
        if complexity > 0:
            reasoning_parts.append("Complex task detected")
        if urgency > 0:
            reasoning_parts.append("Speed prioritized")
        if specificity > 0.7:
            reasoning_parts.append("High specificity required")
        
        reasoning = "Rule-based selection: " + (", ".join(reasoning_parts) if reasoning_parts else "General purpose")
        
        return {
            "model": best_model,
            "confidence": confidence,
            "reasoning": reasoning,
            "scores": model_scores
        }
    
    def _combine_predictions(self, historical: Optional[Dict], rule_based: Dict, features: Dict) -> Dict[str, Any]:
        """Combine historical and rule-based predictions."""
        if historical and historical["confidence"] > 0.7:
            # Trust historical data if confidence is high
            best_model = historical["model"]
            confidence = historical["confidence"]
            reasoning = historical["reasoning"]
            
            # Add rule-based as alternative
            alternatives = [
                {
                    "model": rule_based["model"],
                    "confidence": rule_based["confidence"],
                    "reasoning": rule_based["reasoning"]
                }
            ]
        else:
            # Use rule-based prediction
            best_model = rule_based["model"]
            confidence = rule_based["confidence"]
            reasoning = rule_based["reasoning"]
            
            # Generate alternatives from rule-based scores
            scores = rule_based.get("scores", {})
            sorted_models = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            
            alternatives = []
            for model, score in sorted_models[1:3]:  # Top 2 alternatives
                alternatives.append({
                    "model": model,
                    "confidence": score,
                    "reasoning": f"Alternative based on {model} strengths"
                })
        
        return {
            "best_model": best_model,
            "confidence": round(confidence, 3),
            "reasoning": reasoning,
            "alternatives": alternatives,
            "features_analyzed": {
                "intent": features["detected_intent"],
                "complexity": features["complexity_indicators"],
                "specificity": round(features["specificity_score"], 2),
                "technical_terms": features["technical_terms"]
            }
        }

# Global predictor instance
model_predictor = ModelPredictor()

def predict_best_model(prompt: str, historical_data: Optional[List[Dict]] = None) -> Dict[str, Any]:
    """Convenience function for model prediction."""
    return model_predictor.predict_best_model(prompt, historical_data)
