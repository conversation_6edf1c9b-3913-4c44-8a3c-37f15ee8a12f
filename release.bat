@echo off
REM CodeCrusher Release Script for Windows
REM Alternative batch-based publishing automation

setlocal enabledelayedexpansion

REM Colors (limited in batch)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM Default values
set "VERSION_TYPE="
set "TEST_PYPI=false"
set "CREATE_TAG=false"
set "DRY_RUN=false"

REM Help function
:show_help
echo.
echo CodeCrusher Release Script for Windows
echo.
echo Usage: %~nx0 [OPTIONS] VERSION_TYPE
echo.
echo VERSION_TYPE:
echo     patch       Bump patch version (x.y.Z)
echo     minor       Bump minor version (x.Y.z)
echo     major       Bump major version (X.y.z)
echo.
echo OPTIONS:
echo     --test      Publish to TestPyPI instead of PyPI
echo     --tag       Create and push git tag
echo     --dry-run   Show what would happen without making changes
echo     --help      Show this help message
echo.
echo Examples:
echo     %~nx0 patch                    # Bump patch and publish to PyPI
echo     %~nx0 minor --test             # Bump minor and publish to TestPyPI
echo     %~nx0 major --tag              # Bump major, publish, and create git tag
echo     %~nx0 patch --dry-run          # Show what would happen
echo.
echo Requirements:
echo     - Python 3.8+
echo     - twine (pip install twine)
echo     - setuptools and wheel (pip install setuptools wheel)
echo     - Git (for tagging)
echo.
goto :eof

REM Check requirements
:check_requirements
echo %INFO% Checking requirements...

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Python is required but not installed
    exit /b 1
)

REM Check if we're in the right directory
if not exist "setup.py" (
    echo %ERROR% setup.py not found. Run this script from the project root.
    exit /b 1
)

REM Check twine
python -c "import twine" >nul 2>&1
if errorlevel 1 (
    echo %WARNING% twine not found. Install with: pip install twine
    if not "%DRY_RUN%"=="true" (
        exit /b 1
    )
)

echo %SUCCESS% Requirements check passed
goto :eof

REM Parse arguments
:parse_args
:parse_loop
if "%~1"=="" goto :parse_done

if "%~1"=="patch" (
    if not "%VERSION_TYPE%"=="" (
        echo %ERROR% Multiple version types specified
        exit /b 1
    )
    set "VERSION_TYPE=patch"
) else if "%~1"=="minor" (
    if not "%VERSION_TYPE%"=="" (
        echo %ERROR% Multiple version types specified
        exit /b 1
    )
    set "VERSION_TYPE=minor"
) else if "%~1"=="major" (
    if not "%VERSION_TYPE%"=="" (
        echo %ERROR% Multiple version types specified
        exit /b 1
    )
    set "VERSION_TYPE=major"
) else if "%~1"=="--test" (
    set "TEST_PYPI=true"
) else if "%~1"=="--tag" (
    set "CREATE_TAG=true"
) else if "%~1"=="--dry-run" (
    set "DRY_RUN=true"
) else if "%~1"=="--help" (
    call :show_help
    exit /b 0
) else if "%~1"=="-h" (
    call :show_help
    exit /b 0
) else (
    echo %ERROR% Unknown option: %~1
    call :show_help
    exit /b 1
)

shift
goto :parse_loop

:parse_done
if "%VERSION_TYPE%"=="" (
    echo %ERROR% Version type is required
    call :show_help
    exit /b 1
)
goto :eof

REM Main execution
:main
echo.
echo 🚀 CodeCrusher Release Script for Windows
echo.

call :parse_args %*
if errorlevel 1 exit /b 1

call :check_requirements
if errorlevel 1 exit /b 1

REM Build Python arguments
set "PYTHON_ARGS=--%VERSION_TYPE%"

if "%TEST_PYPI%"=="true" (
    set "PYTHON_ARGS=!PYTHON_ARGS! --test"
)

if "%CREATE_TAG%"=="true" (
    set "PYTHON_ARGS=!PYTHON_ARGS! --tag"
)

if "%DRY_RUN%"=="true" (
    set "PYTHON_ARGS=!PYTHON_ARGS! --dry-run"
)

echo %INFO% Executing: python publish.py !PYTHON_ARGS!
python publish.py !PYTHON_ARGS!

exit /b %errorlevel%

REM Call main function
call :main %*
