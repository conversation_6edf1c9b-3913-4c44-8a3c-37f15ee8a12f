# CodeCrusher Configuration File
# This file can be placed at ~/.codecrusherrc or codecrusher/config.yaml

# Default AI provider to use
ai_provider: mistral

# Number of retry attempts if generation fails
retry_count: 3

# Model to use (provider-specific)
model: mistral-medium

# Whether to use cache by default
use_cache: true

# Temperature for generation (0.0-1.0)
temperature: 0.7

# API keys for different providers
# These will be used if not set in environment variables
api_keys:
  mistral: your-mistral-key-here
  openai: your-openai-key-here
