#!/usr/bin/env python3
"""
Simple startup script for CodeCrusher FastAPI backend.
"""

import subprocess
import sys
import os

def check_dependencies():
    """Check if required packages are installed."""
    try:
        import fastapi
        import uvicorn
        import pydantic
        return True
    except ImportError as e:
        print(f"Missing dependency: {e}")
        return False

def install_dependencies():
    """Install required dependencies."""
    print("Installing dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main startup function."""
    print("CodeCrusher FastAPI Backend")
    print("=" * 30)

    # Check dependencies
    if not check_dependencies():
        print("Installing missing dependencies...")
        if not install_dependencies():
            print("Failed to install dependencies")
            return 1

    # Start server
    print("Starting server on http://localhost:8000")
    print("API docs: http://localhost:8000/docs")

    try:
        import uvicorn
        from main import app
        uvicorn.run(app, host="127.0.0.1", port=8000, reload=True)
    except KeyboardInterrupt:
        print("\nServer stopped")
    except Exception as e:
        print(f"Error starting server: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
