"""
Feedback Engine
Collects and processes user feedback for prompt improvement
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from .prompt_logger import PromptLogger, PromptLogEntry

logger = logging.getLogger(__name__)

@dataclass
class FeedbackPattern:
    """Pattern identified from user feedback"""
    pattern_type: str  # e.g., "null_check", "loop_bounds", "error_handling"
    description: str
    improvement_hint: str
    frequency: int
    confidence: float

class FeedbackEngine:
    """Engine for collecting and analyzing user feedback"""
    
    def __init__(self, prompt_logger: PromptLogger, config_path: str = "data/feedback_config.json"):
        self.prompt_logger = prompt_logger
        self.config_path = Path(config_path)
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self.feedback_patterns = self._load_patterns()
    
    def _load_patterns(self) -> Dict[str, FeedbackPattern]:
        """Load existing feedback patterns from config"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    data = json.load(f)
                    patterns = {}
                    for key, pattern_data in data.get('patterns', {}).items():
                        patterns[key] = FeedbackPattern(**pattern_data)
                    return patterns
            except Exception as e:
                logger.warning(f"Failed to load feedback patterns: {e}")
        
        # Default patterns
        return {
            "null_check": FeedbackPattern(
                pattern_type="null_check",
                description="Missing null/None checks",
                improvement_hint="Always check for None before accessing object properties",
                frequency=0,
                confidence=0.0
            ),
            "loop_bounds": FeedbackPattern(
                pattern_type="loop_bounds",
                description="Loop boundary issues",
                improvement_hint="Be more specific about range/iteration edge cases",
                frequency=0,
                confidence=0.0
            ),
            "error_handling": FeedbackPattern(
                pattern_type="error_handling",
                description="Insufficient error handling",
                improvement_hint="Add try-catch blocks and proper error messages",
                frequency=0,
                confidence=0.0
            ),
            "type_safety": FeedbackPattern(
                pattern_type="type_safety",
                description="Type-related issues",
                improvement_hint="Add type hints and validation",
                frequency=0,
                confidence=0.0
            ),
            "performance": FeedbackPattern(
                pattern_type="performance",
                description="Performance concerns",
                improvement_hint="Consider algorithmic complexity and optimization",
                frequency=0,
                confidence=0.0
            )
        }
    
    def _save_patterns(self):
        """Save feedback patterns to config"""
        try:
            data = {
                'patterns': {
                    key: {
                        'pattern_type': pattern.pattern_type,
                        'description': pattern.description,
                        'improvement_hint': pattern.improvement_hint,
                        'frequency': pattern.frequency,
                        'confidence': pattern.confidence
                    }
                    for key, pattern in self.feedback_patterns.items()
                }
            }
            
            with open(self.config_path, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save feedback patterns: {e}")
    
    def collect_feedback(self, entry_id: str, rating: int, feedback_text: Optional[str] = None) -> bool:
        """Collect user feedback for a prompt execution"""
        try:
            # Store rating and feedback
            self.prompt_logger.add_rating(entry_id, rating, feedback_text)
            
            # Analyze feedback if provided
            if feedback_text and rating <= 2:
                self._analyze_negative_feedback(entry_id, feedback_text)
            
            logger.info(f"Collected feedback for {entry_id}: rating={rating}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to collect feedback: {e}")
            return False
    
    def _analyze_negative_feedback(self, entry_id: str, feedback_text: str):
        """Analyze negative feedback to identify patterns"""
        feedback_lower = feedback_text.lower()
        
        # Pattern matching for common issues
        pattern_matches = []
        
        if any(word in feedback_lower for word in ['null', 'none', 'undefined', 'nullpointer']):
            pattern_matches.append('null_check')
        
        if any(word in feedback_lower for word in ['loop', 'iteration', 'range', 'bounds', 'index']):
            pattern_matches.append('loop_bounds')
        
        if any(word in feedback_lower for word in ['error', 'exception', 'crash', 'fail']):
            pattern_matches.append('error_handling')
        
        if any(word in feedback_lower for word in ['type', 'typing', 'cast', 'conversion']):
            pattern_matches.append('type_safety')
        
        if any(word in feedback_lower for word in ['slow', 'performance', 'optimize', 'efficient']):
            pattern_matches.append('performance')
        
        # Update pattern frequencies
        for pattern_key in pattern_matches:
            if pattern_key in self.feedback_patterns:
                pattern = self.feedback_patterns[pattern_key]
                pattern.frequency += 1
                pattern.confidence = min(1.0, pattern.confidence + 0.1)
        
        self._save_patterns()
        logger.info(f"Analyzed feedback for {entry_id}, found patterns: {pattern_matches}")
    
    def get_improvement_suggestions(self, injection_type: str, tags: List[str] = None) -> List[str]:
        """Get improvement suggestions based on learned patterns"""
        suggestions = []
        
        # Get relevant patterns based on confidence and frequency
        relevant_patterns = [
            pattern for pattern in self.feedback_patterns.values()
            if pattern.confidence > 0.3 and pattern.frequency > 2
        ]
        
        # Sort by confidence and frequency
        relevant_patterns.sort(key=lambda p: (p.confidence, p.frequency), reverse=True)
        
        # Add top suggestions
        for pattern in relevant_patterns[:3]:
            suggestions.append(pattern.improvement_hint)
        
        # Add injection-type specific suggestions
        if injection_type == "bugfix":
            suggestions.append("Focus on edge cases and error conditions")
        elif injection_type == "optimize":
            suggestions.append("Consider time and space complexity")
        elif injection_type == "refactor":
            suggestions.append("Maintain existing functionality while improving structure")
        
        return suggestions
    
    def get_feedback_summary(self) -> Dict[str, Any]:
        """Get summary of feedback patterns and statistics"""
        stats = self.prompt_logger.get_stats()
        
        # Add pattern analysis
        pattern_summary = {}
        for key, pattern in self.feedback_patterns.items():
            if pattern.frequency > 0:
                pattern_summary[key] = {
                    'description': pattern.description,
                    'frequency': pattern.frequency,
                    'confidence': round(pattern.confidence, 2),
                    'improvement_hint': pattern.improvement_hint
                }
        
        return {
            'prompt_stats': stats,
            'feedback_patterns': pattern_summary,
            'total_patterns': len([p for p in self.feedback_patterns.values() if p.frequency > 0])
        }
    
    def should_escalate_model(self, injection_type: str, recent_failures: int = 3) -> bool:
        """Determine if model should be escalated based on recent failures"""
        # Get recent entries for this injection type
        recent_entries = self.prompt_logger.get_recent_entries(limit=10)
        
        # Filter by injection type and check for low ratings
        type_entries = [e for e in recent_entries if e.injection_type == injection_type]
        low_rated = [e for e in type_entries if e.rating and e.rating <= 2]
        
        # Escalate if we have too many recent failures
        return len(low_rated) >= recent_failures
    
    def get_prompt_tweaks(self) -> Dict[str, str]:
        """Get prompt tweaks based on learned patterns"""
        tweaks = {}
        
        for key, pattern in self.feedback_patterns.items():
            if pattern.confidence > 0.5 and pattern.frequency > 1:
                tweaks[key] = pattern.improvement_hint
        
        # Add default tweak
        tweaks['default'] = "Give precise, minimal fixes with one-line explanations"
        
        return tweaks
