# Troubleshooting Guide

This guide helps you diagnose and resolve common issues with CodeCrusher.

## 🚨 Quick Diagnostics

### System Health Check

```bash
# Run comprehensive system check
codecrusher status --verbose

# Test API connectivity
codecrusher status --test-apis

# Check configuration
codecrusher status --config

# Validate installation
codecrusher --version
```

### Common Quick Fixes

```bash
# Clear cache and restart
codecrusher cache --clear
codecrusher status

# Reset configuration to defaults
codecrusher tune --reset

# Regenerate configuration files
codecrusher config --regenerate
```

---

## 🔧 Installation Issues

### Command Not Found

#### **Problem**: `codecrusher: command not found`

**Symptoms:**
- CL<PERSON> command not recognized
- "No such file or directory" error
- Command works with full path but not directly

**Solutions:**

1. **Check Installation**:
   ```bash
   pip show codecrusher
   pip list | grep codecrusher
   ```

2. **Add to PATH**:
   ```bash
   # Linux/Mac
   export PATH="$HOME/.local/bin:$PATH"
   echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
   
   # Windows
   set PATH=%PATH%;C:\Python311\Scripts
   ```

3. **Use Full Path**:
   ```bash
   python -m codecrusher --help
   ~/.local/bin/codecrusher --help
   ```

4. **Reinstall with User Flag**:
   ```bash
   pip uninstall codecrusher
   pip install --user codecrusher
   ```

### Permission Denied

#### **Problem**: Permission errors during installation or execution

**Symptoms:**
- "Permission denied" errors
- Cannot write to directories
- Installation fails with access errors

**Solutions:**

1. **Use Virtual Environment**:
   ```bash
   python -m venv codecrusher-env
   source codecrusher-env/bin/activate  # Linux/Mac
   codecrusher-env\Scripts\activate     # Windows
   pip install codecrusher
   ```

2. **Install with User Flag**:
   ```bash
   pip install --user codecrusher
   ```

3. **Fix Directory Permissions**:
   ```bash
   # Linux/Mac
   sudo chown -R $USER ~/.codecrusher
   chmod -R 755 ~/.codecrusher
   ```

### Dependency Conflicts

#### **Problem**: Package dependency conflicts

**Symptoms:**
- Installation fails with dependency errors
- "Incompatible version" messages
- Import errors after installation

**Solutions:**

1. **Fresh Virtual Environment**:
   ```bash
   python -m venv fresh-env
   source fresh-env/bin/activate
   pip install codecrusher
   ```

2. **Update Dependencies**:
   ```bash
   pip install --upgrade pip setuptools wheel
   pip install --upgrade codecrusher
   ```

3. **Force Reinstall**:
   ```bash
   pip uninstall codecrusher
   pip cache purge
   pip install codecrusher
   ```

---

## 🤖 AI Model Issues

### API Authentication Failures

#### **Problem**: AI model API authentication fails

**Symptoms:**
- "Invalid API key" errors
- 401 Unauthorized responses
- "Authentication failed" messages

**Solutions:**

1. **Check API Keys**:
   ```bash
   # Verify environment variables
   echo $GROQ_API_KEY
   echo $OPENAI_API_KEY
   
   # Test API connectivity
   codecrusher status --test-apis
   ```

2. **Set API Keys**:
   ```bash
   # Set environment variables
   export GROQ_API_KEY="gsk_your_key_here"
   export OPENAI_API_KEY="sk-your_key_here"
   
   # Or create .env file
   echo "GROQ_API_KEY=gsk_your_key_here" > .env
   echo "OPENAI_API_KEY=sk_your_key_here" >> .env
   ```

3. **Validate API Keys**:
   - Check key format and length
   - Verify keys haven't expired
   - Ensure correct provider (Groq vs OpenAI)
   - Test keys with provider's official tools

### Model Unavailable

#### **Problem**: AI models are unavailable or timing out

**Symptoms:**
- "Model not available" errors
- Request timeouts
- All models failing

**Solutions:**

1. **Check Model Status**:
   ```bash
   codecrusher status --models
   ```

2. **Try Different Models**:
   ```bash
   codecrusher inject --model llama3-8b file.py --prompt "test"
   codecrusher inject --model mixtral-8x7b file.py --prompt "test"
   ```

3. **Adjust Timeout Settings**:
   ```bash
   codecrusher tune --set api_timeout=60
   codecrusher tune --set max_retries=5
   ```

4. **Check Network Connectivity**:
   ```bash
   ping api.groq.com
   curl -I https://api.openai.com/v1/models
   ```

### Poor Quality Responses

#### **Problem**: AI generates low-quality or irrelevant code

**Symptoms:**
- Generated code doesn't match prompt
- Code has syntax errors
- Responses are too generic or verbose

**Solutions:**

1. **Improve Prompts**:
   ```bash
   # Be more specific
   codecrusher inject file.py --prompt "Add comprehensive error handling with try/catch blocks and logging"
   
   # Provide context
   codecrusher inject file.py --prompt "Add input validation for user registration form with email and password checks"
   ```

2. **Adjust Tuning Parameters**:
   ```bash
   # Increase quality requirements
   codecrusher tune --set quality_threshold=90
   codecrusher tune --set fallback_sensitivity=0.4
   
   # Use more comprehensive prompting
   codecrusher tune --set prompt_style=comprehensive
   codecrusher tune --set verbosity=high
   ```

3. **Use Better Models**:
   ```bash
   codecrusher inject --model llama3-70b file.py --prompt "your prompt"
   codecrusher inject --model gpt-4-turbo file.py --prompt "your prompt"
   ```

---

## 📁 File Processing Issues

### File Not Found

#### **Problem**: CodeCrusher cannot find or access files

**Symptoms:**
- "File not found" errors
- "Permission denied" when accessing files
- Cannot process certain file types

**Solutions:**

1. **Check File Paths**:
   ```bash
   # Use absolute paths
   codecrusher inject /full/path/to/file.py --prompt "test"
   
   # Verify file exists
   ls -la file.py
   file file.py
   ```

2. **Check Permissions**:
   ```bash
   # Fix file permissions
   chmod 644 file.py
   
   # Check directory permissions
   ls -la directory/
   ```

3. **Supported File Types**:
   ```bash
   # Check if file type is supported
   codecrusher inject --help | grep "supported"
   
   # Force file type detection
   codecrusher inject file.py --language python --prompt "test"
   ```

### Injection Tag Issues

#### **Problem**: Injection tags not detected or processed correctly

**Symptoms:**
- "No injection tags found" messages
- Tags not being replaced with generated code
- Incorrect tag format errors

**Solutions:**

1. **Check Tag Format**:
   ```python
   # Correct Python format
   # AI_INJECT:function_name
   # AI_INJECT:function_name:end
   
   # Correct JavaScript format
   // AI_INJECT:function_name
   // AI_INJECT:function_name:end
   
   # Correct HTML format
   <!-- AI_INJECT:component_name -->
   <!-- AI_INJECT:component_name:end -->
   ```

2. **Validate Tag Syntax**:
   ```bash
   # Check for tag detection
   codecrusher inject file.py --dry-run --verbose
   
   # List detected tags
   grep -n "AI_INJECT" file.py
   ```

3. **Fix Common Tag Issues**:
   - Ensure tags are on separate lines
   - Check for typos in tag names
   - Verify proper comment syntax for file type
   - Ensure matching start and end tags

### Encoding Issues

#### **Problem**: File encoding problems causing processing failures

**Symptoms:**
- "UnicodeDecodeError" messages
- Garbled text in generated code
- Files with special characters fail to process

**Solutions:**

1. **Check File Encoding**:
   ```bash
   file -i filename.py
   chardet filename.py
   ```

2. **Convert Encoding**:
   ```bash
   # Convert to UTF-8
   iconv -f ISO-8859-1 -t UTF-8 file.py > file_utf8.py
   
   # Or use Python
   python -c "
   with open('file.py', 'r', encoding='latin-1') as f:
       content = f.read()
   with open('file_utf8.py', 'w', encoding='utf-8') as f:
       f.write(content)
   "
   ```

3. **Force Encoding**:
   ```bash
   codecrusher inject file.py --encoding utf-8 --prompt "test"
   ```

---

## 🧠 Intelligence System Issues

### Learning Not Working

#### **Problem**: Intelligence system not learning from feedback

**Symptoms:**
- Parameters not updating after feedback
- No improvement in code quality over time
- Learning analytics show no progress

**Solutions:**

1. **Check Learning Status**:
   ```bash
   codecrusher learn --status
   codecrusher learn --analytics
   ```

2. **Provide More Feedback**:
   ```bash
   # Rate recent injections
   codecrusher rate file.py --rating 3 --comment "Needs more error handling"
   
   # Trigger learning manually
   codecrusher learn --apply --verbose
   ```

3. **Reset Learning System**:
   ```bash
   # Reset learning parameters
   codecrusher learn --reset
   
   # Clear feedback history
   codecrusher learn --clear-history
   ```

### Feedback Not Saving

#### **Problem**: User feedback not being saved or processed

**Symptoms:**
- Rating commands complete but don't affect learning
- Feedback database appears empty
- No feedback history in analytics

**Solutions:**

1. **Check Database**:
   ```bash
   # Verify database exists and is writable
   ls -la ~/.codecrusher/intelligence.db
   
   # Check database integrity
   codecrusher status --database
   ```

2. **Fix Database Issues**:
   ```bash
   # Recreate database
   codecrusher database --recreate
   
   # Repair database
   codecrusher database --repair
   ```

3. **Manual Feedback Entry**:
   ```bash
   # Add feedback manually
   codecrusher feedback --add --injection-id abc123 --rating 4 --comment "Good quality"
   ```

---

## 🌐 Network and Connectivity Issues

### WebSocket Connection Failures

#### **Problem**: Dashboard WebSocket connections fail

**Symptoms:**
- Real-time updates not working in dashboard
- "WebSocket connection failed" errors
- Dashboard shows stale data

**Solutions:**

1. **Check Firewall Settings**:
   ```bash
   # Test WebSocket connection
   curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost:3002/ws
   ```

2. **Try Different Ports**:
   ```bash
   codecrusher dashboard --start --port 8080
   ```

3. **Disable Proxy/VPN**:
   - Temporarily disable proxy settings
   - Try without VPN connection
   - Check corporate firewall rules

### API Rate Limiting

#### **Problem**: API requests being rate limited

**Symptoms:**
- "Rate limit exceeded" errors
- Slow response times
- Requests failing intermittently

**Solutions:**

1. **Check Rate Limits**:
   ```bash
   codecrusher status --api-limits
   ```

2. **Adjust Request Frequency**:
   ```bash
   codecrusher tune --set request_delay=2.0
   codecrusher tune --set max_concurrent_requests=2
   ```

3. **Use Caching**:
   ```bash
   codecrusher tune --set cache_enabled=true
   codecrusher tune --set cache_duration=3600
   ```

---

## 🔧 Performance Issues

### Slow Response Times

#### **Problem**: CodeCrusher operations are slow

**Symptoms:**
- Long wait times for code generation
- CLI commands taking too long
- Dashboard loading slowly

**Solutions:**

1. **Use Faster Models**:
   ```bash
   codecrusher tune --models llama3-8b,mixtral-8x7b
   codecrusher tune --set fallback_sensitivity=0.8
   ```

2. **Enable Caching**:
   ```bash
   codecrusher tune --set cache_enabled=true
   codecrusher cache --optimize
   ```

3. **Reduce Verbosity**:
   ```bash
   codecrusher tune --set verbosity=low
   codecrusher tune --set prompt_style=basic
   ```

### High Memory Usage

#### **Problem**: CodeCrusher using too much memory

**Symptoms:**
- System running out of memory
- Slow system performance
- Process killed by OS

**Solutions:**

1. **Clear Cache**:
   ```bash
   codecrusher cache --clear
   codecrusher cache --set-size 100MB
   ```

2. **Reduce Concurrent Operations**:
   ```bash
   codecrusher tune --set max_concurrent_requests=1
   ```

3. **Process Files Individually**:
   ```bash
   # Instead of processing entire directory
   find src/ -name "*.py" -exec codecrusher inject {} --prompt "test" \;
   ```

---

## 🔍 Debug Mode

### Enable Detailed Logging

```bash
# Enable debug mode
export CODECRUSHER_LOG_LEVEL=DEBUG
codecrusher inject file.py --prompt "test" --verbose

# View logs
tail -f ~/.codecrusher/logs/debug.log

# Save debug session
codecrusher inject file.py --prompt "test" --debug > debug_output.txt 2>&1
```

### Diagnostic Commands

```bash
# Comprehensive diagnostics
codecrusher diagnose --full

# Test specific components
codecrusher diagnose --component ai_injector
codecrusher diagnose --component intelligence_system
codecrusher diagnose --component model_router

# Generate diagnostic report
codecrusher diagnose --report diagnostic_report.json
```

---

## 🆘 Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Run diagnostic commands**
3. **Search existing GitHub issues**
4. **Try with minimal configuration**

### Creating Bug Reports

Include this information:

```bash
# System information
codecrusher --version
python --version
uname -a  # Linux/Mac
systeminfo  # Windows

# Configuration
codecrusher status --verbose
codecrusher tune --show

# Error logs
tail -50 ~/.codecrusher/logs/error.log

# Reproduction steps
codecrusher inject test.py --prompt "test" --debug
```

### Support Channels

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: Questions and community help
- **Documentation**: Comprehensive guides and tutorials

---

**[← Changelog](changelog.md)** | **[Back to Index](index.md)**
