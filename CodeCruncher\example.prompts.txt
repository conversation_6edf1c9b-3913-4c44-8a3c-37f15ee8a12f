// INJECT: entry point logic
print("🚀 Starting application...")
data = {"values": [1, 2, 3, 4]}
results = process_data(data)
print_results(results)


// INJECT: data processing logic
def normalize(values):
    total = sum(values)
    return [v / total for v in values]

def process_data(data):
    return normalize(data["values"])


// INJECT: result printing logic
def print_results(results):
    print("✅ Normalized Results:", results)
