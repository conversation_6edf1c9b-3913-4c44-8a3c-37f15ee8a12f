version: '3.8'

services:
  codecrusher-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG=false
    volumes:
      # Mount source code for development (comment out for production)
      - .:/app
      # Mount a directory for file processing (optional)
      - ./workspace:/workspace
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # Add SSL certificates for HTTPS
      # - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - codecrusher-api
    restart: unless-stopped
    profiles:
      - production

networks:
  default:
    name: codecrusher-network
