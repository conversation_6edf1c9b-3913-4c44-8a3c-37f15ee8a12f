<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        #messages { border: 1px solid #ccc; height: 300px; overflow-y: auto; padding: 10px; }
        button { padding: 10px 20px; margin: 5px; }
    </style>
</head>
<body>
    <h1>CodeCrusher WebSocket Test</h1>
    
    <div id="status" class="status disconnected">Disconnected</div>
    
    <button onclick="connect()">Connect</button>
    <button onclick="disconnect()">Disconnect</button>
    <button onclick="sendTest()">Send Test Message</button>
    <button onclick="clearMessages()">Clear Messages</button>
    
    <h3>Messages:</h3>
    <div id="messages"></div>
    
    <script>
        let ws = null;
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function updateStatus(status, className) {
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }
        
        function addMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = `<strong>[${timestamp}] ${type.toUpperCase()}:</strong> ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                addMessage('Already connected', 'warning');
                return;
            }
            
            const wsUrl = 'ws://127.0.0.1:8001/ws/logs';
            addMessage(`Attempting to connect to: ${wsUrl}`, 'info');
            updateStatus('Connecting...', 'connecting');
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    addMessage('WebSocket connection opened!', 'success');
                    updateStatus('Connected', 'connected');
                };
                
                ws.onmessage = function(event) {
                    addMessage(`Received: ${event.data}`, 'message');
                };
                
                ws.onclose = function(event) {
                    addMessage(`Connection closed. Code: ${event.code}, Reason: ${event.reason}`, 'warning');
                    updateStatus('Disconnected', 'disconnected');
                };
                
                ws.onerror = function(error) {
                    addMessage(`WebSocket error: ${error}`, 'error');
                    updateStatus('Error', 'disconnected');
                };
                
            } catch (error) {
                addMessage(`Failed to create WebSocket: ${error}`, 'error');
                updateStatus('Error', 'disconnected');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                addMessage('Disconnected by user', 'info');
            }
        }
        
        function sendTest() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testMessage = 'Test message from browser';
                ws.send(testMessage);
                addMessage(`Sent: ${testMessage}`, 'sent');
            } else {
                addMessage('Not connected - cannot send message', 'error');
            }
        }
        
        function clearMessages() {
            messagesDiv.innerHTML = '';
        }
        
        // Auto-connect on page load
        window.onload = function() {
            addMessage('Page loaded - ready to test WebSocket connection', 'info');
        };
    </script>
</body>
</html>
