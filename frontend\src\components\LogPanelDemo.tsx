import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import LiveLogPanel from './LiveLogPanel';
import { LogDisplay } from './LogDisplay';
import { useLogStream } from '@/hooks/useLogStream';
import { EnterpriseFooter } from './EnterpriseFooter';

export function LogPanelDemo() {
  const [useAdvanced, setUseAdvanced] = useState(false);

  const {
    logs,
    isConnected,
    connectionError,
    clearLogs,
    sendTestMessage
  } = useLogStream('ws://localhost:8000/ws/logs');

  const handleSendTestMessage = () => {
    const testMessages = [
      "🚀 Test injection started",
      "🔍 Scanning files...",
      "✅ Processing complete",
      "📊 Results: 5 files processed"
    ];
    const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
    sendTestMessage(randomMessage);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 space-y-6">
        {/* Demo Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Live Log Panel Demo</span>
              <div className="flex items-center space-x-2">
                <Badge variant={isConnected ? "default" : "destructive"}>
                  {isConnected ? 'Connected' : 'Disconnected'}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Switch
                id="advanced-mode"
                checked={useAdvanced}
                onCheckedChange={setUseAdvanced}
              />
              <Label htmlFor="advanced-mode">
                Use Advanced Log Display
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSendTestMessage}
              >
                Send Test Message
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearLogs}
              >
                Clear Logs
              </Button>
            </div>
          </div>

          {connectionError && (
            <div className="text-red-500 text-sm">
              ⚠️ Connection Error: {connectionError}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Log Panel Display */}
      <Card>
        <CardHeader>
          <CardTitle>
            {useAdvanced ? 'Advanced Log Display' : 'Simple Live Log Panel'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {useAdvanced ? (
            <LogDisplay
              logs={logs}
              isConnected={isConnected}
              connectionError={connectionError}
              onClearLogs={clearLogs}
              onSendTestMessage={sendTestMessage}
              className="h-80"
            />
          ) : (
            <LiveLogPanel
              height="320px"
              className="w-full"
            />
          )}
        </CardContent>
      </Card>

      {/* Usage Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Simple LiveLogPanel:</h4>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`import LiveLogPanel from './LiveLogPanel';

function Dashboard() {
  return (
    <div>
      <h2>Real-time Injection Logs</h2>
      <LiveLogPanel />
    </div>
  );
}`}
            </pre>
          </div>

          <div>
            <h4 className="font-semibold mb-2">Advanced LogDisplay:</h4>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
{`import { LogDisplay } from './LogDisplay';
import { useLogStream } from './hooks/useLogStream';

function Dashboard() {
  const { logs, isConnected, clearLogs, sendTestMessage } =
    useLogStream('ws://localhost:8000/ws/logs');

  return (
    <LogDisplay
      logs={logs}
      isConnected={isConnected}
      onClearLogs={clearLogs}
      onSendTestMessage={sendTestMessage}
    />
  );
}`}
            </pre>
          </div>

          <div>
            <h4 className="font-semibold mb-2">Features Comparison:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 className="font-medium text-green-600">Simple LiveLogPanel:</h5>
                <ul className="text-sm space-y-1 mt-2">
                  <li>• Minimal setup required</li>
                  <li>• Auto-scroll functionality</li>
                  <li>• Connection status indicator</li>
                  <li>• Clean monospace styling</li>
                  <li>• Lightweight and fast</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium text-blue-600">Advanced LogDisplay:</h5>
                <ul className="text-sm space-y-1 mt-2">
                  <li>• Full-featured terminal UI</li>
                  <li>• Message type icons and colors</li>
                  <li>• Test and clear functionality</li>
                  <li>• Progress and stats display</li>
                  <li>• Professional appearance</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>

      {/* Enterprise Footer */}
      <EnterpriseFooter />
    </div>
  );
}
