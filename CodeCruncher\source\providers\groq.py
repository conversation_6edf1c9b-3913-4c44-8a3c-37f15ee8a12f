"""
Groq API provider
"""

import os
import asyncio
import aiohttp
import requests
from rich import print as rprint
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

GROQ_API_KEY = os.getenv("GROQ_API_KEY")
GROQ_ENDPOINT = "https://api.groq.com/openai/v1/chat/completions"

# Updated model IDs based on current Groq API offerings
GROQ_MODELS = {
    "llama3-8b": "llama3-8b-8192",
    "llama3-70b": "llama3-70b-8192",
    "mixtral": "mixtral-8x7b-instruct",  # Updated to supported model
    "llama3.3-70b": "llama-3.3-70b-versatile",
}

async def generate_async(prompt, tag="", model_id=None):
    """
    Generate code using the Groq API asynchronously

    Args:
        prompt (str): The prompt to send to the AI
        tag (str, optional): The tag name (for logging)
        model_id (str, optional): The model ID to use

    Returns:
        str: The generated code or None if generation failed
    """
    if not GROQ_API_KEY:
        rprint("[red]❌ GROQ_API_KEY not found in environment variables[/red]")
        return None

    if not model_id:
        model_id = "llama3-8b-8192"  # Default model

    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }

    body = {
        "model": model_id,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.4,
    }

    try:
        # Create a timeout for the entire operation
        timeout = aiohttp.ClientTimeout(total=15)  # 15 seconds timeout
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(GROQ_ENDPOINT, headers=headers, json=body) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"API error: {response.status} - {error_text}")

                data = await response.json()
                content = data.get("choices", [{}])[0].get("message", {}).get("content", "")

                if content.strip():
                    rprint(f"[green]✅ Success with model:[/green] [bold]{model_id}[/bold]")
                    return content
                else:
                    raise ValueError("Empty response")
    except Exception as e:
        rprint(f"[yellow]⚠️ Model failed:[/yellow] {model_id} — {e}")
        return None

def generate(prompt, tag="", model_id=None):
    """
    Generate code using the Groq API synchronously

    Args:
        prompt (str): The prompt to send to the AI
        tag (str, optional): The tag name (for logging)
        model_id (str, optional): The model ID to use

    Returns:
        str: The generated code or None if generation failed
    """
    if not GROQ_API_KEY:
        rprint("[red]❌ GROQ_API_KEY not found in environment variables[/red]")
        return None

    if not model_id:
        model_id = "llama3-8b-8192"  # Default model

    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }

    body = {
        "model": model_id,
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "temperature": 0.4,
    }

    try:
        # Add a timeout to prevent hanging
        response = requests.post(GROQ_ENDPOINT, headers=headers, json=body, timeout=15)
        response.raise_for_status()

        content = response.json().get("choices", [{}])[0].get("message", {}).get("content", "")

        if content.strip():
            rprint(f"[green]✅ Success with model:[/green] [bold]{model_id}[/bold]")
            return content
        else:
            raise ValueError("Empty response")
    except Exception as e:
        rprint(f"[yellow]⚠️ Model failed:[/yellow] {model_id} — {e}")
        return None
