# Manual Build Workaround for CodeCrusher VS Code Extension

Since the automated build is having issues, here's how to manually set up and test the extension:

## Option 1: Use Extension Without Building (Recommended)

VS Code can run TypeScript extensions directly without compilation in development mode:

### Steps:

1. **Open VS Code in the extension directory:**
   ```bash
   cd vscode-extension
   code .
   ```

2. **Update launch configuration** - Create `.vscode/launch.json`:
   ```json
   {
     "version": "0.2.0",
     "configurations": [
       {
         "name": "Extension",
         "type": "extensionHost",
         "request": "launch",
         "args": ["--extensionDevelopmentPath=${workspaceFolder}"],
         "outFiles": ["${workspaceFolder}/out/**/*.js"],
         "preLaunchTask": "${workspaceFolder}/node_modules/.bin/tsc -p ."
       }
     ]
   }
   ```

3. **Press F5** - This will:
   - Automatically compile TypeScript
   - Launch Extension Development Host
   - Load your extension for testing

## Option 2: Manual JavaScript Conversion

If TypeScript compilation continues to fail, you can manually convert the main files:

### Create out/extension.js manually:

1. **Create the out directory:**
   ```bash
   mkdir out
   ```

2. **Copy and convert the main extension file** (simplified version):

```javascript
// out/extension.js
const vscode = require('vscode');
const path = require('path');
const cp = require('child_process');
const axios = require('axios');

// Status bar item
let statusBarItem;

function activate(context) {
    console.log('CodeCrusher extension is now active');

    // Register main inject command
    const injectCmd = vscode.commands.registerCommand('codecrusher.inject', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        
        if (!selectedText) {
            vscode.window.showErrorMessage('Please select some code to inject');
            return;
        }

        // Show input box for injection prompt
        const prompt = await vscode.window.showInputBox({
            prompt: 'Enter your injection prompt',
            placeHolder: 'e.g., Add error handling to this function'
        });

        if (!prompt) return;

        // Show progress
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'CodeCrusher: Processing injection...',
            cancellable: false
        }, async (progress) => {
            try {
                // Here you would call your CodeCrusher CLI or API
                // For now, just show a success message
                vscode.window.showInformationMessage(
                    `CodeCrusher injection completed for: "${prompt}"`
                );
            } catch (error) {
                vscode.window.showErrorMessage(`CodeCrusher error: ${error.message}`);
            }
        });
    });

    // Register show logs command
    const showLogsCmd = vscode.commands.registerCommand('codecrusher.showLogs', () => {
        vscode.window.showInformationMessage('CodeCrusher: Live logs feature coming soon!');
    });

    // Add commands to context
    context.subscriptions.push(injectCmd);
    context.subscriptions.push(showLogsCmd);

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "$(zap) CodeCrusher";
    statusBarItem.tooltip = "CodeCrusher Extension Active";
    statusBarItem.command = 'codecrusher.inject';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);
}

function deactivate() {
    if (statusBarItem) {
        statusBarItem.dispose();
    }
}

module.exports = {
    activate,
    deactivate
};
```

## Option 3: Simplified Package.json

Update package.json to point to a simpler main file:

```json
{
  "main": "./src/extension.js",
  "scripts": {
    "compile": "echo 'Using TypeScript files directly'",
    "watch": "echo 'Using TypeScript files directly'"
  }
}
```

Then rename `src/extension.ts` to `src/extension.js` and remove TypeScript-specific syntax.

## Testing the Extension

Once you have any of the above working:

1. **Press F5** in VS Code (extension directory)
2. **New VS Code window opens** (Extension Development Host)
3. **Open any code file**
4. **Right-click** → Look for CodeCrusher commands
5. **Command Palette** (Ctrl+Shift+P) → Type "CodeCrusher"

## Troubleshooting

- If F5 doesn't work, check the VS Code output panel for errors
- Make sure you're in the `vscode-extension` directory when pressing F5
- Check that `package.json` has the correct `main` entry point
- Verify that the main file exists and has no syntax errors

The extension should work even with basic functionality for testing purposes.
