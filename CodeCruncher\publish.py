#!/usr/bin/env python3
"""
CodeCrusher Auto-Publish Script
Automates versioning, building, and publishing to PyPI
"""

import os
import sys
import re
import subprocess
import shutil
import argparse
from pathlib import Path
from datetime import datetime
import os

class CodeCrusherPublisher:
    """Professional publishing automation for CodeCrusher."""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.setup_py = self.project_root / "setup.py"
        self.changelog = self.project_root / "CHANGELOG.md"
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"

        # Colors for console output
        self.GREEN = '\033[92m'
        self.YELLOW = '\033[93m'
        self.RED = '\033[91m'
        self.BLUE = '\033[94m'
        self.BOLD = '\033[1m'
        self.END = '\033[0m'

    def log(self, message: str, level: str = "INFO"):
        """Enhanced logging with colors and timestamps."""
        timestamp = datetime.now().strftime("%H:%M:%S")

        if level == "SUCCESS":
            color = self.GREEN
            icon = "✅"
        elif level == "WARNING":
            color = self.YELLOW
            icon = "⚠️"
        elif level == "ERROR":
            color = self.RED
            icon = "❌"
        elif level == "INFO":
            color = self.BLUE
            icon = "ℹ️"
        else:
            color = ""
            icon = "📝"

        print(f"{color}{icon} [{timestamp}] {message}{self.END}")

    def run_command(self, command: str, check: bool = True) -> subprocess.CompletedProcess:
        """Execute shell command with logging."""
        self.log(f"Running: {command}")
        try:
            result = subprocess.run(
                command,
                shell=True,
                check=check,
                capture_output=True,
                text=True,
                cwd=self.project_root
            )
            if result.stdout:
                self.log(f"Output: {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            self.log(f"Command failed: {e}", "ERROR")
            if e.stderr:
                self.log(f"Error: {e.stderr.strip()}", "ERROR")
            raise

    def get_current_version(self) -> str:
        """Extract current version from setup.py."""
        if not self.setup_py.exists():
            raise FileNotFoundError("setup.py not found")

        content = self.setup_py.read_text(encoding='utf-8')
        version_match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)

        if not version_match:
            raise ValueError("Version not found in setup.py")

        return version_match.group(1)

    def bump_version(self, current_version: str, bump_type: str) -> str:
        """Bump version according to semantic versioning."""
        try:
            major, minor, patch = map(int, current_version.split('.'))
        except ValueError:
            raise ValueError(f"Invalid version format: {current_version}")

        if bump_type == "major":
            major += 1
            minor = 0
            patch = 0
        elif bump_type == "minor":
            minor += 1
            patch = 0
        elif bump_type == "patch":
            patch += 1
        else:
            raise ValueError(f"Invalid bump type: {bump_type}")

        return f"{major}.{minor}.{patch}"

    def update_version_in_setup(self, new_version: str):
        """Update version in setup.py."""
        content = self.setup_py.read_text(encoding='utf-8')

        # Replace version string
        updated_content = re.sub(
            r'(version\s*=\s*["\'])([^"\']+)(["\'])',
            f'\\g<1>{new_version}\\g<3>',
            content
        )

        if updated_content == content:
            raise ValueError("Failed to update version in setup.py")

        self.setup_py.write_text(updated_content, encoding='utf-8')
        self.log(f"Updated setup.py version to {new_version}", "SUCCESS")

    def update_changelog(self, new_version: str):
        """Update CHANGELOG.md with new version."""
        if not self.changelog.exists():
            self.log("CHANGELOG.md not found, skipping update", "WARNING")
            return

        content = self.changelog.read_text(encoding='utf-8')
        today = datetime.now().strftime("%Y-%m-%d")

        # Replace [Unreleased] with new version
        updated_content = content.replace(
            "## [Unreleased]",
            f"## [Unreleased]\n\n### 🚀 Coming Soon\n- Future features and improvements\n\n---\n\n## [{new_version}] - {today}"
        )

        self.changelog.write_text(updated_content, encoding='utf-8')
        self.log(f"Updated CHANGELOG.md with version {new_version}", "SUCCESS")

    def clean_build_dirs(self):
        """Clean dist/ and build/ directories."""
        for directory in [self.dist_dir, self.build_dir]:
            if directory.exists():
                shutil.rmtree(directory)
                self.log(f"Cleaned {directory.name}/ directory", "SUCCESS")

    def build_package(self):
        """Build source and wheel distributions."""
        self.log("Building package distributions...")

        # Build source distribution and wheel
        self.run_command("python setup.py sdist bdist_wheel")

        # Verify build artifacts
        if not self.dist_dir.exists() or not list(self.dist_dir.glob("*.tar.gz")):
            raise RuntimeError("Build failed - no distribution files created")

        # List created files
        dist_files = list(self.dist_dir.glob("*"))
        self.log(f"Created {len(dist_files)} distribution files:", "SUCCESS")
        for file in dist_files:
            self.log(f"  - {file.name}")

    def check_twine_available(self) -> bool:
        """Check if twine is installed."""
        try:
            self.run_command("twine --version", check=False)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False

    def upload_to_pypi(self, test: bool = False):
        """Upload package to PyPI or TestPyPI."""
        if not self.check_twine_available():
            self.log("twine not found. Install with: pip install twine", "ERROR")
            return False

        repository = "testpypi" if test else "pypi"
        repository_url = "https://test.pypi.org/legacy/" if test else "https://upload.pypi.org/legacy/"

        self.log(f"Uploading to {'TestPyPI' if test else 'PyPI'}...")

        try:
            if test:
                self.run_command(f"twine upload --repository-url {repository_url} dist/*")
            else:
                self.run_command("twine upload dist/*")

            self.log(f"Successfully uploaded to {'TestPyPI' if test else 'PyPI'}!", "SUCCESS")
            return True

        except subprocess.CalledProcessError:
            self.log(f"Upload to {'TestPyPI' if test else 'PyPI'} failed", "ERROR")
            self.log("Check your credentials and try again", "WARNING")
            return False

    def git_tag_and_push(self, version: str):
        """Create git tag and push to remote."""
        try:
            # Check if git repo
            self.run_command("git status", check=False)

            # Create and push tag
            tag_name = f"v{version}"
            self.run_command(f"git tag {tag_name}")
            self.run_command(f"git push origin {tag_name}")

            self.log(f"Created and pushed git tag: {tag_name}", "SUCCESS")
            return True

        except subprocess.CalledProcessError:
            self.log("Git tagging failed - not a git repository or no remote", "WARNING")
            return False

    def commit_version_changes(self, version: str):
        """Commit version bump changes."""
        try:
            self.run_command("git add setup.py CHANGELOG.md")
            self.run_command(f'git commit -m "chore: bump version to {version}"')
            self.log(f"Committed version bump to {version}", "SUCCESS")
            return True
        except subprocess.CalledProcessError:
            self.log("Failed to commit version changes", "WARNING")
            return False

    def create_github_release(self, version: str):
        """Create GitHub release with CLI binary."""
        try:
            import subprocess

            self.log("Creating GitHub release...")

            # Check if GITHUB_TOKEN is set
            if not os.getenv('GITHUB_TOKEN'):
                self.log("GITHUB_TOKEN not set, skipping GitHub release", "WARNING")
                self.log("Set GITHUB_TOKEN to enable GitHub releases", "INFO")
                return False

            # Run GitHub release script
            result = subprocess.run([
                "python", "release_to_github.py",
                "--tag", f"v{version}"
            ], capture_output=True, text=True, cwd=self.project_root)

            if result.returncode == 0:
                self.log("GitHub release created successfully", "SUCCESS")
                return True
            else:
                self.log(f"GitHub release failed: {result.stderr}", "WARNING")
                return False

        except Exception as e:
            self.log(f"GitHub release error: {e}", "WARNING")
            return False

    def publish(self, bump_type: str, test: bool = False, tag: bool = False, github: bool = False, dry_run: bool = False):
        """Main publishing workflow."""
        try:
            self.log(f"{self.BOLD}🚀 CodeCrusher Auto-Publisher{self.END}")
            self.log(f"Starting {bump_type} version bump...")

            # Get current version
            current_version = self.get_current_version()
            self.log(f"Current version: {current_version}")

            # Calculate new version
            new_version = self.bump_version(current_version, bump_type)
            self.log(f"New version: {new_version}")

            if dry_run:
                self.log("DRY RUN - No changes will be made", "WARNING")
                self.log(f"Would bump version from {current_version} to {new_version}")
                if github:
                    self.log("Would create GitHub release with CLI binary")
                return True

            # Update version files
            self.update_version_in_setup(new_version)
            self.update_changelog(new_version)

            # Commit version changes
            self.commit_version_changes(new_version)

            # Clean and build
            self.clean_build_dirs()
            self.build_package()

            # Upload to PyPI
            upload_success = self.upload_to_pypi(test=test)

            if upload_success and tag:
                self.git_tag_and_push(new_version)

            # Create GitHub release if requested and PyPI upload was successful
            if upload_success and github:
                self.create_github_release(new_version)

            # Final success message
            target = "TestPyPI" if test else "PyPI"
            self.log(f"{self.BOLD}🎉 Successfully published CodeCrusher {new_version} to {target}!{self.END}", "SUCCESS")

            if test:
                self.log("Install with: pip install --index-url https://test.pypi.org/simple/ codecrusher")
            else:
                self.log("Install with: pip install codecrusher")

            if github and os.getenv('GITHUB_TOKEN'):
                self.log("GitHub release with CLI binary also created", "SUCCESS")

            return True

        except Exception as e:
            self.log(f"Publishing failed: {str(e)}", "ERROR")
            return False

def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(
        description="CodeCrusher Auto-Publisher",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python publish.py --patch              # Bump patch version and publish to PyPI
  python publish.py --minor --test       # Bump minor version and publish to TestPyPI
  python publish.py --major --tag        # Bump major version, publish, and create git tag
  python publish.py --patch --dry-run    # Show what would happen without making changes
        """
    )

    # Version bump options (mutually exclusive)
    version_group = parser.add_mutually_exclusive_group(required=True)
    version_group.add_argument("--patch", action="store_true", help="Bump patch version (x.y.Z)")
    version_group.add_argument("--minor", action="store_true", help="Bump minor version (x.Y.z)")
    version_group.add_argument("--major", action="store_true", help="Bump major version (X.y.z)")

    # Publishing options
    parser.add_argument("--test", action="store_true", help="Publish to TestPyPI instead of PyPI")
    parser.add_argument("--tag", action="store_true", help="Create and push git tag")
    parser.add_argument("--github", action="store_true", help="Create GitHub release with CLI binary")
    parser.add_argument("--dry-run", action="store_true", help="Show what would happen without making changes")

    args = parser.parse_args()

    # Determine bump type
    if args.patch:
        bump_type = "patch"
    elif args.minor:
        bump_type = "minor"
    elif args.major:
        bump_type = "major"

    # Create publisher and run
    publisher = CodeCrusherPublisher()
    success = publisher.publish(
        bump_type=bump_type,
        test=args.test,
        tag=args.tag,
        github=args.github,
        dry_run=args.dry_run
    )

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
