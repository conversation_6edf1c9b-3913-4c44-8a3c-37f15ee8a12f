# FILE: cache_manager.py

import os
import json
import hashlib
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Union

# Support both home directory and local cache
HOME_CACHE_FILE = os.path.expanduser("~/.codecrusher_cache.json")
LOCAL_CACHE_FILE = Path(".cache/codecrusher_cache.json")

# Default to home directory cache
CACHE_FILE = HOME_CACHE_FILE
CACHE_LOCK = threading.Lock()


def ensure_cache_dir():
    """
    Ensure the cache directory and file exist
    """
    # For local cache
    if isinstance(CACHE_FILE, Path):
        CACHE_FILE.parent.mkdir(parents=True, exist_ok=True)

    # Create cache file if it doesn't exist
    if not os.path.exists(CACHE_FILE):
        with open(CACHE_FILE, "w", encoding="utf-8") as f:
            json.dump({}, f)


def _generate_key(tag, prompt, provider=None, model_id=None):
    """
    Generate a cache key based on tag, prompt, provider, and model_id

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        provider (str, optional): The AI provider name
        model_id (str, optional): The specific model ID

    Returns:
        str: A unique hash key
    """
    # For backward compatibility
    if model_id is None and provider is not None:
        base = f"{tag}:{prompt}:{provider}"
        return hashlib.sha256(base.encode('utf-8')).hexdigest()
    # New format with model_id
    elif model_id is not None:
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()
        return f"{tag}_{prompt_hash}_{model_id}"
    # Fallback
    else:
        base = f"{tag}:{prompt}"
        return hashlib.sha256(base.encode('utf-8')).hexdigest()


def check_cache(tag, prompt, provider=None, model_id=None):
    """
    Check if a result exists in the cache

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        provider (str, optional): The AI provider name
        model_id (str, optional): The specific model ID

    Returns:
        dict or None: The cached result or None if not found
    """
    ensure_cache_dir()
    key = _generate_key(tag, prompt, provider, model_id)
    with CACHE_LOCK:
        try:
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data = json.load(f)
            if key in cache_data:
                if model_id:
                    print(f"[{datetime.now()}] 🔁 Cache hit for tag='{tag}', model='{model_id}'")
                else:
                    print(f"[{datetime.now()}] 🔁 Cache hit for tag='{tag}', provider='{provider}'")
                return cache_data[key]
            else:
                if model_id:
                    print(f"[{datetime.now()}] 🆕 Cache miss for tag='{tag}', model='{model_id}'")
                else:
                    print(f"[{datetime.now()}] 🆕 Cache miss for tag='{tag}', provider='{provider}'")
        except Exception as e:
            print(f"[{datetime.now()}] ⚠️ Cache read error: {e}")
    return None


def save_cache(tag, prompt, provider=None, content=None, model_id=None, result=None, tags=None):
    """
    Save a result to the cache

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        provider (str, optional): The AI provider name
        content (str, optional): The content to cache (legacy)
        model_id (str, optional): The specific model ID
        result (dict, optional): The result object with score, model info, etc.
        tags (list, optional): List of tags to associate with the cache entry
    """
    ensure_cache_dir()
    key = _generate_key(tag, prompt, provider, model_id)
    with CACHE_LOCK:
        try:
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data = json.load(f)
        except:
            cache_data = {}

        # Support both legacy and new format
        if result is not None:
            # Add tags to the result if provided
            if tags:
                result["tags"] = tags
            cache_data[key] = result
            display_model = model_id or result.get('model', 'unknown')
        else:
            # For legacy format, create a dict with content and tags
            if isinstance(content, str):
                entry = {"content": content}
                if tags:
                    entry["tags"] = tags
                cache_data[key] = entry
            else:
                # If content is already a dict, add tags to it
                if tags and isinstance(content, dict):
                    content["tags"] = tags
                cache_data[key] = content
            display_model = provider or 'unknown'

        try:
            with open(CACHE_FILE, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, indent=2)
            print(f"[{datetime.now()}] 💾 Cached result for tag='{tag}', model='{display_model}'")
        except Exception as e:
            print(f"[{datetime.now()}] ❌ Cache write error: {e}")


# New functions for multi-model caching with improved structure
def get_prompt_key(tag, prompt):
    """
    Generate a prompt key for the cache

    Args:
        tag (str): The tag name
        prompt (str): The prompt text

    Returns:
        str: A unique prompt key
    """
    # Create a key in the format "inject::filename::tag" or use hash for uniqueness
    if ":" in tag:
        # Tag already has a structure like "inject::filename::tag"
        return tag
    else:
        # Create a structured key
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()[:8]
        return f"inject::{prompt_hash}::{tag}"

def load_cache():
    """
    Load the cache from disk

    Returns:
        dict: The cache data
    """
    ensure_cache_dir()
    try:
        with CACHE_LOCK:
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception as e:
        print(f"[{datetime.now()}] ⚠️ Cache load error: {e}")
        return {}

def save_cache_data(cache_data):
    """
    Save the cache data to disk

    Args:
        cache_data (dict): The cache data to save
    """
    ensure_cache_dir()
    try:
        with CACHE_LOCK:
            with open(CACHE_FILE, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, indent=2)
    except Exception as e:
        print(f"[{datetime.now()}] ❌ Cache write error: {e}")

def save_result(tag, prompt, model_id, result, tags=None):
    """
    Save a model result to the cache using the new structure

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        model_id (str): The model ID
        result (dict): The result object with score, output, etc.
        tags (list, optional): List of tags to associate with the cache entry
    """
    cache = load_cache()
    prompt_key = get_prompt_key(tag, prompt)

    # Initialize the prompt key if it doesn't exist
    if prompt_key not in cache:
        cache[prompt_key] = {}

    # Extract the output and score from the result
    output = result.get("output", "")
    score = result.get("score", 0)

    # Create the cache entry
    cache_entry = {
        "output": output,
        "score": score,
        "timestamp": datetime.now().isoformat()
    }

    # Add tags if provided
    if tags:
        cache_entry["tags"] = tags

    # Save the result in the new structure
    cache[prompt_key][model_id] = cache_entry

    # Save the updated cache
    save_cache_data(cache)
    print(f"[{datetime.now()}] 💾 Cached result for prompt_key='{prompt_key}', model='{model_id}', score={score}")

def get_cached_result(tag, prompt, model_id, filter_tags=None):
    """
    Get a cached result for a specific model using the new structure

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        model_id (str): The model ID
        filter_tags (list, optional): List of tags to filter by

    Returns:
        dict or None: The cached result or None if not found
    """
    cache = load_cache()
    prompt_key = get_prompt_key(tag, prompt)

    # Check if the prompt key and model ID exist in the cache
    if prompt_key in cache and model_id in cache[prompt_key]:
        cached_data = cache[prompt_key][model_id]

        # Filter by tags if provided
        if filter_tags and "tags" in cached_data:
            if not all(tag in cached_data["tags"] for tag in filter_tags):
                print(f"[{datetime.now()}] 🔍 Cache entry does not match filter tags: {filter_tags}")
                return None

        print(f"[{datetime.now()}] 🔁 Cache hit for prompt_key='{prompt_key}', model='{model_id}', score={cached_data.get('score', 0)}")

        # Convert to the format expected by the AI engine
        result = {
            "model": model_id,
            "id": model_id,
            "output": cached_data.get("output", ""),
            "score": cached_data.get("score", 0),
            "timestamp": cached_data.get("timestamp", datetime.now().isoformat())
        }

        # Add tags if they exist
        if "tags" in cached_data:
            result["tags"] = cached_data["tags"]

        return result

    print(f"[{datetime.now()}] 🆕 Cache miss for prompt_key='{prompt_key}', model='{model_id}'")
    return None

# Additional utility functions
def clear_cache():
    """
    Clear the entire cache
    """
    ensure_cache_dir()
    with CACHE_LOCK:
        try:
            with open(CACHE_FILE, "w", encoding="utf-8") as f:
                json.dump({}, f)
            print(f"[{datetime.now()}] 🧹 Cache cleared successfully")
        except Exception as e:
            print(f"[{datetime.now()}] ❌ Cache clear error: {e}")


def cache_exists(tag, prompt, model_id):
    """
    Check if a result exists in the cache without retrieving it

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        model_id (str): The model ID

    Returns:
        bool: True if the result exists in the cache, False otherwise
    """
    cache = load_cache()
    prompt_key = get_prompt_key(tag, prompt)

    # Check if the prompt key and model ID exist in the cache
    exists = prompt_key in cache and model_id in cache[prompt_key]

    if exists:
        print(f"[{datetime.now()}] 🔍 Cache entry exists for prompt_key='{prompt_key}', model='{model_id}'")
    else:
        print(f"[{datetime.now()}] 🔍 Cache entry does not exist for prompt_key='{prompt_key}', model='{model_id}'")

    return exists


# Function to filter cache by tags
def filter_cache_by_tags(tags):
    """
    Filter the cache by tags

    Args:
        tags (list): List of tags to filter by

    Returns:
        dict: Filtered cache data
    """
    if not tags:
        return load_cache()

    cache = load_cache()
    filtered_cache = {}

    for key, entry in cache.items():
        # Check if the entry is a dict with model IDs
        if isinstance(entry, dict) and not any(k.startswith("@") for k in entry.keys()):
            # This is a prompt key with model IDs
            filtered_models = {}
            for model_id, model_entry in entry.items():
                if isinstance(model_entry, dict) and "tags" in model_entry:
                    if all(tag in model_entry["tags"] for tag in tags):
                        filtered_models[model_id] = model_entry

            if filtered_models:
                filtered_cache[key] = filtered_models
        # Check if the entry is a direct cache entry
        elif isinstance(entry, dict) and "tags" in entry:
            if all(tag in entry["tags"] for tag in tags):
                filtered_cache[key] = entry

    return filtered_cache

# Maintain backward compatibility with existing code
def save_to_cache(tag, prompt, provider, content):
    """Legacy function for backward compatibility."""
    save_cache(tag, prompt, provider, content)


def get_cached_code(tag_name, prompt_text, model="mistral"):
    """Legacy function for backward compatibility."""
    return check_cache(tag_name, prompt_text, model)


# Simple cache functions as requested
def load_cache_result(cache_key: str, model_id: str, filter_tags=None) -> str:
    """
    Load cached AI result for a given cache_key and model_id from the cache file.

    This is a simplified interface to the caching system that works with the
    existing cache structure but provides a simpler API.

    Args:
        cache_key (str): The cache key to look up
        model_id (str): The model ID to look up
        filter_tags (list, optional): List of tags to filter by

    Returns:
        Optional[str]: The cached result string if found, else None
    """
    with CACHE_LOCK:
        try:
            # Load the cache directly from the file
            ensure_cache_dir()
            try:
                with open(CACHE_FILE, "r", encoding="utf-8") as f:
                    cache_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                cache_data = {}

            # Check if the cache_key exists and contains the model_id
            if cache_key in cache_data and model_id in cache_data[cache_key]:
                cached_entry = cache_data[cache_key][model_id]

                # Filter by tags if provided
                if filter_tags and isinstance(cached_entry, dict) and "tags" in cached_entry:
                    if not all(tag in cached_entry["tags"] for tag in filter_tags):
                        print(f"[{datetime.now()}] 🔍 Cache entry does not match filter tags: {filter_tags}")
                        return None

                # Return just the output string as requested
                if isinstance(cached_entry, dict):
                    print(f"[{datetime.now()}] 🔁 Simple cache hit for key='{cache_key}', model='{model_id}'")
                    return cached_entry.get("output")
                else:
                    print(f"[{datetime.now()}] 🔁 Simple cache hit for key='{cache_key}', model='{model_id}'")
                    return cached_entry
            return None
        except Exception as e:
            print(f"[{datetime.now()}] ⚠️ Simple cache load error: {e}")
            return None


def save_cache_result(cache_key: str, model_id: str, result: str, tags=None) -> None:
    """
    Save the AI result string into the cache file under the cache_key and model_id.

    This is a simplified interface to the caching system that works with the
    existing cache structure but provides a simpler API.

    Args:
        cache_key (str): The cache key to store under
        model_id (str): The model ID to store under
        result (str): The result string to cache
        tags (list, optional): List of tags to associate with the cache entry
    """
    with CACHE_LOCK:
        try:
            # Load the cache directly from the file
            ensure_cache_dir()
            try:
                with open(CACHE_FILE, "r", encoding="utf-8") as f:
                    cache_data = json.load(f)
            except (FileNotFoundError, json.JSONDecodeError):
                cache_data = {}

            # Initialize the cache_key if it doesn't exist
            if cache_key not in cache_data:
                cache_data[cache_key] = {}

            # Create the cache entry
            cache_entry = {
                "output": result,
                "timestamp": datetime.now().isoformat()
            }

            # Add tags if provided
            if tags:
                cache_entry["tags"] = tags

            # Store the result with the structure
            cache_data[cache_key][model_id] = cache_entry

            # Save the updated cache directly to the file
            with open(CACHE_FILE, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, indent=2)
            print(f"[{datetime.now()}] 💾 Simple cache saved for key='{cache_key}', model='{model_id}'")
        except Exception as e:
            print(f"[{datetime.now()}] ❌ Simple cache save error: {e}")