#!/usr/bin/env python3
"""
CodeCrusher CLI - Click-based Entry Point
Compatible with pyproject.toml [project.scripts] entry point
"""

import click
import os
import sys
from pathlib import Path
from typing import Optional

# Add the codecrusher package to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.table import Table
    console = Console()
except ImportError:
    # Fallback if rich is not available
    class Console:
        def print(self, *args, **kwargs):
            print(*args)
    console = Console()

@click.group()
@click.version_option(version="0.1.0", prog_name="codecrusher")
def cli():
    """
    🚀 CodeCrusher CLI – AugmentKiller with smart injection, fallback, feedback, and dashboard integration

    AI-powered code injection and optimization tool with team collaboration features.
    """
    pass

@cli.command()
@click.argument('source', type=click.Path(exists=True))
@click.option('--prompt', '-p', required=True, help='💬 Prompt for AI injection')
@click.option('--recursive', '-r', is_flag=True, help='🔄 Recursively scan directories')
@click.option('--preview', is_flag=True, help='🔍 Show preview without applying changes')
@click.option('--apply', is_flag=True, help='✅ Apply changes to files')
@click.option('--ext', help='📄 File extensions (comma-separated)')
@click.option('--model', '-m', default='auto', help='🤖 AI model to use')
@click.option('--force', is_flag=True, help='⚡ Force injection without confirmation')
@click.option('--verbose', '-v', is_flag=True, help='🔍 Enable verbose output')
def inject(source, prompt, recursive, preview, apply, ext, model, force, verbose):
    """
    🔌 Inject AI-generated code into source files

    Examples:
      codecrusher inject ./test-cases --prompt "Add logging" --recursive --preview
      codecrusher inject ./src/main.py --prompt "Add error handling" --apply
      codecrusher inject ./project --prompt "Optimize code" --ext py,js --recursive --preview
    """
    # Validate arguments
    if not preview and not apply:
        console.print(Panel(
            "[red]❌ Either --preview or --apply is required[/red]\n\n"
            "[yellow]Usage examples:[/yellow]\n"
            "  codecrusher inject ./test-cases --prompt 'Add logging' --preview\n"
            "  codecrusher inject ./src --prompt 'Add logging' --apply",
            title="[bold red]Missing Mode[/bold red]",
            border_style="red"
        ))
        sys.exit(1)

    # Display welcome message
    console.print(Panel(
        "[bold]CodeCrusher: AI-powered code injection[/bold]\n"
        f"Processing: [yellow]{source}[/yellow]",
        title="[bold cyan]🚀 CodeCrusher Injection[/bold cyan]",
        border_style="cyan"
    ))

    # Create configuration display
    try:
        config_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
        config_table.add_row("[cyan]Source:[/cyan]", f"[yellow]{source}[/yellow]")
        config_table.add_row("[cyan]Prompt:[/cyan]", f"[yellow]{prompt[:100]}{'...' if len(prompt) > 100 else ''}[/yellow]")
        config_table.add_row("[cyan]Model:[/cyan]", f"[green]{model}[/green]")
        config_table.add_row("[cyan]Recursive:[/cyan]", f"[{'green' if recursive else 'red'}]{recursive}[/{'green' if recursive else 'red'}]")
        config_table.add_row("[cyan]Preview:[/cyan]", f"[{'green' if preview else 'red'}]{preview}[/{'green' if preview else 'red'}]")
        config_table.add_row("[cyan]Apply:[/cyan]", f"[{'green' if apply else 'red'}]{apply}[/{'green' if apply else 'red'}]")

        if ext:
            config_table.add_row("[cyan]Extensions:[/cyan]", f"[yellow]{ext}[/yellow]")

        console.print(Panel(config_table, title="[bold]Configuration[/bold]", border_style="blue"))
    except:
        # Fallback for basic display
        print(f"Source: {source}")
        print(f"Prompt: {prompt}")
        print(f"Mode: {'Preview' if preview else 'Apply'}")
        print(f"Recursive: {recursive}")

    # Try to use the advanced AI injector, fallback to simulation
    try:
        from codecrusher.ai_injector import AIInjector

        # Create AI injector instance
        injector = AIInjector(model=model, verbose=verbose)

        # Process the files
        result = injector.process_files(
            source_path=source,
            prompt=prompt,
            recursive=recursive,
            extensions=ext.split(',') if ext else None,
            preview_mode=preview
        )

        # Display results
        if result['success']:
            console.print(Panel(
                f"[green]✅ Successfully processed {result['files_processed']} files[/green]\n"
                f"[yellow]Mode: {'Preview' if preview else 'Applied changes'}[/yellow]\n"
                f"[cyan]Changes made: {result['changes_made']}[/cyan]",
                title="[bold green]Injection Complete[/bold green]",
                border_style="green"
            ))

            # Show diff if in preview mode
            if preview and result.get('preview_diff'):
                console.print("\n[bold cyan]Preview of Changes:[/bold cyan]")
                console.print(result['preview_diff'])
                console.print("\n[dim]Use --apply to make these changes permanent[/dim]")

        else:
            console.print(Panel(
                f"[red]❌ Processing failed: {result.get('error', 'Unknown error')}[/red]",
                title="[bold red]Injection Failed[/bold red]",
                border_style="red"
            ))
            sys.exit(1)

    except ImportError:
        # Fallback simulation
        console.print(Panel(
            f"[yellow]⚠️ AI injection module not found[/yellow]\n"
            f"[dim]Simulating processing for now...[/dim]",
            title="[bold yellow]Simulation Mode[/bold yellow]",
            border_style="yellow"
        ))

        import time
        time.sleep(2)
        console.print(Panel(
            f"[green]✅ Simulation completed for {source}[/green]\n"
            f"[yellow]Mode: {'Preview' if preview else 'Apply'}[/yellow]\n"
            f"[dim]Install AI dependencies to enable real processing[/dim]",
            title="[bold green]Simulation Complete[/bold green]",
            border_style="green"
        ))

@cli.command()
def auth():
    """🔐 Authentication commands"""
    console.print(Panel(
        "[bold]Authentication Commands[/bold]\n\n"
        "[yellow]Available commands:[/yellow]\n"
        "  codecrusher auth login   - Login to your account\n"
        "  codecrusher auth logout  - Logout from your account\n"
        "  codecrusher auth status  - Check authentication status",
        title="[bold cyan]🔐 Authentication[/bold cyan]",
        border_style="cyan"
    ))

@cli.command()
def version():
    """📋 Show version information"""
    console.print(Panel(
        "[bold]CodeCrusher v0.1.0[/bold]\n"
        "[cyan]CodeCrusher CLI – AugmentKiller with smart injection, fallback, feedback, and dashboard integration[/cyan]\n"
        "[dim]Developed by Your Name[/dim]",
        title="[bold cyan]Version Info[/bold cyan]",
        border_style="cyan"
    ))

@cli.command()
def status():
    """📊 Show system status"""
    console.print(Panel(
        "[bold]CodeCrusher System Status[/bold]\n\n"
        "[green]✅ CLI Entry Point:[/green] Working\n"
        "[green]✅ Click Interface:[/green] Available\n"
        "[green]✅ Intelligence Loop:[/green] Available\n"
        "[yellow]⚠️ Backend Connection:[/yellow] Not configured\n\n"
        "[cyan]Available Commands:[/cyan]\n"
        "  inject   - Code injection with AI\n"
        "  rate     - Rate injection results\n"
        "  learn    - Trigger intelligence learner\n"
        "  auth     - Authentication\n"
        "  version  - Version information\n"
        "  status   - System status",
        title="[bold blue]📊 Status[/bold blue]",
        border_style="blue"
    ))

@cli.command()
@click.argument('source', type=click.Path(exists=True))
@click.option('--recursive', '-r', is_flag=True, help='🔄 Recursively scan directories')
@click.option('--rating', type=click.IntRange(1, 5), required=True, help='⭐ Rating (1-5)')
@click.option('--comment', help='💬 Optional feedback comment')
@click.option('--verbose', '-v', is_flag=True, help='🔍 Enable verbose output')
def rate(source, recursive, rating, comment, verbose):
    """
    ⭐ Rate injection results for intelligence learning

    Examples:
      codecrusher rate ./test-cases --recursive --rating 2
      codecrusher rate ./src/main.py --rating 4 --comment "Good but needs optimization"
    """
    console.print(Panel(
        "[bold]CodeCrusher: Rating injection results[/bold]\n"
        f"Processing: [yellow]{source}[/yellow]",
        title="[bold cyan]⭐ Rating System[/bold cyan]",
        border_style="cyan"
    ))

    # Create configuration display
    config_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    config_table.add_row("[cyan]Source:[/cyan]", f"[yellow]{source}[/yellow]")
    config_table.add_row("[cyan]Rating:[/cyan]", f"[{'green' if rating >= 4 else 'yellow' if rating >= 3 else 'red'}]{rating}/5 {'⭐' * rating}[/{'green' if rating >= 4 else 'yellow' if rating >= 3 else 'red'}]")
    config_table.add_row("[cyan]Recursive:[/cyan]", f"[{'green' if recursive else 'red'}]{recursive}[/{'green' if recursive else 'red'}]")
    if comment:
        config_table.add_row("[cyan]Comment:[/cyan]", f"[yellow]{comment}[/yellow]")

    console.print(Panel(config_table, title="[bold]Rating Configuration[/bold]", border_style="blue"))

    try:
        # Import intelligence system
        from codecrusher.intelligence_system import IntelligenceSystem

        intelligence = IntelligenceSystem(verbose=verbose)
        result = intelligence.rate_injection(
            source_path=source,
            rating=rating,
            comment=comment,
            recursive=recursive
        )

        if result['success']:
            console.print(Panel(
                f"[green]✅ Successfully rated {result['files_rated']} files[/green]\n"
                f"[cyan]Average rating: {result['average_rating']:.1f}/5[/cyan]\n"
                f"[yellow]Feedback entries: {result['total_entries']}[/yellow]",
                title="[bold green]Rating Complete[/bold green]",
                border_style="green"
            ))
        else:
            console.print(Panel(
                f"[red]❌ Rating failed: {result.get('error', 'Unknown error')}[/red]",
                title="[bold red]Rating Failed[/bold red]",
                border_style="red"
            ))
            sys.exit(1)

    except ImportError:
        # Fallback simulation
        console.print(Panel(
            f"[yellow]⚠️ Intelligence system not found[/yellow]\n"
            f"[dim]Simulating rating for now...[/dim]",
            title="[bold yellow]Simulation Mode[/bold yellow]",
            border_style="yellow"
        ))

        import time
        time.sleep(1)
        console.print(Panel(
            f"[green]✅ Simulation: Rated {source} with {rating}/5 stars[/green]\n"
            f"[yellow]Comment: {comment or 'No comment provided'}[/yellow]",
            title="[bold green]Rating Simulation Complete[/bold green]",
            border_style="green"
        ))

@cli.command()
@click.option('--apply', is_flag=True, help='✅ Apply learned improvements')
@click.option('--dry-run', is_flag=True, help='🔍 Show what would be learned without applying')
@click.option('--verbose', '-v', is_flag=True, help='🔍 Enable verbose output')
def learn(apply, dry_run, verbose):
    """
    🧠 Trigger intelligence learner to improve AI responses

    Examples:
      codecrusher learn --dry-run
      codecrusher learn --apply
    """
    mode = "Apply" if apply else "Dry Run" if dry_run else "Interactive"

    console.print(Panel(
        f"[bold]CodeCrusher: Intelligence Learning[/bold]\n"
        f"Mode: [yellow]{mode}[/yellow]",
        title="[bold cyan]Intelligence Learner[/bold cyan]",
        border_style="cyan"
    ))

    try:
        # Import intelligence system
        from codecrusher.intelligence_system import IntelligenceSystem

        intelligence = IntelligenceSystem(verbose=verbose)
        result = intelligence.learn_from_feedback(
            apply_changes=apply,
            dry_run=dry_run
        )

        if result['success']:
            console.print(Panel(
                f"[green]✅ Learning complete[/green]\n"
                f"[cyan]Feedback entries analyzed: {result['entries_analyzed']}[/cyan]\n"
                f"[yellow]Improvements identified: {result['improvements_made']}[/yellow]\n"
                f"[blue]Shaping parameters updated: {result['parameters_updated']}[/blue]",
                title="[bold green]Learning Complete[/bold green]",
                border_style="green"
            ))

            if result.get('improvements'):
                console.print("\n[bold cyan]Improvements Made:[/bold cyan]")
                for improvement in result['improvements']:
                    console.print(f"  • {improvement}")

        else:
            console.print(Panel(
                f"[red]❌ Learning failed: {result.get('error', 'Unknown error')}[/red]",
                title="[bold red]Learning Failed[/bold red]",
                border_style="red"
            ))
            sys.exit(1)

    except ImportError:
        # Fallback simulation
        console.print(Panel(
            f"[yellow]⚠️ Intelligence system not found[/yellow]\n"
            f"[dim]Simulating learning for now...[/dim]",
            title="[bold yellow]Simulation Mode[/bold yellow]",
            border_style="yellow"
        ))

        import time
        time.sleep(2)
        console.print(Panel(
            f"[green]✅ Simulation: Learning complete[/green]\n"
            f"[cyan]Analyzed feedback and updated parameters[/cyan]\n"
            f"[yellow]Mode: {mode}[/yellow]",
            title="[bold green]Learning Simulation Complete[/bold green]",
            border_style="green"
        ))

def main():
    """
    Main entry point for the CLI - compatible with pyproject.toml [project.scripts]
    This function is called when 'codecrusher' command is executed.
    """
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️ Operation cancelled by user[/yellow]")
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]❌ Unexpected error: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    main()
