"""
Analyze command for CodeCrusher.

This module provides the analyze command for CodeCrusher, which analyzes
code and provides insights and recommendations.
"""

import typer
import json
from pathlib import Path
from datetime import datetime
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.syntax import Syntax
from rich.text import Text
from rich.box import ROUNDED

# Import tag manager
from codecrusher.tag_manager import parse_user_tags

# Import filter utilities
from codecrusher.filter_utils import (
    parse_filter,
    apply_filters,
    format_entry_preview,
    PreviewMode,
    truncate_text
)

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

@app.command("run")
def run_analyze(
    input_file: str = typer.Option(..., "--input", "-i", help="Path to the input file"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Filter by tag (space-separated)"),
    filter: Optional[str] = typer.Option(None, "--filter", help="Filter by fields (field=value,...)"),
    preview: str = typer.Option("full", "--preview", help="Preview mode (short, full, metadata)"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Show detailed information"),
):
    """
    Analyze code and provide insights and recommendations.
    
    This command analyzes the input file and provides insights and recommendations
    for improving the code. You can filter the results by tags and fields.
    
    Preview modes:
        --preview short      # show ID, date, truncated input/output (1-2 lines each)
        --preview full       # default: shows everything
        --preview metadata   # only ID, model, tags, and timestamps
    
    Filter options:
        --filter model=mistral
        --filter fallback=true
        --filter tag=teamA
        --filter error=timeout
    
    Examples:
        codecrusher analyze run -i file.py
        codecrusher analyze run -i file.py --tag performance
        codecrusher analyze run -i file.py --filter model=mistral
        codecrusher analyze run -i file.py --preview metadata
    """
    # Parse filter tags
    filter_tags = parse_user_tags(tag)
    
    # Parse field filters
    field_filters = parse_filter(filter)
    
    # Determine preview mode
    try:
        preview_mode = PreviewMode(preview.lower())
    except ValueError:
        console.print(f"[bold red]❌ Error:[/bold red] Invalid preview mode: {preview}")
        console.print("[yellow]Valid preview modes: short, full, metadata[/yellow]")
        preview_mode = PreviewMode.FULL
    
    # Format filter tags for display
    filter_tags_display = ", ".join(filter_tags) if filter_tags else "None"
    
    # Format field filters for display
    field_filters_display = ", ".join([f"{k}={v}" for k, v in field_filters.items()]) if field_filters else "None"
    
    console.print(Panel(
        f"[bold]CodeCrusher Analyze[/bold]\n\n"
        f"[cyan]Input file:[/cyan] {input_file}\n"
        f"[cyan]Filter Tags:[/cyan] {filter_tags_display}\n"
        f"[cyan]Field Filters:[/cyan] {field_filters_display}\n"
        f"[cyan]Preview Mode:[/cyan] {preview_mode.value}\n"
        f"[cyan]Verbose:[/cyan] {'Enabled' if verbose else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))
    
    # Validate file exists
    input_path = Path(input_file)
    if not input_path.exists():
        console.print(f"[bold red]❌ Error:[/bold red] File not found: {input_file}")
        raise typer.Exit(code=1)
    
    # Read file content
    try:
        file_content = input_path.read_text(encoding="utf-8")
    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] Failed to read file: {str(e)}")
        raise typer.Exit(code=1)
    
    # Placeholder for analysis results
    analysis_results = [
        {
            "id": "complexity",
            "model": "analyzer",
            "timestamp": datetime.now().isoformat(),
            "tags": ["complexity", "performance"],
            "title": "Code Complexity Analysis",
            "description": "The code has high cyclomatic complexity in several functions.",
            "recommendations": [
                "Refactor the main loop to reduce nesting",
                "Extract complex conditions into separate functions",
                "Consider using a state machine pattern"
            ]
        },
        {
            "id": "security",
            "model": "analyzer",
            "timestamp": datetime.now().isoformat(),
            "tags": ["security", "bugfix"],
            "title": "Security Analysis",
            "description": "Potential security issues found in the code.",
            "recommendations": [
                "Validate user input before processing",
                "Use parameterized queries for database access",
                "Implement proper error handling"
            ]
        }
    ]
    
    # Filter results by tags and fields
    filtered_results = []
    for result in analysis_results:
        # Filter by tags
        if filter_tags and not all(tag in result["tags"] for tag in filter_tags):
            continue
        
        # Filter by fields
        if field_filters and not apply_filters(result, field_filters):
            continue
        
        filtered_results.append(result)
    
    # Display results based on preview mode
    if filtered_results:
        console.print(f"\n[bold green]Found {len(filtered_results)} analysis results:[/bold green]")
        
        for result in filtered_results:
            if preview_mode == PreviewMode.SHORT:
                # Short preview
                console.print(Panel(
                    f"[bold]{result['title']}[/bold]\n"
                    f"[dim]{truncate_text(result['description'], 80)}[/dim]",
                    title=f"[cyan]{result['id']}[/cyan]",
                    border_style="blue"
                ))
            elif preview_mode == PreviewMode.METADATA:
                # Metadata only
                console.print(Panel(
                    f"[bold]{result['title']}[/bold]\n"
                    f"[cyan]ID:[/cyan] {result['id']}\n"
                    f"[cyan]Model:[/cyan] {result['model']}\n"
                    f"[cyan]Timestamp:[/cyan] {result['timestamp']}\n"
                    f"[cyan]Tags:[/cyan] {', '.join(result['tags'])}",
                    title="Metadata",
                    border_style="blue"
                ))
            else:  # FULL
                # Full preview
                console.print(Panel(
                    f"[bold]{result['title']}[/bold]\n\n"
                    f"{result['description']}\n\n"
                    f"[bold]Recommendations:[/bold]\n" +
                    "\n".join([f"- {rec}" for rec in result['recommendations']]),
                    title=f"[cyan]{result['id']}[/cyan]",
                    border_style="blue"
                ))
    else:
        console.print("[bold yellow]No analysis results found matching the filter criteria[/bold yellow]")
    
    return True
