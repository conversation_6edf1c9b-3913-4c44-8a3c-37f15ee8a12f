#!/usr/bin/env python3
"""
Simple CodeCrusher CLI - Inject Command
"""

import typer
import asyncio
import logging
from typing import Optional
from pathlib import Path

from codecrusher_engine import run_codecrusher_injection

app = typer.Typer(help="CodeCrusher CLI - Inject AI-generated code into source files")

@app.command()
def inject(
    input: str = typer.Option(..., "--input", "-i", help="Path to input file"),
    prompt_text: str = typer.Option(..., "--prompt-text", "-t", help="Prompt for code injection"),
    provider: str = typer.Option("auto", "--provider", help="AI provider to use"),
    model: str = typer.Option("auto", "--model", help="Model to use"),
    cache: bool = typer.Option(False, "--cache", help="Enable caching"),
    verbose: bool = typer.Option(False, "--verbose", help="Enable verbose logging"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file path (default: print to console)"),
):
    """
    Run code injection on the input file using AI engine.

    This command reads the input file, sends the code and prompt to the AI engine,
    and either prints the injected code or saves it to a file.
    """
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG if verbose else logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)

    # Log configuration
    logger.info(f"Starting injection for file: {input}")
    logger.info(f"Prompt: {prompt_text}")
    logger.info(f"Provider: {provider}, Model: {model}, Cache: {cache}")

    # Read input file
    try:
        input_path = Path(input)
        if not input_path.exists():
            logger.error(f"File not found: {input}")
            typer.echo(f"❌ Error: File not found: {input}")
            raise typer.Exit(code=1)

        code = input_path.read_text(encoding="utf-8")
        if verbose:
            logger.debug(f"Original code length: {len(code)}")
    except Exception as e:
        logger.error(f"Error reading file: {str(e)}")
        typer.echo(f"❌ Error: {str(e)}")
        raise typer.Exit(code=1)

    # Call AI engine
    try:
        logger.info("Sending code and prompt to AI engine...")
        typer.echo("⏳ Processing... Please wait")

        # Call the async function using asyncio.run
        injected_code = asyncio.run(run_codecrusher_injection(
            code=code,
            prompt=prompt_text,
            provider=provider,
            model=model,
            cache=cache,
            verbose=verbose
        ))

        # Handle output
        if output:
            # Save to output file
            try:
                output_path = Path(output)
                output_path.write_text(injected_code, encoding="utf-8")
                logger.info(f"Injected code saved to: {output}")
                typer.echo(f"✅ Injected code saved to: {output}")
            except Exception as e:
                logger.error(f"Error writing to output file: {str(e)}")
                typer.echo(f"❌ Error: {str(e)}")
                raise typer.Exit(code=1)
        else:
            # Print to console
            typer.echo("\n=== Injected Code ===\n")
            typer.echo(injected_code)

        logger.info("Injection completed successfully.")
        typer.echo("\n✅ Injection completed successfully!")

    except Exception as e:
        logger.error(f"Error during injection: {str(e)}")
        typer.echo(f"❌ Error: {str(e)}")
        raise typer.Exit(code=1)

def main():
    app()

if __name__ == "__main__":
    main()
