import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Loader2, CheckCircle, XCircle, Info, Brain, Zap, Target } from 'lucide-react';
import { useLogStream } from '@/hooks/useLogStream';
import { LogDisplay } from '@/components/LogDisplay';
import { LogPanelDemo } from '@/components/LogPanelDemo';
import LiveLogPanel from '@/components/LiveLogPanel';
import Dashboard from '@/components/Dashboard';
import StreamlinedDashboard from '@/components/StreamlinedDashboard';
import CleanDashboard from '@/components/CleanDashboard';
import EnhancedDashboard from '@/components/EnhancedDashboard';
import BackendDashboard from '@/components/BackendDashboard';
import DashboardUI from '@/components/DashboardUI';
import StatusDashboard from '@/components/StatusDashboard';
import StableStatusDashboard from '@/components/StableStatusDashboard';
import EnterpriseDashboard from '@/components/EnterpriseDashboard';
import StyleTest from '@/components/StyleTest';

interface InjectionRequest {
  source: string;
  prompt_text: string;
  recursive: boolean;
  ext: string;
  tag: string;
  apply: boolean;
  auto_model_routing: boolean;
  refresh_cache: boolean;
  force: boolean;
  summary: boolean;
}

interface InjectionResponse {
  success: boolean;
  message: string;
  output?: string;
  error?: string;
  execution_time?: number;
  timestamp: string;
}

interface HealthStatus {
  status: string;
  codecrusher_available: boolean;
  codecrusher_path?: string;
  timestamp: string;
}

const API_BASE_URL = 'http://localhost:8000';

export default function App() {
  // Form state - Enhanced with your design
  const [sourceFolder, setSourceFolder] = useState('./src');
  const [prompt, setPrompt] = useState('Optimize logic for clarity');
  const [tag, setTag] = useState('clarity-pass');
  const [recursive, setRecursive] = useState(true);
  const [ext, setExt] = useState('py');
  const [apply, setApply] = useState(false);

  // Model selection - Your enhanced design
  const [model, setModel] = useState('auto');
  const [fallbackEnabled, setFallbackEnabled] = useState(true);
  const [refreshCache, setRefreshCache] = useState(false);
  const [force, setForce] = useState(false);
  const [summary, setSummary] = useState(true);

  // UI state - Enhanced with your progress design
  const [progress, setProgress] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [lastResponse, setLastResponse] = useState<InjectionResponse | null>(null);
  const [completionStats, setCompletionStats] = useState<any>(null);

  // WebSocket integration
  const {
    logs: wsLogs,
    isConnected: wsConnected,
    connectionError: wsError,
    clearLogs: clearWsLogs,
    sendTestMessage,
    latestProgress,
    latestStats
  } = useLogStream('ws://localhost:8000/ws/logs');

  // Available options
  const [models, setModels] = useState<any[]>([]);
  const [extensions, setExtensions] = useState<string[]>([]);

  // View mode toggle
  const [viewMode, setViewMode] = useState<'main' | 'simple' | 'streamlined' | 'clean' | 'enhanced' | 'backend' | 'ui' | 'status' | 'stable' | 'enterprise' | 'demo' | 'styletest'>('enterprise');

  useEffect(() => {
    checkHealth();
    loadModels();
    loadExtensions();
  }, []);

  // Update progress and stats from WebSocket
  useEffect(() => {
    if (latestProgress > 0) {
      setProgress(latestProgress);
    }
  }, [latestProgress]);

  useEffect(() => {
    if (latestStats) {
      setCompletionStats(latestStats);
    }
  }, [latestStats]);

  const checkHealth = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      const data = await response.json();
      setHealthStatus(data);
    } catch (error) {
      console.error('Health check failed:', error);
    }
  };

  const loadModels = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/models`);
      const data = await response.json();
      setModels(data.models || []);
    } catch (error) {
      console.error('Failed to load models:', error);
    }
  };

  const loadExtensions = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/extensions`);
      const data = await response.json();
      setExtensions(data.extensions || []);
    } catch (error) {
      console.error('Failed to load extensions:', error);
    }
  };

  const validateSource = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/validate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: `source=${encodeURIComponent(sourceFolder)}`
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Source validation failed:', error);
      return { valid: false, error: 'Validation failed' };
    }
  };

  const runInjection = async () => {
    setIsRunning(true);
    setProgress(0);
    setCompletionStats(null);
    clearWsLogs(); // Clear previous logs

    try {
      // Build request data
      const requestData: InjectionRequest = {
        source: sourceFolder,
        prompt_text: prompt,
        recursive,
        ext,
        tag,
        apply,
        auto_model_routing: model === 'auto',
        refresh_cache: refreshCache,
        force,
        summary
      };

      // Execute injection - WebSocket will handle real-time logging
      const response = await fetch(`${API_BASE_URL}/inject`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      const data: InjectionResponse = await response.json();
      setLastResponse(data);

      // Final progress update if not already at 100%
      if (progress < 100) {
        setProgress(100);
      }

    } catch (error) {
      setLastResponse({
        success: false,
        message: 'Request failed',
        error: String(error),
        timestamp: new Date().toISOString()
      });
      setProgress(100);
    } finally {
      setIsRunning(false);
    }
  };

  const extractStatsFromOutput = (output: string) => {
    // Extract statistics from CLI output
    const stats = {
      totalFiles: 0,
      processedFiles: 0,
      failedFiles: 0,
      successRate: 0
    };

    try {
      // Look for patterns in the output
      const totalMatch = output.match(/Total Files:\s*(\d+)/);
      const processedMatch = output.match(/(\d+)\s*injected/);
      const failedMatch = output.match(/(\d+)\s*failed/);

      if (totalMatch) stats.totalFiles = parseInt(totalMatch[1]);
      if (processedMatch) stats.processedFiles = parseInt(processedMatch[1]);
      if (failedMatch) stats.failedFiles = parseInt(failedMatch[1]);

      if (stats.totalFiles > 0) {
        stats.successRate = Math.round((stats.processedFiles / stats.totalFiles) * 100);
      }
    } catch (error) {
      console.error('Failed to extract stats:', error);
    }

    return stats;
  };

  const getStatusIcon = () => {
    if (!healthStatus) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (healthStatus.codecrusher_available) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusText = () => {
    if (!healthStatus) return 'Checking...';
    if (healthStatus.codecrusher_available) return 'CodeCrusher Available';
    return 'CodeCrusher Not Found';
  };

  // Enterprise mode should take full screen but include navigation
  if (viewMode === 'enterprise') {
    return (
      <EnterpriseDashboard
        viewMode={viewMode}
        setViewMode={setViewMode}
        healthStatus={healthStatus}
        wsConnected={wsConnected}
        getStatusIcon={getStatusIcon}
        getStatusText={getStatusText}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6 text-gray-800">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header - Your enhanced design */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex-1"></div>
          <h1 className="text-3xl font-bold text-center flex items-center gap-2">
            <Brain className="h-8 w-8 text-blue-600" />
            CodeCrusher Dashboard
          </h1>
          <div className="flex-1 flex justify-end">
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'main' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('main')}
                className="text-sm"
              >
                Main Dashboard
              </Button>
              <Button
                variant={viewMode === 'simple' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('simple')}
                className="text-sm"
              >
                Full Dashboard
              </Button>
              <Button
                variant={viewMode === 'streamlined' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('streamlined')}
                className="text-sm"
              >
                Streamlined
              </Button>
              <Button
                variant={viewMode === 'clean' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('clean')}
                className="text-sm"
              >
                Clean
              </Button>
              <Button
                variant={viewMode === 'enhanced' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('enhanced')}
                className="text-sm"
              >
                Enhanced
              </Button>
              <Button
                variant={viewMode === 'backend' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('backend')}
                className="text-sm"
              >
                Backend
              </Button>
              <Button
                variant={viewMode === 'ui' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('ui')}
                className="text-sm"
              >
                UI
              </Button>
              <Button
                variant={viewMode === 'status' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('status')}
                className="text-sm"
              >
                Status
              </Button>
              <Button
                variant={viewMode === 'stable' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('stable')}
                className="text-sm"
              >
                Stable
              </Button>
              <Button
                variant={viewMode === 'enterprise' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('enterprise')}
                className="text-sm bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 hover:from-blue-700 hover:to-indigo-700"
              >
                Enterprise
              </Button>
              <Button
                variant={viewMode === 'demo' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('demo')}
                className="text-sm"
              >
                Log Panel Demo
              </Button>
              <Button
                variant={viewMode === 'styletest' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('styletest')}
                className="text-sm bg-yellow-500 text-white border-0 hover:bg-yellow-600"
              >
                Style Test
              </Button>
            </div>
          </div>
        </div>

        {/* Health Status */}
        <div className="flex items-center justify-center space-x-4 mb-4">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className="text-sm font-medium">{getStatusText()}</span>
          </div>
          <div className="flex items-center space-x-2">
            {wsConnected ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
            <span className="text-sm font-medium">
              WebSocket {wsConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>

        {/* Health Alert */}
        {healthStatus && !healthStatus.codecrusher_available && (
          <Alert className="mb-6">
            <Info className="h-4 w-4" />
            <AlertDescription>
              CodeCrusher CLI not found. Please ensure it's installed and available in your PATH or virtual environment.
              {healthStatus.codecrusher_path && ` Detected path: ${healthStatus.codecrusher_path}`}
            </AlertDescription>
          </Alert>
        )}

        {/* Conditional Content */}
        {viewMode === 'demo' ? (
          <LogPanelDemo />
        ) : viewMode === 'simple' ? (
          <Dashboard
            viewMode={viewMode}
            setViewMode={setViewMode}
            healthStatus={healthStatus}
            wsConnected={wsConnected}
            getStatusIcon={getStatusIcon}
            getStatusText={getStatusText}
          />
        ) : viewMode === 'streamlined' ? (
          <StreamlinedDashboard
            viewMode={viewMode}
            setViewMode={setViewMode}
            healthStatus={healthStatus}
            wsConnected={wsConnected}
            getStatusIcon={getStatusIcon}
            getStatusText={getStatusText}
          />
        ) : viewMode === 'clean' ? (
          <CleanDashboard />
        ) : viewMode === 'enhanced' ? (
          <EnhancedDashboard />
        ) : viewMode === 'backend' ? (
          <BackendDashboard />
        ) : viewMode === 'ui' ? (
          <DashboardUI />
        ) : viewMode === 'status' ? (
          <StatusDashboard />
        ) : viewMode === 'stable' ? (
          <StableStatusDashboard />
        ) : viewMode === 'styletest' ? (
          <StyleTest />
        ) : (
          <>
            {/* Main Form - Your streamlined design */}
            <div className="grid grid-cols-1 gap-4">
          {/* Source Folder Input */}
          <Input
            value={sourceFolder}
            onChange={e => setSourceFolder(e.target.value)}
            placeholder="Source folder (e.g., ./src)"
            className="text-lg"
          />

          {/* AI Prompt */}
          <Textarea
            value={prompt}
            onChange={e => setPrompt(e.target.value)}
            placeholder="Prompt for AI injection"
            rows={3}
            className="text-lg"
          />

          {/* Tag Input */}
          <Input
            value={tag}
            onChange={e => setTag(e.target.value)}
            placeholder="Injection tag"
            className="text-lg"
          />

          {/* Model Selection and Options Row */}
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex-1 min-w-48">
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger>
                  <SelectValue placeholder="Select AI Model" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">🤖 Auto Selection</SelectItem>
                  <SelectItem value="groq-mixtral">🧠 Groq Mixtral</SelectItem>
                  <SelectItem value="groq-llama3">🦙 Groq LLaMA 3</SelectItem>
                  <SelectItem value="groq-gemma">💎 Groq Gemma</SelectItem>
                  <SelectItem value="mistral">🌟 Mistral</SelectItem>
                  <SelectItem value="openai-gpt4">🚀 OpenAI GPT-4</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                checked={fallbackEnabled}
                onCheckedChange={setFallbackEnabled}
                id="fallback"
              />
              <Label htmlFor="fallback" className="text-sm font-medium">
                Fallback Routing
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                checked={recursive}
                onCheckedChange={setRecursive}
                id="recursive"
              />
              <Label htmlFor="recursive" className="text-sm font-medium">
                Recursive
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                checked={apply}
                onCheckedChange={setApply}
                id="apply"
              />
              <Label htmlFor="apply" className="text-sm font-medium">
                {apply ? 'Apply Changes' : 'Preview Mode'}
              </Label>
            </div>
          </div>

          {/* Run Button - Your enhanced design */}
          <Button
            disabled={isRunning || !healthStatus?.codecrusher_available}
            onClick={runInjection}
            className="w-full text-lg py-6 bg-blue-600 hover:bg-blue-700"
          >
            {isRunning ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Running...
              </>
            ) : (
              <>
                <Zap className="mr-2 h-5 w-5" />
                🚀 Run Injection
              </>
            )}
          </Button>

          {/* Progress Bar - Your design */}
          <Progress value={progress} className="w-full h-4 rounded-lg" />

          {/* Completion Stats - Your enhancement */}
          {completionStats && (
            <Card className="bg-green-50 border-green-200">
              <CardContent className="pt-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-600">{completionStats.totalFiles}</div>
                    <div className="text-sm text-gray-600">Total Files</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{completionStats.processedFiles}</div>
                    <div className="text-sm text-gray-600">Processed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-600">{completionStats.failedFiles}</div>
                    <div className="text-sm text-gray-600">Failed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-purple-600">{completionStats.successRate}%</div>
                    <div className="text-sm text-gray-600">Success Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* WebSocket Real-time Log Display */}
          <LogDisplay
            logs={wsLogs}
            isConnected={wsConnected}
            connectionError={wsError}
            onClearLogs={clearWsLogs}
            onSendTestMessage={sendTestMessage}
            className="h-64"
          />

            </div>
          </>
        )}
      </div>
    </div>
  );
}
