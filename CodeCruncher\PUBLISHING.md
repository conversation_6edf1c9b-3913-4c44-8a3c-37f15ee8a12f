# CodeCrusher Publishing Guide

This guide explains how to use the automated publishing system to release new versions of CodeCrusher to PyPI.

## 🚀 Quick Start

### Prerequisites

1. **Install publishing dependencies:**
   ```bash
   pip install twine setuptools wheel
   ```

2. **Set up PyPI credentials** (see [Authentication](#authentication) below)

3. **Ensure you're on the main branch with clean working directory:**
   ```bash
   git checkout main
   git pull origin main
   git status  # Should show clean working directory
   ```

### Basic Usage

```bash
# Bump patch version and publish to PyPI
python publish.py --patch

# Bump minor version and publish to TestPyPI (for testing)
python publish.py --minor --test

# Bump major version, publish, and create git tag
python publish.py --major --tag

# See what would happen without making changes
python publish.py --patch --dry-run
```

### Alternative Scripts

**Unix/Linux/Mac:**
```bash
./release.sh patch
./release.sh minor --test --tag
```

**Windows:**
```cmd
release.bat patch
release.bat minor --test --tag
```

## 📋 Publishing Workflow

The automated publishing process follows these steps:

1. **Version Bump**: Updates version in `setup.py` and `CHANGELOG.md`
2. **Git Commit**: Commits version changes with standardized message
3. **Clean Build**: Removes old `dist/` and `build/` directories
4. **Package Build**: Creates source distribution and wheel
5. **Upload**: Publishes to PyPI or TestPyPI
6. **Git Tag**: Optionally creates and pushes version tag

## 🔧 Command Options

### Version Types

| Option | Description | Example |
|--------|-------------|---------|
| `--patch` | Bug fixes, small changes | `1.0.0` → `1.0.1` |
| `--minor` | New features, backwards compatible | `1.0.1` → `1.1.0` |
| `--major` | Breaking changes | `1.1.0` → `2.0.0` |

### Publishing Options

| Option | Description |
|--------|-------------|
| `--test` | Publish to TestPyPI instead of PyPI |
| `--tag` | Create and push git tag after successful publish |
| `--dry-run` | Show what would happen without making changes |

## 🔐 Authentication

### Method 1: PyPI Configuration File (Recommended)

1. **Copy the template:**
   ```bash
   cp .pypirc.template ~/.pypirc
   ```

2. **Get API tokens:**
   - PyPI: https://pypi.org/manage/account/token/
   - TestPyPI: https://test.pypi.org/manage/account/token/

3. **Edit `~/.pypirc` with your tokens:**
   ```ini
   [pypi]
   repository = https://upload.pypi.org/legacy/
   username = __token__
   password = pypi-YOUR_ACTUAL_TOKEN_HERE
   ```

### Method 2: Environment Variables

```bash
export TWINE_USERNAME=__token__
export TWINE_PASSWORD=pypi-YOUR_API_TOKEN_HERE
```

### Method 3: Interactive Prompt

If no credentials are configured, twine will prompt for username and password.

## 📊 Version Management

### Semantic Versioning

CodeCrusher follows [Semantic Versioning](https://semver.org/):

- **MAJOR** version: Incompatible API changes
- **MINOR** version: Backwards-compatible functionality additions
- **PATCH** version: Backwards-compatible bug fixes

### Changelog Updates

The publishing script automatically updates `CHANGELOG.md`:

- Replaces `[Unreleased]` section with new version and date
- Maintains proper changelog format
- Preserves existing version history

## 🧪 Testing Releases

### TestPyPI Workflow

1. **Publish to TestPyPI:**
   ```bash
   python publish.py --minor --test
   ```

2. **Test installation:**
   ```bash
   pip install --index-url https://test.pypi.org/simple/ codecrusher
   ```

3. **Verify functionality:**
   ```bash
   codecrusher --version
   codecrusher status
   ```

4. **If successful, publish to production PyPI:**
   ```bash
   python publish.py --minor --tag
   ```

## 🔍 Troubleshooting

### Common Issues

**1. Authentication Failed**
```
ERROR: HTTP Error 403: Invalid or non-existent authentication information
```
- Check your API token in `~/.pypirc`
- Ensure token has upload permissions
- Verify you're using `__token__` as username

**2. Version Already Exists**
```
ERROR: File already exists
```
- PyPI doesn't allow re-uploading same version
- Bump to next version number
- Use `--dry-run` to check version before publishing

**3. Package Build Failed**
```
ERROR: Build failed - no distribution files created
```
- Check `setup.py` syntax
- Ensure all dependencies are installed
- Review build output for specific errors

**4. Git Tag Already Exists**
```
ERROR: tag 'v1.0.1' already exists
```
- Delete existing tag: `git tag -d v1.0.1`
- Push deletion: `git push origin :refs/tags/v1.0.1`
- Re-run publishing command

### Debug Mode

Use `--dry-run` to see what would happen:

```bash
python publish.py --patch --dry-run
```

This shows:
- Current and new version numbers
- Files that would be modified
- Commands that would be executed
- No actual changes are made

## 📦 Manual Publishing

If automated publishing fails, you can publish manually:

```bash
# Clean previous builds
rm -rf dist/ build/

# Build package
python setup.py sdist bdist_wheel

# Upload to PyPI
twine upload dist/*

# Upload to TestPyPI
twine upload --repository-url https://test.pypi.org/legacy/ dist/*
```

## 🔒 Security Best Practices

1. **Never commit `.pypirc` or API tokens to version control**
2. **Use API tokens instead of passwords**
3. **Limit token scope to upload permissions only**
4. **Rotate tokens regularly**
5. **Use TestPyPI for testing before production releases**

## 📈 Release Checklist

Before publishing a new version:

- [ ] All tests pass
- [ ] Documentation is updated
- [ ] CHANGELOG.md has unreleased changes
- [ ] Version bump type is appropriate (patch/minor/major)
- [ ] Clean git working directory
- [ ] PyPI credentials are configured

After publishing:

- [ ] Verify package installs correctly
- [ ] Test key functionality
- [ ] Update any dependent projects
- [ ] Announce release if significant

## 🤝 Team Publishing

For team environments:

1. **Designate release managers** with PyPI access
2. **Use shared TestPyPI account** for testing
3. **Require code review** before version bumps
4. **Automate with CI/CD** for consistent releases
5. **Document release process** for team members

---

**Need help?** Check the [troubleshooting section](#-troubleshooting) or open an issue on GitHub.
