"""
Phase 13 Step 1: Shared Intelligence Layer - Centralized Storage
Provides unified path management for feedback logs and configuration across CLI and VS Code extension.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Shared intelligence directory
INTEL_DIR = os.path.expanduser("~/.codecrusher/intel")

# Legacy paths for migration
LEGACY_CONFIG_PATH = os.path.expanduser("~/.codecrusher_config.json")
LEGACY_FEEDBACK_LOG = "feedback_log.json"

def ensure_intel_dir_exists() -> None:
    """
    Ensure the shared intelligence directory exists.
    Creates the directory structure if it doesn't exist.
    """
    try:
        Path(INTEL_DIR).mkdir(parents=True, exist_ok=True)
        logging.debug(f"Intelligence directory ensured: {INTEL_DIR}")
    except Exception as e:
        logging.error(f"Failed to create intelligence directory {INTEL_DIR}: {e}")
        raise


def get_feedback_log_path() -> str:
    """
    Get the path to the shared feedback log file.
    
    Returns:
        str: Full path to feedback_log.json in the intel directory
    """
    return os.path.join(INTEL_DIR, "feedback_log.json")


def get_config_path() -> str:
    """
    Get the path to the shared configuration file.
    
    Returns:
        str: Full path to config.json in the intel directory
    """
    return os.path.join(INTEL_DIR, "config.json")


def get_injection_registry_path() -> str:
    """
    Get the path to the shared injection registry file.
    
    Returns:
        str: Full path to injection_registry.json in the intel directory
    """
    return os.path.join(INTEL_DIR, "injection_registry.json")


def get_quality_scores_path() -> str:
    """
    Get the path to the shared quality scores file.
    
    Returns:
        str: Full path to quality_scores.json in the intel directory
    """
    return os.path.join(INTEL_DIR, "quality_scores.json")


def get_shared_logs_path() -> str:
    """
    Get the path to the shared logs database.
    
    Returns:
        str: Full path to logs.db in the intel directory
    """
    return os.path.join(INTEL_DIR, "logs.db")


def migrate_legacy_files() -> Dict[str, bool]:
    """
    Migrate legacy configuration and feedback files to the shared intel directory.
    
    Returns:
        Dict[str, bool]: Migration status for each file type
    """
    migration_results = {
        "config": False,
        "feedback_log": False,
        "logs_db": False
    }
    
    try:
        ensure_intel_dir_exists()
        
        # Migrate legacy config file
        if os.path.exists(LEGACY_CONFIG_PATH):
            new_config_path = get_config_path()
            if not os.path.exists(new_config_path):
                try:
                    import shutil
                    shutil.copy2(LEGACY_CONFIG_PATH, new_config_path)
                    migration_results["config"] = True
                    logging.info(f"Migrated config: {LEGACY_CONFIG_PATH} → {new_config_path}")
                except Exception as e:
                    logging.warning(f"Failed to migrate config file: {e}")
        
        # Migrate legacy feedback log
        if os.path.exists(LEGACY_FEEDBACK_LOG):
            new_feedback_path = get_feedback_log_path()
            if not os.path.exists(new_feedback_path):
                try:
                    import shutil
                    shutil.copy2(LEGACY_FEEDBACK_LOG, new_feedback_path)
                    migration_results["feedback_log"] = True
                    logging.info(f"Migrated feedback log: {LEGACY_FEEDBACK_LOG} → {new_feedback_path}")
                except Exception as e:
                    logging.warning(f"Failed to migrate feedback log: {e}")
        
        # Migrate logs.db if it exists in current directory
        legacy_logs_db = "logs.db"
        if os.path.exists(legacy_logs_db):
            new_logs_path = get_shared_logs_path()
            if not os.path.exists(new_logs_path):
                try:
                    import shutil
                    shutil.copy2(legacy_logs_db, new_logs_path)
                    migration_results["logs_db"] = True
                    logging.info(f"Migrated logs database: {legacy_logs_db} → {new_logs_path}")
                except Exception as e:
                    logging.warning(f"Failed to migrate logs database: {e}")
        
    except Exception as e:
        logging.error(f"Migration process failed: {e}")
    
    return migration_results


def get_intel_summary() -> Dict[str, Any]:
    """
    Get a summary of the shared intelligence storage status.
    
    Returns:
        Dict[str, Any]: Summary of intelligence files and their status
    """
    summary = {
        "intel_dir": INTEL_DIR,
        "intel_dir_exists": os.path.exists(INTEL_DIR),
        "files": {}
    }
    
    # Check each intelligence file
    intel_files = {
        "config": get_config_path(),
        "feedback_log": get_feedback_log_path(),
        "injection_registry": get_injection_registry_path(),
        "quality_scores": get_quality_scores_path(),
        "logs_db": get_shared_logs_path()
    }
    
    for file_type, file_path in intel_files.items():
        file_info = {
            "path": file_path,
            "exists": os.path.exists(file_path),
            "size": 0,
            "readable": False
        }
        
        if file_info["exists"]:
            try:
                file_info["size"] = os.path.getsize(file_path)
                file_info["readable"] = os.access(file_path, os.R_OK)
            except Exception as e:
                file_info["error"] = str(e)
        
        summary["files"][file_type] = file_info
    
    return summary


def initialize_shared_intelligence() -> bool:
    """
    Initialize the shared intelligence system.
    Creates directory, migrates legacy files, and ensures basic structure.
    
    Returns:
        bool: True if initialization was successful
    """
    try:
        # Ensure directory exists
        ensure_intel_dir_exists()
        
        # Migrate legacy files
        migration_results = migrate_legacy_files()
        
        # Log migration results
        migrated_files = [k for k, v in migration_results.items() if v]
        if migrated_files:
            logging.info(f"Successfully migrated: {', '.join(migrated_files)}")
        
        # Create empty files if they don't exist
        intel_files = [
            get_feedback_log_path(),
            get_injection_registry_path(),
            get_quality_scores_path()
        ]
        
        for file_path in intel_files:
            if not os.path.exists(file_path):
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump([], f, indent=2)
                    logging.debug(f"Created empty intelligence file: {file_path}")
                except Exception as e:
                    logging.warning(f"Failed to create {file_path}: {e}")
        
        # Verify initialization
        summary = get_intel_summary()
        logging.info(f"Shared intelligence initialized: {summary['intel_dir']}")
        
        return True
        
    except Exception as e:
        logging.error(f"Failed to initialize shared intelligence: {e}")
        return False


def get_legacy_paths() -> Dict[str, str]:
    """
    Get paths to legacy files for reference.
    
    Returns:
        Dict[str, str]: Mapping of legacy file types to their paths
    """
    return {
        "config": LEGACY_CONFIG_PATH,
        "feedback_log": LEGACY_FEEDBACK_LOG,
        "logs_db": "logs.db"
    }


def cleanup_legacy_files(confirm: bool = False) -> Dict[str, bool]:
    """
    Clean up legacy files after successful migration.
    
    Args:
        confirm (bool): Must be True to actually delete files
        
    Returns:
        Dict[str, bool]: Cleanup status for each file type
    """
    cleanup_results = {
        "config": False,
        "feedback_log": False,
        "logs_db": False
    }
    
    if not confirm:
        logging.warning("Cleanup not confirmed. Use confirm=True to actually delete legacy files.")
        return cleanup_results
    
    try:
        # Only cleanup if new files exist and are readable
        summary = get_intel_summary()
        
        # Cleanup legacy config
        if (os.path.exists(LEGACY_CONFIG_PATH) and 
            summary["files"]["config"]["exists"] and 
            summary["files"]["config"]["readable"]):
            try:
                os.remove(LEGACY_CONFIG_PATH)
                cleanup_results["config"] = True
                logging.info(f"Cleaned up legacy config: {LEGACY_CONFIG_PATH}")
            except Exception as e:
                logging.warning(f"Failed to cleanup legacy config: {e}")
        
        # Cleanup legacy feedback log
        if (os.path.exists(LEGACY_FEEDBACK_LOG) and 
            summary["files"]["feedback_log"]["exists"] and 
            summary["files"]["feedback_log"]["readable"]):
            try:
                os.remove(LEGACY_FEEDBACK_LOG)
                cleanup_results["feedback_log"] = True
                logging.info(f"Cleaned up legacy feedback log: {LEGACY_FEEDBACK_LOG}")
            except Exception as e:
                logging.warning(f"Failed to cleanup legacy feedback log: {e}")
        
        # Cleanup legacy logs.db
        legacy_logs_db = "logs.db"
        if (os.path.exists(legacy_logs_db) and 
            summary["files"]["logs_db"]["exists"] and 
            summary["files"]["logs_db"]["readable"]):
            try:
                os.remove(legacy_logs_db)
                cleanup_results["logs_db"] = True
                logging.info(f"Cleaned up legacy logs database: {legacy_logs_db}")
            except Exception as e:
                logging.warning(f"Failed to cleanup legacy logs database: {e}")
        
    except Exception as e:
        logging.error(f"Cleanup process failed: {e}")
    
    return cleanup_results


# Initialize on import
try:
    initialize_shared_intelligence()
except Exception as e:
    logging.warning(f"Failed to initialize shared intelligence on import: {e}")


# Export key functions and constants
__all__ = [
    'INTEL_DIR',
    'ensure_intel_dir_exists',
    'get_feedback_log_path',
    'get_config_path',
    'get_injection_registry_path',
    'get_quality_scores_path',
    'get_shared_logs_path',
    'migrate_legacy_files',
    'get_intel_summary',
    'initialize_shared_intelligence',
    'get_legacy_paths',
    'cleanup_legacy_files'
]
