// API Configuration with environment variable support and fallbacks
const getBaseUrl = (): string => {
  // In development, use Vite proxy (window.location.origin = http://127.0.0.1:3000)
  // In production, use explicit backend URL
  if (import.meta.env.DEV && typeof window !== 'undefined') {
    return window.location.origin; // Use Vite proxy: http://127.0.0.1:3000
  }

  // Priority order for production: VITE_SERVER_URL > VITE_API_URL > 127.0.0.1:8001
  return import.meta.env.VITE_SERVER_URL ||
         import.meta.env.VITE_API_URL ||
         'http://127.0.0.1:8001';
};

const getWebSocketUrl = (): string => {
  // Use explicit WS URL if provided, otherwise derive from base URL
  if (import.meta.env.VITE_WS_URL) {
    return import.meta.env.VITE_WS_URL;
  }

  const baseUrl = getBaseUrl();
  return baseUrl.replace(/^http/, 'ws') + '/ws/logs';
};

export const API_CONFIG = {
  // Backend API base URL - supports environment variables
  BASE_URL: getBaseUrl(),

  // WebSocket URL for real-time logs
  WS_URL: getWebSocketUrl(),

  // Timeout configurations
  TIMEOUT: {
    API_REQUEST: 30000, // 30 seconds
    WEBSOCKET_CONNECT: 10000, // 10 seconds
    HEALTH_CHECK: 5000 // 5 seconds
  },

  // Retry configurations
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY_MS: 1000,
    WEBSOCKET_MAX_RETRIES: 5
  }
};

// API endpoints
export const API_ENDPOINTS = {
  HEALTH: '/health',
  INJECT: '/inject',
  MODELS: '/models',
  EXTENSIONS: '/extensions',
  VALIDATE: '/validate',
  RUN_INJECTION: '/run-injection'
};

// Helper function to build full API URLs
export const buildApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
};

// Helper function to build WebSocket URLs
export const buildWsUrl = (path: string = '/logs'): string => {
  const baseWsUrl = API_CONFIG.BASE_URL.replace('http://', 'ws://').replace('https://', 'wss://');
  return `${baseWsUrl}/ws${path}`;
};

// API client with error handling and retries
export class ApiClient {
  private static async fetchWithRetry(
    url: string,
    options: RequestInit = {},
    retries: number = API_CONFIG.RETRY.MAX_ATTEMPTS
  ): Promise<Response> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.TIMEOUT.API_REQUEST);

      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      console.error(`API request failed: ${url}`, error);

      if (retries > 1) {
        console.log(`Retrying API request... (${retries - 1} attempts left)`);
        await new Promise(resolve => setTimeout(resolve, API_CONFIG.RETRY.DELAY_MS));
        return this.fetchWithRetry(url, options, retries - 1);
      }

      throw error;
    }
  }

  static async get(endpoint: string): Promise<any> {
    const response = await this.fetchWithRetry(buildApiUrl(endpoint));
    return response.json();
  }

  static async post(endpoint: string, data: any): Promise<any> {
    const response = await this.fetchWithRetry(buildApiUrl(endpoint), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }

  static async postForm(endpoint: string, data: FormData | URLSearchParams): Promise<any> {
    const response = await this.fetchWithRetry(buildApiUrl(endpoint), {
      method: 'POST',
      body: data
    });
    return response.json();
  }
}

// Connection test function
export const testConnection = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(buildApiUrl(API_ENDPOINTS.HEALTH), {
      method: 'GET',
      signal: AbortSignal.timeout(API_CONFIG.TIMEOUT.HEALTH_CHECK)
    });

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `Connected to backend at ${API_CONFIG.BASE_URL}`
      };
    } else {
      return {
        success: false,
        message: `Backend responded with ${response.status}: ${response.statusText}`
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to connect to backend at ${API_CONFIG.BASE_URL}: ${error}`
    };
  }
};

// Log configuration for debugging
console.log('🔧 API Configuration:', {
  BASE_URL: API_CONFIG.BASE_URL,
  WS_URL: API_CONFIG.WS_URL,
  ENV_VARS: {
    VITE_API_URL: import.meta.env.VITE_API_URL,
    VITE_SERVER_URL: import.meta.env.VITE_SERVER_URL,
    VITE_WS_URL: import.meta.env.VITE_WS_URL
  }
});
