# CodeCrusher GitHub Releases Guide

This guide explains how to use the automated GitHub release system to create releases with CLI binary distributions.

## 🚀 Quick Start

### Prerequisites

1. **GitHub Personal Access Token:**
   ```bash
   # Get token from: https://github.com/settings/tokens
   export GITHUB_TOKEN=ghp_YourRealTokenHere
   ```

2. **Required token scopes:**
   - `repo` (for private repositories)
   - `public_repo` (for public repositories)

### Basic Usage

```bash
# Create release with auto-generated CLI binary
python release_to_github.py

# Upload specific file
python release_to_github.py --file dist/codecrusher-v1.0.0.zip

# Custom description
python release_to_github.py --desc "🔥 Auto-tune, fallback control, and dashboard sync."

# Test without making changes
python release_to_github.py --dry-run
```

## 📦 CLI Binary Creation

The system automatically creates a CLI binary archive containing:

### 📁 Included Files
- `codecrusher_cli.py` - Main CLI script
- `codecrusher/` - Core Python modules
- `setup.py` - Package configuration
- `README.md` - Documentation
- `LICENSE` - License file
- `requirements.txt` - Dependencies
- `install.sh` - Unix installation script
- `install.bat` - Windows installation script

### 🔧 Installation Scripts

**Unix/Linux/Mac (`install.sh`):**
```bash
#!/bin/bash
echo "🚀 Installing CodeCrusher CLI..."

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

# Install dependencies
pip3 install click rich requests groq pydantic sqlalchemy aiosqlite python-dotenv pyyaml jinja2

# Make CLI executable
chmod +x codecrusher_cli.py

echo "✅ CodeCrusher CLI installed successfully!"
```

**Windows (`install.bat`):**
```cmd
@echo off
echo 🚀 Installing CodeCrusher CLI...

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is required but not installed
    exit /b 1
)

pip install click rich requests groq pydantic sqlalchemy aiosqlite python-dotenv pyyaml jinja2
echo ✅ CodeCrusher CLI installed successfully!
```

## 🔧 Command Options

### Basic Options

| Option | Description | Example |
|--------|-------------|---------|
| `--file` | Path to asset file to upload | `--file dist/codecrusher-v1.0.0.zip` |
| `--desc` | Custom release description | `--desc "🔥 New features!"` |
| `--tag` | Override auto-detected version tag | `--tag v1.0.1` |
| `--dry-run` | Show what would happen without changes | `--dry-run` |

### Advanced Usage

```bash
# Create release with specific file and description
python release_to_github.py \
  --file dist/codecrusher-v1.0.0.zip \
  --desc "🔥 Auto-tune, fallback control, and dashboard sync." \
  --tag v1.0.0

# Test release creation
python release_to_github.py --tag v1.0.1 --dry-run
```

## 🔄 Integrated Publishing Workflow

### Enhanced publish.py Integration

The main publishing script now supports GitHub releases:

```bash
# Publish to PyPI and create GitHub release
python publish.py --patch --github

# Full release workflow with tagging
python publish.py --minor --tag --github

# Test on TestPyPI first, then GitHub
python publish.py --patch --test --github
```

### Complete Release Workflow

```bash
# 1. Test release
python publish.py --patch --test --dry-run

# 2. Publish to TestPyPI
python publish.py --patch --test

# 3. Verify TestPyPI installation
pip install --index-url https://test.pypi.org/simple/ codecrusher

# 4. Full production release
python publish.py --patch --tag --github
```

## 🤖 GitHub Actions Automation

### Automated Release Workflow

The repository includes a GitHub Actions workflow (`.github/workflows/release.yml`) that:

1. **Triggers on version tags** (`v*`)
2. **Builds and validates** the package
3. **Publishes to PyPI** automatically
4. **Creates GitHub release** with CLI binary
5. **Provides release summary** in workflow output

### Manual Workflow Dispatch

You can also trigger releases manually from GitHub:

1. Go to **Actions** tab in your repository
2. Select **🚀 CodeCrusher Release** workflow
3. Click **Run workflow**
4. Choose options:
   - Version bump type (patch/minor/major)
   - TestPyPI vs production PyPI
   - Create GitHub release (default: true)

### Workflow Configuration

Required secrets in repository settings:

```yaml
# Repository Secrets
PYPI_API_TOKEN: pypi-your-production-token
TEST_PYPI_API_TOKEN: pypi-your-test-token
GITHUB_TOKEN: # Automatically provided by GitHub
```

## 📋 Release Process

### Automatic Process

1. **Version Detection**: Reads current version from `setup.py`
2. **Changelog Extraction**: Pulls release notes from `CHANGELOG.md`
3. **CLI Binary Creation**: Packages essential files into zip archive
4. **GitHub Release**: Creates release with auto-generated description
5. **Asset Upload**: Attaches CLI binary as downloadable asset

### Manual Process

If you need to create releases manually:

```bash
# 1. Set environment variable
export GITHUB_TOKEN=ghp_YourTokenHere

# 2. Create release
python release_to_github.py --desc "Manual release with custom description"

# 3. Verify release
# Check: https://github.com/your-org/codecrusher/releases
```

## 🔍 Troubleshooting

### Common Issues

**1. Authentication Failed**
```
❌ GitHub authentication failed: 401
```
- Check your `GITHUB_TOKEN` environment variable
- Verify token has correct scopes (`repo` or `public_repo`)
- Ensure token hasn't expired

**2. Release Already Exists**
```
Failed to create release: 422 - Validation Failed
```
- The script automatically updates existing releases
- Check if tag already exists: `git tag -l`
- Delete tag if needed: `git tag -d v1.0.0`

**3. File Upload Failed**
```
Failed to upload asset: 422
```
- Check file exists and is readable
- Verify file size (GitHub has limits)
- Ensure unique filename

**4. Repository Not Found**
```
Failed to create release: 404
```
- Check repository owner/name in script
- Verify token has access to repository
- Ensure repository exists and is accessible

### Debug Mode

Use `--dry-run` to test without making changes:

```bash
python release_to_github.py --dry-run --tag v1.0.1
```

This shows:
- Authentication status
- Version and tag information
- Files that would be included
- Release description preview
- No actual release is created

## 🔒 Security Best Practices

1. **Token Security**
   - Never commit tokens to version control
   - Use environment variables or secure storage
   - Rotate tokens regularly
   - Limit token scopes to minimum required

2. **Repository Access**
   - Use fine-grained personal access tokens when available
   - Limit token access to specific repositories
   - Monitor token usage in GitHub settings

3. **Automation Security**
   - Use repository secrets for GitHub Actions
   - Enable branch protection rules
   - Require reviews for release-related changes

## 📈 Release Analytics

### Tracking Downloads

GitHub provides download statistics for release assets:

1. Go to repository **Insights** tab
2. Select **Traffic** section
3. View **Popular content** for download stats

### Release Metrics

Monitor release success with:

- **Download counts** for CLI binaries
- **PyPI download statistics** via pypistats
- **GitHub release views** and engagement
- **Issue reports** related to specific versions

## 🤝 Team Collaboration

### Release Responsibilities

1. **Release Manager**: Creates and manages releases
2. **QA Team**: Tests pre-release versions
3. **Documentation Team**: Updates release notes
4. **DevOps Team**: Monitors automation and deployment

### Release Checklist

Before creating a release:

- [ ] All tests pass
- [ ] Documentation is updated
- [ ] CHANGELOG.md has release notes
- [ ] Version number is appropriate
- [ ] GitHub token is configured
- [ ] PyPI credentials are set up

After creating a release:

- [ ] Verify GitHub release is created
- [ ] Test CLI binary download and installation
- [ ] Confirm PyPI package is available
- [ ] Update any dependent projects
- [ ] Announce release to team/community

---

**Need help?** Check the [troubleshooting section](#-troubleshooting) or open an issue on GitHub.
