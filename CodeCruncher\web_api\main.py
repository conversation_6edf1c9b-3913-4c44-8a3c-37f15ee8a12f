"""
CodeCrusher Web API - FastAPI Backend with CLI Integration

This module provides a web API interface for CodeCrusher's AI-powered code injection
capabilities, integrating with the polished CLI for enterprise-grade functionality.
"""

from fastapi import FastAPI, File, Form, UploadFile, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import List, Optional, Dict, Any
import subprocess
import os
import json
import asyncio
import tempfile
import shutil
from pathlib import Path
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="CodeCrusher Web API",
    description="Enterprise-grade AI-powered code injection via web interface",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS support for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class InjectionRequest(BaseModel):
    source: str = Field(..., description="Path to source file or folder")
    prompt_text: str = Field(..., description="Prompt text for AI injection")
    recursive: bool = Field(False, description="Scan subfolders recursively")
    ext: Optional[str] = Field("py", description="File extensions (comma-separated)")
    tag: Optional[str] = Field("default", description="Tag for injection version")
    apply: bool = Field(False, description="Apply changes (vs preview mode)")
    preview: bool = Field(True, description="Preview mode (mutually exclusive with apply)")
    auto_model_routing: bool = Field(True, description="Use best model selection")
    model: Optional[str] = Field(None, description="Specific model to use")
    refresh_cache: bool = Field(False, description="Bypass cache and re-inject")
    force: bool = Field(False, description="Inject even without AI_INJECT tags")
    summary: bool = Field(True, description="Include detailed summary")

class InjectionResponse(BaseModel):
    success: bool
    message: str
    output: Optional[str] = None
    error: Optional[str] = None
    statistics: Optional[Dict[str, Any]] = None
    execution_time: Optional[float] = None
    timestamp: str

class FileUploadRequest(BaseModel):
    prompt_text: str
    apply: bool = False
    auto_model_routing: bool = True
    tag: Optional[str] = "upload"

class HealthResponse(BaseModel):
    status: str
    version: str
    timestamp: str
    codecrusher_available: bool

# Global variables for tracking
active_jobs = {}
job_counter = 0

@app.get("/", response_model=HealthResponse)
async def read_root():
    """Health check endpoint."""
    # Check if codecrusher CLI is available
    codecrusher_available = await check_codecrusher_availability()

    return HealthResponse(
        status="running",
        version="1.0.0",
        timestamp=datetime.now().isoformat(),
        codecrusher_available=codecrusher_available
    )

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Detailed health check endpoint."""
    codecrusher_available = await check_codecrusher_availability()

    return HealthResponse(
        status="healthy" if codecrusher_available else "degraded",
        version="1.0.0",
        timestamp=datetime.now().isoformat(),
        codecrusher_available=codecrusher_available
    )

async def check_codecrusher_availability() -> bool:
    """Check if codecrusher CLI is available."""
    try:
        process = await asyncio.create_subprocess_exec(
            "codecrusher", "--help",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        await process.communicate()
        return process.returncode == 0
    except Exception:
        return False

@app.post("/inject", response_model=InjectionResponse)
async def inject_code(req: InjectionRequest):
    """
    Main code injection endpoint.

    Integrates with the polished CodeCrusher CLI to perform AI-powered code injection
    with comprehensive configuration options and detailed response.
    """
    start_time = datetime.now()

    try:
        # Validate request
        if req.apply and req.preview:
            raise HTTPException(
                status_code=400,
                detail="Cannot specify both --apply and --preview modes"
            )

        # Build codecrusher command
        cmd = await build_codecrusher_command(req)

        logger.info(f"Executing codecrusher command: {' '.join(cmd)}")

        # Execute codecrusher CLI
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=os.getcwd()
        )

        stdout, stderr = await process.communicate()

        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()

        # Parse output and extract statistics
        output_text = stdout.decode('utf-8') if stdout else ""
        error_text = stderr.decode('utf-8') if stderr else ""

        statistics = extract_statistics_from_output(output_text)

        # Determine success based on return code and output
        success = process.returncode == 0

        if success:
            message = "Code injection completed successfully"
            if statistics and statistics.get('failed_files', 0) > 0:
                message += f" with {statistics['failed_files']} failed files"
        else:
            message = "Code injection failed"

        return InjectionResponse(
            success=success,
            message=message,
            output=output_text,
            error=error_text if not success else None,
            statistics=statistics,
            execution_time=execution_time,
            timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during code injection: {str(e)}")
        execution_time = (datetime.now() - start_time).total_seconds()

        return InjectionResponse(
            success=False,
            message=f"Internal error: {str(e)}",
            error=str(e),
            execution_time=execution_time,
            timestamp=datetime.now().isoformat()
        )

async def build_codecrusher_command(req: InjectionRequest) -> List[str]:
    """Build the codecrusher CLI command from the request."""
    cmd = ["codecrusher", "--source", req.source, "--prompt-text", req.prompt_text]

    # Add mode (apply or preview)
    if req.apply:
        cmd.append("--apply")
    else:
        cmd.append("--preview")

    # Add optional flags
    if req.recursive:
        cmd.append("--recursive")

    if req.ext:
        cmd.extend(["--ext", req.ext])

    if req.tag:
        cmd.extend(["--tag", req.tag])

    if req.auto_model_routing:
        cmd.append("--auto-model-routing")
    elif req.model:
        cmd.extend(["--model", req.model])

    if req.refresh_cache:
        cmd.append("--refresh-cache")

    if req.force:
        cmd.append("--force")

    if req.summary:
        cmd.append("--summary")

    return cmd

def extract_statistics_from_output(output: str) -> Optional[Dict[str, Any]]:
    """Extract statistics from codecrusher CLI output."""
    try:
        statistics = {}

        # Look for summary line: "✅ Summary: X injected, Y cached, Z failed"
        if "Summary:" in output:
            lines = output.split('\n')
            for line in lines:
                if "Summary:" in line:
                    # Parse the summary line
                    parts = line.split("Summary:")[1].strip()
                    if "injected" in parts:
                        injected = int(parts.split("injected")[0].strip().split()[-1])
                        statistics['injected_files'] = injected
                    if "cached" in parts:
                        cached = int(parts.split("cached")[0].strip().split()[-1])
                        statistics['cached_files'] = cached
                    if "failed" in parts:
                        failed = int(parts.split("failed")[0].strip().split()[-1])
                        statistics['failed_files'] = failed
                    break

        # Look for other statistics in the output
        if "Total Files:" in output:
            lines = output.split('\n')
            for line in lines:
                if "Total Files:" in line:
                    total = int(line.split("Total Files:")[1].strip().split()[0])
                    statistics['total_files'] = total
                elif "Success Rate:" in line:
                    rate = float(line.split("Success Rate:")[1].strip().split('%')[0])
                    statistics['success_rate'] = rate
                elif "Model Used:" in line:
                    model = line.split("Model Used:")[1].strip()
                    statistics['model_used'] = model

        return statistics if statistics else None

    except Exception as e:
        logger.warning(f"Failed to extract statistics: {str(e)}")
        return None

@app.post("/upload-inject")
async def upload_and_inject(
    file: UploadFile = File(...),
    prompt_text: str = Form(...),
    apply: bool = Form(False),
    auto_model_routing: bool = Form(True),
    tag: str = Form("upload"),
    ext: str = Form("py")
):
    """
    Upload a file and perform code injection.

    This endpoint allows users to upload a file, temporarily save it,
    perform code injection, and return the results.
    """
    temp_dir = None
    try:
        # Create temporary directory
        temp_dir = Path(tempfile.mkdtemp())

        # Save uploaded file
        file_path = temp_dir / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Create injection request
        req = InjectionRequest(
            source=str(file_path),
            prompt_text=prompt_text,
            apply=apply,
            preview=not apply,
            auto_model_routing=auto_model_routing,
            tag=tag,
            ext=ext,
            summary=True
        )

        # Perform injection
        result = await inject_code(req)

        # If apply mode, read the modified file content
        if apply and result.success:
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    modified_content = f.read()
                result.output = modified_content
            except Exception as e:
                logger.warning(f"Failed to read modified file: {str(e)}")

        return result

    except Exception as e:
        logger.error(f"Error in upload-inject: {str(e)}")
        return InjectionResponse(
            success=False,
            message=f"Upload and injection failed: {str(e)}",
            error=str(e),
            timestamp=datetime.now().isoformat()
        )
    finally:
        # Clean up temporary directory
        if temp_dir and temp_dir.exists():
            try:
                shutil.rmtree(temp_dir)
            except Exception as e:
                logger.warning(f"Failed to clean up temp directory: {str(e)}")

@app.get("/models")
async def get_available_models():
    """Get list of available AI models."""
    # This would typically query the actual model providers
    # For now, return a static list based on CodeCrusher's capabilities
    return {
        "models": [
            {
                "id": "auto",
                "name": "Auto Selection",
                "description": "Automatically select the best model for the task",
                "provider": "auto"
            },
            {
                "id": "llama3-70b-8192",
                "name": "Llama 3 70B",
                "description": "Large language model optimized for code generation",
                "provider": "groq"
            },
            {
                "id": "mixtral-8x7b-32768",
                "name": "Mixtral 8x7B",
                "description": "Mixture of experts model for complex code tasks",
                "provider": "groq"
            },
            {
                "id": "mistral-fallback",
                "name": "Mistral Fallback",
                "description": "Reliable fallback model for consistent results",
                "provider": "mistral"
            }
        ],
        "default": "auto",
        "auto_routing_available": True
    }

@app.get("/extensions")
async def get_supported_extensions():
    """Get list of supported file extensions."""
    return {
        "extensions": [
            {"ext": "py", "name": "Python", "description": "Python source files"},
            {"ext": "js", "name": "JavaScript", "description": "JavaScript source files"},
            {"ext": "ts", "name": "TypeScript", "description": "TypeScript source files"},
            {"ext": "java", "name": "Java", "description": "Java source files"},
            {"ext": "c", "name": "C", "description": "C source files"},
            {"ext": "cpp", "name": "C++", "description": "C++ source files"},
            {"ext": "cs", "name": "C#", "description": "C# source files"},
            {"ext": "go", "name": "Go", "description": "Go source files"},
            {"ext": "rs", "name": "Rust", "description": "Rust source files"},
            {"ext": "php", "name": "PHP", "description": "PHP source files"}
        ],
        "default": "py",
        "multiple_supported": True
    }

@app.post("/validate-source")
async def validate_source_path(source: str = Form(...)):
    """Validate if a source path exists and is accessible."""
    try:
        path = Path(source)

        if not path.exists():
            return {
                "valid": False,
                "error": "Path does not exist",
                "type": None,
                "files_count": 0
            }

        if path.is_file():
            return {
                "valid": True,
                "type": "file",
                "files_count": 1,
                "size": path.stat().st_size,
                "extension": path.suffix[1:] if path.suffix else None
            }
        elif path.is_dir():
            # Count files in directory
            files_count = len(list(path.rglob("*"))) if path.is_dir() else 0
            return {
                "valid": True,
                "type": "directory",
                "files_count": files_count
            }
        else:
            return {
                "valid": False,
                "error": "Path is neither a file nor a directory",
                "type": None,
                "files_count": 0
            }

    except Exception as e:
        return {
            "valid": False,
            "error": str(e),
            "type": None,
            "files_count": 0
        }

@app.get("/jobs/{job_id}")
async def get_job_status(job_id: str):
    """Get status of a background job (for future async processing)."""
    if job_id in active_jobs:
        return active_jobs[job_id]
    else:
        raise HTTPException(status_code=404, detail="Job not found")

@app.get("/version")
async def get_version():
    """Get API and CodeCrusher version information."""
    try:
        # Try to get codecrusher version
        process = await asyncio.create_subprocess_exec(
            "codecrusher", "--help",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()

        codecrusher_available = process.returncode == 0
        codecrusher_version = "unknown"

        if codecrusher_available:
            # Extract version from help output if available
            output = stdout.decode('utf-8')
            if "CodeCrusher" in output:
                codecrusher_version = "1.0.0"  # Default version

        return {
            "api_version": "1.0.0",
            "codecrusher_version": codecrusher_version,
            "codecrusher_available": codecrusher_available,
            "features": [
                "parallel_injection",
                "cache_aware_processing",
                "tag_detection",
                "multi_model_routing",
                "file_upload",
                "recursive_scanning"
            ]
        }

    except Exception as e:
        return {
            "api_version": "1.0.0",
            "codecrusher_version": "unknown",
            "codecrusher_available": False,
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
