#!/usr/bin/env python3
"""
CodeCrusher GitHub Release Automation
Creates GitHub releases with CLI binary uploads
"""

import os
import sys
import re
import json
import argparse
import requests
import zipfile
import tarfile
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any

class GitHubReleaseManager:
    """Professional GitHub release automation for CodeCrusher."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.setup_py = self.project_root / "setup.py"
        self.changelog = self.project_root / "CHANGELOG.md"
        
        # GitHub configuration
        self.github_token = os.getenv('GITHUB_TOKEN')
        self.repo_owner = "Codegx-Technology"  # Update if different
        self.repo_name = "CodeCruncher"
        self.api_base = "https://api.github.com"
        
        # Colors for console output
        self.GREEN = '\033[92m'
        self.YELLOW = '\033[93m'
        self.RED = '\033[91m'
        self.BLUE = '\033[94m'
        self.BOLD = '\033[1m'
        self.END = '\033[0m'
    
    def log(self, message: str, level: str = "INFO"):
        """Enhanced logging with colors and timestamps."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if level == "SUCCESS":
            color = self.GREEN
            icon = "✅"
        elif level == "WARNING":
            color = self.YELLOW
            icon = "⚠️"
        elif level == "ERROR":
            color = self.RED
            icon = "❌"
        elif level == "INFO":
            color = self.BLUE
            icon = "ℹ️"
        else:
            color = ""
            icon = "📝"
        
        print(f"{color}{icon} [{timestamp}] {message}{self.END}")
    
    def validate_github_token(self) -> bool:
        """Validate GitHub token and permissions."""
        if not self.github_token:
            self.log("GITHUB_TOKEN environment variable not set", "ERROR")
            self.log("Get a token from: https://github.com/settings/tokens", "INFO")
            self.log("Required scopes: repo (for private repos) or public_repo (for public repos)", "INFO")
            return False
        
        # Test token validity
        headers = {
            'Authorization': f'token {self.github_token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        try:
            response = requests.get(f"{self.api_base}/user", headers=headers)
            if response.status_code == 200:
                user_data = response.json()
                self.log(f"Authenticated as: {user_data.get('login', 'Unknown')}", "SUCCESS")
                return True
            else:
                self.log(f"GitHub authentication failed: {response.status_code}", "ERROR")
                return False
        except requests.RequestException as e:
            self.log(f"Failed to validate GitHub token: {e}", "ERROR")
            return False
    
    def get_current_version(self) -> str:
        """Extract current version from setup.py."""
        if not self.setup_py.exists():
            raise FileNotFoundError("setup.py not found")
        
        content = self.setup_py.read_text(encoding='utf-8')
        version_match = re.search(r'version\s*=\s*["\']([^"\']+)["\']', content)
        
        if not version_match:
            raise ValueError("Version not found in setup.py")
        
        return version_match.group(1)
    
    def get_changelog_for_version(self, version: str) -> str:
        """Extract changelog content for specific version."""
        if not self.changelog.exists():
            return f"Release {version} - See commit history for details."
        
        content = self.changelog.read_text(encoding='utf-8')
        
        # Find version section
        version_pattern = rf"## \[{re.escape(version)}\].*?(?=## \[|\Z)"
        match = re.search(version_pattern, content, re.DOTALL)
        
        if match:
            # Clean up the changelog content
            changelog_content = match.group(0)
            # Remove the version header
            changelog_content = re.sub(rf"## \[{re.escape(version)}\][^\n]*\n", "", changelog_content)
            # Clean up extra whitespace
            changelog_content = changelog_content.strip()
            return changelog_content if changelog_content else f"Release {version}"
        
        return f"Release {version} - See commit history for details."
    
    def create_cli_binary(self, version: str) -> Path:
        """Create CLI binary archive for distribution."""
        self.log("Creating CLI binary archive...")
        
        # Create distribution directory
        dist_dir = self.project_root / "dist"
        dist_dir.mkdir(exist_ok=True)
        
        # Archive filename
        archive_name = f"codecrusher-v{version}-cli.zip"
        archive_path = dist_dir / archive_name
        
        # Files to include in CLI binary
        cli_files = [
            "codecrusher_cli.py",
            "codecrusher/",
            "setup.py",
            "README.md",
            "LICENSE",
            "requirements.txt"
        ]
        
        # Create zip archive
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_pattern in cli_files:
                file_path = self.project_root / file_pattern
                
                if file_path.is_file():
                    zipf.write(file_path, file_path.name)
                    self.log(f"Added file: {file_path.name}")
                elif file_path.is_dir():
                    for py_file in file_path.rglob("*.py"):
                        arcname = str(py_file.relative_to(self.project_root))
                        zipf.write(py_file, arcname)
                        self.log(f"Added file: {arcname}")
        
        # Add installation script
        install_script = """#!/bin/bash
# CodeCrusher CLI Installation Script

echo "🚀 Installing CodeCrusher CLI..."

# Check Python
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
pip3 install -r requirements.txt

# Make CLI executable
chmod +x codecrusher_cli.py

echo "✅ CodeCrusher CLI installed successfully!"
echo "Usage: python3 codecrusher_cli.py --help"
"""
        
        with zipfile.ZipFile(archive_path, 'a') as zipf:
            zipf.writestr("install.sh", install_script)
        
        self.log(f"Created CLI binary: {archive_path}", "SUCCESS")
        return archive_path
    
    def check_release_exists(self, tag: str) -> Optional[Dict[str, Any]]:
        """Check if release with tag already exists."""
        headers = {
            'Authorization': f'token {self.github_token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        url = f"{self.api_base}/repos/{self.repo_owner}/{self.repo_name}/releases/tags/{tag}"
        
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 404:
                return None
            else:
                self.log(f"Error checking release: {response.status_code}", "WARNING")
                return None
        except requests.RequestException as e:
            self.log(f"Failed to check existing release: {e}", "WARNING")
            return None
    
    def create_github_release(self, version: str, description: str, tag: str) -> Dict[str, Any]:
        """Create GitHub release."""
        headers = {
            'Authorization': f'token {self.github_token}',
            'Accept': 'application/vnd.github.v3+json',
            'Content-Type': 'application/json'
        }
        
        # Check if release exists
        existing_release = self.check_release_exists(tag)
        
        release_data = {
            'tag_name': tag,
            'target_commitish': 'main',
            'name': f"CodeCrusher {version}",
            'body': description,
            'draft': False,
            'prerelease': False
        }
        
        if existing_release:
            # Update existing release
            url = f"{self.api_base}/repos/{self.repo_owner}/{self.repo_name}/releases/{existing_release['id']}"
            self.log(f"Updating existing release: {tag}", "INFO")
            response = requests.patch(url, headers=headers, json=release_data)
        else:
            # Create new release
            url = f"{self.api_base}/repos/{self.repo_owner}/{self.repo_name}/releases"
            self.log(f"Creating new release: {tag}", "INFO")
            response = requests.post(url, headers=headers, json=release_data)
        
        if response.status_code in [200, 201]:
            release_info = response.json()
            self.log(f"Release created/updated successfully: {release_info['html_url']}", "SUCCESS")
            return release_info
        else:
            error_msg = f"Failed to create/update release: {response.status_code}"
            if response.text:
                error_msg += f" - {response.text}"
            raise RuntimeError(error_msg)
    
    def upload_release_asset(self, release_info: Dict[str, Any], file_path: Path) -> bool:
        """Upload file as release asset."""
        if not file_path.exists():
            self.log(f"Asset file not found: {file_path}", "ERROR")
            return False
        
        # Get upload URL
        upload_url = release_info['upload_url'].replace('{?name,label}', '')
        
        headers = {
            'Authorization': f'token {self.github_token}',
            'Content-Type': 'application/zip' if file_path.suffix == '.zip' else 'application/gzip'
        }
        
        params = {
            'name': file_path.name,
            'label': f"CodeCrusher CLI Binary - {file_path.name}"
        }
        
        self.log(f"Uploading asset: {file_path.name} ({file_path.stat().st_size} bytes)")
        
        try:
            with open(file_path, 'rb') as f:
                response = requests.post(upload_url, headers=headers, params=params, data=f)
            
            if response.status_code == 201:
                asset_info = response.json()
                self.log(f"Asset uploaded successfully: {asset_info['browser_download_url']}", "SUCCESS")
                return True
            else:
                self.log(f"Failed to upload asset: {response.status_code} - {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"Error uploading asset: {e}", "ERROR")
            return False
    
    def create_release(self, file_path: Optional[Path] = None, description: Optional[str] = None, 
                      tag: Optional[str] = None, dry_run: bool = False) -> bool:
        """Main release creation workflow."""
        try:
            self.log(f"{self.BOLD}🚀 CodeCrusher GitHub Release Manager{self.END}")
            
            # Validate GitHub token
            if not self.validate_github_token():
                return False
            
            # Get version
            version = self.get_current_version()
            release_tag = tag or f"v{version}"
            
            self.log(f"Creating release for version: {version}")
            self.log(f"Release tag: {release_tag}")
            
            # Generate description if not provided
            if not description:
                description = self.get_changelog_for_version(version)
            
            # Create CLI binary if no file provided
            if not file_path:
                file_path = self.create_cli_binary(version)
            
            if dry_run:
                self.log("DRY RUN - No changes will be made", "WARNING")
                self.log(f"Would create release: {release_tag}")
                self.log(f"Would upload asset: {file_path}")
                self.log(f"Description preview:\n{description}")
                return True
            
            # Create GitHub release
            release_info = self.create_github_release(version, description, release_tag)
            
            # Upload asset
            if file_path and file_path.exists():
                upload_success = self.upload_release_asset(release_info, file_path)
                if not upload_success:
                    self.log("Asset upload failed, but release was created", "WARNING")
            
            # Final success message
            self.log(f"{self.BOLD}🎉 GitHub release created successfully!{self.END}", "SUCCESS")
            self.log(f"Release URL: {release_info['html_url']}")
            self.log(f"Download CLI: {release_info['html_url'].replace('/tag/', '/download/')}/{file_path.name}")
            
            return True
            
        except Exception as e:
            self.log(f"Release creation failed: {str(e)}", "ERROR")
            return False

def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(
        description="CodeCrusher GitHub Release Manager",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python release_to_github.py                                    # Auto-create CLI binary and release
  python release_to_github.py --file dist/codecrusher-v1.0.0.zip # Upload specific file
  python release_to_github.py --desc "🔥 New features!"         # Custom description
  python release_to_github.py --tag v1.0.1 --dry-run           # Test without changes

Environment Variables:
  GITHUB_TOKEN    GitHub personal access token (required)
                  Get from: https://github.com/settings/tokens
                  Scopes: repo (private) or public_repo (public)
        """
    )
    
    parser.add_argument("--file", type=Path, help="Path to asset file to upload")
    parser.add_argument("--desc", help="Release description (auto-generated from CHANGELOG if not provided)")
    parser.add_argument("--tag", help="Override auto-detected version tag")
    parser.add_argument("--dry-run", action="store_true", help="Show what would happen without making changes")
    
    args = parser.parse_args()
    
    # Create release manager and run
    manager = GitHubReleaseManager()
    success = manager.create_release(
        file_path=args.file,
        description=args.desc,
        tag=args.tag,
        dry_run=args.dry_run
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
