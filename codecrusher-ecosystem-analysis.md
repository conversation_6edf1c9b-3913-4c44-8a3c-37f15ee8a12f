# 🚀 **CodeCrusher Ecosystem: Complete Workflow Analysis**

## 📋 **Table of Contents**
1. [System Architecture Overview](#architecture)
2. [Backend Ecosystem](#backend)
3. [Frontend Ecosystem](#frontend)
4. [Complete Workflow](#workflow)
5. [Real-time Communication](#realtime)
6. [Authentication & Teams](#auth)
7. [Data Flow Diagrams](#diagrams)

---

## 🏗️ **System Architecture Overview** {#architecture}

CodeCrusher is a **sophisticated AI-powered code injection platform** with multiple layers:

### **🎯 Core Components:**
- **CLI Tool** - Command-line interface for direct code injection
- **Backend API** - FastAPI server with WebSocket support
- **Frontend Dashboard** - React-based web interface
- **Authentication System** - JWT-based user management
- **Team Collaboration** - Multi-user workspace features
- **Intelligence Hub** - AI model performance analytics

### **🔄 Communication Layers:**
- **HTTP REST APIs** - Standard request/response
- **WebSocket Streams** - Real-time bidirectional communication
- **Database Layer** - SQLite for user/team data
- **File System** - Local code files and telemetry

---

## 🔧 **Backend Ecosystem** {#backend}

### **📁 Backend Structure:**

```
app/
├── backend_main.py          # Main FastAPI application
├── routes/
│   ├── inject.py           # Code injection endpoints
│   ├── auth_routes.py      # Authentication endpoints
│   ├── team_routes.py      # Team collaboration endpoints
│   └── dashboard_routes.py # Intelligence Hub endpoints
├── core_injector.py        # Core injection logic
├── ws_logs.py             # WebSocket logging system
├── database.py            # SQLite database management
├── auth.py                # JWT authentication
└── team_events.py         # Real-time team events
```

### **🚀 Backend Startup Process:**

```python
app = FastAPI(
    title="CodeCrusher Backend API",
    description="WebSocket + Injection API with Intelligence Hub integration",
    version="1.0.0"
)

# Register route modules
app.include_router(dashboard_router, tags=["Intelligence Hub"])
app.include_router(auth_router, tags=["authentication"])
app.include_router(team_router, tags=["teams"])
```

### **🔌 Core API Endpoints:**

#### **1. Health & Status**
- `GET /` - API root with endpoint information
- `GET /health` - Health check endpoint
- `GET /api/status` - System status

#### **2. Code Injection**
- `POST /inject` - Main code injection endpoint
- `WebSocket /ws/logs` - Real-time injection logs

#### **3. Authentication**
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/profile` - User profile

#### **4. Team Collaboration**
- `POST /teams` - Create team
- `POST /teams/{id}/invite` - Invite team members
- `WebSocket /team-activity/ws/{team_id}` - Team activity feed

#### **5. Intelligence Hub**
- `GET /api/intel/summary` - AI model analytics
- `WebSocket /ws/intelligence` - Real-time intelligence updates

---

## 🎨 **Frontend Ecosystem** {#frontend}

### **📁 Frontend Structure:**

```
frontend/src/
├── App.tsx                 # Main React application
├── components/
│   ├── EnterpriseDashboard.tsx    # Main dashboard
│   ├── Dashboard.tsx              # Alternative dashboard
│   ├── IntelligenceHubDashboard.tsx # AI analytics
│   ├── TeamWorkspace.tsx          # Team collaboration
│   └── StatusDashboard.tsx        # System monitoring
├── hooks/
│   ├── useWebSocket.ts            # WebSocket management
│   ├── useIntelligenceWebSocket.ts # Intelligence updates
│   └── useTeamPermissions.ts      # Team access control
├── contexts/
│   └── AuthContext.tsx            # Authentication state
└── config/
    └── api.ts                     # API configuration
```

### **🎯 Dashboard Components:**

#### **1. Enterprise Dashboard** (Main Interface)
- **Real-time injection monitoring**
- **Team activity feeds**
- **AI model performance metrics**
- **System health indicators**

#### **2. Intelligence Hub Dashboard**
- **AI model analytics**
- **Performance comparisons**
- **Usage statistics**
- **Quality metrics**

#### **3. Team Workspace**
- **Collaborative code injection**
- **Shared project management**
- **Team member activity**
- **Permission management**

---

## 🔄 **Complete Workflow** {#workflow}

### **🎯 User Journey: From Login to Code Injection**

#### **Step 1: Authentication Flow**

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant DB as Database

    U->>F: Enter credentials
    F->>B: POST /auth/login
    B->>DB: Verify user
    DB-->>B: User data
    B-->>F: JWT token
    F->>F: Store token
    F-->>U: Dashboard access
```

#### **Step 2: Dashboard Initialization**

```javascript
return (
  <AuthProvider>
    <ToastProvider>
      <Routes>
        <Route path="/" element={
          <EnterpriseDashboard
            viewMode={currentViewMode}
            healthStatus={healthStatus}
            wsConnected={wsConnected}
          />
        } />
      </Routes>
    </ToastProvider>
  </AuthProvider>
);
```

#### **Step 3: WebSocket Connection Establishment**

```javascript
export function useWebSocket(options: UseWebSocketOptions): UseWebSocketReturn {
  const connectWebSocket = useCallback(() => {
    try {
      const socket = new WebSocket(url);

      socket.onopen = () => {
        updateStatus('connected');
        setRetryCount(0);
      };

      socket.onmessage = handleMessage;
      socket.onerror = () => updateStatus('error');
    } catch (error) {
      updateStatus('error');
    }
  }, [url, handleMessage]);
}
```

#### **Step 4: Code Injection Process**

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant WS as WebSocket
    participant AI as AI Engine
    participant FS as File System

    U->>F: Configure injection (prompt, files, model)
    F->>B: POST /inject
    B->>AI: Process with AI model
    B->>WS: Broadcast progress updates
    WS-->>F: Real-time progress
    F-->>U: Live progress display
    AI-->>B: Generated code
    B->>FS: Apply changes to files
    B->>WS: Broadcast completion
    WS-->>F: Final results
    F-->>U: Show results
```

### **🔧 Detailed Injection Workflow:**

#### **Frontend Injection Request:**

```javascript
const runInjection = async () => {
  const payload = {
    prompt: prompt,
    file_path: sourceFolder,
    model: model,
    apply: apply,
    tags: tag ? [tag] : []
  };

  const response = await fetch('http://localhost:8001/inject', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
};
```

#### **Backend Injection Processing:**

```python
def inject_code(prompt: str, files: Union[str, List[str]], model: str = "auto",
               apply: bool = False, tags: Optional[List[str]] = None):
    """
    Core code injection function that can be used by both CLI and API.
    """
    result = {
        "success": False,
        "message": "",
        "logs": [],
        "stats": {}
    }

    # Add initial log
    result["logs"].append(f"🚀 Starting injection with model: {model}")

    try:
        # Use CLI injection logic
        cli_result = _use_cli_injection(
            files, prompt, model, apply, recursive, extensions
        )

        if cli_result:
            result.update(_convert_cli_result(cli_result, result["logs"]))
            return result
    except Exception as e:
        logger.warning(f"CLI injection failed: {e}")
```

#### **Real-time Progress Broadcasting:**

```python
async def broadcast_log(message: str, log_type: str = "log", level: str = "info", **kwargs):
    """Broadcast a log message to all connected WebSocket clients."""
    log_data = {
        "type": log_type,
        "message": message,
        "timestamp": datetime.now().isoformat(),
        "level": level,
        **kwargs
    }

    # Remove disconnected clients
    active_clients = []
    for client in clients:
        try:
            await client.send_text(json.dumps(log_data))
            active_clients.append(client)
        except Exception as e:
            logger.warning(f"Failed to send message to WebSocket client: {e}")
```

---

## 📡 **Real-time Communication System** {#realtime}

### **🔌 WebSocket Architecture:**

#### **1. Main Logs WebSocket** (`/ws/logs`)
- **Purpose**: Real-time injection progress and system logs
- **Data**: Progress updates, error messages, completion status

#### **2. Intelligence Hub WebSocket** (`/ws/intelligence`)
- **Purpose**: AI model performance and analytics updates
- **Data**: Model metrics, usage statistics, performance data

#### **3. Team Activity WebSocket** (`/ws/{team_id}`)
- **Purpose**: Team collaboration and activity feeds
- **Data**: Member actions, shared injections, team events

### **📊 WebSocket Message Types:**

```typescript
interface WebSocketMessage {
  type: 'log' | 'progress' | 'stats' | 'intelligence_update' | 'team_event';
  message: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  data?: any;
}
```

### **🔄 Frontend WebSocket Handling:**

```javascript
const handleMessage = useCallback((event: MessageEvent) => {
  try {
    // Try to parse as JSON first
    const data = JSON.parse(event.data);
    const message: WebSocketMessage = {
      type: data.type,
      message: data.message,
      value: data.value || data.progress,
      data: data
    };

    setLastMessage(message);
    onMessage?.(message);
  } catch {
    // Handle plain text messages
    const message: WebSocketMessage = {
      type: 'text',
      message: event.data,
      data: event.data
    };

    // Extract progress from plain text
    const progressMatch = event.data.match(/Progress: (\d+)%/);
    if (progressMatch) {
      message.value = parseInt(progressMatch[1]);
      message.type = 'progress';
    }

    setLastMessage(message);
    onMessage?.(message);
  }
}, [onMessage]);
```

---

## 🔐 **Authentication & Team System** {#auth}

### **🔑 Authentication Flow:**

#### **1. JWT-Based Authentication:**

```python
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

#### **2. User Registration & Login:**

```python
@router.post("/register")
async def register_user(user_data: UserCreate, db: DatabaseManager = Depends(get_db)):
    """Register a new user account."""
    # Check if user already exists
    existing_user = db.get_user_by_email(user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Hash password and create user
    hashed_password = hash_password(user_data.password)
    user_id = db.create_user(
        email=user_data.email,
        hashed_password=hashed_password,
        role=user_data.role
    )
```

### **👥 Team Collaboration System:**

#### **1. Team Creation & Management:**

```python
@router.post("/teams")
async def create_team(
    team_data: TeamCreate,
    current_user: dict = Depends(get_current_user),
    db: DatabaseManager = Depends(get_db)
):
    """Create a new team."""
    team_id = db.create_team(
        name=team_data.name,
        description=team_data.description,
        created_by=current_user["id"]
    )

    # Add creator as admin
    db.add_team_member(team_id, current_user["id"], "admin")
```

#### **2. Real-time Team Events:**

```python
async def broadcast_team_event(self, team_id: int, event: TeamEvent):
    """Broadcast an event to all team members."""
    # Store event in history
    if team_id not in self.team_events:
        self.team_events[team_id] = []

    self.team_events[team_id].append(event)

    # Broadcast to all connected team members
    if team_id in self.team_connections:
        message = json.dumps({
            "type": "team_event",
            "event": event.to_dict()
        })

        # Send to all connections
        for connection_info in self.team_connections[team_id]:
            try:
                await connection_info["websocket"].send_text(message)
            except Exception as e:
                logger.warning(f"Failed to send event to WebSocket: {e}")
```

---

## 📊 **Data Flow Diagrams** {#diagrams}

### **🔄 Complete System Data Flow:**

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React Dashboard]
        WS_CLIENT[WebSocket Client]
        AUTH_CTX[Auth Context]
    end

    subgraph "Backend Layer"
        API[FastAPI Server]
        WS_SERVER[WebSocket Server]
        AUTH[JWT Auth]
        DB[(SQLite Database)]
    end

    subgraph "Processing Layer"
        INJECTOR[Core Injector]
        AI[AI Engine]
        CLI[CLI Tool]
    end

    subgraph "Storage Layer"
        FILES[Code Files]
        TELEMETRY[Telemetry Data]
        INTEL[Intelligence Data]
    end

    UI --> API
    UI --> WS_CLIENT
    WS_CLIENT --> WS_SERVER
    API --> AUTH
    AUTH --> DB
    API --> INJECTOR
    INJECTOR --> AI
    INJECTOR --> CLI
    INJECTOR --> FILES
    WS_SERVER --> UI
    AI --> TELEMETRY
    TELEMETRY --> INTEL
```

### **🎯 Injection Process Flow:**

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant WebSocket
    participant AI_Engine
    participant File_System

    User->>Frontend: Configure injection
    Frontend->>Backend: POST /inject
    Backend->>WebSocket: "Starting injection..."
    WebSocket-->>Frontend: Progress update
    Backend->>AI_Engine: Process with AI
    AI_Engine->>Backend: Generated code
    Backend->>WebSocket: "AI processing complete..."
    WebSocket-->>Frontend: Progress update
    Backend->>File_System: Apply changes
    File_System->>Backend: Success/failure
    Backend->>WebSocket: "Injection complete!"
    WebSocket-->>Frontend: Final status
    Frontend-->>User: Show results
```

---

## 🎯 **Key Workflow Insights:**

### **✅ Strengths:**
1. **Real-time Communication** - WebSocket integration provides instant feedback
2. **Modular Architecture** - Clean separation between CLI, API, and UI
3. **Team Collaboration** - Multi-user support with real-time activity feeds
4. **Intelligence Analytics** - AI model performance tracking
5. **Robust Authentication** - JWT-based security with role management

### **🔧 Technical Highlights:**
1. **Dual Interface** - Both CLI and web interface for flexibility
2. **WebSocket Broadcasting** - Real-time updates to all connected clients
3. **Fallback Systems** - Multiple AI models with automatic routing
4. **Progress Tracking** - Detailed injection progress with live updates
5. **Team Features** - Collaborative workspaces with permission management

### **🚀 User Experience:**
1. **Immediate Feedback** - Users see progress in real-time
2. **Professional Interface** - Enterprise-grade dashboard design
3. **Team Collaboration** - Shared workspaces and activity feeds
4. **Analytics Dashboard** - AI model performance insights
5. **Mobile Responsive** - Works across different devices

This comprehensive ecosystem makes CodeCrusher a **powerful, enterprise-ready AI code injection platform** with real-time collaboration capabilities! 🎯
