"""
SQLite-based log store for CodeCrusher injection tracking
AugmentCode Surgical Implementation - Step 1
"""

import sqlite3
import uuid
import hashlib
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)

class InjectionLogStore:
    """SQLite-based storage for injection logs with comprehensive metadata tracking"""

    def __init__(self, db_path: str = None):
        # Use shared intelligence path if available, otherwise fallback to legacy
        if db_path is None:
            try:
                from intel_paths import get_shared_logs_path
                db_path = get_shared_logs_path()
            except ImportError:
                db_path = "logs.db"
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_db()

    def _init_db(self):
        """Initialize SQLite database with injection_logs table"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS injection_logs (
                        id TEXT PRIMARY KEY,
                        timestamp TEXT NOT NULL,
                        filename TEXT NOT NULL,
                        injection_type TEXT NOT NULL,
                        model TEXT NOT NULL,
                        prompt TEXT NOT NULL,
                        output_hash TEXT NOT NULL,
                        tags TEXT,
                        rating INTEGER,
                        feedback TEXT,
                        learned BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)

                # Create indexes for efficient querying
                conn.execute("CREATE INDEX IF NOT EXISTS idx_filename ON injection_logs(filename)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_tags ON injection_logs(tags)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON injection_logs(timestamp)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_rating ON injection_logs(rating)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_injection_type ON injection_logs(injection_type)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_learned ON injection_logs(learned)")

                conn.commit()
                logger.info(f"Initialized injection logs database: {self.db_path}")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise

    def log_injection(self, data: Dict[str, Any]) -> str:
        """
        Insert a new injection entry

        Args:
            data: Dictionary containing injection metadata
                - filename (str): Target file path
                - injection_type (str): Type of injection (bugfix, optimize, etc.)
                - model (str): AI model used
                - prompt (str): Original prompt text
                - output (str): Generated output
                - tags (List[str], optional): Associated tags

        Returns:
            str: UUID of the created log entry
        """
        try:
            # Generate unique ID
            log_id = str(uuid.uuid4())

            # Create timestamp
            timestamp = datetime.now().isoformat()

            # Generate output hash
            output_hash = hashlib.sha256(data.get('output', '').encode()).hexdigest()

            # Process tags
            tags = data.get('tags', [])
            tags_str = ','.join(tags) if tags else ''

            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO injection_logs
                    (id, timestamp, filename, injection_type, model, prompt, output_hash, tags)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    log_id,
                    timestamp,
                    data['filename'],
                    data['injection_type'],
                    data['model'],
                    data['prompt'],
                    output_hash,
                    tags_str
                ))
                conn.commit()

            logger.info(f"Logged injection: {log_id} for {data['filename']}")
            return log_id

        except Exception as e:
            logger.error(f"Failed to log injection: {e}")
            raise

    def get_logs_by_file(self, filename: str) -> List[Dict[str, Any]]:
        """
        Get all logs for a specific file

        Args:
            filename: Target filename to search for

        Returns:
            List of log entries as dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM injection_logs
                    WHERE filename = ?
                    ORDER BY timestamp DESC
                """, (filename,))

                logs = []
                for row in cursor.fetchall():
                    log_dict = dict(row)
                    # Parse tags back to list
                    if log_dict['tags']:
                        log_dict['tags'] = log_dict['tags'].split(',')
                    else:
                        log_dict['tags'] = []
                    logs.append(log_dict)

                logger.debug(f"Retrieved {len(logs)} logs for file: {filename}")
                return logs

        except Exception as e:
            logger.error(f"Failed to get logs by file: {e}")
            return []

    def get_logs_by_tag(self, tag: str) -> List[Dict[str, Any]]:
        """
        Get all logs containing a specific tag

        Args:
            tag: Tag to search for

        Returns:
            List of log entries as dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM injection_logs
                    WHERE tags LIKE ?
                    ORDER BY timestamp DESC
                """, (f'%{tag}%',))

                logs = []
                for row in cursor.fetchall():
                    log_dict = dict(row)
                    # Parse tags back to list
                    if log_dict['tags']:
                        log_dict['tags'] = log_dict['tags'].split(',')
                    else:
                        log_dict['tags'] = []

                    # Verify tag is actually in the list (not just substring match)
                    if tag in log_dict['tags']:
                        logs.append(log_dict)

                logger.debug(f"Retrieved {len(logs)} logs for tag: {tag}")
                return logs

        except Exception as e:
            logger.error(f"Failed to get logs by tag: {e}")
            return []

    def update_rating(self, log_id: str, rating: int, feedback: str = None) -> bool:
        """
        Update rating and feedback for a log entry

        Args:
            log_id: UUID of the log entry
            rating: Rating value (1-5)
            feedback: Optional feedback text

        Returns:
            bool: True if update was successful
        """
        try:
            if not 1 <= rating <= 5:
                raise ValueError("Rating must be between 1 and 5")

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    UPDATE injection_logs
                    SET rating = ?, feedback = ?
                    WHERE id = ?
                """, (rating, feedback, log_id))

                if cursor.rowcount == 0:
                    logger.warning(f"No log entry found with ID: {log_id}")
                    return False

                conn.commit()
                logger.info(f"Updated rating for log {log_id}: {rating}/5")
                return True

        except Exception as e:
            logger.error(f"Failed to update rating: {e}")
            return False

    def get_recent_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent log entries"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM injection_logs
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (limit,))

                logs = []
                for row in cursor.fetchall():
                    log_dict = dict(row)
                    # Parse tags back to list
                    if log_dict['tags']:
                        log_dict['tags'] = log_dict['tags'].split(',')
                    else:
                        log_dict['tags'] = []
                    logs.append(log_dict)

                return logs

        except Exception as e:
            logger.error(f"Failed to get recent logs: {e}")
            return []

    def get_low_rated_logs(self, rating_threshold: int = 2) -> List[Dict[str, Any]]:
        """Get logs with low ratings for analysis"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM injection_logs
                    WHERE rating IS NOT NULL AND rating <= ?
                    ORDER BY timestamp DESC
                """, (rating_threshold,))

                logs = []
                for row in cursor.fetchall():
                    log_dict = dict(row)
                    # Parse tags back to list
                    if log_dict['tags']:
                        log_dict['tags'] = log_dict['tags'].split(',')
                    else:
                        log_dict['tags'] = []
                    logs.append(log_dict)

                return logs

        except Exception as e:
            logger.error(f"Failed to get low rated logs: {e}")
            return []

    def mark_as_learned(self, log_id: str) -> bool:
        """Mark a log entry as learned from"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    UPDATE injection_logs
                    SET learned = TRUE
                    WHERE id = ?
                """, (log_id,))

                if cursor.rowcount == 0:
                    logger.warning(f"No log entry found with ID: {log_id}")
                    return False

                conn.commit()
                logger.info(f"Marked log {log_id} as learned")
                return True

        except Exception as e:
            logger.error(f"Failed to mark as learned: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about logged injections"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                stats = {}

                # Total injections
                cursor = conn.execute("SELECT COUNT(*) FROM injection_logs")
                stats['total_injections'] = cursor.fetchone()[0]

                # Average rating
                cursor = conn.execute("SELECT AVG(rating) FROM injection_logs WHERE rating IS NOT NULL")
                avg_rating = cursor.fetchone()[0]
                stats['average_rating'] = round(avg_rating, 2) if avg_rating else None

                # Rating distribution
                cursor = conn.execute("""
                    SELECT rating, COUNT(*) as count
                    FROM injection_logs
                    WHERE rating IS NOT NULL
                    GROUP BY rating
                    ORDER BY rating
                """)
                stats['rating_distribution'] = dict(cursor.fetchall())

                # Model usage
                cursor = conn.execute("""
                    SELECT model, COUNT(*) as count
                    FROM injection_logs
                    GROUP BY model
                    ORDER BY count DESC
                """)
                stats['model_usage'] = dict(cursor.fetchall())

                # Injection type distribution
                cursor = conn.execute("""
                    SELECT injection_type, COUNT(*) as count
                    FROM injection_logs
                    GROUP BY injection_type
                    ORDER BY count DESC
                """)
                stats['injection_types'] = dict(cursor.fetchall())

                # Learned entries
                cursor = conn.execute("SELECT COUNT(*) FROM injection_logs WHERE learned = TRUE")
                stats['learned_count'] = cursor.fetchone()[0]

                return stats

        except Exception as e:
            logger.error(f"Failed to get statistics: {e}")
            return {}


# Convenience functions for easy integration
def init_db(db_path: str = "logs.db") -> InjectionLogStore:
    """Initialize the injection log store"""
    return InjectionLogStore(db_path)


def log_injection(data: Dict[str, Any], db_path: str = "logs.db") -> str:
    """Convenience function to log an injection"""
    store = InjectionLogStore(db_path)
    return store.log_injection(data)


def get_logs_by_file(filename: str, db_path: str = "logs.db") -> List[Dict[str, Any]]:
    """Convenience function to get logs by file"""
    store = InjectionLogStore(db_path)
    return store.get_logs_by_file(filename)


def get_logs_by_tag(tag: str, db_path: str = "logs.db") -> List[Dict[str, Any]]:
    """Convenience function to get logs by tag"""
    store = InjectionLogStore(db_path)
    return store.get_logs_by_tag(tag)


def update_rating(log_id: str, rating: int, feedback: str = None, db_path: str = "logs.db") -> bool:
    """Convenience function to update rating"""
    store = InjectionLogStore(db_path)
    return store.update_rating(log_id, rating, feedback)


if __name__ == "__main__":
    # Test the log store implementation
    print("🧪 Testing AugmentCode Surgical Log Store...")

    # Initialize store
    store = init_db("test_logs.db")

    # Test logging
    test_data = {
        'filename': 'test_service.py',
        'injection_type': 'bugfix',
        'model': 'mixtral',
        'prompt': 'Add error handling to this function',
        'output': 'Added try-catch blocks with proper error messages',
        'tags': ['error-handling', 'bugfix', 'test']
    }

    log_id = store.log_injection(test_data)
    print(f"✅ Logged injection: {log_id}")

    # Test rating
    success = store.update_rating(log_id, 4, "Good implementation but could use better error messages")
    print(f"✅ Updated rating: {success}")

    # Test queries
    file_logs = store.get_logs_by_file('test_service.py')
    print(f"✅ File logs: {len(file_logs)} entries")

    tag_logs = store.get_logs_by_tag('bugfix')
    print(f"✅ Tag logs: {len(tag_logs)} entries")

    # Test statistics
    stats = store.get_statistics()
    print(f"✅ Statistics: {stats}")

    print("🎉 AugmentCode Surgical Log Store test completed successfully!")
