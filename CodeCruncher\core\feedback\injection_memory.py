"""
Injection Memory → Prompt Rebuilder
Intelligent prompt shaping based on historical injection data
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from .prompt_logger import PromptLogger, PromptLogEntry
from .feedback_engine import FeedbackEngine

logger = logging.getLogger(__name__)

@dataclass
class InjectionContext:
    """Context for injection memory lookup"""
    file_path: str
    file_extension: str
    injection_type: str
    tags: List[str]
    model: str
    similar_files: List[str] = None

@dataclass
class PromptShapingRule:
    """Rule for shaping prompts based on memory"""
    pattern: str
    condition: str
    enhancement: str
    confidence: float
    success_rate: float
    usage_count: int

class InjectionMemory:
    """Intelligent prompt shaping based on injection history"""
    
    def __init__(self, prompt_logger: PromptLogger, feedback_engine: FeedbackEngine,
                 memory_config_path: str = "data/injection_memory.json"):
        self.prompt_logger = prompt_logger
        self.feedback_engine = feedback_engine
        self.memory_config_path = Path(memory_config_path)
        self.memory_config_path.parent.mkdir(parents=True, exist_ok=True)
        self.shaping_rules = self._load_shaping_rules()
        self.model_hierarchy = [
            "llama3-8b", "mistral", "gemma", "mixtral", "llama3-70b", "gpt-4-turbo"
        ]
    
    def _load_shaping_rules(self) -> Dict[str, PromptShapingRule]:
        """Load prompt shaping rules from memory"""
        if self.memory_config_path.exists():
            try:
                with open(self.memory_config_path, 'r') as f:
                    data = json.load(f)
                    rules = {}
                    for key, rule_data in data.get('shaping_rules', {}).items():
                        rules[key] = PromptShapingRule(**rule_data)
                    return rules
            except Exception as e:
                logger.warning(f"Failed to load shaping rules: {e}")
        
        # Default shaping rules
        return {
            "null_safety": PromptShapingRule(
                pattern="null|none|undefined",
                condition="file_extension in ['py', 'js', 'ts']",
                enhancement="Always add null/None checks before accessing properties",
                confidence=0.8,
                success_rate=0.85,
                usage_count=0
            ),
            "error_handling": PromptShapingRule(
                pattern="error|exception|fail",
                condition="injection_type == 'bugfix'",
                enhancement="Include comprehensive error handling with try-catch blocks",
                confidence=0.9,
                success_rate=0.82,
                usage_count=0
            ),
            "performance_focus": PromptShapingRule(
                pattern="slow|optimize|performance",
                condition="injection_type == 'optimize'",
                enhancement="Focus on algorithmic complexity and memory efficiency",
                confidence=0.85,
                success_rate=0.78,
                usage_count=0
            ),
            "clean_code": PromptShapingRule(
                pattern="refactor|clean|structure",
                condition="injection_type == 'refactor'",
                enhancement="Follow clean code principles and maintain readability",
                confidence=0.75,
                success_rate=0.88,
                usage_count=0
            )
        }
    
    def _save_shaping_rules(self):
        """Save shaping rules to memory"""
        try:
            data = {
                'shaping_rules': {
                    key: {
                        'pattern': rule.pattern,
                        'condition': rule.condition,
                        'enhancement': rule.enhancement,
                        'confidence': rule.confidence,
                        'success_rate': rule.success_rate,
                        'usage_count': rule.usage_count
                    }
                    for key, rule in self.shaping_rules.items()
                }
            }
            
            with open(self.memory_config_path, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save shaping rules: {e}")
    
    def query_similar_injections(self, context: InjectionContext, limit: int = 10) -> List[PromptLogEntry]:
        """Query past log entries for similar files/tags/models"""
        recent_entries = self.prompt_logger.get_recent_entries(limit=100)
        
        similar_entries = []
        for entry in recent_entries:
            similarity_score = self._calculate_similarity(entry, context)
            if similarity_score > 0.3:  # Threshold for similarity
                similar_entries.append((entry, similarity_score))
        
        # Sort by similarity score and return top entries
        similar_entries.sort(key=lambda x: x[1], reverse=True)
        return [entry for entry, score in similar_entries[:limit]]
    
    def _calculate_similarity(self, entry: PromptLogEntry, context: InjectionContext) -> float:
        """Calculate similarity score between entry and context"""
        score = 0.0
        
        # File extension similarity
        entry_ext = Path(entry.file).suffix.lower()
        if entry_ext == f".{context.file_extension}":
            score += 0.3
        
        # Injection type similarity
        if entry.injection_type == context.injection_type:
            score += 0.4
        
        # Tag overlap
        if entry.tags and context.tags:
            common_tags = set(entry.tags) & set(context.tags)
            tag_similarity = len(common_tags) / max(len(entry.tags), len(context.tags))
            score += tag_similarity * 0.2
        
        # Model similarity
        if entry.model == context.model:
            score += 0.1
        
        return score
    
    def rebuild_prompt_with_memory(self, original_prompt: str, context: InjectionContext) -> Tuple[str, str, Dict[str, Any]]:
        """Rebuild prompt using injection memory and learned patterns"""
        
        # Query similar injections
        similar_injections = self.query_similar_injections(context)
        
        # Analyze successful patterns
        successful_patterns = self._extract_successful_patterns(similar_injections)
        
        # Apply shaping rules
        enhanced_prompt = self._apply_shaping_rules(original_prompt, context, successful_patterns)
        
        # Determine optimal model based on history
        optimal_model = self._determine_optimal_model(context, similar_injections)
        
        # Create memory metadata
        memory_metadata = {
            'similar_injections_found': len(similar_injections),
            'successful_patterns_applied': len(successful_patterns),
            'shaping_rules_triggered': self._get_triggered_rules(context),
            'model_recommendation_reason': self._get_model_recommendation_reason(context, similar_injections),
            'confidence_score': self._calculate_confidence_score(similar_injections, successful_patterns)
        }
        
        logger.info(f"Rebuilt prompt using {len(similar_injections)} similar injections")
        return enhanced_prompt, optimal_model, memory_metadata
    
    def _extract_successful_patterns(self, similar_injections: List[PromptLogEntry]) -> List[str]:
        """Extract patterns from successful similar injections"""
        patterns = []
        
        for entry in similar_injections:
            if entry.rating and entry.rating >= 4 and entry.success:
                # Extract key phrases from successful prompts
                prompt_words = entry.prompt.lower().split()
                
                # Look for common success indicators
                success_indicators = [
                    "add error handling", "include null check", "optimize performance",
                    "follow best practices", "add comments", "improve readability",
                    "handle edge cases", "add validation", "use proper naming"
                ]
                
                for indicator in success_indicators:
                    if indicator in entry.prompt.lower():
                        patterns.append(indicator)
        
        return list(set(patterns))  # Remove duplicates
    
    def _apply_shaping_rules(self, prompt: str, context: InjectionContext, patterns: List[str]) -> str:
        """Apply shaping rules to enhance the prompt"""
        enhanced_prompt = prompt
        
        # Apply context-specific rules
        for rule_name, rule in self.shaping_rules.items():
            if self._rule_matches_context(rule, context, patterns):
                enhanced_prompt = self._enhance_prompt_with_rule(enhanced_prompt, rule)
                rule.usage_count += 1
        
        self._save_shaping_rules()
        return enhanced_prompt
    
    def _rule_matches_context(self, rule: PromptShapingRule, context: InjectionContext, patterns: List[str]) -> bool:
        """Check if a shaping rule matches the current context"""
        try:
            # Create evaluation context
            eval_context = {
                'file_extension': context.file_extension,
                'injection_type': context.injection_type,
                'tags': context.tags,
                'model': context.model,
                'patterns': patterns
            }
            
            # Evaluate condition
            return eval(rule.condition, {"__builtins__": {}}, eval_context)
        except:
            return False
    
    def _enhance_prompt_with_rule(self, prompt: str, rule: PromptShapingRule) -> str:
        """Enhance prompt with a specific rule"""
        if rule.enhancement not in prompt:
            return f"{prompt}\n\nAdditional guidance: {rule.enhancement}"
        return prompt
    
    def _determine_optimal_model(self, context: InjectionContext, similar_injections: List[PromptLogEntry]) -> str:
        """Determine optimal model based on historical performance"""
        
        # Analyze model performance for similar contexts
        model_performance = {}
        
        for entry in similar_injections:
            if entry.model not in model_performance:
                model_performance[entry.model] = {'success': 0, 'total': 0, 'avg_rating': 0}
            
            model_performance[entry.model]['total'] += 1
            if entry.success:
                model_performance[entry.model]['success'] += 1
            if entry.rating:
                current_avg = model_performance[entry.model]['avg_rating']
                total = model_performance[entry.model]['total']
                model_performance[entry.model]['avg_rating'] = (current_avg * (total - 1) + entry.rating) / total
        
        # Find best performing model
        best_model = context.model  # Default to requested model
        best_score = 0
        
        for model, perf in model_performance.items():
            if perf['total'] >= 2:  # Minimum sample size
                success_rate = perf['success'] / perf['total']
                avg_rating = perf['avg_rating']
                score = (success_rate * 0.7) + (avg_rating / 5.0 * 0.3)
                
                if score > best_score:
                    best_score = score
                    best_model = model
        
        return best_model
    
    def _get_triggered_rules(self, context: InjectionContext) -> List[str]:
        """Get list of shaping rules that would be triggered"""
        triggered = []
        for rule_name, rule in self.shaping_rules.items():
            if self._rule_matches_context(rule, context, []):
                triggered.append(rule_name)
        return triggered
    
    def _get_model_recommendation_reason(self, context: InjectionContext, similar_injections: List[PromptLogEntry]) -> str:
        """Get reason for model recommendation"""
        if not similar_injections:
            return "No historical data available, using requested model"
        
        model_counts = {}
        for entry in similar_injections:
            if entry.success and entry.rating and entry.rating >= 4:
                model_counts[entry.model] = model_counts.get(entry.model, 0) + 1
        
        if model_counts:
            best_model = max(model_counts, key=model_counts.get)
            return f"Based on {model_counts[best_model]} successful similar injections"
        
        return "Based on general performance patterns"
    
    def _calculate_confidence_score(self, similar_injections: List[PromptLogEntry], patterns: List[str]) -> float:
        """Calculate confidence score for the memory-based enhancement"""
        if not similar_injections:
            return 0.3  # Low confidence without historical data
        
        # Base confidence on number of similar injections and success rate
        successful_injections = [e for e in similar_injections if e.success and e.rating and e.rating >= 3]
        success_rate = len(successful_injections) / len(similar_injections)
        
        # Adjust for number of patterns found
        pattern_bonus = min(len(patterns) * 0.1, 0.3)
        
        # Adjust for sample size
        sample_size_factor = min(len(similar_injections) / 10, 1.0)
        
        confidence = (success_rate * 0.6) + pattern_bonus + (sample_size_factor * 0.1)
        return min(confidence, 1.0)
    
    def update_rule_performance(self, rule_name: str, success: bool, rating: Optional[int] = None):
        """Update performance metrics for a shaping rule"""
        if rule_name in self.shaping_rules:
            rule = self.shaping_rules[rule_name]
            
            # Update success rate using exponential moving average
            alpha = 0.1
            new_success = 1.0 if success and (rating is None or rating >= 3) else 0.0
            
            if rule.usage_count == 1:
                rule.success_rate = new_success
            else:
                rule.success_rate = (1 - alpha) * rule.success_rate + alpha * new_success
            
            # Update confidence based on usage and success
            rule.confidence = min(0.95, rule.confidence + (0.05 if success else -0.02))
            
            self._save_shaping_rules()
            logger.debug(f"Updated rule {rule_name}: success_rate={rule.success_rate:.2f}, confidence={rule.confidence:.2f}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get statistics about injection memory"""
        recent_entries = self.prompt_logger.get_recent_entries(limit=100)
        
        # Analyze memory effectiveness
        memory_enhanced_entries = [e for e in recent_entries if 'memory_enhanced' in (e.tags or [])]
        
        stats = {
            'total_entries': len(recent_entries),
            'memory_enhanced_entries': len(memory_enhanced_entries),
            'shaping_rules': {
                name: {
                    'confidence': rule.confidence,
                    'success_rate': rule.success_rate,
                    'usage_count': rule.usage_count
                }
                for name, rule in self.shaping_rules.items()
            },
            'memory_effectiveness': len(memory_enhanced_entries) / len(recent_entries) if recent_entries else 0
        }
        
        return stats
