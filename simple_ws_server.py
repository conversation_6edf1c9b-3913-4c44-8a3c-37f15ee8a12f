#!/usr/bin/env python3
"""
Ultra-simple WebSocket server for testing Intelligence Hub connection
"""

import asyncio
import json
import logging
from datetime import datetime
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(title="Simple WebSocket Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Track connected clients
clients = set()

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": "Simple WebSocket Test Server is running",
        "active_clients": len(clients),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/intel/summary")
async def get_test_summary():
    """Test API endpoint that mimics the intelligence summary."""
    logger.info("📊 API endpoint called")
    return {
        "intelligence_data": {
            "total_entries": 9,
            "models": ['GPT-4', 'TestAPI', 'TestModel', 'Mixtral'],
            "files_processed": 6,
            "average_rating": 4.0,
            "average_score": 85.2,
            "model_performance": {
                'GPT-4': {'total_entries': 2, 'average_score': 95, 'average_rating': 5.0},
                'TestAPI': {'total_entries': 1, 'average_score': 95, 'average_rating': 5.0},
                'TestModel': {'total_entries': 1, 'average_score': 88, 'average_rating': 4.0},
                'Mixtral': {'total_entries': 5, 'average_score': 78, 'average_rating': 3.4}
            },
            "tone_effectiveness": {
                'assertive': 5.0,
                'neutral': 4.5,
                'formal': 4.5,
                'friendly': 3.2
            },
            "best_model": 'GPT-4',
            "recent_activity": 3
        },
        "timestamp": datetime.now().isoformat(),
        "status": "success"
    }

@app.websocket("/ws/intelligence")
@app.websocket("/intelligence")  # Handle rewritten path from Vite proxy
async def websocket_intelligence_simple(websocket: WebSocket):
    """Ultra-simple WebSocket endpoint for testing."""
    client_info = f"{websocket.client.host}:{websocket.client.port}" if websocket.client else "unknown"
    logger.info(f"🔗 WebSocket connection attempt from {client_info}")
    logger.info(f"Headers: {dict(websocket.headers)}")

    try:
        logger.info("📝 Accepting WebSocket connection...")
        await websocket.accept()
        clients.add(websocket)

        logger.info(f"[WS CONNECTED] {client_info} - Total clients: {len(clients)}")
        print(f"[WS CONNECTED] {client_info} - Total clients: {len(clients)}")

        # Send welcome message immediately
        welcome_data = {
            "type": "welcome",
            "message": "Connected to Simple Test Server",
            "data": {
                "total_entries": 9,
                "models": ['GPT-4', 'TestAPI', 'TestModel', 'Mixtral'],
                "files_processed": 6,
                "average_rating": 4.0,
                "average_score": 85.2,
                "best_model": 'GPT-4',
                "recent_activity": 3
            },
            "timestamp": datetime.now().isoformat()
        }

        await websocket.send_text(json.dumps(welcome_data))
        logger.info("📨 Welcome message sent successfully")

        # Keep connection alive with periodic heartbeats
        counter = 0
        while True:
            await asyncio.sleep(5)  # Send heartbeat every 5 seconds
            counter += 1

            heartbeat_data = {
                "type": "heartbeat",
                "counter": counter,
                "timestamp": datetime.now().isoformat(),
                "clients": len(clients)
            }

            await websocket.send_text(json.dumps(heartbeat_data))
            logger.info(f"💓 Heartbeat #{counter} sent")

    except WebSocketDisconnect as e:
        logger.info(f"🔌 WebSocket client {client_info} disconnected normally (code: {e.code})")
    except Exception as e:
        logger.error(f"❌ WebSocket error for {client_info}: {e}")
        logger.exception("Full error details:")
    finally:
        clients.discard(websocket)
        logger.info(f"🧹 Client {client_info} removed. Total clients: {len(clients)}")

if __name__ == "__main__":
    logger.info("🚀 Starting simple WebSocket test server...")
    logger.info("📍 Server will run on http://localhost:8001")
    logger.info("🔗 WebSocket endpoint: ws://localhost:8001/ws/intelligence")
    logger.info("📊 API endpoint: http://localhost:8001/api/intel/summary")

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        log_level="info",
        access_log=True
    )
