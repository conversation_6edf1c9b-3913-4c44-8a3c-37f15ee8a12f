"""
Auto-Testing: Simulate Learn Loop
Simulates the complete self-improving cycle: Inject → Rate → Re-inject → Log improvement
"""

import json
import random
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from .prompt_logger import Prompt<PERSON>ogger
from .feedback_engine import FeedbackEngine
from .auto_refine import AutoRefineEngine
from .injection_memory import InjectionMemory, InjectionContext
from .manual_tuning import ManualTuningPanel

logger = logging.getLogger(__name__)

@dataclass
class TestScenario:
    """Test scenario for auto-testing"""
    name: str
    file_path: str
    injection_type: str
    original_prompt: str
    expected_improvement: str
    tags: List[str]
    model: str = "mistral"

@dataclass
class TestResult:
    """Result of a test cycle"""
    scenario_name: str
    cycle_number: int
    original_prompt: str
    refined_prompt: str
    original_rating: int
    refined_rating: int
    improvement_score: float
    execution_time: float
    success: bool
    feedback_text: str

class AutoTestingEngine:
    """Engine for simulating the complete self-improving feedback loop"""
    
    def __init__(self, prompt_logger: PromptLogger, feedback_engine: FeedbackEngine,
                 auto_refine_engine: AutoRefineEngine, injection_memory: InjectionMemory,
                 tuning_panel: ManualTuningPanel, test_config_path: str = "data/auto_testing.json"):
        self.prompt_logger = prompt_logger
        self.feedback_engine = feedback_engine
        self.auto_refine_engine = auto_refine_engine
        self.injection_memory = injection_memory
        self.tuning_panel = tuning_panel
        self.test_config_path = Path(test_config_path)
        self.test_config_path.parent.mkdir(parents=True, exist_ok=True)
        self.test_scenarios = self._load_test_scenarios()
    
    def _load_test_scenarios(self) -> List[TestScenario]:
        """Load test scenarios from configuration"""
        if self.test_config_path.exists():
            try:
                with open(self.test_config_path, 'r') as f:
                    data = json.load(f)
                    scenarios = []
                    for scenario_data in data.get('scenarios', []):
                        scenarios.append(TestScenario(**scenario_data))
                    return scenarios
            except Exception as e:
                logger.warning(f"Failed to load test scenarios: {e}")
        
        # Default test scenarios
        return [
            TestScenario(
                name="null_pointer_fix",
                file_path="test_app.py",
                injection_type="bugfix",
                original_prompt="Fix the null pointer error",
                expected_improvement="Add null checks and proper error handling",
                tags=["bugfix", "null-check"],
                model="mistral"
            ),
            TestScenario(
                name="performance_optimization",
                file_path="slow_function.py",
                injection_type="optimize",
                original_prompt="Make this faster",
                expected_improvement="Optimize algorithm complexity and memory usage",
                tags=["optimize", "performance"],
                model="mixtral"
            ),
            TestScenario(
                name="code_refactoring",
                file_path="messy_code.py",
                injection_type="refactor",
                original_prompt="Clean up this code",
                expected_improvement="Improve structure, readability, and maintainability",
                tags=["refactor", "cleanup"],
                model="llama3-70b"
            ),
            TestScenario(
                name="error_handling",
                file_path="api_client.py",
                injection_type="bugfix",
                original_prompt="Add error handling",
                expected_improvement="Comprehensive try-catch blocks with proper error messages",
                tags=["bugfix", "error-handling"],
                model="gemma"
            ),
            TestScenario(
                name="feature_addition",
                file_path="user_service.py",
                injection_type="feature",
                original_prompt="Add user authentication",
                expected_improvement="Secure authentication with proper validation and error handling",
                tags=["feature", "security"],
                model="llama3-70b"
            )
        ]
    
    def _save_test_scenarios(self):
        """Save test scenarios to configuration"""
        try:
            data = {
                'scenarios': [asdict(scenario) for scenario in self.test_scenarios]
            }
            with open(self.test_config_path, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save test scenarios: {e}")
    
    def simulate_injection_cycle(self, scenario: TestScenario, cycle_number: int = 1) -> TestResult:
        """Simulate a complete injection cycle: Inject → Rate → Re-inject → Log improvement"""
        
        start_time = time.time()
        
        # Step 1: Create injection context
        context = InjectionContext(
            file_path=scenario.file_path,
            file_extension=Path(scenario.file_path).suffix[1:],
            injection_type=scenario.injection_type,
            tags=scenario.tags,
            model=scenario.model
        )
        
        # Step 2: Use injection memory to rebuild prompt
        if cycle_number > 1:
            refined_prompt, recommended_model, memory_metadata = self.injection_memory.rebuild_prompt_with_memory(
                scenario.original_prompt, context
            )
        else:
            refined_prompt = scenario.original_prompt
            recommended_model = scenario.model
        
        # Step 3: Simulate injection execution
        original_output = self._simulate_injection_output(scenario.original_prompt, context)
        refined_output = self._simulate_injection_output(refined_prompt, context)
        
        # Step 4: Log the executions
        original_entry_id = self.prompt_logger.log_prompt(
            file=scenario.file_path,
            injection_type=scenario.injection_type,
            model=scenario.model,
            prompt=scenario.original_prompt,
            output=original_output,
            execution_time=time.time() - start_time,
            success=True,
            tags=scenario.tags + ["auto-test", "original"]
        )
        
        refined_entry_id = self.prompt_logger.log_prompt(
            file=scenario.file_path,
            injection_type=scenario.injection_type,
            model=recommended_model,
            prompt=refined_prompt,
            output=refined_output,
            execution_time=time.time() - start_time,
            success=True,
            tags=scenario.tags + ["auto-test", "refined", "memory-enhanced"]
        )
        
        # Step 5: Simulate user ratings
        original_rating = self._simulate_rating(scenario.original_prompt, original_output, scenario)
        refined_rating = self._simulate_rating(refined_prompt, refined_output, scenario)
        
        # Step 6: Generate feedback
        original_feedback = self._generate_feedback(original_rating, scenario.original_prompt, scenario)
        refined_feedback = self._generate_feedback(refined_rating, refined_prompt, scenario)
        
        # Step 7: Collect feedback
        self.feedback_engine.collect_feedback(original_entry_id, original_rating, original_feedback)
        self.feedback_engine.collect_feedback(refined_entry_id, refined_rating, refined_feedback)
        
        # Step 8: Calculate improvement score
        improvement_score = self._calculate_improvement_score(
            original_rating, refined_rating, scenario.original_prompt, refined_prompt
        )
        
        execution_time = time.time() - start_time
        
        return TestResult(
            scenario_name=scenario.name,
            cycle_number=cycle_number,
            original_prompt=scenario.original_prompt,
            refined_prompt=refined_prompt,
            original_rating=original_rating,
            refined_rating=refined_rating,
            improvement_score=improvement_score,
            execution_time=execution_time,
            success=refined_rating > original_rating,
            feedback_text=refined_feedback
        )
    
    def _simulate_injection_output(self, prompt: str, context: InjectionContext) -> str:
        """Simulate injection output based on prompt quality"""
        # Simulate different output quality based on prompt characteristics
        quality_indicators = [
            "error handling", "null check", "validation", "try-catch",
            "optimize", "performance", "algorithm", "complexity",
            "refactor", "clean", "readable", "maintainable",
            "comments", "documentation", "best practices"
        ]
        
        quality_score = sum(1 for indicator in quality_indicators if indicator in prompt.lower())
        
        # Generate simulated output based on quality score
        if quality_score >= 3:
            return f"High-quality {context.injection_type} implementation with proper {', '.join(quality_indicators[:quality_score])}."
        elif quality_score >= 1:
            return f"Basic {context.injection_type} implementation with some improvements."
        else:
            return f"Simple {context.injection_type} with minimal changes."
    
    def _simulate_rating(self, prompt: str, output: str, scenario: TestScenario) -> int:
        """Simulate user rating based on prompt and output quality"""
        # Base rating on prompt quality
        base_rating = 3
        
        # Improve rating based on prompt characteristics
        if "error handling" in prompt.lower() or "try-catch" in prompt.lower():
            base_rating += 1
        if "null check" in prompt.lower() or "validation" in prompt.lower():
            base_rating += 1
        if "optimize" in prompt.lower() or "performance" in prompt.lower():
            base_rating += 1
        if "clean" in prompt.lower() or "refactor" in prompt.lower():
            base_rating += 1
        if "best practices" in prompt.lower() or "comments" in prompt.lower():
            base_rating += 1
        
        # Adjust based on expected improvement
        if scenario.expected_improvement.lower() in prompt.lower():
            base_rating += 1
        
        # Add some randomness
        rating = base_rating + random.randint(-1, 1)
        
        return max(1, min(5, rating))
    
    def _generate_feedback(self, rating: int, prompt: str, scenario: TestScenario) -> str:
        """Generate realistic feedback based on rating and prompt"""
        if rating >= 4:
            positive_feedback = [
                "Excellent implementation with proper error handling",
                "Great optimization with significant performance improvement",
                "Clean refactoring that improves maintainability",
                "Comprehensive solution with good documentation",
                "Well-structured code with proper validation"
            ]
            return random.choice(positive_feedback)
        elif rating == 3:
            neutral_feedback = [
                "Good implementation but could use better error handling",
                "Decent optimization but missing some edge cases",
                "Acceptable refactoring but could be cleaner",
                "Basic implementation that works but needs improvement"
            ]
            return random.choice(neutral_feedback)
        else:
            negative_feedback = [
                "Missing proper error handling and null checks",
                "Performance optimization is insufficient",
                "Refactoring doesn't improve code structure enough",
                "Implementation is too basic and lacks robustness",
                "Needs better validation and edge case handling"
            ]
            return random.choice(negative_feedback)
    
    def _calculate_improvement_score(self, original_rating: int, refined_rating: int,
                                   original_prompt: str, refined_prompt: str) -> float:
        """Calculate improvement score between original and refined versions"""
        # Rating improvement (40% weight)
        rating_improvement = (refined_rating - original_rating) / 4.0 * 0.4
        
        # Prompt enhancement (30% weight)
        prompt_enhancement = (len(refined_prompt) - len(original_prompt)) / len(original_prompt) * 0.3
        prompt_enhancement = max(0, min(prompt_enhancement, 0.3))  # Cap at 30%
        
        # Quality indicators (30% weight)
        quality_indicators = [
            "error handling", "null check", "validation", "try-catch",
            "optimize", "performance", "best practices", "comments"
        ]
        
        original_quality = sum(1 for indicator in quality_indicators if indicator in original_prompt.lower())
        refined_quality = sum(1 for indicator in quality_indicators if indicator in refined_prompt.lower())
        
        quality_improvement = (refined_quality - original_quality) / len(quality_indicators) * 0.3
        
        total_improvement = rating_improvement + prompt_enhancement + quality_improvement
        return max(0, min(1, total_improvement))
    
    def run_test_suite(self, cycles: int = 3, scenarios: Optional[List[str]] = None) -> Dict[str, Any]:
        """Run complete test suite with multiple cycles"""
        logger.info(f"Starting auto-testing suite with {cycles} cycles")
        
        # Filter scenarios if specified
        test_scenarios = self.test_scenarios
        if scenarios:
            test_scenarios = [s for s in self.test_scenarios if s.name in scenarios]
        
        results = []
        
        for cycle in range(1, cycles + 1):
            logger.info(f"Running test cycle {cycle}/{cycles}")
            
            for scenario in test_scenarios:
                try:
                    result = self.simulate_injection_cycle(scenario, cycle)
                    results.append(result)
                    logger.info(f"Completed {scenario.name} cycle {cycle}: improvement={result.improvement_score:.2f}")
                except Exception as e:
                    logger.error(f"Failed to run scenario {scenario.name} cycle {cycle}: {e}")
        
        # Analyze results
        analysis = self._analyze_test_results(results)
        
        return {
            'test_results': [asdict(result) for result in results],
            'analysis': analysis,
            'total_scenarios': len(test_scenarios),
            'total_cycles': cycles,
            'total_tests': len(results)
        }
    
    def _analyze_test_results(self, results: List[TestResult]) -> Dict[str, Any]:
        """Analyze test results to identify patterns and improvements"""
        if not results:
            return {}
        
        # Overall statistics
        total_tests = len(results)
        successful_improvements = len([r for r in results if r.success])
        average_improvement = sum(r.improvement_score for r in results) / total_tests
        average_execution_time = sum(r.execution_time for r in results) / total_tests
        
        # Improvement by scenario
        scenario_analysis = {}
        for result in results:
            if result.scenario_name not in scenario_analysis:
                scenario_analysis[result.scenario_name] = {
                    'tests': 0,
                    'improvements': 0,
                    'avg_improvement_score': 0,
                    'avg_rating_improvement': 0
                }
            
            analysis = scenario_analysis[result.scenario_name]
            analysis['tests'] += 1
            if result.success:
                analysis['improvements'] += 1
            analysis['avg_improvement_score'] += result.improvement_score
            analysis['avg_rating_improvement'] += (result.refined_rating - result.original_rating)
        
        # Calculate averages
        for analysis in scenario_analysis.values():
            analysis['avg_improvement_score'] /= analysis['tests']
            analysis['avg_rating_improvement'] /= analysis['tests']
            analysis['success_rate'] = analysis['improvements'] / analysis['tests']
        
        # Cycle analysis
        cycle_analysis = {}
        for result in results:
            cycle = result.cycle_number
            if cycle not in cycle_analysis:
                cycle_analysis[cycle] = {
                    'tests': 0,
                    'avg_improvement': 0,
                    'success_rate': 0
                }
            
            cycle_analysis[cycle]['tests'] += 1
            cycle_analysis[cycle]['avg_improvement'] += result.improvement_score
            if result.success:
                cycle_analysis[cycle]['success_rate'] += 1
        
        for cycle, analysis in cycle_analysis.items():
            analysis['avg_improvement'] /= analysis['tests']
            analysis['success_rate'] /= analysis['tests']
        
        return {
            'overall': {
                'total_tests': total_tests,
                'successful_improvements': successful_improvements,
                'success_rate': successful_improvements / total_tests,
                'average_improvement_score': average_improvement,
                'average_execution_time': average_execution_time
            },
            'by_scenario': scenario_analysis,
            'by_cycle': cycle_analysis,
            'learning_effectiveness': self._calculate_learning_effectiveness(results)
        }
    
    def _calculate_learning_effectiveness(self, results: List[TestResult]) -> float:
        """Calculate how effectively the system is learning over cycles"""
        if len(results) < 2:
            return 0.0
        
        # Group results by scenario and cycle
        scenario_cycles = {}
        for result in results:
            key = result.scenario_name
            if key not in scenario_cycles:
                scenario_cycles[key] = {}
            scenario_cycles[key][result.cycle_number] = result
        
        # Calculate improvement trend
        total_trend = 0
        scenario_count = 0
        
        for scenario, cycles in scenario_cycles.items():
            if len(cycles) > 1:
                cycle_numbers = sorted(cycles.keys())
                first_cycle = cycles[cycle_numbers[0]]
                last_cycle = cycles[cycle_numbers[-1]]
                
                trend = last_cycle.improvement_score - first_cycle.improvement_score
                total_trend += trend
                scenario_count += 1
        
        return total_trend / scenario_count if scenario_count > 0 else 0.0
    
    def add_test_scenario(self, scenario: TestScenario):
        """Add a new test scenario"""
        self.test_scenarios.append(scenario)
        self._save_test_scenarios()
    
    def get_test_scenarios(self) -> List[TestScenario]:
        """Get all test scenarios"""
        return self.test_scenarios
