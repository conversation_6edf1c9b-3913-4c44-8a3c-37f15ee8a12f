"""
Team-aware injection routes for CodeCrusher
Handles injection logging, rating, and team synchronization
"""

from fastapi import APIRouter, Depends, HTTPException, status, Header
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import hashlib

from auth import get_current_user
from database import get_db
from team_events import team_event_manager

router = APIRouter(prefix="/injections", tags=["injections"])

# Pydantic models
class InjectionRequest(BaseModel):
    file_path: str
    prompt: str
    model: str
    mode: str = "preview"
    tags: List[str] = []
    intent: str = "auto"
    team_id: Optional[int] = None

class InjectionResponse(BaseModel):
    success: bool
    message: str
    log_id: Optional[int] = None
    team_context: bool = False

class InjectionLogResponse(BaseModel):
    id: int
    user_id: int
    user_email: str
    team_id: Optional[int]
    file_path: str
    prompt: str
    model: str
    mode: str
    tags: Optional[str]
    intent: Optional[str]
    success: bool
    output: Optional[str]
    error_message: Optional[str]
    execution_time: Optional[float]
    created_at: str
    rating: Optional[int]
    feedback: Optional[str]

class RatingRequest(BaseModel):
    rating: int
    feedback: Optional[str] = None

class PromptMemoryResponse(BaseModel):
    id: int
    user_id: int
    user_email: str
    team_id: Optional[int]
    prompt_hash: str
    original_prompt: str
    shaped_prompt: str
    model: str
    intent: Optional[str]
    success_count: int
    failure_count: int
    avg_rating: Optional[float]
    last_used: str
    created_at: str

def get_team_context(current_user: dict, team_id: Optional[int] = None) -> Optional[int]:
    """Get team context for injection logging."""
    if team_id:
        # Verify user is member of the team
        db = get_db()
        role = db.check_team_membership(current_user["id"], team_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this team"
            )
        return team_id
    return None

@router.post("/log", response_model=InjectionResponse)
async def log_injection(
    request: InjectionRequest,
    current_user: dict = Depends(get_current_user)
):
    """Log an injection with team context."""
    db = get_db()

    # Get team context
    team_context = get_team_context(current_user, request.team_id)

    # For now, simulate injection execution
    # In real implementation, this would call the actual injection logic
    success = True
    output = f"Simulated injection on {request.file_path}"
    execution_time = 2.5

    # Log the injection
    log_id = db.log_injection(
        user_id=current_user["id"],
        team_id=team_context,
        file_path=request.file_path,
        prompt=request.prompt,
        model=request.model,
        mode=request.mode,
        tags=request.tags,
        intent=request.intent,
        success=success,
        output=output,
        execution_time=execution_time
    )

    if not log_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to log injection"
        )

    # Save prompt memory for team learning
    if success and team_context:
        prompt_hash = hashlib.md5(request.prompt.encode()).hexdigest()
        db.save_prompt_memory(
            user_id=current_user["id"],
            team_id=team_context,
            prompt_hash=prompt_hash,
            original_prompt=request.prompt,
            shaped_prompt=request.prompt,  # For now, same as original
            model=request.model,
            intent=request.intent
        )

        # Emit prompt shaped event
        prompt_event = team_event_manager.create_prompt_shaped_event(
            team_id=team_context,
            user_id=current_user["id"],
            user_email=current_user["email"],
            prompt_data={
                "prompt_hash": prompt_hash,
                "original_prompt": request.prompt,
                "model": request.model,
                "intent": request.intent
            }
        )
        await team_event_manager.broadcast_event(prompt_event)

    # Emit injection created event
    if team_context:
        injection_event = team_event_manager.create_injection_event(
            team_id=team_context,
            user_id=current_user["id"],
            user_email=current_user["email"],
            injection_data={
                "id": log_id,
                "file_path": request.file_path,
                "model": request.model,
                "mode": request.mode,
                "tags": request.tags,
                "intent": request.intent,
                "success": success
            }
        )
        await team_event_manager.broadcast_event(injection_event)

    return InjectionResponse(
        success=True,
        message=f"Injection logged successfully",
        log_id=log_id,
        team_context=team_context is not None
    )

@router.get("/logs", response_model=List[InjectionLogResponse])
async def get_injection_logs(
    team_id: Optional[int] = None,
    user_only: bool = False,
    limit: int = 50,
    current_user: dict = Depends(get_current_user)
):
    """Get injection logs with team filtering."""
    db = get_db()

    if team_id:
        # Verify team membership
        role = db.check_team_membership(current_user["id"], team_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this team"
            )

        # Get team logs
        logs = db.get_injection_logs(team_id=team_id, limit=limit)
    elif user_only:
        # Get user's personal logs only
        logs = db.get_injection_logs(user_id=current_user["id"], limit=limit)
    else:
        # Get all user's logs (personal + team)
        logs = db.get_injection_logs(user_id=current_user["id"], limit=limit)

    return [
        InjectionLogResponse(
            id=log["id"],
            user_id=log["user_id"],
            user_email=log["user_email"],
            team_id=log["team_id"],
            file_path=log["file_path"],
            prompt=log["prompt"],
            model=log["model"],
            mode=log["mode"],
            tags=log["tags"],
            intent=log["intent"],
            success=bool(log["success"]),
            output=log["output"],
            error_message=log["error_message"],
            execution_time=log["execution_time"],
            created_at=log["created_at"],
            rating=log["rating"],
            feedback=log["feedback"]
        )
        for log in logs
    ]

@router.post("/logs/{log_id}/rate", response_model=dict)
async def rate_injection(
    log_id: int,
    rating_request: RatingRequest,
    current_user: dict = Depends(get_current_user)
):
    """Rate an injection log."""
    if not (1 <= rating_request.rating <= 5):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Rating must be between 1 and 5"
        )

    db = get_db()

    # Get injection log to check team context
    logs = db.get_injection_logs(limit=1000)  # Get all to find specific log
    injection_log = next((log for log in logs if log["id"] == log_id), None)

    if not injection_log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Injection log not found"
        )

    # Rate the injection
    success = db.rate_injection(
        log_id=log_id,
        rating=rating_request.rating,
        feedback=rating_request.feedback
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to rate injection"
        )

    # Emit rating event if this is a team injection
    if injection_log.get("team_id"):
        rating_event = team_event_manager.create_rating_event(
            team_id=injection_log["team_id"],
            user_id=current_user["id"],
            user_email=current_user["email"],
            injection_id=log_id,
            rating=rating_request.rating,
            feedback=rating_request.feedback
        )
        await team_event_manager.broadcast_event(rating_event)

    return {
        "message": "Injection rated successfully",
        "rating": rating_request.rating
    }

@router.get("/prompt-memory", response_model=List[PromptMemoryResponse])
async def get_prompt_memory(
    team_id: Optional[int] = None,
    user_only: bool = False,
    current_user: dict = Depends(get_current_user)
):
    """Get prompt memory with team context."""
    db = get_db()

    if team_id:
        # Verify team membership
        role = db.check_team_membership(current_user["id"], team_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You are not a member of this team"
            )

        # Get team prompt memory
        memories = db.get_prompt_memory(team_id=team_id)
    elif user_only:
        # Get user's personal prompt memory only
        memories = db.get_prompt_memory(user_id=current_user["id"])
    else:
        # Get all accessible prompt memory
        memories = db.get_prompt_memory(user_id=current_user["id"])

    return [
        PromptMemoryResponse(
            id=memory["id"],
            user_id=memory["user_id"],
            user_email=memory["user_email"],
            team_id=memory["team_id"],
            prompt_hash=memory["prompt_hash"],
            original_prompt=memory["original_prompt"],
            shaped_prompt=memory["shaped_prompt"],
            model=memory["model"],
            intent=memory["intent"],
            success_count=memory["success_count"],
            failure_count=memory["failure_count"],
            avg_rating=memory["avg_rating"],
            last_used=memory["last_used"],
            created_at=memory["created_at"]
        )
        for memory in memories
    ]

@router.get("/team/{team_id}/stats", response_model=dict)
async def get_team_injection_stats(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get team injection statistics."""
    db = get_db()

    # Verify team membership
    role = db.check_team_membership(current_user["id"], team_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this team"
        )

    # Get team logs for statistics
    logs = db.get_injection_logs(team_id=team_id, limit=1000)

    total_injections = len(logs)
    successful_injections = sum(1 for log in logs if log["success"])
    rated_injections = sum(1 for log in logs if log["rating"] is not None)
    avg_rating = sum(log["rating"] for log in logs if log["rating"] is not None) / max(rated_injections, 1)

    # Get unique users
    unique_users = len(set(log["user_id"] for log in logs))

    # Get prompt memory stats
    memories = db.get_prompt_memory(team_id=team_id)
    total_prompts = len(memories)

    return {
        "team_id": team_id,
        "total_injections": total_injections,
        "successful_injections": successful_injections,
        "success_rate": successful_injections / max(total_injections, 1),
        "rated_injections": rated_injections,
        "average_rating": avg_rating,
        "unique_contributors": unique_users,
        "total_prompt_memories": total_prompts,
        "collective_learning_active": total_prompts > 0
    }
