"""
Core injection logic for CodeCrusher API
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class InjectionResult(BaseModel):
    success: bool
    file_path: str
    original_content: str
    modified_content: str
    changes_applied: bool
    model_used: str
    execution_time: float
    error_message: Optional[str] = None

def validate_injection_request(file_path: str, prompt: str, model: str) -> Dict[str, Any]:
    """Validate injection request parameters."""
    errors = []
    
    if not file_path:
        errors.append("File path is required")
    elif not os.path.exists(file_path):
        errors.append(f"File or directory does not exist: {file_path}")
    
    if not prompt or len(prompt.strip()) < 10:
        errors.append("Prompt must be at least 10 characters long")
    
    if not model:
        errors.append("Model is required")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors
    }

async def inject_code(
    file_path: str,
    prompt: str,
    model: str = "mixtral-8x7b",
    mode: str = "preview",
    tags: List[str] = None,
    **kwargs
) -> InjectionResult:
    """
    Core code injection function.
    
    This is a simplified implementation for testing.
    In production, this would integrate with actual AI models.
    """
    start_time = datetime.now()
    
    try:
        # Validate request
        validation = validate_injection_request(file_path, prompt, model)
        if not validation["valid"]:
            return InjectionResult(
                success=False,
                file_path=file_path,
                original_content="",
                modified_content="",
                changes_applied=False,
                model_used=model,
                execution_time=0.0,
                error_message="; ".join(validation["errors"])
            )
        
        # Read file content
        if os.path.isfile(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
        else:
            # For directories, create a summary
            original_content = f"Directory: {file_path}"
        
        # Simulate AI processing
        modified_content = simulate_ai_injection(original_content, prompt, model)
        
        # Apply changes if in apply mode
        changes_applied = False
        if mode == "apply" and os.path.isfile(file_path):
            try:
                # Create backup
                backup_path = f"{file_path}.backup.{int(datetime.now().timestamp())}"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # Write modified content
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                changes_applied = True
                logger.info(f"Applied changes to {file_path}, backup saved to {backup_path}")
            except Exception as e:
                logger.error(f"Failed to apply changes to {file_path}: {e}")
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return InjectionResult(
            success=True,
            file_path=file_path,
            original_content=original_content,
            modified_content=modified_content,
            changes_applied=changes_applied,
            model_used=model,
            execution_time=execution_time
        )
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Injection failed for {file_path}: {e}")
        
        return InjectionResult(
            success=False,
            file_path=file_path,
            original_content="",
            modified_content="",
            changes_applied=False,
            model_used=model,
            execution_time=execution_time,
            error_message=str(e)
        )

def simulate_ai_injection(content: str, prompt: str, model: str) -> str:
    """
    Simulate AI injection for testing purposes.
    In production, this would call actual AI models.
    """
    # Add a comment indicating the injection
    injection_comment = f"\n# AI Injection Applied: {prompt[:50]}...\n# Model: {model}\n# Timestamp: {datetime.now().isoformat()}\n"
    
    if content.strip():
        # Add the comment at the beginning of the file
        return injection_comment + content
    else:
        # For empty files, create basic structure
        return injection_comment + f"# Generated content based on: {prompt}\n\npass  # TODO: Implement functionality\n"

def get_supported_file_types() -> List[str]:
    """Get list of supported file types for injection."""
    return [
        "py", "js", "ts", "jsx", "tsx", "java", "cpp", "c", "cs", "php",
        "rb", "go", "rs", "swift", "kt", "scala", "html", "css", "scss",
        "sass", "less", "vue", "svelte", "md", "json", "yaml", "yml", "xml"
    ]

def estimate_injection_complexity(file_path: str, prompt: str) -> str:
    """Estimate the complexity of an injection task."""
    if not os.path.exists(file_path):
        return "unknown"
    
    if os.path.isfile(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = len(content.splitlines())
            prompt_words = len(prompt.split())
            
            if lines < 50 and prompt_words < 20:
                return "simple"
            elif lines < 200 and prompt_words < 50:
                return "moderate"
            else:
                return "complex"
        except:
            return "unknown"
    else:
        # Directory complexity based on file count
        try:
            file_count = sum(len(files) for _, _, files in os.walk(file_path))
            if file_count < 10:
                return "simple"
            elif file_count < 50:
                return "moderate"
            else:
                return "complex"
        except:
            return "unknown"
