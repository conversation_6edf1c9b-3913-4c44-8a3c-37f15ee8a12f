"""
Prompt Shaping Module for CodeCrusher

This module provides intelligent prompt reshaping based on detected intent,
transforming raw user prompts into targeted, model-friendly instructions
that produce better AI generation results.

Features:
- Built-in intent-specific templates for common development tasks
- Project-specific template registry (codecrusher.prompts.json)
- Template variable substitution ({{code}}, {{prompt}})
- Fallback to global templates when project templates are unavailable
"""

import re
import os
import json
import logging
from typing import Dict, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)


def load_project_templates(project_root: str) -> Dict[str, str]:
    """
    Load project-specific prompt templates from codecrusher.prompts.json.

    Args:
        project_root: Root directory of the project to search for templates

    Returns:
        Dictionary mapping intent names to template strings

    Example:
        >>> templates = load_project_templates("/path/to/project")
        >>> print(templates.get("refactor", "No refactor template"))
    """
    if not project_root:
        return {}

    template_path = os.path.join(project_root, "codecrusher.prompts.json")

    if not os.path.exists(template_path):
        logger.debug(f"No project templates found at {template_path}")
        return {}

    try:
        with open(template_path, "r", encoding="utf-8") as f:
            templates = json.load(f)

        if not isinstance(templates, dict):
            logger.warning(f"Invalid template format in {template_path}: expected dict, got {type(templates)}")
            return {}

        logger.info(f"Loaded {len(templates)} project templates from {template_path}")
        return templates

    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in project templates file {template_path}: {e}")
        return {}
    except Exception as e:
        logger.error(f"Failed to load project templates from {template_path}: {e}")
        return {}


def apply_template(template: str, code: str, prompt: str) -> str:
    """
    Apply a project template by substituting variables.

    Args:
        template: Template string with {{code}} and {{prompt}} placeholders
        code: Code content to substitute for {{code}}
        prompt: User prompt to substitute for {{prompt}}

    Returns:
        Template with variables substituted

    Example:
        >>> template = "Refactor this code:\\n\\n{{code}}\\n\\nInstructions: {{prompt}}"
        >>> result = apply_template(template, "def func():", "make it faster")
        >>> print(result)
        Refactor this code:

        def func():

        Instructions: make it faster
    """
    # Replace template variables
    result = template.replace("{{code}}", code)
    result = result.replace("{{prompt}}", prompt)

    # Also support alternative syntax for compatibility
    result = result.replace("{code}", code)
    result = result.replace("{prompt}", prompt)

    return result


def find_project_root(file_path: str) -> str:
    """
    Find the project root directory by looking for common project indicators.

    Args:
        file_path: Path to a file within the project

    Returns:
        Path to the project root directory, or empty string if not found
    """
    if not file_path:
        return ""

    # Start from the file's directory
    current_dir = os.path.dirname(os.path.abspath(file_path))

    # Look for common project root indicators
    project_indicators = [
        ".git",
        "package.json",
        "requirements.txt",
        "Cargo.toml",
        "go.mod",
        "pom.xml",
        "build.gradle",
        "codecrusher.prompts.json",
        ".codecrusher",
        "pyproject.toml"
    ]

    # Walk up the directory tree
    while current_dir != os.path.dirname(current_dir):  # Stop at filesystem root
        for indicator in project_indicators:
            if os.path.exists(os.path.join(current_dir, indicator)):
                logger.debug(f"Found project root at {current_dir} (indicator: {indicator})")
                return current_dir

        current_dir = os.path.dirname(current_dir)

    logger.debug(f"No project root found for {file_path}")
    return ""


class PromptShaper:
    """Advanced prompt shaper that creates context-aware prompts based on intent."""

    # Intent-specific prompt templates
    INTENT_TEMPLATES = {
        "refactor": {
            "template": """You are a senior software engineer performing code refactoring.

TASK: Refactor the following code to improve structure, maintainability, and readability.

ORIGINAL CODE:
{file_context}

USER REQUEST: {prompt}

REQUIREMENTS:
- Maintain the same functionality
- Improve code structure and organization
- Follow best practices and design patterns
- Add meaningful variable/function names
- Remove code duplication
- Improve error handling where needed

RESPONSE FORMAT: Return only the refactored code with clear, concise improvements.""",
            "context_required": True
        },

        "optimize": {
            "template": """You are a performance optimization expert.

TASK: Optimize the following code for better performance and efficiency.

ORIGINAL CODE:
{file_context}

USER REQUEST: {prompt}

OPTIMIZATION GOALS:
- Improve execution speed and memory usage
- Reduce computational complexity where possible
- Optimize database queries and API calls
- Implement efficient algorithms and data structures
- Remove performance bottlenecks
- Maintain code readability

RESPONSE FORMAT: Return the optimized code with performance improvements.""",
            "context_required": True
        },

        "document": {
            "template": """You are a technical documentation specialist.

TASK: Add comprehensive documentation to the following code.

CODE TO DOCUMENT:
{file_context}

DOCUMENTATION REQUIREMENTS:
- Add clear, professional docstrings for all functions and classes
- Include parameter descriptions and return value documentation
- Add inline comments for complex logic
- Follow standard documentation conventions (Google/NumPy style)
- Explain the purpose and usage of each component
- Include examples where helpful

RESPONSE FORMAT: Return the code with complete documentation added.""",
            "context_required": True
        },

        "localize": {
            "template": """You are an internationalization (i18n) specialist.

TASK: Prepare the following code for internationalization and localization.

ORIGINAL CODE:
{file_context}

USER REQUEST: {prompt}

LOCALIZATION REQUIREMENTS:
- Extract all user-facing strings into translatable resources
- Implement proper i18n framework integration
- Handle date, time, and number formatting
- Support right-to-left (RTL) languages where applicable
- Use locale-aware string operations
- Add translation keys and resource files

RESPONSE FORMAT: Return the internationalized code with proper i18n implementation.""",
            "context_required": True
        },

        "test": {
            "template": """You are a test automation engineer specializing in comprehensive test coverage.

TASK: Generate thorough unit tests for the following code.

CODE TO TEST:
{file_context}

TEST REQUIREMENTS:
- Create comprehensive unit tests covering all functions and methods
- Include edge cases and error conditions
- Test both positive and negative scenarios
- Use appropriate testing framework (pytest, unittest, Jest, etc.)
- Include setup and teardown methods where needed
- Aim for high code coverage
- Add clear test descriptions and assertions

RESPONSE FORMAT: Return complete test code with multiple test cases.""",
            "context_required": True
        },

        "explain": {
            "template": """You are a code mentor providing clear, educational explanations.

TASK: Explain how the following code works in detail.

CODE TO EXPLAIN:
{file_context}

EXPLANATION REQUIREMENTS:
- Break down the code step by step
- Explain the purpose and logic of each component
- Describe data flow and control flow
- Highlight important design patterns or algorithms
- Explain any complex or non-obvious parts
- Use clear, beginner-friendly language
- Include examples of how the code would execute

RESPONSE FORMAT: Provide a detailed, educational explanation of the code.""",
            "context_required": True
        },

        "debug": {
            "template": """You are a debugging expert specializing in identifying and fixing code issues.

TASK: Identify and fix bugs or logic errors in the following code.

PROBLEMATIC CODE:
{file_context}

USER REPORT: {prompt}

DEBUGGING APPROACH:
- Analyze the code for logical errors, syntax issues, and runtime problems
- Identify potential edge cases that could cause failures
- Look for common bug patterns (null pointer, off-by-one, race conditions)
- Check error handling and exception management
- Verify input validation and boundary conditions
- Fix any identified issues

RESPONSE FORMAT: Return the corrected code with bugs fixed and explanations of what was wrong.""",
            "context_required": True
        },

        "transform": {
            "template": """You are a code transformation specialist.

TASK: Convert or migrate the following code according to the specified requirements.

ORIGINAL CODE:
{file_context}

TRANSFORMATION REQUEST: {prompt}

TRANSFORMATION GUIDELINES:
- Maintain the original functionality while adapting to new requirements
- Follow best practices for the target language/framework
- Ensure proper error handling in the transformed code
- Update dependencies and imports as needed
- Adapt coding style to target conventions
- Preserve important comments and documentation

RESPONSE FORMAT: Return the transformed code that meets the specified requirements.""",
            "context_required": True
        },

        "security": {
            "template": """You are a cybersecurity expert specializing in secure coding practices.

TASK: Improve the security of the following code.

CODE TO SECURE:
{file_context}

USER REQUEST: {prompt}

SECURITY REQUIREMENTS:
- Identify and fix security vulnerabilities
- Implement proper input validation and sanitization
- Add authentication and authorization checks
- Secure sensitive data handling (encryption, hashing)
- Prevent common attacks (SQL injection, XSS, CSRF)
- Follow security best practices and standards
- Add proper error handling without information leakage

RESPONSE FORMAT: Return the secured code with security improvements implemented.""",
            "context_required": True
        },

        "feature": {
            "template": """You are a feature development specialist.

TASK: Add new functionality to the existing codebase.

EXISTING CODE:
{file_context}

FEATURE REQUEST: {prompt}

DEVELOPMENT REQUIREMENTS:
- Integrate the new feature seamlessly with existing code
- Follow established code patterns and architecture
- Implement proper error handling and validation
- Add appropriate logging and monitoring
- Ensure the feature is scalable and maintainable
- Include necessary imports and dependencies
- Follow coding standards and best practices

RESPONSE FORMAT: Return the enhanced code with the new feature implemented.""",
            "context_required": True
        }
    }

    def __init__(self):
        """Initialize the prompt shaper."""
        pass

    def shape_prompt_by_intent(self, prompt: str, intent: str, file_context: str = "",
                              file_path: str = "", additional_context: Dict = None,
                              project_root: str = "") -> str:
        """
        Rewrite the prompt based on the detected intent, with project template support.

        Args:
            prompt: The original user prompt
            intent: The detected intent category
            file_context: The content of the file being processed
            file_path: Path to the file (for additional context)
            additional_context: Extra context information
            project_root: Root directory of the project (for loading project templates)

        Returns:
            The reshaped, intent-specific prompt
        """
        # Step 1: Try to find project root if not provided
        if not project_root and file_path:
            project_root = find_project_root(file_path)

        # Step 2: Load project-specific templates
        project_templates = load_project_templates(project_root)

        # Step 3: Check if project has a template for this intent
        if intent in project_templates:
            logger.info(f"Using project template for intent '{intent}' from {project_root}")
            return apply_template(project_templates[intent], file_context, prompt)

        # Step 4: Check if project has a default template
        if "default" in project_templates:
            logger.info(f"Using project default template from {project_root}")
            return apply_template(project_templates["default"], file_context, prompt)

        # Step 5: Fallback to built-in global templates
        logger.debug(f"Using built-in template for intent '{intent}'")
        return self._apply_builtin_template(prompt, intent, file_context, file_path, additional_context)

    def _apply_builtin_template(self, prompt: str, intent: str, file_context: str = "",
                               file_path: str = "", additional_context: Dict = None) -> str:
        """Apply built-in global templates (original logic)."""
        # Get the template for this intent
        template_config = self.INTENT_TEMPLATES.get(intent)

        if not template_config:
            # Fallback for unknown intents
            return self._create_generic_prompt(prompt, file_context, file_path)

        template = template_config["template"]
        context_required = template_config.get("context_required", False)

        # If context is required but not provided, try to create a meaningful prompt anyway
        if context_required and not file_context.strip():
            file_context = f"# File: {file_path}\n# (File content not available - working with user prompt only)"

        # Format the template with provided information
        try:
            shaped_prompt = template.format(
                prompt=prompt,
                file_context=file_context,
                file_path=file_path or "unknown"
            )

            # Add additional context if provided
            if additional_context:
                context_info = self._format_additional_context(additional_context)
                shaped_prompt += f"\n\nADDITIONAL CONTEXT:\n{context_info}"

            return shaped_prompt

        except KeyError:
            # Fallback if template formatting fails
            return self._create_generic_prompt(prompt, file_context, file_path)

    def _create_generic_prompt(self, prompt: str, file_context: str = "", file_path: str = "") -> str:
        """Create a generic prompt when intent-specific shaping fails."""

        if file_context.strip():
            return f"""You are an expert software engineer.

TASK: {prompt}

CODE CONTEXT:
{file_context}

REQUIREMENTS:
- Provide high-quality, production-ready code
- Follow best practices and coding standards
- Include proper error handling
- Write clean, maintainable code
- Add comments where helpful

RESPONSE FORMAT: Return the requested code or solution."""
        else:
            return f"""You are an expert software engineer.

TASK: {prompt}

REQUIREMENTS:
- Provide high-quality, production-ready code
- Follow best practices and coding standards
- Include proper error handling
- Write clean, maintainable code
- Add comments where helpful

RESPONSE FORMAT: Return the requested code or solution."""

    def _format_additional_context(self, context: Dict) -> str:
        """Format additional context information into readable text."""
        formatted_lines = []

        for key, value in context.items():
            if isinstance(value, (list, tuple)):
                formatted_lines.append(f"- {key}: {', '.join(map(str, value))}")
            else:
                formatted_lines.append(f"- {key}: {value}")

        return '\n'.join(formatted_lines)

    def get_shaped_prompt_preview(self, prompt: str, intent: str, max_length: int = 200) -> str:
        """
        Get a preview of how the prompt would be shaped.

        Args:
            prompt: The original user prompt
            intent: The detected intent
            max_length: Maximum length of the preview

        Returns:
            A truncated preview of the shaped prompt
        """
        # Create a minimal shaped prompt for preview
        template_config = self.INTENT_TEMPLATES.get(intent)

        if not template_config:
            preview = f"Generic prompt: {prompt}"
        else:
            # Extract the first few lines of the template
            template_lines = template_config["template"].split('\n')
            preview_lines = []

            for line in template_lines[:5]:  # First 5 lines
                if '{prompt}' in line:
                    line = line.replace('{prompt}', prompt)
                if '{file_context}' in line:
                    line = line.replace('{file_context}', '[FILE_CONTENT]')
                preview_lines.append(line)

            preview = '\n'.join(preview_lines)

        # Truncate if too long
        if len(preview) > max_length:
            preview = preview[:max_length] + "..."

        return preview


# Global shaper instance
_shaper = PromptShaper()


def shape_prompt_by_intent(prompt: str, intent: str, file_context: str = "",
                          file_path: str = "", additional_context: Dict = None,
                          project_root: str = "") -> str:
    """
    Rewrite the prompt based on the detected intent, with project template support.

    Args:
        prompt: The original user prompt
        intent: The detected intent category
        file_context: The content of the file being processed
        file_path: Path to the file (for additional context)
        additional_context: Extra context information
        project_root: Root directory of the project (for loading project templates)

    Returns:
        The reshaped, intent-specific prompt

    Examples:
        >>> shape_prompt_by_intent("make this faster", "optimize", "def slow_func()...")
        'You are a performance optimization expert...'
        >>> shape_prompt_by_intent("add tests", "test", "class Calculator...")
        'You are a test automation engineer...'
        >>> # With project template
        >>> shape_prompt_by_intent("refactor", "refactor", "def func():", project_root="/my/project")
        'Please refactor the following code for readability:\\n\\ndef func():\\n\\nInstructions: refactor'
    """
    return _shaper.shape_prompt_by_intent(prompt, intent, file_context, file_path, additional_context, project_root)


def get_shaped_prompt_preview(prompt: str, intent: str, max_length: int = 200) -> str:
    """
    Get a preview of how the prompt would be shaped.

    Args:
        prompt: The original user prompt
        intent: The detected intent
        max_length: Maximum length of the preview

    Returns:
        A truncated preview of the shaped prompt
    """
    return _shaper.get_shaped_prompt_preview(prompt, intent, max_length)


def get_available_intent_templates() -> Dict[str, Dict]:
    """
    Get information about available intent templates.

    Returns:
        Dictionary mapping intent names to template information
    """
    return {
        intent: {
            "context_required": config.get("context_required", False),
            "description": config["template"].split('\n')[2].replace("TASK: ", "")
        }
        for intent, config in _shaper.INTENT_TEMPLATES.items()
    }


def create_project_template_file(project_root: str, templates: Dict[str, str] = None) -> bool:
    """
    Create a codecrusher.prompts.json file in the project root.

    Args:
        project_root: Root directory of the project
        templates: Optional dictionary of templates to write (uses defaults if None)

    Returns:
        True if file was created successfully, False otherwise
    """
    if not project_root or not os.path.isdir(project_root):
        logger.error(f"Invalid project root directory: {project_root}")
        return False

    # Default project templates
    if templates is None:
        templates = {
            "refactor": "Please refactor the following code for readability:\n\n{{code}}\n\nInstructions: {{prompt}}",
            "document": "Add documentation and comments:\n\n{{code}}",
            "optimize": "Speed this up:\n\n{{code}}\n\nOptimization request: {{prompt}}",
            "test": "Generate comprehensive tests for:\n\n{{code}}\n\nTest requirements: {{prompt}}",
            "default": "{{prompt}}"
        }

    template_path = os.path.join(project_root, "codecrusher.prompts.json")

    try:
        with open(template_path, "w", encoding="utf-8") as f:
            json.dump(templates, f, indent=2, ensure_ascii=False)

        logger.info(f"Created project template file at {template_path}")
        return True

    except Exception as e:
        logger.error(f"Failed to create project template file at {template_path}: {e}")
        return False


def get_project_template_info(project_root: str) -> Dict[str, any]:
    """
    Get information about project templates.

    Args:
        project_root: Root directory of the project

    Returns:
        Dictionary with template information
    """
    templates = load_project_templates(project_root)
    template_path = os.path.join(project_root, "codecrusher.prompts.json")

    return {
        "has_templates": bool(templates),
        "template_count": len(templates),
        "template_path": template_path,
        "template_exists": os.path.exists(template_path),
        "available_intents": list(templates.keys()) if templates else [],
        "has_default": "default" in templates
    }


def validate_project_templates(project_root: str) -> Dict[str, any]:
    """
    Validate project templates and return validation results.

    Args:
        project_root: Root directory of the project

    Returns:
        Dictionary with validation results
    """
    template_path = os.path.join(project_root, "codecrusher.prompts.json")

    if not os.path.exists(template_path):
        return {
            "valid": True,
            "message": "No project templates file found (using global templates)",
            "warnings": [],
            "errors": []
        }

    warnings = []
    errors = []

    try:
        templates = load_project_templates(project_root)

        if not templates:
            errors.append("Template file exists but is empty or invalid")
        else:
            # Check for common template issues
            for intent, template in templates.items():
                if not isinstance(template, str):
                    errors.append(f"Template '{intent}' is not a string")
                elif not template.strip():
                    warnings.append(f"Template '{intent}' is empty")
                elif "{{code}}" not in template and "{{prompt}}" not in template:
                    warnings.append(f"Template '{intent}' doesn't use {{code}} or {{prompt}} variables")

            # Check for recommended templates
            recommended_intents = ["refactor", "document", "optimize", "test", "default"]
            missing_recommended = [intent for intent in recommended_intents if intent not in templates]
            if missing_recommended:
                warnings.append(f"Missing recommended templates: {', '.join(missing_recommended)}")

        return {
            "valid": len(errors) == 0,
            "message": "Templates validated successfully" if len(errors) == 0 else f"Found {len(errors)} errors",
            "warnings": warnings,
            "errors": errors,
            "template_count": len(templates)
        }

    except Exception as e:
        return {
            "valid": False,
            "message": f"Failed to validate templates: {e}",
            "warnings": [],
            "errors": [str(e)]
        }
