"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeCrusherApi = void 0;
const axios_1 = require("axios");
const vscode = require("vscode");
// Base URL for the CodeCrusher API
const API_BASE_URL = 'http://localhost:9000/api';
// API client class
class CodeCrusherApi {
    /**
     * Get status information
     */
    static getStatus(since = '24h', model) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const params = { since };
                if (model) {
                    params.model = model;
                }
                const response = yield axios_1.default.get(`${API_BASE_URL}/status`, { params });
                return response.data;
            }
            catch (error) {
                this.handleApiError('Failed to fetch status', error);
                throw error;
            }
        });
    }
    /**
     * Run scan for anomalies
     */
    static runScan(since = '24h', model, tag, limit = 10, verbose = false) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const params = {
                    since,
                    limit,
                    verbose
                };
                if (model) {
                    params.model = model;
                }
                if (tag) {
                    params.tag = tag;
                }
                const response = yield axios_1.default.get(`${API_BASE_URL}/scan`, { params });
                return response.data;
            }
            catch (error) {
                this.handleApiError('Failed to run scan', error);
                throw error;
            }
        });
    }
    /**
     * Get trends data
     */
    static getTrends(since = '7d', until = 'now', model, tag) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const params = {
                    since,
                    until
                };
                if (model) {
                    params.model = model;
                }
                if (tag) {
                    params.tag = tag;
                }
                const response = yield axios_1.default.get(`${API_BASE_URL}/trends`, { params });
                return response.data;
            }
            catch (error) {
                this.handleApiError('Failed to fetch trends', error);
                throw error;
            }
        });
    }
    /**
     * Get telemetry data
     */
    static getTelemetry(limit = 100, since, model, tag, cached, fallback, error) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const params = { limit };
                if (since) {
                    params.since = since;
                }
                if (model) {
                    params.model = model;
                }
                if (tag) {
                    params.tag = tag;
                }
                if (cached !== undefined) {
                    params.cached = cached;
                }
                if (fallback !== undefined) {
                    params.fallback = fallback;
                }
                if (error !== undefined) {
                    params.error = error;
                }
                const response = yield axios_1.default.get(`${API_BASE_URL}/telemetry`, { params });
                return response.data;
            }
            catch (error) {
                this.handleApiError('Failed to fetch telemetry', error);
                return { entries: [], count: 0 };
            }
        });
    }
    /**
     * Check if the API server is running
     */
    static isServerRunning() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                yield axios_1.default.get(`${API_BASE_URL}/status`, { timeout: 1000 });
                return true;
            }
            catch (error) {
                return false;
            }
        });
    }
    /**
     * Handle API errors
     */
    static handleApiError(message, error) {
        console.error(`${message}:`, error);
        if (axios_1.default.isAxiosError(error)) {
            if (error.code === 'ECONNREFUSED') {
                vscode.window.showErrorMessage('Cannot connect to CodeCrusher API server. Make sure it\'s running with: codecrusher serve run');
            }
            else if (error.response) {
                vscode.window.showErrorMessage(`CodeCrusher API error (${error.response.status}): ${error.response.data.detail || 'Unknown error'}`);
            }
            else {
                vscode.window.showErrorMessage(`CodeCrusher API error: ${error.message}`);
            }
        }
        else {
            vscode.window.showErrorMessage(`CodeCrusher API error: ${error}`);
        }
    }
}
exports.CodeCrusherApi = CodeCrusherApi;
//# sourceMappingURL=api.js.map