"""
AugmentCode Surgical Implementation - STEP 4: Auto-Adjust Prompts Based on Historical Ratings
Intelligent prompt shaping system that learns from previous weak vs. strong injections
"""

import logging
from typing import List, Dict, Optional, Tuple
from collections import defaultdict, Counter

# Import AugmentCode SQLite log store
try:
    from log_store import InjectionLogStore
    LOGGING_AVAILABLE = True
except ImportError:
    LOGGING_AVAILABLE = False
    logging.warning("AugmentCode log store not available - prompt shaping disabled")


def adapt_prompt(base_prompt: str, filename: str, tags: List[str]) -> str:
    """
    Adapt prompt based on historical ratings for file and tags.
    Now includes manual tuning configuration from config.py.

    Args:
        base_prompt (str): Original prompt text
        filename (str): Target filename for context
        tags (List[str]): List of tags for context matching

    Returns:
        str: Adapted prompt with learned improvements
    """
    if not LOGGING_AVAILABLE:
        return base_prompt

    try:
        # Load manual tuning configuration
        config_settings = _load_tuning_config()

        # Check if shaping is disabled
        if config_settings.get('shaping_strength') == 'off':
            logging.debug("Prompt shaping disabled by configuration")
            return _apply_tone_modifier(base_prompt, config_settings.get('tone', 'neutral'))

        store = InjectionLogStore()

        # Get historical data for this file and tags
        file_logs = store.get_logs_by_file(filename) if filename else []
        tag_logs = []

        for tag in tags:
            tag_logs.extend(store.get_logs_by_tag(tag))

        # Combine and deduplicate logs
        all_logs = _deduplicate_logs(file_logs + tag_logs)

        # Filter logs with ratings
        rated_logs = [log for log in all_logs if log.get('rating') is not None]

        if not rated_logs:
            logging.info(f"No historical ratings found for {filename} or tags {tags}")
            return _apply_tone_modifier(base_prompt, config_settings.get('tone', 'neutral'))

        # Extract trends and patterns
        trends = extract_trend(rated_logs)

        # Apply adaptive shaping based on trends and configuration
        adapted_prompt = _apply_shaping_rules(base_prompt, trends, rated_logs, config_settings)

        if adapted_prompt != base_prompt:
            logging.info(f"Prompt adapted based on {len(rated_logs)} historical ratings")
            logging.debug(f"Original: {base_prompt}")
            logging.debug(f"Adapted: {adapted_prompt}")
        else:
            logging.debug(f"No adaptation applied. Pattern: {trends.get('pattern')}, Confidence: {trends.get('confidence'):.2f}")

        return adapted_prompt

    except Exception as e:
        logging.warning(f"Failed to adapt prompt: {e}")
        return base_prompt


def extract_trend(rated_logs: List[Dict]) -> Dict:
    """
    Extract trends and patterns from historical ratings.

    Args:
        rated_logs (List[Dict]): List of logs with ratings

    Returns:
        Dict: Trend analysis with patterns and recommendations
    """
    if not rated_logs:
        return {"pattern": "no_data", "confidence": 0.0}

    # Sort by timestamp (most recent first)
    sorted_logs = sorted(rated_logs, key=lambda x: x.get('timestamp', ''), reverse=True)

    # Analyze rating patterns
    ratings = [log['rating'] for log in sorted_logs]
    recent_ratings = ratings[:3]  # Last 3 ratings

    # Calculate statistics
    avg_rating = sum(ratings) / len(ratings)
    recent_avg = sum(recent_ratings) / len(recent_ratings) if recent_ratings else 0

    # Analyze feedback patterns
    feedback_patterns = _analyze_feedback_patterns(sorted_logs)

    # Determine trend pattern
    pattern = _determine_pattern(ratings, recent_ratings, avg_rating, recent_avg)

    # Calculate confidence based on data volume
    confidence = min(1.0, len(rated_logs) / 5.0)  # Full confidence with 5+ ratings

    return {
        "pattern": pattern,
        "confidence": confidence,
        "avg_rating": avg_rating,
        "recent_avg": recent_avg,
        "total_samples": len(rated_logs),
        "recent_samples": len(recent_ratings),
        "feedback_patterns": feedback_patterns,
        "ratings_history": ratings[:10]  # Last 10 ratings for context
    }


def _deduplicate_logs(logs: List[Dict]) -> List[Dict]:
    """Remove duplicate logs based on ID."""
    seen_ids = set()
    unique_logs = []

    for log in logs:
        log_id = log.get('id')
        if log_id and log_id not in seen_ids:
            seen_ids.add(log_id)
            unique_logs.append(log)

    return unique_logs


def _analyze_feedback_patterns(logs: List[Dict]) -> Dict:
    """Analyze common patterns in user feedback."""
    feedback_texts = [log.get('feedback', '') for log in logs if log.get('feedback')]

    if not feedback_texts:
        return {"common_issues": [], "positive_patterns": []}

    # Common negative feedback patterns
    negative_keywords = {
        "unclear": ["unclear", "confusing", "vague", "ambiguous"],
        "incomplete": ["incomplete", "missing", "partial", "unfinished"],
        "error": ["error", "bug", "broken", "fail", "exception"],
        "performance": ["slow", "inefficient", "performance", "optimize"],
        "style": ["style", "format", "convention", "inconsistent"]
    }

    # Common positive feedback patterns
    positive_keywords = {
        "clear": ["clear", "precise", "exact", "specific"],
        "complete": ["complete", "thorough", "comprehensive", "full"],
        "good": ["good", "great", "excellent", "perfect", "nice"],
        "helpful": ["helpful", "useful", "effective", "works"]
    }

    # Count patterns
    negative_counts = defaultdict(int)
    positive_counts = defaultdict(int)

    for feedback in feedback_texts:
        feedback_lower = feedback.lower()

        for category, keywords in negative_keywords.items():
            if any(keyword in feedback_lower for keyword in keywords):
                negative_counts[category] += 1

        for category, keywords in positive_keywords.items():
            if any(keyword in feedback_lower for keyword in keywords):
                positive_counts[category] += 1

    return {
        "common_issues": dict(negative_counts),
        "positive_patterns": dict(positive_counts)
    }


def _determine_pattern(ratings: List[int], recent_ratings: List[int],
                      avg_rating: float, recent_avg: float) -> str:
    """Determine the overall trend pattern."""
    if len(ratings) < 2:
        return "insufficient_data"

    # Check for consistent high performance
    if avg_rating >= 4.0 and recent_avg >= 4.0:
        return "consistently_high"

    # Check for recent decline
    if avg_rating >= 3.5 and recent_avg <= 2.5:
        return "recent_decline"

    # Check for improvement trend
    if len(ratings) >= 3:
        first_half_avg = sum(ratings[len(ratings)//2:]) / (len(ratings) - len(ratings)//2)
        second_half_avg = sum(ratings[:len(ratings)//2]) / (len(ratings)//2)

        if second_half_avg > first_half_avg + 0.5:
            return "improving"

    # Check for consistent low performance
    if avg_rating <= 2.5 and recent_avg <= 2.5:
        return "consistently_low"

    # Check for volatility
    rating_variance = sum((r - avg_rating) ** 2 for r in ratings) / len(ratings)
    if rating_variance > 1.5:
        return "volatile"

    return "stable"


def _load_tuning_config() -> Dict:
    """
    Load manual tuning configuration from config.py.

    Returns:
        Dict: Configuration settings with defaults
    """
    try:
        import config
        return config.load_config()
    except ImportError:
        logging.debug("Config module not available, using defaults")
        return {
            'tone': 'neutral',
            'fallback_sensitivity': 'medium',
            'shaping_strength': 'smart'
        }
    except Exception as e:
        logging.warning(f"Failed to load tuning config: {e}")
        return {
            'tone': 'neutral',
            'fallback_sensitivity': 'medium',
            'shaping_strength': 'smart'
        }


def _apply_tone_modifier(prompt: str, tone: str) -> str:
    """
    Apply tone modifier to prompt based on configuration.

    Args:
        prompt (str): Original prompt
        tone (str): Tone setting from configuration

    Returns:
        str: Prompt with tone modifier applied
    """
    tone_modifiers = {
        'neutral': '',
        'friendly': 'Please ',
        'assertive': 'Ensure you ',
        'formal': 'It is required that you '
    }

    modifier = tone_modifiers.get(tone, '')
    if modifier and not prompt.lower().startswith(modifier.lower()):
        return modifier + prompt.lower()
    return prompt


def _apply_shaping_rules(base_prompt: str, trends: Dict, rated_logs: List[Dict], config_settings: Dict = None) -> str:
    """Apply shaping rules based on trend analysis and manual configuration."""
    if config_settings is None:
        config_settings = _load_tuning_config()

    pattern = trends.get("pattern", "no_data")
    confidence = trends.get("confidence", 0.0)
    feedback_patterns = trends.get("feedback_patterns", {})

    logging.debug(f"Applying shaping rules: pattern={pattern}, confidence={confidence:.2f}")

    # Get configuration-based thresholds
    fallback_sensitivity = config_settings.get('fallback_sensitivity', 'medium')
    shaping_strength = config_settings.get('shaping_strength', 'smart')
    tone = config_settings.get('tone', 'neutral')

    # Map sensitivity to confidence thresholds
    sensitivity_thresholds = {
        'low': 0.1,     # Very permissive
        'medium': 0.2,  # Balanced
        'high': 0.4     # Conservative
    }

    # Map strength to multipliers
    strength_multipliers = {
        'off': 0.0,      # Disabled (handled earlier)
        'soft': 0.5,     # Gentle modifications
        'smart': 1.0,    # Default intelligent shaping
        'aggressive': 2.0 # Strong modifications
    }

    threshold = sensitivity_thresholds.get(fallback_sensitivity, 0.2)
    multiplier = strength_multipliers.get(shaping_strength, 1.0)

    # Only apply shaping if we have sufficient confidence
    if confidence < threshold:
        logging.debug(f"Insufficient confidence ({confidence:.2f}) for prompt shaping (threshold: {threshold})")
        return _apply_tone_modifier(base_prompt, tone)

    shaped_prompt = base_prompt

    # Apply pattern-specific shaping with strength multiplier
    if pattern == "recent_decline" or pattern == "consistently_low":
        logging.debug(f"Applying improvement shaping for pattern: {pattern} (strength: {multiplier})")
        shaped_prompt = _apply_improvement_shaping(shaped_prompt, feedback_patterns, multiplier)

    elif pattern == "volatile":
        logging.debug(f"Applying stability shaping for pattern: {pattern} (strength: {multiplier})")
        shaped_prompt = _apply_stability_shaping(shaped_prompt, multiplier)

    elif pattern == "consistently_high":
        logging.debug(f"Maintaining original prompt for high performance pattern: {pattern}")
        # For high-performing contexts, maintain current approach but apply tone
        return _apply_tone_modifier(base_prompt, tone)

    # Apply feedback-specific improvements
    if shaped_prompt == base_prompt:  # Only if no pattern-specific shaping was applied
        logging.debug("Applying feedback-specific shaping")
        shaped_prompt = _apply_feedback_shaping(shaped_prompt, feedback_patterns, multiplier)

    # Apply tone modifier to final result
    shaped_prompt = _apply_tone_modifier(shaped_prompt, tone)

    logging.debug(f"Final shaped prompt: {shaped_prompt}")
    return shaped_prompt


def _apply_improvement_shaping(prompt: str, feedback_patterns: Dict, multiplier: float = 1.0) -> str:
    """Apply shaping for low-performing contexts with configurable strength."""
    common_issues = feedback_patterns.get("common_issues", {})

    improvements = []

    if common_issues.get("unclear", 0) > 0:
        if multiplier >= 1.0:
            improvements.append("Make the changes clearer and more precise")
        else:
            improvements.append("Clarify the implementation")

    if common_issues.get("incomplete", 0) > 0:
        if multiplier >= 1.0:
            improvements.append("Ensure the implementation is complete and thorough")
        else:
            improvements.append("Complete the implementation")

    if common_issues.get("error", 0) > 0:
        if multiplier >= 1.5:
            improvements.append("Include comprehensive error handling and validation")
        elif multiplier >= 1.0:
            improvements.append("Include proper error handling")
        else:
            improvements.append("Add error handling")

    if common_issues.get("performance", 0) > 0:
        if multiplier >= 1.0:
            improvements.append("Focus on performance optimization")
        else:
            improvements.append("Consider performance")

    if common_issues.get("style", 0) > 0:
        if multiplier >= 1.0:
            improvements.append("Follow consistent coding style and conventions")
        else:
            improvements.append("Follow coding conventions")

    if improvements:
        improvement_text = ". ".join(improvements)
        return f"{prompt}. {improvement_text}."

    # Generic improvement for low ratings with strength adjustment
    if multiplier >= 1.5:
        return f"{prompt}. Make the changes clearer and more precise. Avoid vague suggestions and ensure comprehensive implementation."
    elif multiplier >= 1.0:
        return f"{prompt}. Make the changes clearer and more precise. Avoid vague suggestions."
    else:
        return f"{prompt}. Please clarify the implementation."


def _apply_stability_shaping(prompt: str, multiplier: float = 1.0) -> str:
    """Apply shaping for volatile performance contexts with configurable strength."""
    if multiplier >= 1.5:
        return f"{prompt}. Focus on consistent, reliable solutions with clear documentation and comprehensive testing."
    elif multiplier >= 1.0:
        return f"{prompt}. Focus on consistent, reliable solutions with clear documentation."
    else:
        return f"{prompt}. Ensure consistent implementation."


def _apply_feedback_shaping(prompt: str, feedback_patterns: Dict, multiplier: float = 1.0) -> str:
    """Apply shaping based on specific feedback patterns with configurable strength."""
    common_issues = feedback_patterns.get("common_issues", {})

    # If no specific issues, return as-is
    if not common_issues:
        return prompt

    # Find the most common issue
    top_issue = max(common_issues.items(), key=lambda x: x[1])[0] if common_issues else None

    if top_issue == "unclear":
        if multiplier >= 1.5:
            return f"{prompt}. Provide clear, specific implementation details with comprehensive explanations."
        elif multiplier >= 1.0:
            return f"{prompt}. Provide clear, specific implementation details."
        else:
            return f"{prompt}. Be more specific."
    elif top_issue == "incomplete":
        if multiplier >= 1.5:
            return f"{prompt}. Ensure complete implementation with all necessary components and edge cases."
        elif multiplier >= 1.0:
            return f"{prompt}. Ensure complete implementation with all necessary components."
        else:
            return f"{prompt}. Complete the implementation."
    elif top_issue == "error":
        if multiplier >= 1.5:
            return f"{prompt}. Include comprehensive error handling and edge case management with proper validation."
        elif multiplier >= 1.0:
            return f"{prompt}. Include comprehensive error handling and edge case management."
        else:
            return f"{prompt}. Add error handling."
    elif top_issue == "performance":
        if multiplier >= 1.5:
            return f"{prompt}. Prioritize performance optimization and efficiency with detailed benchmarking."
        elif multiplier >= 1.0:
            return f"{prompt}. Prioritize performance optimization and efficiency."
        else:
            return f"{prompt}. Consider performance."
    elif top_issue == "style":
        if multiplier >= 1.5:
            return f"{prompt}. Follow established coding conventions and best practices with consistent formatting."
        elif multiplier >= 1.0:
            return f"{prompt}. Follow established coding conventions and best practices."
        else:
            return f"{prompt}. Follow coding standards."

    return prompt


# Helper function for testing and debugging
def get_shaping_stats(filename: str = None, tags: List[str] = None) -> Dict:
    """Get statistics about prompt shaping for debugging."""
    if not LOGGING_AVAILABLE:
        return {"error": "Logging not available"}

    try:
        store = InjectionLogStore()

        # Get logs
        all_logs = []
        if filename:
            all_logs.extend(store.get_logs_by_file(filename))
        if tags:
            for tag in tags:
                all_logs.extend(store.get_logs_by_tag(tag))

        all_logs = _deduplicate_logs(all_logs)
        rated_logs = [log for log in all_logs if log.get('rating') is not None]

        if not rated_logs:
            return {"total_logs": len(all_logs), "rated_logs": 0, "trends": None}

        trends = extract_trend(rated_logs)

        return {
            "total_logs": len(all_logs),
            "rated_logs": len(rated_logs),
            "trends": trends,
            "filename": filename,
            "tags": tags
        }

    except Exception as e:
        return {"error": str(e)}
