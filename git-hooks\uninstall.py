#!/usr/bin/env python3
"""
Uninstall CodeCrusher Git hooks.

This script uninstalls the CodeCrusher Git hooks from the current Git repository.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def get_git_hooks_dir():
    """Get the Git hooks directory for the current repository."""
    result = subprocess.run(
        ["git", "rev-parse", "--git-dir"],
        capture_output=True,
        text=True
    )
    
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        print("Are you in a Git repository?")
        return None
    
    git_dir = result.stdout.strip()
    hooks_dir = os.path.join(git_dir, "hooks")
    
    return hooks_dir

def uninstall_hook(hooks_dir, hook_name):
    """Uninstall a Git hook."""
    # Hook path
    hook_path = os.path.join(hooks_dir, hook_name)
    
    # Check if hook exists
    if not os.path.exists(hook_path):
        print(f"Hook {hook_name} not found.")
        return
    
    # Check if backup exists
    backup_path = f"{hook_path}.bak"
    
    if os.path.exists(backup_path):
        # Restore backup
        shutil.copy2(backup_path, hook_path)
        os.remove(backup_path)
        print(f"Restored backup of {hook_name} hook.")
    else:
        # Remove hook
        os.remove(hook_path)
        print(f"Removed {hook_name} hook.")

def main():
    print("Uninstalling CodeCrusher Git hooks...")
    
    # Get Git hooks directory
    hooks_dir = get_git_hooks_dir()
    
    if not hooks_dir:
        return 1
    
    # Uninstall hooks
    uninstall_hook(hooks_dir, "pre-commit")
    uninstall_hook(hooks_dir, "post-commit")
    
    print("CodeCrusher Git hooks uninstalled successfully.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
