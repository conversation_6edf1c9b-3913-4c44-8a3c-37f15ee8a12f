Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8FAD30000 ntdll.dll
7FF8F9350000 KERNEL32.DLL
7FF8F7FF0000 KERNELBASE.dll
7FF8F9F50000 USER32.dll
7FF8F8990000 win32u.dll
7FF8FACC0000 GDI32.dll
7FF8F83D0000 gdi32full.dll
7FF8F8500000 msvcp_win.dll
7FF8F7EA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8F8C90000 advapi32.dll
7FF8F9DF0000 msvcrt.dll
7FF8F8D50000 sechost.dll
7FF8F7FC0000 bcrypt.dll
7FF8F9210000 RPCRT4.dll
7FF8F75C0000 CRYPTBASE.DLL
7FF8F7E20000 bcryptPrimitives.dll
7FF8FA110000 IMM32.DLL
