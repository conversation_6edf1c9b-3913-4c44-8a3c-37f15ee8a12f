"""
Debug AugmentCode STEP 4 - Intelligent Prompt Shaping System
"""

import os
import sys
import logging
from pathlib import Path

# Set up debug logging
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

# Add the parent directory to the path to import modules
sys.path.insert(0, str(Path(__file__).parent))

def debug_prompt_shaping():
    """Debug the prompt shaping functionality with detailed logging"""
    
    print("🔍 Debugging AugmentCode STEP 4 - Intelligent Prompt Shaping...")
    
    try:
        from log_store import InjectionLogStore
        from prompt_shaper import adapt_prompt, extract_trend, get_shaping_stats
        
        # Clean up any existing test database
        test_db = "debug_prompt_shaper.db"
        if os.path.exists(test_db):
            os.remove(test_db)
        
        # Initialize store and create test data
        store = InjectionLogStore(test_db)
        
        # Create test scenarios with low ratings
        test_scenarios = [
            {
                "filename": "auth_service.py",
                "injection_type": "bugfix",
                "model": "mixtral",
                "prompt": "Fix authentication bug",
                "output": "Added basic fix",
                "tags": ["auth", "bugfix"],
                "rating": 2,
                "feedback": "Incomplete implementation, missing error handling"
            },
            {
                "filename": "auth_service.py",
                "injection_type": "bugfix",
                "model": "mixtral",
                "prompt": "Fix authentication bug",
                "output": "Added another fix",
                "tags": ["auth", "bugfix"],
                "rating": 1,
                "feedback": "Still unclear and confusing implementation"
            }
        ]
        
        # Add test data to store
        log_ids = []
        for scenario in test_scenarios:
            log_id = store.log_injection(scenario)
            store.update_rating(log_id, scenario["rating"], scenario["feedback"])
            log_ids.append(log_id)
            print(f"Created log: {log_id[:8]}... with rating {scenario['rating']}")
        
        # Debug: Check what logs we have
        auth_logs = store.get_logs_by_file("auth_service.py")
        print(f"\nFound {len(auth_logs)} logs for auth_service.py")
        
        for log in auth_logs:
            print(f"  Log {log['id'][:8]}...: rating={log.get('rating')}, feedback='{log.get('feedback', '')[:50]}...'")
        
        # Debug: Extract trends
        rated_logs = [log for log in auth_logs if log.get('rating') is not None]
        print(f"\nFound {len(rated_logs)} rated logs")
        
        trends = extract_trend(rated_logs)
        print(f"\nTrend analysis:")
        print(f"  Pattern: {trends['pattern']}")
        print(f"  Confidence: {trends['confidence']:.2f}")
        print(f"  Avg Rating: {trends['avg_rating']:.1f}")
        print(f"  Recent Avg: {trends['recent_avg']:.1f}")
        print(f"  Feedback patterns: {trends['feedback_patterns']}")
        
        # Debug: Test prompt adaptation
        base_prompt = "Fix the authentication issue"
        print(f"\nTesting prompt adaptation:")
        print(f"  Base prompt: {base_prompt}")
        
        adapted_prompt = adapt_prompt(base_prompt, "auth_service.py", ["auth", "bugfix"])
        print(f"  Adapted prompt: {adapted_prompt}")
        
        if adapted_prompt != base_prompt:
            print("  ✅ Prompt was successfully adapted!")
        else:
            print("  ❌ Prompt was not adapted")
        
        # Debug: Get shaping stats
        stats = get_shaping_stats("auth_service.py", ["auth", "bugfix"])
        print(f"\nShaping stats: {stats}")
        
        # Cleanup
        if os.path.exists(test_db):
            os.remove(test_db)
        
        return adapted_prompt != base_prompt
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_manual_trend_extraction():
    """Test trend extraction manually"""
    print("\n🔧 Testing manual trend extraction...")
    
    try:
        from prompt_shaper import extract_trend
        
        # Create manual test data
        test_logs = [
            {
                "id": "test1",
                "timestamp": "2024-01-01T10:00:00",
                "rating": 2,
                "feedback": "Incomplete implementation, missing error handling"
            },
            {
                "id": "test2", 
                "timestamp": "2024-01-01T11:00:00",
                "rating": 1,
                "feedback": "Still unclear and confusing implementation"
            }
        ]
        
        trends = extract_trend(test_logs)
        print(f"Manual trend analysis:")
        print(f"  Pattern: {trends['pattern']}")
        print(f"  Confidence: {trends['confidence']:.2f}")
        print(f"  Avg Rating: {trends['avg_rating']:.1f}")
        print(f"  Feedback patterns: {trends['feedback_patterns']}")
        
        return trends['pattern'] in ['consistently_low', 'recent_decline']
        
    except Exception as e:
        print(f"❌ Manual trend test failed: {e}")
        return False


def test_shaping_rules():
    """Test shaping rules directly"""
    print("\n⚙️ Testing shaping rules directly...")
    
    try:
        from prompt_shaper import _apply_shaping_rules
        
        # Create test trends
        test_trends = {
            "pattern": "consistently_low",
            "confidence": 0.8,
            "avg_rating": 1.5,
            "recent_avg": 1.5,
            "feedback_patterns": {
                "common_issues": {"unclear": 2, "incomplete": 1},
                "positive_patterns": {}
            }
        }
        
        base_prompt = "Fix the authentication issue"
        shaped_prompt = _apply_shaping_rules(base_prompt, test_trends, [])
        
        print(f"Direct shaping test:")
        print(f"  Base: {base_prompt}")
        print(f"  Shaped: {shaped_prompt}")
        
        return shaped_prompt != base_prompt
        
    except Exception as e:
        print(f"❌ Direct shaping test failed: {e}")
        return False


def main():
    """Run debug tests"""
    print("🚀 AugmentCode STEP 4 - Debug Prompt Shaping")
    print("="*50)
    
    tests = [
        ("Manual Trend Extraction", test_manual_trend_extraction),
        ("Direct Shaping Rules", test_shaping_rules),
        ("Full Prompt Shaping", debug_prompt_shaping)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # Display results
    print("\n" + "="*50)
    print("🎯 DEBUG RESULTS:")
    print("="*50)
    
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
    
    return 0


if __name__ == "__main__":
    exit(main())
