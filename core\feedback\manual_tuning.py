"""
Manual Tuning Panel
Allows users to edit internal memory and fine-tune AI behavior
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.prompt import Prompt, Confirm

logger = logging.getLogger(__name__)
console = Console()

@dataclass
class TuningConfig:
    """Configuration for manual tuning parameters"""
    fallback_sensitivity: float = 0.7
    style_preference: str = "minimal"
    max_retries: int = 3
    prefer_comments: bool = True
    model_escalation_threshold: int = 2
    prompt_enhancement_level: str = "moderate"
    confidence_threshold: float = 0.5
    auto_refine_enabled: bool = True
    memory_lookup_depth: int = 50
    pattern_learning_rate: float = 0.1
    template_optimization: bool = True
    export_detailed_logs: bool = False

class ManualTuningPanel:
    """Interactive panel for manual tuning of AI behavior"""
    
    def __init__(self, config_path: str = "data/manual_tuning.json"):
        self.config_path = Path(config_path)
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self.config = self._load_config()
    
    def _load_config(self) -> TuningConfig:
        """Load tuning configuration from file"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    data = json.load(f)
                    return TuningConfig(**data)
            except Exception as e:
                logger.warning(f"Failed to load tuning config: {e}")
        
        # Return default config
        return TuningConfig()
    
    def _save_config(self):
        """Save tuning configuration to file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(asdict(self.config), f, indent=2)
            logger.info(f"Saved tuning config to {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save tuning config: {e}")
    
    def display_current_config(self):
        """Display current tuning configuration"""
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("Parameter", style="cyan", width=25)
        table.add_column("Current Value", style="green", width=20)
        table.add_column("Description", style="yellow", width=40)
        
        config_descriptions = {
            'fallback_sensitivity': 'Threshold for triggering model fallback (0.0-1.0)',
            'style_preference': 'Code style preference (minimal, verbose, balanced)',
            'max_retries': 'Maximum retry attempts for failed injections',
            'prefer_comments': 'Whether to prefer code with comments',
            'model_escalation_threshold': 'Number of failures before escalating model',
            'prompt_enhancement_level': 'Level of prompt enhancement (low, moderate, high)',
            'confidence_threshold': 'Minimum confidence for applying suggestions',
            'auto_refine_enabled': 'Enable automatic prompt refinement',
            'memory_lookup_depth': 'Number of historical entries to analyze',
            'pattern_learning_rate': 'Rate of learning from feedback (0.0-1.0)',
            'template_optimization': 'Enable template performance optimization',
            'export_detailed_logs': 'Export detailed logs for analysis'
        }
        
        for param, value in asdict(self.config).items():
            description = config_descriptions.get(param, "No description available")
            table.add_row(param, str(value), description)
        
        console.print(Panel(table, title="[bold]Current Tuning Configuration[/bold]"))
    
    def interactive_tuning_session(self):
        """Start an interactive tuning session"""
        console.print(Panel(
            "[bold]Manual Tuning Panel[/bold]\n"
            "Fine-tune AI behavior and memory settings",
            title="[bold cyan]CodeCrusher Tuning[/bold cyan]",
            border_style="cyan"
        ))
        
        while True:
            console.print("\n[bold]Available Actions:[/bold]")
            console.print("1. View current configuration")
            console.print("2. Edit parameters")
            console.print("3. Reset to defaults")
            console.print("4. Export configuration")
            console.print("5. Import configuration")
            console.print("6. Test configuration")
            console.print("7. Exit")
            
            choice = Prompt.ask("Select an action", choices=["1", "2", "3", "4", "5", "6", "7"])
            
            if choice == "1":
                self.display_current_config()
            elif choice == "2":
                self._edit_parameters()
            elif choice == "3":
                self._reset_to_defaults()
            elif choice == "4":
                self._export_configuration()
            elif choice == "5":
                self._import_configuration()
            elif choice == "6":
                self._test_configuration()
            elif choice == "7":
                console.print("[green]Exiting tuning panel...[/green]")
                break
    
    def _edit_parameters(self):
        """Edit individual parameters"""
        console.print("\n[bold]Parameter Editor[/bold]")
        
        parameters = list(asdict(self.config).keys())
        
        # Display parameters with numbers
        for i, param in enumerate(parameters, 1):
            value = getattr(self.config, param)
            console.print(f"{i:2d}. {param}: [green]{value}[/green]")
        
        param_choice = Prompt.ask(
            "Select parameter to edit (number or name)",
            choices=[str(i) for i in range(1, len(parameters) + 1)] + parameters
        )
        
        # Convert number to parameter name
        if param_choice.isdigit():
            param_name = parameters[int(param_choice) - 1]
        else:
            param_name = param_choice
        
        current_value = getattr(self.config, param_name)
        console.print(f"\nEditing: [cyan]{param_name}[/cyan]")
        console.print(f"Current value: [green]{current_value}[/green]")
        
        # Get new value based on type
        new_value = self._get_new_value(param_name, current_value)
        
        if new_value is not None:
            setattr(self.config, param_name, new_value)
            self._save_config()
            console.print(f"[green]✅ Updated {param_name} to {new_value}[/green]")
    
    def _get_new_value(self, param_name: str, current_value: Any) -> Any:
        """Get new value for a parameter based on its type"""
        if isinstance(current_value, bool):
            return Confirm.ask(f"New value for {param_name}")
        elif isinstance(current_value, int):
            while True:
                try:
                    new_val = int(Prompt.ask(f"New value for {param_name} (integer)"))
                    if param_name == "max_retries" and new_val < 1:
                        console.print("[red]Max retries must be at least 1[/red]")
                        continue
                    return new_val
                except ValueError:
                    console.print("[red]Please enter a valid integer[/red]")
        elif isinstance(current_value, float):
            while True:
                try:
                    new_val = float(Prompt.ask(f"New value for {param_name} (float)"))
                    if param_name in ["fallback_sensitivity", "confidence_threshold", "pattern_learning_rate"]:
                        if not 0.0 <= new_val <= 1.0:
                            console.print("[red]Value must be between 0.0 and 1.0[/red]")
                            continue
                    return new_val
                except ValueError:
                    console.print("[red]Please enter a valid number[/red]")
        elif isinstance(current_value, str):
            if param_name == "style_preference":
                return Prompt.ask(
                    f"New value for {param_name}",
                    choices=["minimal", "verbose", "balanced"],
                    default=current_value
                )
            elif param_name == "prompt_enhancement_level":
                return Prompt.ask(
                    f"New value for {param_name}",
                    choices=["low", "moderate", "high"],
                    default=current_value
                )
            else:
                return Prompt.ask(f"New value for {param_name}", default=current_value)
        
        return None
    
    def _reset_to_defaults(self):
        """Reset configuration to defaults"""
        if Confirm.ask("Are you sure you want to reset all settings to defaults?"):
            self.config = TuningConfig()
            self._save_config()
            console.print("[green]✅ Configuration reset to defaults[/green]")
    
    def _export_configuration(self):
        """Export configuration to a file"""
        export_path = Prompt.ask("Export path", default="tuning_export.json")
        try:
            with open(export_path, 'w') as f:
                json.dump(asdict(self.config), f, indent=2)
            console.print(f"[green]✅ Configuration exported to {export_path}[/green]")
        except Exception as e:
            console.print(f"[red]❌ Export failed: {e}[/red]")
    
    def _import_configuration(self):
        """Import configuration from a file"""
        import_path = Prompt.ask("Import path")
        try:
            with open(import_path, 'r') as f:
                data = json.load(f)
            
            # Validate imported data
            imported_config = TuningConfig(**data)
            
            if Confirm.ask("Replace current configuration with imported settings?"):
                self.config = imported_config
                self._save_config()
                console.print(f"[green]✅ Configuration imported from {import_path}[/green]")
        except FileNotFoundError:
            console.print(f"[red]❌ File not found: {import_path}[/red]")
        except Exception as e:
            console.print(f"[red]❌ Import failed: {e}[/red]")
    
    def _test_configuration(self):
        """Test the current configuration"""
        console.print("\n[bold]Testing Configuration...[/bold]")
        
        # Validate configuration values
        issues = []
        
        if not 0.0 <= self.config.fallback_sensitivity <= 1.0:
            issues.append("fallback_sensitivity must be between 0.0 and 1.0")
        
        if self.config.max_retries < 1:
            issues.append("max_retries must be at least 1")
        
        if not 0.0 <= self.config.confidence_threshold <= 1.0:
            issues.append("confidence_threshold must be between 0.0 and 1.0")
        
        if not 0.0 <= self.config.pattern_learning_rate <= 1.0:
            issues.append("pattern_learning_rate must be between 0.0 and 1.0")
        
        if self.config.memory_lookup_depth < 1:
            issues.append("memory_lookup_depth must be at least 1")
        
        if issues:
            console.print("[red]❌ Configuration issues found:[/red]")
            for issue in issues:
                console.print(f"  • {issue}")
        else:
            console.print("[green]✅ Configuration is valid[/green]")
            
            # Show configuration impact
            impact_table = Table(show_header=True, header_style="bold blue")
            impact_table.add_column("Setting", style="cyan")
            impact_table.add_column("Impact", style="yellow")
            
            impact_table.add_row(
                "Fallback Sensitivity",
                f"{'High' if self.config.fallback_sensitivity > 0.7 else 'Low'} sensitivity to model failures"
            )
            impact_table.add_row(
                "Style Preference",
                f"Code will be {self.config.style_preference} in style"
            )
            impact_table.add_row(
                "Auto-Refine",
                f"{'Enabled' if self.config.auto_refine_enabled else 'Disabled'} automatic prompt improvement"
            )
            impact_table.add_row(
                "Memory Depth",
                f"Will analyze {self.config.memory_lookup_depth} historical entries"
            )
            
            console.print(Panel(impact_table, title="[bold]Configuration Impact[/bold]"))
    
    def get_config(self) -> TuningConfig:
        """Get current configuration"""
        return self.config
    
    def update_config(self, **kwargs):
        """Update configuration programmatically"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        self._save_config()
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary"""
        return asdict(self.config)
    
    def apply_preset(self, preset_name: str):
        """Apply a predefined configuration preset"""
        presets = {
            "conservative": TuningConfig(
                fallback_sensitivity=0.8,
                style_preference="verbose",
                max_retries=2,
                prefer_comments=True,
                model_escalation_threshold=1,
                prompt_enhancement_level="low",
                confidence_threshold=0.7
            ),
            "aggressive": TuningConfig(
                fallback_sensitivity=0.5,
                style_preference="minimal",
                max_retries=5,
                prefer_comments=False,
                model_escalation_threshold=3,
                prompt_enhancement_level="high",
                confidence_threshold=0.3
            ),
            "balanced": TuningConfig(
                fallback_sensitivity=0.7,
                style_preference="balanced",
                max_retries=3,
                prefer_comments=True,
                model_escalation_threshold=2,
                prompt_enhancement_level="moderate",
                confidence_threshold=0.5
            )
        }
        
        if preset_name in presets:
            self.config = presets[preset_name]
            self._save_config()
            console.print(f"[green]✅ Applied {preset_name} preset[/green]")
        else:
            console.print(f"[red]❌ Unknown preset: {preset_name}[/red]")
            console.print(f"Available presets: {', '.join(presets.keys())}")


def main():
    """Main function for running the tuning panel"""
    tuning_panel = ManualTuningPanel()
    tuning_panel.interactive_tuning_session()


if __name__ == "__main__":
    main()
