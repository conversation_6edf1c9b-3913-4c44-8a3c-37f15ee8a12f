"""
File discovery utilities for CodeCrusher.

This module provides functionality for recursively discovering source files
with extension filtering and injection tag detection.
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Optional
from rich.console import Console

console = Console()

# Default file extensions to scan
DEFAULT_EXTENSIONS = ['py']

# Common source file extensions
COMMON_EXTENSIONS = {
    'py': 'Python',
    'js': 'JavaScript',
    'ts': 'TypeScript',
    'java': 'Java',
    'cpp': 'C++',
    'c': 'C',
    'cs': 'C#',
    'go': 'Go',
    'rs': 'Rust',
    'php': 'PHP',
    'rb': 'Ruby',
    'swift': 'Swift',
    'kt': 'Kotlin',
    'scala': 'Scala',
    'sh': 'Shell',
    'ps1': 'PowerShell',
    'sql': 'SQL',
    'html': 'HTML',
    'css': 'CSS',
    'scss': 'SCSS',
    'less': 'LESS',
    'vue': 'Vue',
    'jsx': 'JSX',
    'tsx': 'TSX'
}

# Injection tag patterns
INJECTION_TAG_PATTERNS = [
    r'#\s*AI_INJECT:\s*(\w+)',  # Python, Shell, etc.
    r'//\s*AI_INJECT:\s*(\w+)',  # JavaScript, C++, Java, etc.
    r'/\*\s*AI_INJECT:\s*(\w+)\s*\*/',  # Multi-line comments
    r'<!--\s*AI_INJECT:\s*(\w+)\s*-->',  # HTML, XML
    r'--\s*AI_INJECT:\s*(\w+)',  # SQL
]


def parse_extensions(ext_string: Optional[str]) -> List[str]:
    """
    Parse extension string into a list of extensions.

    Args:
        ext_string: Comma-separated string of extensions (e.g., "py,js,java")

    Returns:
        List of extensions without dots
    """
    if not ext_string:
        return DEFAULT_EXTENSIONS

    extensions = []
    for ext in ext_string.split(','):
        ext = ext.strip().lower()
        # Remove leading dot if present
        # FIXED: Add defensive check for None ext before calling .startswith()
        if ext and ext.startswith('.'):
            ext = ext[1:]
        if ext:
            extensions.append(ext)

    return extensions if extensions else DEFAULT_EXTENSIONS


def is_source_file(file_path: Path, extensions: List[str]) -> bool:
    """
    Check if a file is a source file based on its extension.

    Args:
        file_path: Path to the file
        extensions: List of allowed extensions

    Returns:
        True if the file matches one of the extensions
    """
    if not file_path.is_file():
        return False

    file_ext = file_path.suffix.lower()
    # FIXED: Add defensive check for None file_ext before calling .startswith()
    if file_ext and file_ext.startswith('.'):
        file_ext = file_ext[1:]

    return file_ext in extensions


def should_skip_directory(dir_path: Path) -> bool:
    """
    Check if a directory should be skipped during recursive scanning.

    Args:
        dir_path: Path to the directory

    Returns:
        True if the directory should be skipped
    """
    skip_dirs = {
        '__pycache__', '.git', '.svn', '.hg', '.bzr',
        'node_modules', '.vscode', '.idea', '.vs',
        'build', 'dist', 'target', 'bin', 'obj',
        '.pytest_cache', '.coverage', '.tox',
        'venv', 'env', '.env', 'virtualenv'
    }

    # FIXED: Add defensive check for None dir_path.name before calling .startswith()
    return dir_path.name in skip_dirs or (dir_path.name and dir_path.name.startswith('.'))


def gather_source_files(source_path: str, recursive: bool, extensions: List[str], filter_tags: bool = True) -> List[str]:
    """
    Gather source files based on path, recursive flag, and extensions, with optional tag filtering.

    This is the main function for file gathering as specified in the requirements.

    Args:
        source_path: Path to file or directory
        recursive: Whether to scan recursively (only applies to directories)
        extensions: List of file extensions to include (without dots)
        filter_tags: Whether to filter files to only include those with injection tags

    Returns:
        List of file paths as strings

    Raises:
        FileNotFoundError: If source_path doesn't exist
        ValueError: If source_path is neither file nor directory, or if single file doesn't match extensions
    """
    source_path_obj = Path(source_path)

    if not source_path_obj.exists():
        raise FileNotFoundError(f"Source path does not exist: {source_path}")

    # If it's a single file, verify its extension matches the filter
    if source_path_obj.is_file():
        if is_source_file(source_path_obj, extensions):
            return [str(source_path_obj)]
        else:
            raise ValueError(f"File {source_path} does not match any of the specified extensions: {', '.join(extensions)}")

    # If it's a directory
    if not source_path_obj.is_dir():
        raise ValueError(f"Source path is neither a file nor directory: {source_path}")

    discovered_files = []

    if recursive:
        # Recursive mode: walk all subfolders
        for root, dirs, files in os.walk(source_path_obj):
            root_path = Path(root)

            # Filter out directories to skip
            dirs_to_remove = []
            for dir_name in dirs:
                dir_path = root_path / dir_name
                if should_skip_directory(dir_path):
                    dirs_to_remove.append(dir_name)

            # Remove skipped directories from dirs list
            for dir_name in dirs_to_remove:
                dirs.remove(dir_name)

            # Check files in current directory
            for file_name in files:
                file_path = root_path / file_name
                if is_source_file(file_path, extensions):
                    discovered_files.append(str(file_path))
    else:
        # Non-recursive mode: list only files directly inside
        for item in source_path_obj.iterdir():
            if item.is_file() and is_source_file(item, extensions):
                discovered_files.append(str(item))

    # Apply tag filtering if requested
    if filter_tags and discovered_files:
        console.print(f"[cyan]Scanning {len(discovered_files)} files for injection tags...[/cyan]")

        # Convert to Path objects for tag detection
        file_paths = [Path(f) for f in discovered_files]

        # Scan files for tags
        file_tags = scan_files_for_tags(file_paths, verbose=False)

        # Filter to only files with tags
        files_with_tags = filter_files_with_tags(file_tags)

        # Convert back to strings
        filtered_files = [str(path) for path in files_with_tags.keys()]

        # Print feedback
        total_files = len(discovered_files)
        matched_files = len(filtered_files)

        if matched_files > 0:
            console.print(f"[green]✅ {matched_files} out of {total_files} files matched injection tags.[/green]")

            # Show unique tags found
            all_tags = set()
            for tags in files_with_tags.values():
                all_tags.update(tags)

            if all_tags:
                console.print(f"[cyan]Tags found:[/cyan] {', '.join(sorted(all_tags))}")
        else:
            console.print(f"[yellow]⚠️ No files found with injection tags. Aborting.[/yellow]")
            console.print(f"[dim]Searched {total_files} files with extensions: {', '.join(extensions)}[/dim]")
            console.print(f"[dim]Looking for patterns like: # AI_INJECT:tag_name, // AI_INJECT:tag_name[/dim]")
            return []

        return sorted(filtered_files)

    return sorted(discovered_files)


def discover_files_recursive(
    source_path: str,
    extensions: List[str],
    verbose: bool = False
) -> List[Path]:
    """
    Recursively discover source files in a directory tree.

    Args:
        source_path: Root directory to start scanning
        extensions: List of file extensions to include
        verbose: Enable verbose output

    Returns:
        List of discovered file paths
    """
    source_path = Path(source_path)

    if not source_path.exists():
        raise FileNotFoundError(f"Source path does not exist: {source_path}")

    # If it's a single file, return it if it matches
    if source_path.is_file():
        if is_source_file(source_path, extensions):
            return [source_path]
        else:
            return []

    # If it's a directory, scan recursively
    if not source_path.is_dir():
        raise ValueError(f"Source path is neither a file nor directory: {source_path}")

    discovered_files = []
    skipped_dirs = []

    for root, dirs, files in os.walk(source_path):
        root_path = Path(root)

        # Filter out directories to skip
        dirs_to_remove = []
        for dir_name in dirs:
            dir_path = root_path / dir_name
            if should_skip_directory(dir_path):
                dirs_to_remove.append(dir_name)
                skipped_dirs.append(str(dir_path))

        # Remove skipped directories from dirs list to prevent os.walk from entering them
        for dir_name in dirs_to_remove:
            dirs.remove(dir_name)

        # Check files in current directory
        for file_name in files:
            file_path = root_path / file_name
            if is_source_file(file_path, extensions):
                discovered_files.append(file_path)
                if verbose:
                    console.print(f"[green]Found:[/green] {file_path}")

    if verbose and skipped_dirs:
        console.print(f"\n[yellow]Skipped {len(skipped_dirs)} directories:[/yellow]")
        for skipped_dir in skipped_dirs[:10]:  # Show first 10
            console.print(f"  {skipped_dir}")
        if len(skipped_dirs) > 10:
            console.print(f"  ... and {len(skipped_dirs) - 10} more")

    return sorted(discovered_files)


def detect_injection_tags(file_path: Path) -> List[str]:
    """
    Detect injection tags in a source file.

    Args:
        file_path: Path to the source file

    Returns:
        List of detected injection tag names
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
    except Exception:
        return []

    tags = []
    for pattern in INJECTION_TAG_PATTERNS:
        matches = re.findall(pattern, content, re.IGNORECASE)
        tags.extend(matches)

    return list(set(tags))  # Remove duplicates


def scan_files_for_tags(
    file_paths: List[Path],
    verbose: bool = False
) -> Dict[Path, List[str]]:
    """
    Scan multiple files for injection tags.

    Args:
        file_paths: List of file paths to scan
        verbose: Enable verbose output

    Returns:
        Dictionary mapping file paths to their injection tags
    """
    file_tags = {}

    for file_path in file_paths:
        tags = detect_injection_tags(file_path)
        file_tags[file_path] = tags

        if verbose:
            if tags:
                console.print(f"[green]Tags found in {file_path}:[/green] {', '.join(tags)}")
            else:
                console.print(f"[dim]No tags in {file_path}[/dim]")

    return file_tags


def filter_files_with_tags(file_tags: Dict[Path, List[str]]) -> Dict[Path, List[str]]:
    """
    Filter files to only include those with injection tags.

    Args:
        file_tags: Dictionary mapping file paths to their injection tags

    Returns:
        Dictionary containing only files with tags
    """
    return {path: tags for path, tags in file_tags.items() if tags}


def print_discovery_summary(
    discovered_files: List[Path],
    extensions: List[str],
    source_path: str
) -> None:
    """
    Print a summary of file discovery results.

    Args:
        discovered_files: List of discovered files
        extensions: List of extensions that were searched
        source_path: Root path that was scanned
    """
    console.print(f"\n[bold blue]File Discovery Summary[/bold blue]")
    console.print(f"[cyan]Source path:[/cyan] {source_path}")
    console.print(f"[cyan]Extensions:[/cyan] {', '.join(extensions)}")
    console.print(f"[cyan]Files found:[/cyan] {len(discovered_files)}")

    if discovered_files:
        # Group by extension
        ext_counts = {}
        for file_path in discovered_files:
            ext = file_path.suffix.lower()
            # FIXED: Add defensive check for None ext before calling .startswith()
            if ext and ext.startswith('.'):
                ext = ext[1:]
            ext_counts[ext] = ext_counts.get(ext, 0) + 1

        console.print(f"\n[bold]Breakdown by extension:[/bold]")
        for ext, count in sorted(ext_counts.items()):
            lang_name = COMMON_EXTENSIONS.get(ext, ext.upper())
            console.print(f"  {lang_name} (.{ext}): {count} files")


def print_tag_summary(
    files_with_tags: Dict[Path, List[str]],
    total_files: int
) -> None:
    """
    Print a summary of injection tag detection results.

    Args:
        files_with_tags: Dictionary of files with their tags
        total_files: Total number of files scanned
    """
    console.print(f"\n[bold blue]Injection Tag Summary[/bold blue]")
    console.print(f"[cyan]Files scanned:[/cyan] {total_files}")
    console.print(f"[cyan]Files with tags:[/cyan] {len(files_with_tags)}")

    if files_with_tags:
        # Count unique tags
        all_tags = set()
        for tags in files_with_tags.values():
            all_tags.update(tags)

        console.print(f"[cyan]Unique tags found:[/cyan] {len(all_tags)}")
        console.print(f"[cyan]Tags:[/cyan] {', '.join(sorted(all_tags))}")

        console.print(f"\n[bold]Files to be processed:[/bold]")
        for file_path, tags in sorted(files_with_tags.items()):
            console.print(f"  {file_path}: {', '.join(tags)}")
    else:
        console.print(f"[yellow]No files contain injection tags.[/yellow]")
