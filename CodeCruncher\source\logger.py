"""
Logger module for AI Engine
"""

import logging
import time
from rich import print as rprint
from rich.console import Console

console = Console()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def log(message, level="info"):
    """
    Log a message with timestamp and color
    
    Args:
        message (str): The message to log
        level (str, optional): The log level (info, warning, error, success)
    """
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    
    if level == "warning":
        rprint(f"[yellow][{timestamp}] ⚠️ {message}[/yellow]")
        logging.warning(message)
    elif level == "error":
        rprint(f"[red][{timestamp}] ❌ {message}[/red]")
        logging.error(message)
    elif level == "success":
        rprint(f"[green][{timestamp}] ✅ {message}[/green]")
        logging.info(message)
    else:  # info
        rprint(f"[cyan][{timestamp}] ℹ️ {message}[/cyan]")
        logging.info(message)
