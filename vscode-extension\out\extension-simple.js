const vscode = require('vscode');
const path = require('path');
const cp = require('child_process');

// Status bar item
let statusBarItem;

function activate(context) {
    console.log('CodeCrusher extension is now active');

    // Register main inject command
    const injectCmd = vscode.commands.registerCommand('codecrusher.inject', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }

        const selection = editor.selection;
        const selectedText = editor.document.getText(selection);
        
        if (!selectedText) {
            vscode.window.showErrorMessage('Please select some code to inject');
            return;
        }

        // Show input box for injection prompt
        const prompt = await vscode.window.showInputBox({
            prompt: 'Enter your injection prompt',
            placeHolder: 'e.g., Add error handling to this function'
        });

        if (!prompt) return;

        // Show progress
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'CodeCrusher: Processing injection...',
            cancellable: false
        }, async (progress) => {
            try {
                // Get configuration
                const config = vscode.workspace.getConfiguration('codecrusher');
                const pythonPath = config.get('pythonPath') || 'python';
                
                // Get the workspace root to find codecrusher_cli.py
                const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                if (!workspaceRoot) {
                    vscode.window.showErrorMessage('No workspace folder found');
                    return;
                }
                
                // Prepare command - use the direct CLI script
                const tempFile = path.join(__dirname, 'temp_injection.txt');
                require('fs').writeFileSync(tempFile, selectedText);
                
                // Use the codecrusher_cli.py script directly
                const cliScript = path.join(workspaceRoot, 'codecrusher_cli.py');
                const command = `"${pythonPath}" "${cliScript}" inject "${tempFile}" --prompt "${prompt}" --preview`;
                
                console.log('Executing command:', command);
                
                // Execute CodeCrusher CLI
                cp.exec(command, { cwd: workspaceRoot }, (error, stdout, stderr) => {
                    // Clean up temp file
                    try {
                        require('fs').unlinkSync(tempFile);
                    } catch (e) {}
                    
                    if (error) {
                        console.error('CodeCrusher CLI error:', error);
                        vscode.window.showErrorMessage(`CodeCrusher failed to apply changes: Error: CLI Error: ${error.message}`);
                        return;
                    }
                    
                    if (stderr) {
                        console.warn('CodeCrusher stderr:', stderr);
                        if (stderr.includes('ModuleNotFoundError')) {
                            vscode.window.showErrorMessage('CodeCrusher CLI not properly installed. Please run: pip install -e . from the project directory');
                            return;
                        }
                    }
                    
                    // Show success message
                    vscode.window.showInformationMessage(
                        `CodeCrusher injection completed: "${prompt}"`
                    );
                    
                    // If there's output, show it in a new document
                    if (stdout && stdout.trim()) {
                        vscode.workspace.openTextDocument({
                            content: stdout,
                            language: editor.document.languageId
                        }).then(doc => {
                            vscode.window.showTextDocument(doc);
                        });
                    }
                });
            } catch (error) {
                vscode.window.showErrorMessage(`CodeCrusher error: ${error.message}`);
            }
        });
    });

    // Register show logs command
    const showLogsCmd = vscode.commands.registerCommand('codecrusher.showLogs', () => {
        // Create and show a new webview panel for logs
        const panel = vscode.window.createWebviewPanel(
            'codecrusher.logs',
            'CodeCrusher Live Logs',
            vscode.ViewColumn.Two,
            {
                enableScripts: true
            }
        );

        panel.webview.html = `
            <html>
                <head>
                    <style>
                        body { 
                            font-family: var(--vscode-font-family); 
                            padding: 20px;
                            background-color: var(--vscode-editor-background);
                            color: var(--vscode-editor-foreground);
                        }
                        .log-entry {
                            margin-bottom: 10px;
                            padding: 5px;
                            border-left: 3px solid var(--vscode-textLink-foreground);
                            background-color: var(--vscode-editor-selectionBackground);
                        }
                        .timestamp {
                            color: var(--vscode-descriptionForeground);
                            font-size: 0.9em;
                        }
                    </style>
                </head>
                <body>
                    <h1>🔗 CodeCrusher Live Logs</h1>
                    <div class="log-entry">
                        <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                        <div>CodeCrusher extension activated</div>
                    </div>
                    <div class="log-entry">
                        <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                        <div>Live logs panel opened</div>
                    </div>
                    <p><em>Live log streaming will be implemented in future versions.</em></p>
                </body>
            </html>
        `;
    });

    // Register run injection command
    const runInjectionCmd = vscode.commands.registerCommand('codecrusher.runInjection', () => {
        vscode.commands.executeCommand('codecrusher.inject');
    });

    // Register insert tag command
    const insertTagCmd = vscode.commands.registerCommand('codecrusher.insertTag', async () => {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor');
            return;
        }

        const position = editor.selection.active;
        const tagText = '// @codecrusher: ';
        
        editor.edit(editBuilder => {
            editBuilder.insert(position, tagText);
        });
        
        // Move cursor to end of tag for user to type
        const newPosition = position.translate(0, tagText.length);
        editor.selection = new vscode.Selection(newPosition, newPosition);
    });

    // Register show status command
    const showStatusCmd = vscode.commands.registerCommand('codecrusher.showStatus', () => {
        const config = vscode.workspace.getConfiguration('codecrusher');
        const status = {
            pythonPath: config.get('pythonPath') || 'python',
            defaultModel: config.get('defaultModel') || 'auto',
            defaultMode: config.get('defaultMode') || 'preview'
        };
        
        vscode.window.showInformationMessage(
            `CodeCrusher Status: Model=${status.defaultModel}, Mode=${status.defaultMode}, Python=${status.pythonPath}`
        );
    });

    // Add commands to context
    context.subscriptions.push(injectCmd);
    context.subscriptions.push(showLogsCmd);
    context.subscriptions.push(runInjectionCmd);
    context.subscriptions.push(insertTagCmd);
    context.subscriptions.push(showStatusCmd);

    // Create status bar item
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.text = "$(zap) CodeCrusher";
    statusBarItem.tooltip = "CodeCrusher Extension Active - Click to inject";
    statusBarItem.command = 'codecrusher.inject';
    statusBarItem.show();
    context.subscriptions.push(statusBarItem);

    console.log('CodeCrusher extension activation complete');
}

function deactivate() {
    if (statusBarItem) {
        statusBarItem.dispose();
    }
    console.log('CodeCrusher extension deactivated');
}

module.exports = {
    activate,
    deactivate
};
