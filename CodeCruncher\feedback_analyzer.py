"""
Feedback Analysis Module
Scans logs for patterns of low-rated injections and creates shaping rules
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
from log_store import LogStore

logger = logging.getLogger(__name__)

class FeedbackAnalyzer:
    """Analyzes feedback patterns and creates prompt shaping rules"""
    
    def __init__(self, log_store: LogStore = None, config_path: str = "prompt_tweaks.json"):
        self.log_store = log_store or LogStore()
        self.config_path = Path(config_path)
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self.shaping_rules = self._load_shaping_rules()
    
    def _load_shaping_rules(self) -> Dict[str, Any]:
        """Load existing prompt shaping rules"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load shaping rules: {e}")
        
        # Default structure
        return {
            "version": "1.0",
            "rules": {},
            "patterns": {},
            "statistics": {
                "total_analyses": 0,
                "rules_created": 0,
                "last_analysis": None
            }
        }
    
    def _save_shaping_rules(self):
        """Save shaping rules to config file"""
        try:
            with open(self.config_path, 'w') as f:
                json.dump(self.shaping_rules, f, indent=2)
            logger.info(f"Saved shaping rules to {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save shaping rules: {e}")
    
    def analyze_feedback_patterns(self, rating_threshold: int = 2, min_occurrences: int = 2) -> Dict[str, Any]:
        """Analyze feedback patterns and create shaping rules"""
        
        # Get low-rated injections
        low_rated = self.log_store.get_low_rated_injections(rating_threshold, limit=100)
        
        if len(low_rated) < min_occurrences:
            logger.info(f"Not enough low-rated injections ({len(low_rated)}) for pattern analysis")
            return {"patterns_found": 0, "rules_created": 0}
        
        # Analyze patterns
        patterns = self._extract_patterns(low_rated)
        
        # Create shaping rules from patterns
        new_rules = self._create_shaping_rules(patterns, min_occurrences)
        
        # Update statistics
        self.shaping_rules["statistics"]["total_analyses"] += 1
        self.shaping_rules["statistics"]["rules_created"] += len(new_rules)
        self.shaping_rules["statistics"]["last_analysis"] = self._get_timestamp()
        
        # Save updated rules
        self._save_shaping_rules()
        
        analysis_result = {
            "patterns_found": len(patterns),
            "rules_created": len(new_rules),
            "low_rated_count": len(low_rated),
            "new_rules": new_rules,
            "patterns": patterns
        }
        
        logger.info(f"Feedback analysis complete: {len(new_rules)} new rules created from {len(patterns)} patterns")
        return analysis_result
    
    def _extract_patterns(self, low_rated_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract patterns from low-rated injection logs"""
        patterns = {
            "by_tag": defaultdict(list),
            "by_injection_type": defaultdict(list),
            "by_model": defaultdict(list),
            "by_file_extension": defaultdict(list),
            "feedback_keywords": Counter(),
            "prompt_keywords": Counter()
        }
        
        for log in low_rated_logs:
            # Group by tags
            if log['tags']:
                tags = json.loads(log['tags'])
                for tag in tags:
                    patterns["by_tag"][tag].append(log)
            
            # Group by injection type
            patterns["by_injection_type"][log['injection_type']].append(log)
            
            # Group by model
            patterns["by_model"][log['model']].append(log)
            
            # Group by file extension
            file_ext = Path(log['filename']).suffix.lower()
            if file_ext:
                patterns["by_file_extension"][file_ext].append(log)
            
            # Extract feedback keywords
            if log['feedback']:
                feedback_words = self._extract_keywords(log['feedback'])
                patterns["feedback_keywords"].update(feedback_words)
            
            # Extract prompt keywords
            prompt_words = self._extract_keywords(log['prompt'])
            patterns["prompt_keywords"].update(prompt_words)
        
        return patterns
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract meaningful keywords from text"""
        # Common negative feedback keywords
        negative_keywords = [
            'error', 'bug', 'fail', 'wrong', 'incorrect', 'missing', 'incomplete',
            'null', 'undefined', 'exception', 'crash', 'slow', 'performance',
            'unclear', 'confusing', 'hard', 'difficult', 'complex', 'messy',
            'unsafe', 'insecure', 'vulnerable', 'leak', 'memory', 'timeout'
        ]
        
        text_lower = text.lower()
        found_keywords = []
        
        for keyword in negative_keywords:
            if keyword in text_lower:
                found_keywords.append(keyword)
        
        return found_keywords
    
    def _create_shaping_rules(self, patterns: Dict[str, Any], min_occurrences: int) -> List[Dict[str, Any]]:
        """Create shaping rules from identified patterns"""
        new_rules = []
        
        # Create rules for frequent tag patterns
        for tag, logs in patterns["by_tag"].items():
            if len(logs) >= min_occurrences:
                rule = self._create_tag_rule(tag, logs)
                if rule:
                    new_rules.append(rule)
        
        # Create rules for injection type patterns
        for injection_type, logs in patterns["by_injection_type"].items():
            if len(logs) >= min_occurrences:
                rule = self._create_injection_type_rule(injection_type, logs)
                if rule:
                    new_rules.append(rule)
        
        # Create rules for feedback keyword patterns
        for keyword, count in patterns["feedback_keywords"].most_common(5):
            if count >= min_occurrences:
                rule = self._create_keyword_rule(keyword, count)
                if rule:
                    new_rules.append(rule)
        
        # Add new rules to the configuration
        for rule in new_rules:
            rule_id = rule["id"]
            self.shaping_rules["rules"][rule_id] = rule
        
        return new_rules
    
    def _create_tag_rule(self, tag: str, logs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Create a shaping rule for a specific tag pattern"""
        
        # Analyze common issues for this tag
        feedback_keywords = Counter()
        for log in logs:
            if log['feedback']:
                keywords = self._extract_keywords(log['feedback'])
                feedback_keywords.update(keywords)
        
        if not feedback_keywords:
            return None
        
        # Get the most common issue
        top_issue = feedback_keywords.most_common(1)[0][0]
        
        # Create appropriate enhancement based on the issue
        enhancement = self._get_enhancement_for_issue(top_issue)
        
        rule_id = f"tag_{tag}_{top_issue}"
        
        return {
            "id": rule_id,
            "type": "tag_based",
            "condition": {"tag": tag},
            "issue": top_issue,
            "enhancement": enhancement,
            "confidence": len(logs) / 10.0,  # Simple confidence scoring
            "occurrences": len(logs),
            "created_at": self._get_timestamp()
        }
    
    def _create_injection_type_rule(self, injection_type: str, logs: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Create a shaping rule for a specific injection type pattern"""
        
        # Analyze common issues for this injection type
        feedback_keywords = Counter()
        for log in logs:
            if log['feedback']:
                keywords = self._extract_keywords(log['feedback'])
                feedback_keywords.update(keywords)
        
        if not feedback_keywords:
            return None
        
        top_issue = feedback_keywords.most_common(1)[0][0]
        enhancement = self._get_enhancement_for_issue(top_issue)
        
        rule_id = f"type_{injection_type}_{top_issue}"
        
        return {
            "id": rule_id,
            "type": "injection_type_based",
            "condition": {"injection_type": injection_type},
            "issue": top_issue,
            "enhancement": enhancement,
            "confidence": len(logs) / 10.0,
            "occurrences": len(logs),
            "created_at": self._get_timestamp()
        }
    
    def _create_keyword_rule(self, keyword: str, count: int) -> Optional[Dict[str, Any]]:
        """Create a shaping rule for a feedback keyword pattern"""
        
        enhancement = self._get_enhancement_for_issue(keyword)
        rule_id = f"keyword_{keyword}"
        
        return {
            "id": rule_id,
            "type": "keyword_based",
            "condition": {"feedback_keyword": keyword},
            "issue": keyword,
            "enhancement": enhancement,
            "confidence": min(count / 5.0, 1.0),
            "occurrences": count,
            "created_at": self._get_timestamp()
        }
    
    def _get_enhancement_for_issue(self, issue: str) -> str:
        """Get appropriate prompt enhancement for a specific issue"""
        
        enhancements = {
            "error": "Add comprehensive error handling with try-catch blocks",
            "exception": "Include proper exception handling and error messages",
            "null": "Add null/None checks before accessing properties or methods",
            "undefined": "Ensure all variables are properly defined and initialized",
            "bug": "Include thorough testing and validation logic",
            "fail": "Add fallback mechanisms and error recovery",
            "wrong": "Double-check logic and add validation steps",
            "incorrect": "Verify implementation against requirements",
            "missing": "Ensure all required functionality is implemented",
            "incomplete": "Complete all edge cases and error scenarios",
            "slow": "Optimize for performance and efficiency",
            "performance": "Focus on algorithmic complexity and memory usage",
            "unclear": "Add detailed comments and documentation",
            "confusing": "Improve code readability and structure",
            "complex": "Simplify logic and break down into smaller functions",
            "messy": "Follow clean code principles and consistent formatting",
            "unsafe": "Add security checks and input validation",
            "insecure": "Implement proper security measures",
            "vulnerable": "Address security vulnerabilities and sanitize inputs",
            "memory": "Optimize memory usage and prevent leaks",
            "timeout": "Add timeout handling and async patterns"
        }
        
        return enhancements.get(issue, f"Address {issue} issues in the implementation")
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def get_applicable_rules(self, tags: List[str] = None, injection_type: str = None, 
                           file_extension: str = None) -> List[Dict[str, Any]]:
        """Get shaping rules applicable to the current context"""
        applicable_rules = []
        
        for rule_id, rule in self.shaping_rules["rules"].items():
            if self._rule_matches_context(rule, tags, injection_type, file_extension):
                applicable_rules.append(rule)
        
        # Sort by confidence (highest first)
        applicable_rules.sort(key=lambda r: r.get("confidence", 0), reverse=True)
        
        return applicable_rules
    
    def _rule_matches_context(self, rule: Dict[str, Any], tags: List[str] = None, 
                            injection_type: str = None, file_extension: str = None) -> bool:
        """Check if a rule matches the current context"""
        condition = rule.get("condition", {})
        
        # Check tag-based rules
        if "tag" in condition:
            if not tags or condition["tag"] not in tags:
                return False
        
        # Check injection type rules
        if "injection_type" in condition:
            if injection_type != condition["injection_type"]:
                return False
        
        # Check file extension rules
        if "file_extension" in condition:
            if file_extension != condition["file_extension"]:
                return False
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get feedback analysis statistics"""
        stats = self.shaping_rules["statistics"].copy()
        stats["total_rules"] = len(self.shaping_rules["rules"])
        
        # Calculate rule effectiveness
        if stats["total_rules"] > 0:
            rule_types = Counter()
            confidence_scores = []
            
            for rule in self.shaping_rules["rules"].values():
                rule_types[rule["type"]] += 1
                confidence_scores.append(rule.get("confidence", 0))
            
            stats["rule_types"] = dict(rule_types)
            stats["average_confidence"] = sum(confidence_scores) / len(confidence_scores)
        
        return stats


# Convenience functions
def analyze_feedback(rating_threshold: int = 2, min_occurrences: int = 2) -> Dict[str, Any]:
    """Convenience function to analyze feedback patterns"""
    analyzer = FeedbackAnalyzer()
    return analyzer.analyze_feedback_patterns(rating_threshold, min_occurrences)


def get_shaping_rules(tags: List[str] = None, injection_type: str = None, 
                     file_extension: str = None) -> List[Dict[str, Any]]:
    """Convenience function to get applicable shaping rules"""
    analyzer = FeedbackAnalyzer()
    return analyzer.get_applicable_rules(tags, injection_type, file_extension)


if __name__ == "__main__":
    # Test the feedback analyzer
    analyzer = FeedbackAnalyzer("test_prompt_tweaks.json")
    
    # Run analysis
    result = analyzer.analyze_feedback_patterns()
    print(f"Analysis result: {result}")
    
    # Get statistics
    stats = analyzer.get_statistics()
    print(f"Statistics: {stats}")
    
    print("✅ Feedback analyzer test completed!")
