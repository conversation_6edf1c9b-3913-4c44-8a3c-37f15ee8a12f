"""
Anomaly detection engine for CodeCrusher.

This module provides functions for detecting anomalies in telemetry data,
such as high error rates, model instability, token spikes, and abnormal
fallback patterns.
"""

import os
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional
from collections import defaultdict, Counter
from pathlib import Path

# Import telemetry logger
from codecrusher.telemetry_logger import get_telemetry_entries, TELEMETRY_FILE

# Anomaly types and their emoji indicators
ANOMALY_TYPES = {
    "high_error_rate": "⚠️",  # High error rate for a model
    "model_unstable": "🚨",   # Model is unstable (high failure rate)
    "token_spike": "💥",      # Token usage spike
    "fallback_surge": "🧨",   # Surge in fallback events
    "tag_flood": "🧬",        # Single tag appears too frequently
    "repetition": "🔁",       # Same prompt used multiple times
}

# Thresholds for anomaly detection
THRESHOLDS = {
    "high_error_rate": {
        "rate": 25,           # Error rate percentage
        "timeframe_hours": 2, # Timeframe to check in hours
        "min_samples": 5      # Minimum number of samples required
    },
    "model_unstable": {
        "failure_rate": 50,   # Failure rate percentage
        "sample_size": 10     # Number of recent uses to check
    },
    "token_spike": {
        "tokens_threshold": 10000  # Token threshold for spike detection
    },
    "fallback_surge": {
        "count": 5,           # Number of fallback events
        "timeframe_minutes": 10  # Timeframe to check in minutes
    },
    "tag_flood": {
        "count": 50,          # Number of occurrences of a tag
        "timeframe_hours": 2  # Timeframe to check in hours
    },
    "repetition": {
        "count": 3,           # Number of repetitions
        "timeframe_hours": 1  # Timeframe to check in hours
    }
}

class Anomaly:
    """Class representing a detected anomaly."""
    
    def __init__(
        self, 
        anomaly_type: str, 
        model: Optional[str] = None,
        description: str = "",
        timestamp: Optional[datetime] = None,
        value: Optional[float] = None,
        threshold: Optional[float] = None,
        tags: Optional[List[str]] = None,
        related_entries: Optional[List[Dict[str, Any]]] = None
    ):
        """
        Initialize an anomaly.
        
        Args:
            anomaly_type: Type of anomaly (from ANOMALY_TYPES)
            model: Affected model (if applicable)
            description: Human-readable description of the anomaly
            timestamp: When the anomaly was detected
            value: Measured value that triggered the anomaly
            threshold: Threshold that was exceeded
            tags: Related tags (if applicable)
            related_entries: List of telemetry entries related to this anomaly
        """
        self.anomaly_type = anomaly_type
        self.model = model
        self.description = description
        self.timestamp = timestamp or datetime.now()
        self.value = value
        self.threshold = threshold
        self.tags = tags or []
        self.related_entries = related_entries or []
        
    @property
    def icon(self) -> str:
        """Get the icon for this anomaly type."""
        return ANOMALY_TYPES.get(self.anomaly_type, "⚠️")
    
    @property
    def model_display(self) -> str:
        """Get the model display string."""
        return f"[{self.model}]" if self.model else ""
    
    @property
    def formatted_description(self) -> str:
        """Get a formatted description for display."""
        return f"{self.icon} {self.model_display} {self.description}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert the anomaly to a dictionary."""
        return {
            "type": self.anomaly_type,
            "model": self.model,
            "description": self.description,
            "timestamp": self.timestamp.isoformat(),
            "value": self.value,
            "threshold": self.threshold,
            "tags": self.tags
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Anomaly':
        """Create an anomaly from a dictionary."""
        timestamp = datetime.fromisoformat(data["timestamp"]) if "timestamp" in data else None
        return cls(
            anomaly_type=data["type"],
            model=data.get("model"),
            description=data["description"],
            timestamp=timestamp,
            value=data.get("value"),
            threshold=data.get("threshold"),
            tags=data.get("tags", [])
        )

def detect_high_error_rate(entries: List[Dict[str, Any]]) -> List[Anomaly]:
    """
    Detect high error rates for models.
    
    Args:
        entries: List of telemetry entries
        
    Returns:
        List of detected anomalies
    """
    anomalies = []
    
    # Get thresholds
    error_rate_threshold = THRESHOLDS["high_error_rate"]["rate"]
    timeframe_hours = THRESHOLDS["high_error_rate"]["timeframe_hours"]
    min_samples = THRESHOLDS["high_error_rate"]["min_samples"]
    
    # Calculate time threshold
    now = datetime.now()
    time_threshold = now - timedelta(hours=timeframe_hours)
    
    # Track errors by model
    model_stats = defaultdict(lambda: {"total": 0, "errors": 0})
    
    # Process entries
    for entry in entries:
        # Parse timestamp
        timestamp_str = entry.get("timestamp", "")
        try:
            timestamp = datetime.fromisoformat(timestamp_str)
            if timestamp < time_threshold:
                continue
                
            model = entry.get("model", "unknown")
            model_stats[model]["total"] += 1
            
            # Count as error if error field exists or fallback was used
            if entry.get("error") or entry.get("fallback", False):
                model_stats[model]["errors"] += 1
                
        except (ValueError, TypeError):
            continue
    
    # Check for anomalies
    for model, stats in model_stats.items():
        if stats["total"] >= min_samples:
            error_rate = (stats["errors"] / stats["total"]) * 100
            if error_rate >= error_rate_threshold:
                anomalies.append(Anomaly(
                    anomaly_type="high_error_rate",
                    model=model,
                    description=f"High error rate ({error_rate:.0f}%) in last {timeframe_hours}h",
                    value=error_rate,
                    threshold=error_rate_threshold
                ))
    
    return anomalies

def detect_model_instability(entries: List[Dict[str, Any]]) -> List[Anomaly]:
    """
    Detect model instability (high failure rate in recent uses).
    
    Args:
        entries: List of telemetry entries
        
    Returns:
        List of detected anomalies
    """
    anomalies = []
    
    # Get thresholds
    failure_rate_threshold = THRESHOLDS["model_unstable"]["failure_rate"]
    sample_size = THRESHOLDS["model_unstable"]["sample_size"]
    
    # Group entries by model
    model_entries = defaultdict(list)
    
    # Sort entries by timestamp (newest first)
    sorted_entries = sorted(
        entries, 
        key=lambda x: datetime.fromisoformat(x.get("timestamp", "1970-01-01T00:00:00")),
        reverse=True
    )
    
    # Group by model
    for entry in sorted_entries:
        model = entry.get("model", "unknown")
        model_entries[model].append(entry)
    
    # Check each model's recent performance
    for model, model_data in model_entries.items():
        # Only check models with enough data
        if len(model_data) >= sample_size:
            # Take the most recent entries
            recent_entries = model_data[:sample_size]
            
            # Count failures
            failures = sum(1 for e in recent_entries if e.get("error") or e.get("fallback", False))
            
            # Calculate failure rate
            failure_rate = (failures / sample_size) * 100
            
            if failure_rate >= failure_rate_threshold:
                anomalies.append(Anomaly(
                    anomaly_type="model_unstable",
                    model=model,
                    description=f"Model fails {failure_rate:.0f}% of the time in last {sample_size} uses",
                    value=failure_rate,
                    threshold=failure_rate_threshold,
                    related_entries=recent_entries
                ))
    
    return anomalies

def detect_token_spike(entries: List[Dict[str, Any]]) -> List[Anomaly]:
    """
    Detect token usage spikes.
    
    Args:
        entries: List of telemetry entries
        
    Returns:
        List of detected anomalies
    """
    anomalies = []
    
    # Get threshold
    tokens_threshold = THRESHOLDS["token_spike"]["tokens_threshold"]
    
    # Check each entry for token spikes
    for entry in entries:
        tokens_out = entry.get("tokens_out", 0)
        if tokens_out and tokens_out > tokens_threshold:
            model = entry.get("model", "unknown")
            timestamp_str = entry.get("timestamp", "")
            
            try:
                timestamp = datetime.fromisoformat(timestamp_str)
                anomalies.append(Anomaly(
                    anomaly_type="token_spike",
                    model=model,
                    description=f"Token output spike ({tokens_out:,} tokens)",
                    timestamp=timestamp,
                    value=tokens_out,
                    threshold=tokens_threshold,
                    related_entries=[entry]
                ))
            except (ValueError, TypeError):
                continue
    
    return anomalies

def detect_fallback_surge(entries: List[Dict[str, Any]]) -> List[Anomaly]:
    """
    Detect surges in fallback events.
    
    Args:
        entries: List of telemetry entries
        
    Returns:
        List of detected anomalies
    """
    anomalies = []
    
    # Get thresholds
    fallback_count_threshold = THRESHOLDS["fallback_surge"]["count"]
    timeframe_minutes = THRESHOLDS["fallback_surge"]["timeframe_minutes"]
    
    # Group fallbacks by time windows
    fallbacks_by_window = defaultdict(list)
    
    # Process entries
    for entry in entries:
        if entry.get("fallback", False):
            timestamp_str = entry.get("timestamp", "")
            try:
                timestamp = datetime.fromisoformat(timestamp_str)
                # Create a time window key (10-minute windows)
                window_key = timestamp.strftime("%Y-%m-%d %H:%M")
                window_key = window_key[:-1] + "0"  # Round to 10-minute intervals
                fallbacks_by_window[window_key].append(entry)
            except (ValueError, TypeError):
                continue
    
    # Check for anomalies
    for window, fallbacks in fallbacks_by_window.items():
        if len(fallbacks) >= fallback_count_threshold:
            try:
                window_time = datetime.strptime(window, "%Y-%m-%d %H:%M")
                window_end = window_time + timedelta(minutes=timeframe_minutes)
                time_range = f"{window_time.strftime('%I:%M %p')}–{window_end.strftime('%I:%M %p')}"
                
                # Group by model
                models = Counter([f.get("model", "unknown") for f in fallbacks])
                top_model = models.most_common(1)[0][0] if models else "various models"
                
                anomalies.append(Anomaly(
                    anomaly_type="fallback_surge",
                    model=top_model,
                    description=f"Fallback surge with {len(fallbacks)} events between {time_range}",
                    timestamp=window_time,
                    value=len(fallbacks),
                    threshold=fallback_count_threshold,
                    related_entries=fallbacks
                ))
            except (ValueError, TypeError):
                continue
    
    return anomalies

def detect_tag_flood(entries: List[Dict[str, Any]]) -> List[Anomaly]:
    """
    Detect tag floods (single tag appears too frequently).
    
    Args:
        entries: List of telemetry entries
        
    Returns:
        List of detected anomalies
    """
    anomalies = []
    
    # Get thresholds
    tag_count_threshold = THRESHOLDS["tag_flood"]["count"]
    timeframe_hours = THRESHOLDS["tag_flood"]["timeframe_hours"]
    
    # Calculate time threshold
    now = datetime.now()
    time_threshold = now - timedelta(hours=timeframe_hours)
    
    # Count tags within timeframe
    tag_counter = Counter()
    
    # Process entries
    for entry in entries:
        timestamp_str = entry.get("timestamp", "")
        try:
            timestamp = datetime.fromisoformat(timestamp_str)
            if timestamp >= time_threshold:
                for tag in entry.get("tags", []):
                    tag_counter[tag] += 1
        except (ValueError, TypeError):
            continue
    
    # Check for anomalies
    for tag, count in tag_counter.items():
        if count >= tag_count_threshold:
            anomalies.append(Anomaly(
                anomaly_type="tag_flood",
                description=f"Tag '{tag}' appears {count} times in last {timeframe_hours}h",
                value=count,
                threshold=tag_count_threshold,
                tags=[tag]
            ))
    
    return anomalies

def detect_repetition_anomaly(entries: List[Dict[str, Any]]) -> List[Anomaly]:
    """
    Detect repetition anomalies (same prompt used multiple times).
    
    Args:
        entries: List of telemetry entries
        
    Returns:
        List of detected anomalies
    """
    anomalies = []
    
    # Get thresholds
    repetition_threshold = THRESHOLDS["repetition"]["count"]
    timeframe_hours = THRESHOLDS["repetition"]["timeframe_hours"]
    
    # Calculate time threshold
    now = datetime.now()
    time_threshold = now - timedelta(hours=timeframe_hours)
    
    # Track prompts by hash
    prompt_entries = defaultdict(list)
    
    # Process entries
    for entry in entries:
        timestamp_str = entry.get("timestamp", "")
        try:
            timestamp = datetime.fromisoformat(timestamp_str)
            if timestamp >= time_threshold:
                # We don't have the prompt in telemetry, so we'll use a combination of
                # input file and operation type as a proxy for detecting repetition
                input_file = entry.get("input_file", "")
                operation = entry.get("operation_type", "")
                
                if input_file and operation:
                    key = f"{input_file}:{operation}"
                    prompt_entries[key].append(entry)
        except (ValueError, TypeError):
            continue
    
    # Check for anomalies
    for key, entries_list in prompt_entries.items():
        if len(entries_list) >= repetition_threshold:
            input_file = entries_list[0].get("input_file", "unknown")
            operation = entries_list[0].get("operation_type", "unknown")
            
            anomalies.append(Anomaly(
                anomaly_type="repetition",
                description=f"Repeated operation on '{input_file}' ({len(entries_list)} times in {timeframe_hours}h)",
                value=len(entries_list),
                threshold=repetition_threshold,
                related_entries=entries_list
            ))
    
    return anomalies

def detect_anomalies(
    entries: List[Dict[str, Any]],
    since: Optional[timedelta] = None,
    model_filter: Optional[str] = None,
    tag_filter: Optional[List[str]] = None
) -> List[Anomaly]:
    """
    Detect all types of anomalies in telemetry data.
    
    Args:
        entries: List of telemetry entries
        since: Only consider entries since this time delta
        model_filter: Only consider entries for this model
        tag_filter: Only consider entries with these tags
        
    Returns:
        List of detected anomalies
    """
    # Filter entries if needed
    filtered_entries = entries
    
    # Filter by time
    if since:
        time_threshold = datetime.now() - since
        filtered_entries = [
            e for e in filtered_entries 
            if datetime.fromisoformat(e.get("timestamp", "1970-01-01T00:00:00")) >= time_threshold
        ]
    
    # Filter by model
    if model_filter:
        filtered_entries = [
            e for e in filtered_entries 
            if model_filter.lower() in e.get("model", "").lower()
        ]
    
    # Filter by tags
    if tag_filter:
        filtered_entries = [
            e for e in filtered_entries 
            if any(tag in e.get("tags", []) for tag in tag_filter)
        ]
    
    # Skip if not enough data
    if len(filtered_entries) < 10:
        return []
    
    # Run all anomaly detectors
    anomalies = []
    anomalies.extend(detect_high_error_rate(filtered_entries))
    anomalies.extend(detect_model_instability(filtered_entries))
    anomalies.extend(detect_token_spike(filtered_entries))
    anomalies.extend(detect_fallback_surge(filtered_entries))
    anomalies.extend(detect_tag_flood(filtered_entries))
    anomalies.extend(detect_repetition_anomaly(filtered_entries))
    
    # Sort anomalies by timestamp (newest first)
    anomalies.sort(key=lambda x: x.timestamp, reverse=True)
    
    return anomalies

def get_suggested_action(anomaly: Anomaly) -> str:
    """
    Get a suggested action for an anomaly.
    
    Args:
        anomaly: The anomaly to get a suggestion for
        
    Returns:
        A suggested action
    """
    if anomaly.anomaly_type == "high_error_rate":
        return "Check model API status and review recent prompts for potential issues"
    
    elif anomaly.anomaly_type == "model_unstable":
        return "Consider switching to a different model temporarily"
    
    elif anomaly.anomaly_type == "token_spike":
        return "Review prompt length and complexity to optimize token usage"
    
    elif anomaly.anomaly_type == "fallback_surge":
        return "Check primary model availability and consider adjusting fallback thresholds"
    
    elif anomaly.anomaly_type == "tag_flood":
        return "Review tag usage patterns and consider consolidating similar tags"
    
    elif anomaly.anomaly_type == "repetition":
        return "Check for potential infinite loops or redundant operations in your workflow"
    
    return "Investigate the anomaly further"
