"""
Prompt Enhancer Logic
Enhances prompts based on historical feedback and shaping rules
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from log_store import LogStore
from feedback_analyzer import FeedbackAnalyzer

logger = logging.getLogger(__name__)

class PromptEnhancer:
    """Enhances prompts using historical feedback and learned patterns"""
    
    def __init__(self, log_store: LogStore = None, feedback_analyzer: FeedbackAnalyzer = None):
        self.log_store = log_store or LogStore()
        self.feedback_analyzer = feedback_analyzer or FeedbackAnalyzer(self.log_store)
    
    def enhance_prompt(self, original_prompt: str, filename: str = None, 
                      tags: List[str] = None, injection_type: str = "general") -> Tuple[str, Dict[str, Any]]:
        """
        Enhance a prompt based on historical feedback and shaping rules
        
        Returns:
            Tuple of (enhanced_prompt, enhancement_metadata)
        """
        
        # Get file extension if filename provided
        file_extension = None
        if filename:
            file_extension = Path(filename).suffix.lower()
        
        # Get applicable shaping rules
        applicable_rules = self.feedback_analyzer.get_applicable_rules(
            tags=tags,
            injection_type=injection_type,
            file_extension=file_extension
        )
        
        # Check for similar past injections
        similar_injections = self._find_similar_injections(
            original_prompt, filename, tags, injection_type
        )
        
        # Analyze past failures
        failure_patterns = self._analyze_past_failures(
            filename, tags, injection_type
        )
        
        # Build enhanced prompt
        enhanced_prompt = self._build_enhanced_prompt(
            original_prompt, applicable_rules, similar_injections, failure_patterns
        )
        
        # Create enhancement metadata
        metadata = {
            "original_length": len(original_prompt),
            "enhanced_length": len(enhanced_prompt),
            "rules_applied": len(applicable_rules),
            "similar_injections_found": len(similar_injections),
            "failure_patterns_detected": len(failure_patterns),
            "enhancement_confidence": self._calculate_enhancement_confidence(
                applicable_rules, similar_injections
            ),
            "applied_rules": [rule["id"] for rule in applicable_rules],
            "enhancement_type": self._determine_enhancement_type(applicable_rules)
        }
        
        logger.info(f"Enhanced prompt: {len(original_prompt)} -> {len(enhanced_prompt)} chars, "
                   f"{len(applicable_rules)} rules applied")
        
        return enhanced_prompt, metadata
    
    def _find_similar_injections(self, prompt: str, filename: str = None, 
                               tags: List[str] = None, injection_type: str = None) -> List[Dict[str, Any]]:
        """Find similar past injections for learning"""
        
        similar_injections = []
        
        # Search by filename
        if filename:
            file_logs = self.log_store.query_by_file(filename, limit=20)
            similar_injections.extend(file_logs)
        
        # Search by tags
        if tags:
            for tag in tags:
                tag_logs = self.log_store.query_by_tag(tag, limit=10)
                similar_injections.extend(tag_logs)
        
        # Search by injection type
        if injection_type:
            type_logs = self.log_store.query_by_injection_type(injection_type, limit=10)
            similar_injections.extend(type_logs)
        
        # Remove duplicates and sort by relevance
        unique_injections = {}
        for injection in similar_injections:
            unique_injections[injection['id']] = injection
        
        # Calculate similarity scores and sort
        scored_injections = []
        for injection in unique_injections.values():
            similarity_score = self._calculate_similarity_score(
                prompt, injection, filename, tags, injection_type
            )
            scored_injections.append((injection, similarity_score))
        
        # Sort by similarity score (highest first) and return top 10
        scored_injections.sort(key=lambda x: x[1], reverse=True)
        return [injection for injection, score in scored_injections[:10]]
    
    def _calculate_similarity_score(self, prompt: str, injection: Dict[str, Any], 
                                  filename: str = None, tags: List[str] = None, 
                                  injection_type: str = None) -> float:
        """Calculate similarity score between current context and past injection"""
        score = 0.0
        
        # Prompt similarity (basic keyword matching)
        prompt_words = set(prompt.lower().split())
        injection_words = set(injection['prompt'].lower().split())
        if prompt_words and injection_words:
            common_words = prompt_words & injection_words
            score += len(common_words) / max(len(prompt_words), len(injection_words)) * 0.3
        
        # Filename similarity
        if filename and injection['filename'] == filename:
            score += 0.4
        elif filename and Path(filename).suffix == Path(injection['filename']).suffix:
            score += 0.2
        
        # Tag similarity
        if tags and injection['tags']:
            injection_tags = set(json.loads(injection['tags']))
            tag_overlap = len(set(tags) & injection_tags)
            if tag_overlap > 0:
                score += tag_overlap / max(len(tags), len(injection_tags)) * 0.2
        
        # Injection type similarity
        if injection_type and injection['injection_type'] == injection_type:
            score += 0.1
        
        return score
    
    def _analyze_past_failures(self, filename: str = None, tags: List[str] = None, 
                             injection_type: str = None) -> List[Dict[str, Any]]:
        """Analyze past failures to avoid repeating mistakes"""
        
        failure_patterns = []
        
        # Get low-rated injections for similar context
        low_rated = self.log_store.get_low_rated_injections(rating_threshold=2, limit=50)
        
        for injection in low_rated:
            # Check if this failure is relevant to current context
            if self._is_failure_relevant(injection, filename, tags, injection_type):
                pattern = {
                    "injection_id": injection['id'],
                    "rating": injection['user_rating'],
                    "feedback": injection['feedback'],
                    "prompt": injection['prompt'],
                    "issue_keywords": self._extract_issue_keywords(injection['feedback'] or "")
                }
                failure_patterns.append(pattern)
        
        return failure_patterns
    
    def _is_failure_relevant(self, injection: Dict[str, Any], filename: str = None, 
                           tags: List[str] = None, injection_type: str = None) -> bool:
        """Check if a past failure is relevant to current context"""
        
        # Check filename similarity
        if filename and injection['filename'] == filename:
            return True
        
        # Check file extension similarity
        if filename:
            current_ext = Path(filename).suffix.lower()
            injection_ext = Path(injection['filename']).suffix.lower()
            if current_ext == injection_ext:
                return True
        
        # Check tag overlap
        if tags and injection['tags']:
            injection_tags = set(json.loads(injection['tags']))
            if set(tags) & injection_tags:
                return True
        
        # Check injection type
        if injection_type and injection['injection_type'] == injection_type:
            return True
        
        return False
    
    def _extract_issue_keywords(self, feedback: str) -> List[str]:
        """Extract issue keywords from feedback"""
        issue_keywords = [
            'error', 'bug', 'fail', 'wrong', 'incorrect', 'missing', 'incomplete',
            'null', 'undefined', 'exception', 'crash', 'slow', 'performance',
            'unclear', 'confusing', 'complex', 'messy', 'unsafe', 'insecure'
        ]
        
        feedback_lower = feedback.lower()
        found_keywords = [keyword for keyword in issue_keywords if keyword in feedback_lower]
        return found_keywords
    
    def _build_enhanced_prompt(self, original_prompt: str, applicable_rules: List[Dict[str, Any]], 
                             similar_injections: List[Dict[str, Any]], 
                             failure_patterns: List[Dict[str, Any]]) -> str:
        """Build the enhanced prompt using all available information"""
        
        enhanced_prompt = original_prompt
        
        # Apply shaping rules
        for rule in applicable_rules:
            enhancement = rule.get("enhancement", "")
            if enhancement and enhancement not in enhanced_prompt:
                enhanced_prompt += f"\n\nAdditional guidance: {enhancement}"
        
        # Learn from successful similar injections
        successful_patterns = self._extract_successful_patterns(similar_injections)
        for pattern in successful_patterns:
            if pattern not in enhanced_prompt:
                enhanced_prompt += f"\n\nBased on successful past injections: {pattern}"
        
        # Avoid past failure patterns
        failure_avoidance = self._generate_failure_avoidance_guidance(failure_patterns)
        if failure_avoidance:
            enhanced_prompt += f"\n\nImportant - avoid these common issues: {failure_avoidance}"
        
        # Add context-specific enhancements
        context_enhancements = self._generate_context_enhancements(
            applicable_rules, similar_injections
        )
        if context_enhancements:
            enhanced_prompt += f"\n\nContext-specific guidance: {context_enhancements}"
        
        return enhanced_prompt
    
    def _extract_successful_patterns(self, similar_injections: List[Dict[str, Any]]) -> List[str]:
        """Extract patterns from successful similar injections"""
        patterns = []
        
        # Get high-rated injections
        successful_injections = [
            inj for inj in similar_injections 
            if inj.get('user_rating', 0) >= 4 and inj.get('success', False)
        ]
        
        if not successful_injections:
            return patterns
        
        # Analyze common elements in successful prompts
        common_phrases = [
            "add error handling", "include null checks", "optimize performance",
            "add comments", "follow best practices", "include validation",
            "handle edge cases", "add logging", "use proper naming"
        ]
        
        for phrase in common_phrases:
            count = sum(1 for inj in successful_injections if phrase in inj['prompt'].lower())
            if count >= len(successful_injections) * 0.5:  # If 50%+ of successful injections use this
                patterns.append(phrase)
        
        return patterns[:3]  # Limit to top 3 patterns
    
    def _generate_failure_avoidance_guidance(self, failure_patterns: List[Dict[str, Any]]) -> str:
        """Generate guidance to avoid past failure patterns"""
        if not failure_patterns:
            return ""
        
        # Count common failure keywords
        from collections import Counter
        all_keywords = []
        for pattern in failure_patterns:
            all_keywords.extend(pattern['issue_keywords'])
        
        keyword_counts = Counter(all_keywords)
        top_issues = keyword_counts.most_common(3)
        
        if not top_issues:
            return ""
        
        guidance_map = {
            'error': "ensure proper error handling",
            'null': "add null/None safety checks",
            'performance': "optimize for efficiency",
            'unclear': "add clear documentation",
            'incomplete': "handle all edge cases",
            'unsafe': "implement security measures"
        }
        
        guidance_parts = []
        for issue, count in top_issues:
            if issue in guidance_map:
                guidance_parts.append(guidance_map[issue])
        
        return ", ".join(guidance_parts)
    
    def _generate_context_enhancements(self, applicable_rules: List[Dict[str, Any]], 
                                     similar_injections: List[Dict[str, Any]]) -> str:
        """Generate context-specific enhancements"""
        enhancements = []
        
        # File type specific enhancements
        if any('python' in str(rule.get('condition', {})) for rule in applicable_rules):
            enhancements.append("follow PEP 8 style guidelines")
        
        if any('javascript' in str(rule.get('condition', {})) for rule in applicable_rules):
            enhancements.append("use modern ES6+ syntax")
        
        # Injection type specific enhancements
        rule_types = [rule.get('condition', {}).get('injection_type') for rule in applicable_rules]
        if 'bugfix' in rule_types:
            enhancements.append("include test cases to verify the fix")
        elif 'optimize' in rule_types:
            enhancements.append("measure performance improvements")
        elif 'refactor' in rule_types:
            enhancements.append("maintain existing functionality")
        
        return ", ".join(enhancements[:2])  # Limit to 2 enhancements
    
    def _calculate_enhancement_confidence(self, applicable_rules: List[Dict[str, Any]], 
                                        similar_injections: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for the enhancement"""
        if not applicable_rules and not similar_injections:
            return 0.1  # Low confidence without historical data
        
        # Base confidence on rule confidence scores
        rule_confidence = 0.0
        if applicable_rules:
            rule_confidence = sum(rule.get('confidence', 0) for rule in applicable_rules) / len(applicable_rules)
        
        # Adjust based on similar injections
        injection_confidence = 0.0
        if similar_injections:
            successful_count = sum(1 for inj in similar_injections if inj.get('user_rating', 0) >= 3)
            injection_confidence = successful_count / len(similar_injections)
        
        # Combine confidences
        total_confidence = (rule_confidence * 0.7) + (injection_confidence * 0.3)
        return min(total_confidence, 0.95)  # Cap at 95%
    
    def _determine_enhancement_type(self, applicable_rules: List[Dict[str, Any]]) -> str:
        """Determine the type of enhancement applied"""
        if not applicable_rules:
            return "none"
        
        rule_types = [rule.get('type', 'unknown') for rule in applicable_rules]
        
        if 'tag_based' in rule_types:
            return "tag_based"
        elif 'injection_type_based' in rule_types:
            return "injection_type_based"
        elif 'keyword_based' in rule_types:
            return "keyword_based"
        else:
            return "mixed"


# Convenience functions
def enhance_prompt(prompt: str, filename: str = None, tags: List[str] = None, 
                  injection_type: str = "general") -> Tuple[str, Dict[str, Any]]:
    """Convenience function to enhance a prompt"""
    enhancer = PromptEnhancer()
    return enhancer.enhance_prompt(prompt, filename, tags, injection_type)


if __name__ == "__main__":
    # Test the prompt enhancer
    enhancer = PromptEnhancer()
    
    # Test enhancement
    original = "Fix the bug in this function"
    enhanced, metadata = enhancer.enhance_prompt(
        original, 
        filename="test.py", 
        tags=["bugfix", "error-handling"],
        injection_type="bugfix"
    )
    
    print(f"Original: {original}")
    print(f"Enhanced: {enhanced}")
    print(f"Metadata: {metadata}")
    
    print("✅ Prompt enhancer test completed!")
