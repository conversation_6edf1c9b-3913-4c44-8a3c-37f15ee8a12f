{"version": "1.0", "rules": {"tag_null-check_error": {"id": "tag_null-check_error", "type": "tag_based", "condition": {"tag": "null-check"}, "issue": "error", "enhancement": "Add comprehensive error handling with try-catch blocks", "confidence": 0.2, "occurrences": 2, "created_at": "2025-05-26T13:59:28.246235"}, "tag_bugfix_error": {"id": "tag_bugfix_error", "type": "tag_based", "condition": {"tag": "bugfix"}, "issue": "error", "enhancement": "Add comprehensive error handling with try-catch blocks", "confidence": 0.2, "occurrences": 2, "created_at": "2025-05-26T13:59:28.246235"}, "type_bugfix_error": {"id": "type_bugfix_error", "type": "injection_type_based", "condition": {"injection_type": "bugfix"}, "issue": "error", "enhancement": "Add comprehensive error handling with try-catch blocks", "confidence": 0.2, "occurrences": 2, "created_at": "2025-05-26T13:59:28.246235"}, "keyword_error": {"id": "keyword_error", "type": "keyword_based", "condition": {"feedback_keyword": "error"}, "issue": "error", "enhancement": "Add comprehensive error handling with try-catch blocks", "confidence": 0.4, "occurrences": 2, "created_at": "2025-05-26T13:59:28.246235"}, "keyword_null": {"id": "keyword_null", "type": "keyword_based", "condition": {"feedback_keyword": "null"}, "issue": "null", "enhancement": "Add null/None checks before accessing properties or methods", "confidence": 0.4, "occurrences": 2, "created_at": "2025-05-26T13:59:28.246235"}, "keyword_exception": {"id": "keyword_exception", "type": "keyword_based", "condition": {"feedback_keyword": "exception"}, "issue": "exception", "enhancement": "Include proper exception handling and error messages", "confidence": 0.2, "occurrences": 1, "created_at": "2025-05-26T13:59:28.246235"}, "keyword_missing": {"id": "keyword_missing", "type": "keyword_based", "condition": {"feedback_keyword": "missing"}, "issue": "missing", "enhancement": "Ensure all required functionality is implemented", "confidence": 0.2, "occurrences": 1, "created_at": "2025-05-26T13:59:28.246235"}}, "patterns": {}, "statistics": {"total_analyses": 1, "rules_created": 7, "last_analysis": "2025-05-26T13:59:28.246235"}}