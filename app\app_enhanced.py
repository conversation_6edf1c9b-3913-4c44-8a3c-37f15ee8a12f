"""
Enhanced CodeCrusher FastAPI Backend with APIRouter
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

# Import routers
from routes.inject import router as inject_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("codecrusher")

# Create FastAPI app
app = FastAPI(
    title="CodeCrusher Enhanced API",
    description="Enhanced CodeCrusher backend with APIRouter and real-time WebSocket streaming",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(inject_router, prefix="", tags=["injection"])

@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": "CodeCrusher Enhanced API is running",
        "version": "2.0.0",
        "endpoints": {
            "inject": "/api/inject",
            "websocket": "/ws/logs",
            "models": "/api/models",
            "status": "/api/status"
        }
    }

@app.get("/health")
async def health():
    """Detailed health check."""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "api_version": "enhanced"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
