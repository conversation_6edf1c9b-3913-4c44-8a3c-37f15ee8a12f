import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { EnterpriseFooter } from './EnterpriseFooter';

const BackendDashboard = () => {
  // Form state
  const [source, setSource] = useState('./src');
  const [promptText, setPromptText] = useState('Add comprehensive error handling and logging');
  const [model, setModel] = useState('auto');
  const [tag, setTag] = useState('web-ui');
  const [autoModel, setAutoModel] = useState(true);
  const [apply, setApply] = useState(false);
  const [useFallback, setUseFallback] = useState(true);
  const [recursive, setRecursive] = useState(true);
  const [ext, setExt] = useState('py');

  // UI state
  const [logs, setLogs] = useState('');
  const [progress, setProgress] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [backendStatus, setBackendStatus] = useState(null);

  const logRef = useRef<HTMLPreElement>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const models = [
    { value: 'auto', label: '🤖 Auto Selection' },
    { value: 'mixtral', label: '🧠 Mixtral' },
    { value: 'mistral', label: '🔥 Mistral' },
    { value: 'gemma', label: '💎 Gemma' },
    { value: 'llama3', label: '🦙 LLaMA 3' }
  ];

  const extensions = [
    { value: 'py', label: '🐍 Python (.py)' },
    { value: 'js', label: '📜 JavaScript (.js)' },
    { value: 'ts', label: '📘 TypeScript (.ts)' },
    { value: 'java', label: '☕ Java (.java)' },
    { value: 'cpp', label: '⚡ C++ (.cpp)' },
    { value: 'html', label: '🌐 HTML (.html)' }
  ];

  // Connect to WebSocket
  const connectWebSocket = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    wsRef.current = new WebSocket('ws://localhost:8000/ws/logs');

    wsRef.current.onopen = () => {
      setIsConnected(true);
      console.log('🔗 WebSocket connected');
    };

    wsRef.current.onmessage = (event) => {
      const message = event.data;
      setLogs(prev => prev + message + '\n');

      // Extract progress from messages
      if (message.includes('Progress:')) {
        const match = message.match(/Progress: (\d+)%/);
        if (match) {
          setProgress(Number(match[1]));
        }
      }

      // Check for completion
      if (message.includes('completed successfully') || message.includes('injection failed')) {
        setIsRunning(false);
      }
    };

    wsRef.current.onclose = () => {
      setIsConnected(false);
      console.log('🔌 WebSocket disconnected');

      // Attempt to reconnect after 3 seconds
      setTimeout(() => {
        if (!isConnected) {
          connectWebSocket();
        }
      }, 3000);
    };

    wsRef.current.onerror = (error) => {
      console.error('🚨 WebSocket error:', error);
      setIsConnected(false);
    };
  };

  // Check backend status
  const checkBackendStatus = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/status');
      if (response.ok) {
        const status = await response.json();
        setBackendStatus(status);
      }
    } catch (error) {
      console.error('Failed to check backend status:', error);
      setBackendStatus(null);
    }
  };

  // Initialize
  useEffect(() => {
    connectWebSocket();
    checkBackendStatus();

    // Check status every 30 seconds
    const statusInterval = setInterval(checkBackendStatus, 30000);

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      clearInterval(statusInterval);
    };
  }, []);

  // Auto-scroll logs
  useEffect(() => {
    if (logRef.current) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [logs]);

  const runInjection = async () => {
    if (!isConnected) {
      alert('WebSocket not connected. Please wait for connection.');
      return;
    }

    setIsRunning(true);
    setLogs('');
    setProgress(0);

    try {
      const injectionRequest = {
        source,
        prompt_text: promptText,
        model,
        tag,
        auto_model: autoModel,
        apply,
        use_fallback: useFallback,
        recursive,
        ext
      };

      const response = await fetch('http://localhost:8000/api/inject', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(injectionRequest)
      });

      const result = await response.json();

      if (!result.success) {
        console.error('Injection failed:', result.error);
      }

    } catch (error) {
      console.error('Request failed:', error);
      setIsRunning(false);
      setLogs(prev => prev + `❌ Request failed: ${error}\n`);
    }
  };

  const clearLogs = () => {
    setLogs('');
    setProgress(0);
  };

  const stopInjection = () => {
    setIsRunning(false);
    // In a real implementation, this would send a stop signal
  };

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 p-6 space-y-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">🚀 CodeCrusher Backend Dashboard</h1>
          <div className="flex items-center space-x-4">
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? '🟢 WebSocket Connected' : '🔴 WebSocket Disconnected'}
            </Badge>
            {backendStatus && (
              <Badge variant={(backendStatus as any).virtualenv === 'active' ? "default" : "secondary"}>
                {(backendStatus as any).virtualenv === 'active' ? '🐍 VirtualEnv Active' : '⚠️ VirtualEnv Not Found'}
              </Badge>
            )}
          </div>
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <Card>
          <CardHeader>
            <CardTitle>⚙️ Injection Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="source">Source Path</Label>
              <Input
                id="source"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                placeholder="./src"
              />
            </div>

            <div>
              <Label htmlFor="prompt">Injection Prompt</Label>
              <Textarea
                id="prompt"
                value={promptText}
                onChange={(e) => setPromptText(e.target.value)}
                rows={3}
                placeholder="Enter your injection prompt..."
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="tag">Tag</Label>
                <Input
                  id="tag"
                  value={tag}
                  onChange={(e) => setTag(e.target.value)}
                  placeholder="web-ui"
                />
              </div>
              <div>
                <Label htmlFor="ext">File Extension</Label>
                <Select value={ext} onValueChange={setExt}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {extensions.map((extension) => (
                      <SelectItem key={extension.value} value={extension.value}>
                        {extension.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="model">AI Model</Label>
              <Select value={model} onValueChange={setModel} disabled={autoModel}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {models.map((modelOption) => (
                    <SelectItem key={modelOption.value} value={modelOption.value}>
                      {modelOption.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Switch checked={autoModel} onCheckedChange={setAutoModel} id="auto-model" />
                <Label htmlFor="auto-model">Auto Model Routing</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch checked={useFallback} onCheckedChange={setUseFallback} id="fallback" />
                <Label htmlFor="fallback">Use Fallback</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch checked={recursive} onCheckedChange={setRecursive} id="recursive" />
                <Label htmlFor="recursive">Recursive Processing</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch checked={apply} onCheckedChange={setApply} id="apply" />
                <Label htmlFor="apply" className={apply ? "text-red-600 font-medium" : ""}>
                  {apply ? "⚠️ Apply Changes" : "👁️ Preview Mode"}
                </Label>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={runInjection}
                disabled={isRunning || !isConnected || !promptText.trim()}
                className="flex-1"
              >
                {isRunning ? '⏳ Running...' : '🚀 Run Injection'}
              </Button>

              {isRunning && (
                <Button
                  onClick={stopInjection}
                  variant="destructive"
                  className="px-6"
                >
                  🛑 Stop
                </Button>
              )}

              <Button
                onClick={clearLogs}
                variant="outline"
                className="px-6"
              >
                🗑️ Clear
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Logs & Progress Panel */}
        <Card>
          <CardHeader>
            <CardTitle>📊 Real-time Logs & Progress</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-3" />
            </div>

            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Live Output</span>
                <span className="text-gray-600">
                  {logs.split('\n').length - 1} messages
                </span>
              </div>
              <pre
                ref={logRef}
                className="bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto text-sm font-mono whitespace-pre-wrap border"
              >
                {logs || 'Waiting for logs...'}
              </pre>
            </div>

            {backendStatus && (
              <div className="text-xs text-gray-600 space-y-1">
                <div>Backend Status: {(backendStatus as any).status}</div>
                <div>VirtualEnv: {(backendStatus as any).virtualenv}</div>
                <div>Connected Clients: {(backendStatus as any).connected_clients}</div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      </div>

      {/* Enterprise Footer */}
      <EnterpriseFooter />
    </div>
  );
};

export default BackendDashboard;
