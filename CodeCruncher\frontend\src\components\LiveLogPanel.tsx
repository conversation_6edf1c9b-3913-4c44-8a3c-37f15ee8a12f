import React, { useEffect, useRef } from 'react';
import { useLogStream } from '@/hooks/useLogStream';
import { getWebSocketUrl } from '@/utils/client';

interface LiveLogPanelProps {
  wsUrl?: string;
  height?: string;
  className?: string;
}

export default function LiveLogPanel({
  wsUrl = getWebSocketUrl(),
  height = '300px',
  className = ''
}: LiveLogPanelProps) {
  const { logs, isConnected } = useLogStream(wsUrl);
  const bottomRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [logs]);

  const formatLogMessage = (log: any) => {
    try {
      // If it's already a parsed object, format it
      if (typeof log === 'object' && log.message) {
        const timestamp = new Date(log.timestamp).toLocaleTimeString();
        return `[${timestamp}] ${log.message}`;
      }
      // If it's a string, try to parse it
      if (typeof log === 'string') {
        try {
          const parsed = JSON.parse(log);
          const timestamp = new Date(parsed.timestamp).toLocaleTimeString();
          return `[${timestamp}] ${parsed.message}`;
        } catch {
          // If parsing fails, return as-is
          return log;
        }
      }
      return String(log);
    } catch {
      return String(log);
    }
  };

  return (
    <div
      className={`relative ${className}`}
      style={{
        height,
        overflowY: 'auto',
        background: '#111',
        color: '#eee',
        padding: '10px',
        fontFamily: 'monospace',
        borderRadius: '4px',
        border: '1px solid #333'
      }}
    >
      {/* Connection status indicator */}
      <div
        style={{
          position: 'absolute',
          top: '5px',
          right: '10px',
          fontSize: '12px',
          color: isConnected ? '#4ade80' : '#ef4444'
        }}
      >
        ● {isConnected ? 'Connected' : 'Disconnected'}
      </div>

      {/* Log messages */}
      {logs.length === 0 ? (
        <div style={{ color: '#666', fontStyle: 'italic' }}>
          {isConnected ? 'Waiting for logs...' : 'Connecting...'}
        </div>
      ) : (
        logs.map((log, i) => (
          <div key={i} style={{ marginBottom: '2px', lineHeight: '1.4' }}>
            {formatLogMessage(log)}
          </div>
        ))
      )}

      {/* Auto-scroll anchor */}
      <div ref={bottomRef} />
    </div>
  );
}
