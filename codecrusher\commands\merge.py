"""
Merge command for CodeCrusher.

This module provides the merge command for CodeCrusher, which merges multiple
injections into a single result.
"""

import typer
import json
import os
import uuid
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from typing import Optional, List, Dict, Any

# Import tag manager
from codecrusher.tag_manager import (
    parse_user_tags,
    merge_tags,
    PARENT_TAG_PREFIX
)

# Import file utils
from codecrusher.file_utils import (
    format_annotation
)

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

@app.command("run")
def run_merge(
    inputs: str = typer.Option(..., "--inputs", "-i", help="Comma-separated list of input files to merge"),
    output: str = typer.Option(..., "--output", "-o", help="Output file path"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Additional tags to add (space-separated)"),
    annotate_output: bool = typer.Option(False, "--annotate-output", help="Annotate output with tags"),
):
    """
    Merge multiple injections into a single result.
    
    This command takes multiple input files, extracts their content and tags,
    and merges them into a single output file. Tags are combined and special
    tags like @cached and @fallback are handled appropriately.
    
    Example:
        codecrusher merge run -i file1.py,file2.py -o merged.py
    """
    # Parse input files
    input_files = [f.strip() for f in inputs.split(",") if f.strip()]
    
    # Parse additional tags
    additional_tags = parse_user_tags(tag)
    
    # Format additional tags for display
    additional_tags_display = ", ".join(additional_tags) if additional_tags else "None"
    
    console.print(Panel(
        f"[bold]CodeCrusher Merge[/bold]\n\n"
        f"[cyan]Input files:[/cyan] {', '.join(input_files)}\n"
        f"[cyan]Output file:[/cyan] {output}\n"
        f"[cyan]Additional Tags:[/cyan] {additional_tags_display}\n"
        f"[cyan]Annotate Output:[/cyan] {'Enabled' if annotate_output else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))
    
    # Validate input files
    for input_file in input_files:
        if not os.path.exists(input_file):
            console.print(f"[bold red]❌ Error:[/bold red] Input file not found: {input_file}")
            raise typer.Exit(code=1)
    
    # Read input files and extract content and tags
    contents = []
    all_tag_sets = []
    parent_ids = []
    
    for input_file in input_files:
        try:
            # Read file content
            with open(input_file, "r", encoding="utf-8") as f:
                content = f.read()
            
            # Extract tags from content (assuming they're in comments)
            file_tags = []
            lines = content.split("\n")[:5]
            for line in lines:
                if "Tags:" in line:
                    # Extract tags after "Tags:"
                    tag_part = line.split("Tags:")[1].strip()
                    file_tags.extend([tag.strip() for tag in tag_part.split() if tag.strip()])
            
            # Add content and tags
            contents.append(content)
            all_tag_sets.append(file_tags)
            
            # Extract parent ID if present
            for tag in file_tags:
                if tag.startswith(PARENT_TAG_PREFIX):
                    parent_ids.append(tag[len(PARENT_TAG_PREFIX):])
        except Exception as e:
            console.print(f"[bold red]❌ Error:[/bold red] Failed to read file {input_file}: {str(e)}")
            raise typer.Exit(code=1)
    
    # Generate a unique ID for this merge for provenance tracking
    merge_id = str(uuid.uuid4())[:8]
    
    # Merge tags
    merged_tags = merge_tags(all_tag_sets)
    
    # Add additional tags
    merged_tags.extend(additional_tags)
    
    # Add parent IDs as tags
    for parent_id in parent_ids:
        merged_tags.append(f"{PARENT_TAG_PREFIX}{parent_id}")
    
    # Deduplicate tags
    merged_tags = list(set(merged_tags))
    
    # Format tags for display
    tags_display = ", ".join(merged_tags) if merged_tags else "None"
    
    console.print(f"[cyan]Merged Tags:[/cyan] {tags_display}")
    
    # Merge content (placeholder implementation)
    merged_content = "\n\n".join(contents)
    
    # Add annotation if enabled
    if annotate_output:
        # Determine if the output is code or plain text
        is_code = True  # Assume code for now
        
        # Format the annotation
        annotation = format_annotation(merged_tags, output, is_code)
        
        # Add the annotation to the output
        merged_content = annotation + merged_content
    
    # Write merged content to output file
    try:
        with open(output, "w", encoding="utf-8") as f:
            f.write(merged_content)
        console.print(f"[bold green]✅ Merged content written to {output}[/bold green]")
    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] Failed to write output file: {str(e)}")
        raise typer.Exit(code=1)
    
    return True
