import { useEffect, useState, useRef, useCallback } from 'react';
import { buildApiUrl } from '@/utils/client';

export interface LogMessage {
  type: 'injection' | 'system' | 'error' | 'test' | 'progress' | 'stats';
  message: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'success';
  progress?: number;
  stats?: {
    totalFiles: number;
    processedFiles: number;
    failedFiles: number;
    successRate: number;
  };
}

export interface UseLogStreamReturn {
  logs: LogMessage[];
  isConnected: boolean;
  connectionError: string | null;
  clearLogs: () => void;
  sendTestMessage: (message: string) => void;
  latestProgress: number;
  latestStats: LogMessage['stats'] | null;
}

export function useLogStream(wsUrl: string): UseLogStreamReturn {
  const [logs, setLogs] = useState<LogMessage[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [latestProgress, setLatestProgress] = useState(0);
  const [latestStats, setLatestStats] = useState<LogMessage['stats'] | null>(null);

  const ws = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    try {
      ws.current = new WebSocket(wsUrl);

      ws.current.onopen = () => {
        console.log('🔗 WebSocket connected to:', wsUrl);
        setIsConnected(true);
        setConnectionError(null);
        reconnectAttempts.current = 0;
      };

      ws.current.onmessage = (event) => {
        try {
          const data: LogMessage = JSON.parse(event.data);

          // Add timestamp if not present
          if (!data.timestamp) {
            data.timestamp = new Date().toISOString();
          }

          // Handle different message types
          switch (data.type) {
            case 'progress':
              if (typeof data.progress === 'number') {
                setLatestProgress(data.progress);
              }
              break;
            case 'stats':
              if (data.stats) {
                setLatestStats(data.stats);
              }
              break;
          }

          // Add to logs
          setLogs((prev) => [...prev, data]);

        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
          // Add raw message as fallback
          const fallbackMessage: LogMessage = {
            type: 'system',
            message: event.data,
            timestamp: new Date().toISOString(),
            level: 'info'
          };
          setLogs((prev) => [...prev, fallbackMessage]);
        }
      };

      ws.current.onclose = (event) => {
        console.log('🔌 WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);

        // Attempt to reconnect if not a normal closure
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 10000);
          console.log(`🔄 Attempting to reconnect in ${delay}ms (attempt ${reconnectAttempts.current + 1}/${maxReconnectAttempts})`);

          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connect();
          }, delay);
        } else if (reconnectAttempts.current >= maxReconnectAttempts) {
          setConnectionError('Failed to reconnect after multiple attempts');
        }
      };

      ws.current.onerror = (error) => {
        console.error('🚨 WebSocket error:', error);
        setConnectionError('WebSocket connection error');
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionError('Failed to create WebSocket connection');
    }
  }, [wsUrl]);

  useEffect(() => {
    connect();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (ws.current) {
        ws.current.close(1000, 'Component unmounting');
      }
    };
  }, [connect]);

  const clearLogs = useCallback(() => {
    setLogs([]);
    setLatestProgress(0);
    setLatestStats(null);
  }, []);

  const sendTestMessage = useCallback(async (message: string) => {
    try {
      const testLogUrl = buildApiUrl('/test-log');
      const response = await fetch(testLogUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `message=${encodeURIComponent(message)}&level=info`
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      console.log('✅ Test message sent successfully');
    } catch (error) {
      console.error('❌ Failed to send test message:', error);
    }
  }, []);

  return {
    logs,
    isConnected,
    connectionError,
    clearLogs,
    sendTestMessage,
    latestProgress,
    latestStats
  };
}
