# CodeCrusher Settings Sync API Example

This is a simple FastAPI backend example that demonstrates the settings sync endpoints required for the VS Code extension live sync functionality.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install fastapi uvicorn
```

### 2. Run the API Server

```bash
cd vscode-extension/backend-example
python settings_api.py
```

The API will be available at:
- **API Base**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Settings Endpoint**: http://localhost:8000/api/settings/vscode

### 3. Test with VS Code Extension

1. Open VS Code with the CodeCrusher extension
2. Make sure `codecrusher.enableSettingsSync` is set to `true`
3. Set `codecrusher.dashboardUrl` to `http://localhost:8000`
4. Use the extension and watch settings sync in real-time!

## 📡 API Endpoints

### GET /api/settings/vscode

Returns current VS Code settings:

```json
{
  "model": "auto",
  "fallback": true,
  "mode": "preview",
  "lastSyncTimestamp": 1703123456789
}
```

### POST /api/settings/vscode

Updates VS Code settings:

```bash
curl -X POST http://localhost:8000/api/settings/vscode \
  -H "Content-Type: application/json" \
  -d '{
    "model": "mixtral",
    "fallback": true,
    "mode": "apply"
  }'
```

### Additional Endpoints

- `GET /` - API information
- `GET /api/settings/vscode/history` - Settings history (example)
- `DELETE /api/settings/vscode` - Reset to defaults

## 🔄 How Settings Sync Works

### 1. VS Code Extension → Dashboard (Push)

When a user makes selections in VS Code:

1. User selects different model/mode/fallback in QuickPick
2. Extension updates local VS Code configuration
3. Extension sends POST request to `/api/settings/vscode`
4. Dashboard stores the new settings with timestamp

### 2. Dashboard → VS Code Extension (Pull)

The extension automatically pulls remote settings:

1. On activation (startup)
2. Every 2 minutes (configurable)
3. Extension sends GET request to `/api/settings/vscode`
4. If remote timestamp > local timestamp, apply remote settings

### 3. Conflict Resolution

- **Local changes**: VS Code settings take precedence
- **Remote changes**: Dashboard settings take precedence
- **Timestamp-based**: Most recent change wins

## 🛠️ Configuration

The VS Code extension supports these settings:

```json
{
  "codecrusher.enableSettingsSync": true,
  "codecrusher.dashboardUrl": "http://localhost:8000",
  "codecrusher.syncInterval": 120
}
```

## 📝 Example Usage

### Test Settings Sync

1. **Start the API server**:
   ```bash
   python settings_api.py
   ```

2. **Open VS Code** with CodeCrusher extension

3. **Change settings** in VS Code:
   - Right-click a file → "Inject with CodeCrusher"
   - Select different model (e.g., "mixtral")
   - Select different mode (e.g., "apply")

4. **Check API logs** - you should see:
   ```
   POST /api/settings/vscode - Updated: {'model': 'mixtral', 'fallback': True, 'mode': 'apply', 'lastSyncTimestamp': 1703123456789}
   ```

5. **Simulate remote change**:
   ```bash
   curl -X POST http://localhost:8000/api/settings/vscode \
     -H "Content-Type: application/json" \
     -d '{"model": "llama3-70b", "fallback": false, "mode": "preview"}'
   ```

6. **Wait for sync** (or restart VS Code) - settings will be pulled and applied

## 🔧 Production Considerations

For production use, consider:

1. **Database Storage**: Replace in-memory storage with PostgreSQL/MongoDB
2. **Authentication**: Add user authentication and authorization
3. **Rate Limiting**: Implement rate limiting for API endpoints
4. **Logging**: Add comprehensive logging and monitoring
5. **HTTPS**: Use HTTPS for secure communication
6. **Validation**: Add more robust input validation
7. **Caching**: Implement caching for better performance

## 🧪 Testing

Test the API manually:

```bash
# Get current settings
curl http://localhost:8000/api/settings/vscode

# Update settings
curl -X POST http://localhost:8000/api/settings/vscode \
  -H "Content-Type: application/json" \
  -d '{"model": "mixtral", "fallback": true, "mode": "apply"}'

# Reset to defaults
curl -X DELETE http://localhost:8000/api/settings/vscode
```

## 📚 Integration with CodeCrusher

This API can be integrated with your existing CodeCrusher dashboard:

1. Add these endpoints to your Django/FastAPI backend
2. Update the frontend to allow settings modification
3. Use the same data model for consistency
4. Implement user-specific settings if needed

The VS Code extension will automatically sync with any backend that implements these endpoints!
