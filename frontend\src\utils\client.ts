// Client utilities for dynamic URL configuration
import { API_CONFIG } from '@/config/api';

/**
 * Get the base server URL with environment variable support
 * Priority: VITE_SERVER_URL > VITE_API_URL > window.location.origin > localhost:8000
 */
export const getBaseUrl = (): string => {
  return API_CONFIG.BASE_URL;
};

/**
 * Get the WebSocket URL for real-time streaming
 * Forces IPv4 (127.0.0.1) to avoid IPv6 connection issues
 */
export const getWebSocketUrl = (path: string = '/logs'): string => {
  const baseWsUrl = API_CONFIG.BASE_URL.replace(/^http/, 'ws');
  // Force IPv4 by replacing localhost/::1 with 127.0.0.1
  const ipv4WsUrl = baseWsUrl
    .replace('localhost', '127.0.0.1')
    .replace('::1', '127.0.0.1');
  return `${ipv4WsUrl}/ws${path}`;
};

/**
 * Get the stream URL for event streaming
 */
export const getStreamURL = (): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  return `${baseUrl}/stream`;
};

/**
 * Get the inject URL for code injection
 */
export const getInjectURL = (): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  return `${baseUrl}/inject`;
};

/**
 * Get the health check URL
 */
export const getHealthURL = (): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  return `${baseUrl}/health`;
};

/**
 * Get the models URL
 */
export const getModelsURL = (): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  return `${baseUrl}/models`;
};

/**
 * Get the extensions URL
 */
export const getExtensionsURL = (): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  return `${baseUrl}/extensions`;
};

/**
 * Get the validate URL
 */
export const getValidateURL = (): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  return `${baseUrl}/validate`;
};

/**
 * Get the run-injection URL for job-based injections
 */
export const getRunInjectionURL = (): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  return `${baseUrl}/run-injection`;
};

/**
 * Build a full API URL from an endpoint path
 */
export const buildApiUrl = (endpoint: string): string => {
  const baseUrl = getBaseUrl().replace(/\/$/, '');
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

/**
 * Build a WebSocket URL from a path
 */
export const buildWsUrl = (path: string = '/logs'): string => {
  return getWebSocketUrl(path);
};

/**
 * Test connection to the backend server
 */
export const testServerConnection = async (): Promise<{ success: boolean; message: string; url: string }> => {
  const healthUrl = getHealthURL();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

    const response = await fetch(healthUrl, {
      method: 'GET',
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();
      return {
        success: true,
        message: `Connected to backend successfully. Status: ${data.status || 'healthy'}`,
        url: healthUrl
      };
    } else {
      return {
        success: false,
        message: `Backend responded with ${response.status}: ${response.statusText}`,
        url: healthUrl
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Failed to connect to backend: ${error}`,
      url: healthUrl
    };
  }
};

/**
 * Log current configuration for debugging
 */
export const logApiConfiguration = (): void => {
  console.group('🔧 API Configuration');
  console.log('Base URL:', getBaseUrl());
  console.log('WebSocket URL:', getWebSocketUrl());
  console.log('Environment Variables:', {
    VITE_SERVER_URL: import.meta.env.VITE_SERVER_URL,
    VITE_API_URL: import.meta.env.VITE_API_URL,
    VITE_WS_URL: import.meta.env.VITE_WS_URL,
    VITE_DEV_MODE: import.meta.env.VITE_DEV_MODE
  });
  console.log('Available Endpoints:', {
    health: getHealthURL(),
    inject: getInjectURL(),
    models: getModelsURL(),
    extensions: getExtensionsURL(),
    validate: getValidateURL(),
    runInjection: getRunInjectionURL()
  });
  console.groupEnd();
};

// Auto-log configuration in development mode
if (import.meta.env.VITE_DEV_MODE === 'true') {
  logApiConfiguration();
}
