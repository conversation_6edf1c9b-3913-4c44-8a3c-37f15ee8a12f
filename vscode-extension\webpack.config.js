// @ts-check

'use strict';

const path = require('path');

/** @type {import('webpack').Configuration} */
const config = {
  target: 'node', // Ensures compatibility with VS Code's node runtime

  entry: './src/extension.ts', // Entry point to your extension source

  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'extension.js', // Output bundle file
    libraryTarget: 'commonjs2', // Required for VS Code extensions
    devtoolModuleFilenameTemplate: '../[resource-path]',
  },

  devtool: 'source-map', // Generate source maps for better debugging

  externals: {
    vscode: 'commonjs vscode', // Ignore vscode module (handled by runtime)
  },

  resolve: {
    extensions: ['.ts', '.js'], // Resolve TypeScript and JavaScript
  },

  module: {
    rules: [
      {
        test: /\.ts$/, // All .ts files
        exclude: /node_modules/,
        use: [
          {
            loader: 'ts-loader', // Use ts-loader to compile TS
          },
        ],
      },
    ],
  },
};

module.exports = config;
