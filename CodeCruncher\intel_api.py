"""
Phase 13 Step 3: Lightweight Shared Intelligence Interface
Provides a unified API for accessing feedback, configuration, and intelligence data
across CLI and VS Code extension.
"""

import json
import os
import logging
from typing import List, Dict, Any, Optional
from dataclasses import asdict
from datetime import datetime, timedelta
from pathlib import Path

# Import shared intelligence components
try:
    from intel_paths import get_feedback_log_path, get_config_path, get_injection_registry_path, get_quality_scores_path
    from feedback_logger import FeedbackEntry, FeedbackLogger
except ImportError as e:
    logging.warning(f"Failed to import shared intelligence components: {e}")
    # Fallback implementations
    def get_feedback_log_path():
        return "feedback_log.json"
    def get_config_path():
        return "config.json"
    def get_injection_registry_path():
        return "injection_registry.json"
    def get_quality_scores_path():
        return "quality_scores.json"


class IntelAPI:
    """
    Lightweight shared intelligence interface for CLI and VS Code extension.
    Provides unified access to feedback, configuration, and intelligence data.
    """

    def __init__(self):
        """Initialize the Intel API with shared paths."""
        self.feedback_log_path = get_feedback_log_path()
        self.config_path = get_config_path()
        self.injection_registry_path = get_injection_registry_path()
        self.quality_scores_path = get_quality_scores_path()

        # Prompt shaping paths
        self.intel_dir = Path(os.path.expanduser("~/.codecrusher/intel"))
        self.prompt_weights_path = self.intel_dir / "prompt_weights.json"

    # ===== FEEDBACK OPERATIONS =====

    def load_feedback_entries(self) -> List[Dict[str, Any]]:
        """
        Load all feedback entries from the centralized log.

        Returns:
            List[Dict[str, Any]]: List of feedback entry dictionaries
        """
        try:
            if not os.path.exists(self.feedback_log_path):
                return []

            with open(self.feedback_log_path, "r", encoding="utf-8") as f:
                data = json.load(f)

            if not isinstance(data, list):
                logging.warning("Feedback log is not a list, returning empty list")
                return []

            return data

        except (json.JSONDecodeError, IOError) as e:
            logging.error(f"Failed to load feedback entries: {e}")
            return []

    def save_feedback_entries(self, entries: List[Dict[str, Any]]) -> bool:
        """
        Save feedback entries to the centralized log.

        Args:
            entries (List[Dict[str, Any]]): List of feedback entry dictionaries

        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.feedback_log_path), exist_ok=True)

            with open(self.feedback_log_path, "w", encoding="utf-8") as f:
                json.dump(entries, f, indent=2, ensure_ascii=False)

            logging.debug(f"Saved {len(entries)} feedback entries")
            return True

        except (IOError, OSError) as e:
            logging.error(f"Failed to save feedback entries: {e}")
            return False

    def add_feedback_entry(self, entry: Dict[str, Any]) -> bool:
        """
        Add a single feedback entry to the log.

        Args:
            entry (Dict[str, Any]): Feedback entry dictionary

        Returns:
            bool: True if addition was successful, False otherwise
        """
        try:
            entries = self.load_feedback_entries()
            entries.append(entry)
            return self.save_feedback_entries(entries)
        except Exception as e:
            logging.error(f"Failed to add feedback entry: {e}")
            return False

    # ===== FEEDBACK FILTERING AND ANALYSIS =====

    def get_recent_feedback(self, n: int = 10) -> List[Dict[str, Any]]:
        """
        Get the most recent n feedback entries.

        Args:
            n (int): Number of recent entries to return

        Returns:
            List[Dict[str, Any]]: Recent feedback entries
        """
        entries = self.load_feedback_entries()
        return entries[-n:] if entries else []

    def get_feedback_by_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Get all feedback entries for a specific file.

        Args:
            file_path (str): File path to filter by

        Returns:
            List[Dict[str, Any]]: Filtered feedback entries
        """
        entries = self.load_feedback_entries()
        return [entry for entry in entries if entry.get('file') == file_path]

    def get_feedback_by_model(self, model_name: str) -> List[Dict[str, Any]]:
        """
        Get all feedback entries for a specific model.

        Args:
            model_name (str): Model name to filter by

        Returns:
            List[Dict[str, Any]]: Filtered feedback entries
        """
        entries = self.load_feedback_entries()
        return [entry for entry in entries if entry.get('model') == model_name]

    def get_feedback_by_rating(self, min_rating: int = 1, max_rating: int = 5) -> List[Dict[str, Any]]:
        """
        Get feedback entries within a rating range.

        Args:
            min_rating (int): Minimum rating (inclusive)
            max_rating (int): Maximum rating (inclusive)

        Returns:
            List[Dict[str, Any]]: Filtered feedback entries
        """
        entries = self.load_feedback_entries()
        return [
            entry for entry in entries
            if min_rating <= entry.get('rating', 0) <= max_rating
        ]

    def get_average_score_for_model(self, model_name: str) -> float:
        """
        Calculate average output score for a specific model.

        Args:
            model_name (str): Model name to analyze

        Returns:
            float: Average output score (0.0 if no entries)
        """
        entries = self.get_feedback_by_model(model_name)
        if not entries:
            return 0.0

        scores = [entry.get('output_score', 0) for entry in entries if entry.get('output_score') is not None]
        return sum(scores) / len(scores) if scores else 0.0

    def get_average_rating_for_model(self, model_name: str) -> float:
        """
        Calculate average user rating for a specific model.

        Args:
            model_name (str): Model name to analyze

        Returns:
            float: Average rating (0.0 if no entries)
        """
        entries = self.get_feedback_by_model(model_name)
        if not entries:
            return 0.0

        ratings = [entry.get('rating', 0) for entry in entries if entry.get('rating') is not None]
        return sum(ratings) / len(ratings) if ratings else 0.0

    def get_model_performance_summary(self) -> Dict[str, Dict[str, Any]]:
        """
        Get performance summary for all models.

        Returns:
            Dict[str, Dict[str, Any]]: Model performance data
        """
        entries = self.load_feedback_entries()
        models = set(entry.get('model', '') for entry in entries if entry.get('model'))

        summary = {}
        for model in models:
            model_entries = self.get_feedback_by_model(model)
            summary[model] = {
                'total_entries': len(model_entries),
                'average_score': self.get_average_score_for_model(model),
                'average_rating': self.get_average_rating_for_model(model),
                'fallback_usage': sum(1 for e in model_entries if e.get('fallback_used', False)),
                'recent_entries': len([e for e in model_entries[-10:]])  # Last 10 entries
            }

        return summary

    # ===== INTELLIGENCE INSIGHTS =====

    def get_best_performing_model(self) -> Optional[str]:
        """
        Get the model with the highest average rating.

        Returns:
            Optional[str]: Best performing model name, or None if no data
        """
        summary = self.get_model_performance_summary()
        if not summary:
            return None

        best_model = max(summary.items(), key=lambda x: x[1]['average_rating'])
        return best_model[0] if best_model[1]['average_rating'] > 0 else None

    def get_problematic_files(self, min_entries: int = 2, max_avg_rating: float = 2.5) -> List[Dict[str, Any]]:
        """
        Identify files with consistently low ratings.

        Args:
            min_entries (int): Minimum number of entries to consider
            max_avg_rating (float): Maximum average rating to be considered problematic

        Returns:
            List[Dict[str, Any]]: Files with low performance
        """
        entries = self.load_feedback_entries()
        files = set(entry.get('file', '') for entry in entries if entry.get('file'))

        problematic = []
        for file_path in files:
            file_entries = self.get_feedback_by_file(file_path)
            if len(file_entries) >= min_entries:
                ratings = [e.get('rating', 0) for e in file_entries if e.get('rating')]
                if ratings:
                    avg_rating = sum(ratings) / len(ratings)
                    if avg_rating <= max_avg_rating:
                        problematic.append({
                            'file': file_path,
                            'entries': len(file_entries),
                            'average_rating': avg_rating,
                            'latest_feedback': file_entries[-1].get('feedback_text', 'No feedback')
                        })

        return sorted(problematic, key=lambda x: x['average_rating'])

    def get_tone_effectiveness(self) -> Dict[str, float]:
        """
        Analyze effectiveness of different tone settings.

        Returns:
            Dict[str, float]: Tone to average rating mapping
        """
        entries = self.load_feedback_entries()
        tones = set(entry.get('tone', '') for entry in entries if entry.get('tone'))

        effectiveness = {}
        for tone in tones:
            tone_entries = [e for e in entries if e.get('tone') == tone]
            ratings = [e.get('rating', 0) for e in tone_entries if e.get('rating')]
            if ratings:
                effectiveness[tone] = sum(ratings) / len(ratings)

        return effectiveness

    # ===== PROMPT SHAPING OPERATIONS =====

    def load_prompt_weights(self) -> Dict[str, Any]:
        """
        Load prompt shaping weights from shared storage.

        Returns:
            Dict[str, Any]: Prompt shaping configuration
        """
        try:
            if not self.prompt_weights_path.exists():
                # Return default weights
                return {
                    "tone": "default",
                    "fallback_level": 1,
                    "model_hint": "mixtral",
                    "shaping_strength": 0.5,
                    "fallback_sensitivity": 0.7,
                    "last_updated": datetime.now().isoformat(),
                    "version": "1.0"
                }

            with open(self.prompt_weights_path, "r", encoding="utf-8") as f:
                weights = json.load(f)

            # Ensure all required fields exist
            defaults = {
                "tone": "default",
                "fallback_level": 1,
                "model_hint": "mixtral",
                "shaping_strength": 0.5,
                "fallback_sensitivity": 0.7,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0"
            }

            for key, default_value in defaults.items():
                if key not in weights:
                    weights[key] = default_value

            return weights

        except (json.JSONDecodeError, IOError) as e:
            logging.error(f"Failed to load prompt weights: {e}")
            # Return defaults on error
            return {
                "tone": "default",
                "fallback_level": 1,
                "model_hint": "mixtral",
                "shaping_strength": 0.5,
                "fallback_sensitivity": 0.7,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0"
            }

    def save_prompt_weights(self, weights: Dict[str, Any]) -> bool:
        """
        Save prompt shaping weights to shared storage.

        Args:
            weights (Dict[str, Any]): Prompt shaping configuration

        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Ensure directory exists
            self.intel_dir.mkdir(parents=True, exist_ok=True)

            # Add metadata
            weights["last_updated"] = datetime.now().isoformat()
            weights["version"] = "1.0"

            with open(self.prompt_weights_path, "w", encoding="utf-8") as f:
                json.dump(weights, f, indent=2, ensure_ascii=False)

            logging.debug(f"Saved prompt weights: {weights}")
            return True

        except (IOError, OSError) as e:
            logging.error(f"Failed to save prompt weights: {e}")
            return False

    def update_prompt_weights(self, updates: Dict[str, Any]) -> bool:
        """
        Update specific prompt shaping weights.

        Args:
            updates (Dict[str, Any]): Partial updates to apply

        Returns:
            bool: True if update was successful, False otherwise
        """
        try:
            current_weights = self.load_prompt_weights()
            current_weights.update(updates)
            return self.save_prompt_weights(current_weights)
        except Exception as e:
            logging.error(f"Failed to update prompt weights: {e}")
            return False

    def get_shaping_summary(self) -> Dict[str, Any]:
        """
        Get a summary of current prompt shaping configuration.

        Returns:
            Dict[str, Any]: Shaping summary with usage statistics
        """
        try:
            weights = self.load_prompt_weights()

            # Calculate usage statistics from feedback entries
            entries = self.load_feedback_entries()
            tone_usage = {}
            model_usage = {}

            for entry in entries:
                tone = entry.get('tone', 'unknown')
                model = entry.get('model', 'unknown')

                tone_usage[tone] = tone_usage.get(tone, 0) + 1
                model_usage[model] = model_usage.get(model, 0) + 1

            return {
                "current_weights": weights,
                "usage_statistics": {
                    "tone_distribution": tone_usage,
                    "model_distribution": model_usage,
                    "total_entries": len(entries)
                },
                "recommendations": self._get_shaping_recommendations(weights, entries)
            }

        except Exception as e:
            logging.error(f"Failed to get shaping summary: {e}")
            return {
                "current_weights": self.load_prompt_weights(),
                "usage_statistics": {},
                "recommendations": []
            }

    def _get_shaping_recommendations(self, weights: Dict[str, Any], entries: List[Dict[str, Any]]) -> List[str]:
        """
        Generate intelligent shaping recommendations based on feedback history.

        Args:
            weights (Dict[str, Any]): Current weights
            entries (List[Dict[str, Any]]): Feedback entries

        Returns:
            List[str]: List of recommendations
        """
        recommendations = []

        if not entries:
            return ["No feedback data available for recommendations"]

        # Analyze recent performance
        recent_entries = entries[-10:] if len(entries) >= 10 else entries
        avg_rating = sum(e.get('rating', 0) for e in recent_entries) / len(recent_entries)

        # Tone recommendations
        if avg_rating < 3.0:
            recommendations.append("Consider adjusting tone - recent ratings are low")

        # Model recommendations
        model_performance = {}
        for entry in recent_entries:
            model = entry.get('model', 'unknown')
            rating = entry.get('rating', 0)
            if model not in model_performance:
                model_performance[model] = []
            model_performance[model].append(rating)

        best_model = None
        best_avg = 0
        for model, ratings in model_performance.items():
            avg = sum(ratings) / len(ratings)
            if avg > best_avg:
                best_avg = avg
                best_model = model

        if best_model and best_model != weights.get('model_hint'):
            recommendations.append(f"Consider switching to {best_model} (avg rating: {best_avg:.1f})")

        # Fallback recommendations
        fallback_usage = sum(1 for e in recent_entries if e.get('fallback_used', False))
        if fallback_usage > len(recent_entries) * 0.3:
            recommendations.append("High fallback usage detected - consider adjusting fallback sensitivity")

        return recommendations if recommendations else ["Current configuration appears optimal"]

    # ===== CONFIGURATION OPERATIONS =====

    def load_config(self) -> Dict[str, Any]:
        """
        Load configuration from shared config file.

        Returns:
            Dict[str, Any]: Configuration dictionary
        """
        try:
            if not os.path.exists(self.config_path):
                return {}

            with open(self.config_path, "r", encoding="utf-8") as f:
                return json.load(f)

        except (json.JSONDecodeError, IOError) as e:
            logging.error(f"Failed to load config: {e}")
            return {}

    def save_config(self, config: Dict[str, Any]) -> bool:
        """
        Save configuration to shared config file.

        Args:
            config (Dict[str, Any]): Configuration dictionary

        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)

            with open(self.config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            return True

        except (IOError, OSError) as e:
            logging.error(f"Failed to save config: {e}")
            return False

    # ===== UTILITY METHODS =====

    def get_intelligence_summary(self) -> Dict[str, Any]:
        """
        Get a comprehensive summary of the intelligence data.

        Returns:
            Dict[str, Any]: Intelligence summary
        """
        entries = self.load_feedback_entries()

        if not entries:
            return {
                'total_entries': 0,
                'models': [],
                'files': [],
                'average_rating': 0,
                'average_score': 0
            }

        # Calculate statistics
        ratings = [e.get('rating', 0) for e in entries if e.get('rating')]
        scores = [e.get('output_score', 0) for e in entries if e.get('output_score')]
        models = list(set(e.get('model', '') for e in entries if e.get('model')))
        files = list(set(e.get('file', '') for e in entries if e.get('file')))

        return {
            'total_entries': len(entries),
            'models': models,
            'files_processed': len(files),
            'average_rating': sum(ratings) / len(ratings) if ratings else 0,
            'average_score': sum(scores) / len(scores) if scores else 0,
            'model_performance': self.get_model_performance_summary(),
            'tone_effectiveness': self.get_tone_effectiveness(),
            'best_model': self.get_best_performing_model(),
            'recent_activity': len(self.get_recent_feedback(10))
        }


# ===== CONVENIENCE FUNCTIONS =====

# Global API instance for easy access
_api_instance = None

def get_intel_api() -> IntelAPI:
    """Get the global Intel API instance."""
    global _api_instance
    if _api_instance is None:
        _api_instance = IntelAPI()
    return _api_instance

# Convenience functions for common operations
def load_feedback_entries() -> List[Dict[str, Any]]:
    """Load all feedback entries from the centralized log."""
    return get_intel_api().load_feedback_entries()

def save_feedback_entries(entries: List[Dict[str, Any]]) -> bool:
    """Save feedback entries to the centralized log."""
    return get_intel_api().save_feedback_entries(entries)

def get_recent_feedback(n: int = 10) -> List[Dict[str, Any]]:
    """Get the most recent n feedback entries."""
    return get_intel_api().get_recent_feedback(n)

def get_average_score_for_model(model_name: str) -> float:
    """Calculate average output score for a specific model."""
    return get_intel_api().get_average_score_for_model(model_name)

def get_intelligence_summary() -> Dict[str, Any]:
    """Get a comprehensive summary of the intelligence data."""
    return get_intel_api().get_intelligence_summary()

# Convenience functions for prompt shaping
def load_prompt_weights() -> Dict[str, Any]:
    """Load prompt shaping weights from shared storage."""
    return get_intel_api().load_prompt_weights()

def save_prompt_weights(weights: Dict[str, Any]) -> bool:
    """Save prompt shaping weights to shared storage."""
    return get_intel_api().save_prompt_weights(weights)

def update_prompt_weights(updates: Dict[str, Any]) -> bool:
    """Update specific prompt shaping weights."""
    return get_intel_api().update_prompt_weights(updates)

def get_shaping_summary() -> Dict[str, Any]:
    """Get a summary of current prompt shaping configuration."""
    return get_intel_api().get_shaping_summary()

# Export key classes and functions
__all__ = [
    'IntelAPI',
    'get_intel_api',
    'load_feedback_entries',
    'save_feedback_entries',
    'get_recent_feedback',
    'get_average_score_for_model',
    'get_intelligence_summary',
    'load_prompt_weights',
    'save_prompt_weights',
    'update_prompt_weights',
    'get_shaping_summary'
]
