"""
Code injection module for CodeCrusher
AugmentCode Surgical Implementation - STEP 3: Auto-logging every injection
"""

import logging
import difflib
import os
import re
import uuid
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from halo import Halo
from source.ai_engine import generate_code, is_valid_code_response
from codecrusher.file_discovery import gather_source_files

# Import AugmentCode SQLite log store and intelligent prompt shaping
try:
    from log_store import InjectionLogStore
    LOGGING_AVAILABLE = True
except ImportError:
    LOGGING_AVAILABLE = False
    logging.warning("AugmentCode log store not available - injection logging disabled")

try:
    from prompt_shaper import adapt_prompt
    PROMPT_SHAPING_AVAILABLE = True
except ImportError:
    PROMPT_SHAPING_AVAILABLE = False
    logging.warning("AugmentCode prompt shaping not available - using original prompts")

# Initialize rich console
console = Console()


def _apply_intelligent_prompt_shaping(base_prompt: str, file_path: str, tags: List[str]) -> str:
    """
    Apply intelligent prompt shaping based on historical ratings.

    Args:
        base_prompt (str): Original prompt text
        file_path (str): Target file path for context
        tags (List[str]): List of tags for context matching

    Returns:
        str: Intelligently shaped prompt
    """
    if not PROMPT_SHAPING_AVAILABLE:
        return base_prompt

    try:
        # Extract filename from path for historical analysis
        filename = Path(file_path).name if file_path else ""

        # Apply intelligent shaping based on historical ratings
        shaped_prompt = adapt_prompt(base_prompt, filename, tags)

        if shaped_prompt != base_prompt:
            console.print(f"[cyan]🧠 Prompt intelligently shaped based on historical feedback[/cyan]")
            logging.info(f"Prompt shaped: '{base_prompt}' → '{shaped_prompt}'")

        return shaped_prompt

    except Exception as e:
        logging.warning(f"Failed to apply intelligent prompt shaping: {e}")
        return base_prompt


def _log_injection_automatically(file_path: str, prompt_text: str, model_used: str, injection_type: str, output: str, tags: List[str], success: bool, execution_time: float = None) -> None:
    """
    Automatically log injection to AugmentCode SQLite log store.

    Args:
        file_path (str): Full path of the file injected
        prompt_text (str): The exact prompt string used for injection
        model_used (str): Model name used (e.g., "mixtral", "gemma")
        injection_type (str): "apply" or "preview"
        output (str): The generated output string
        tags (List[str]): List of tags used
        success (bool): Whether the injection was successful
        execution_time (float, optional): Execution time in seconds
    """
    if not LOGGING_AVAILABLE:
        return

    try:
        # Create injection data following AugmentCode specification
        injection_data = {
            "filename": file_path,
            "injection_type": injection_type,
            "model": model_used,
            "prompt": prompt_text,
            "output": output,
            "tags": tags,
            "success": success,
            "execution_time": execution_time
        }

        # Log to AugmentCode SQLite store
        store = InjectionLogStore()
        log_id = store.log_injection(injection_data)

        # Print confirmation message as specified
        console.print("📝 Injection logged.")
        logging.info(f"Injection logged to AugmentCode store: {log_id[:8]}...")

    except Exception as e:
        # Safe fallback logic - do not crash injection
        logging.warning(f"Failed to log injection automatically: {e}")


def _detect_html_injection_tags(content: str, tag: str) -> bool:
    """
    Detect if HTML content has injection tags for the given tag.

    Args:
        content (str): File content to check
        tag (str): Tag name to look for

    Returns:
        bool: True if injection tags are found, False otherwise
    """
    # Check for cc:start/cc:end format
    start_pattern = f"<!-- cc:start:{tag} -->"
    end_pattern = f"<!-- cc:end:{tag} -->"

    if start_pattern in content and end_pattern in content:
        return True

    # Check for AI_INJECT format
    ai_inject_start = f"<!-- AI_INJECT:{tag}:start -->"
    ai_inject_end = f"<!-- AI_INJECT:{tag}:end -->"

    return ai_inject_start in content and ai_inject_end in content


def _auto_insert_html_tags(content: str, tag: str) -> Tuple[str, bool]:
    """
    Auto-insert HTML injection tags around the entire content if missing.

    Args:
        content (str): Original file content
        tag (str): Tag name to use for injection

    Returns:
        Tuple[str, bool]: (modified_content, was_modified)
    """
    if _detect_html_injection_tags(content, tag):
        return content, False

    # Wrap entire content with injection tags
    start_tag = f"<!-- cc:start:{tag} -->\n"
    end_tag = f"\n<!-- cc:end:{tag} -->"

    modified_content = start_tag + content + end_tag

    console.print(f"[yellow]Auto-inserted HTML injection tags for tag '[bold]{tag}[/bold]'[/yellow]")

    return modified_content, True


def _detect_body_tag(content: str) -> bool:
    """
    Detect if HTML content has a <body> tag.

    Args:
        content (str): File content to check

    Returns:
        bool: True if <body> tag is found, False otherwise
    """
    import re
    return bool(re.search(r'<body[^>]*>', content, re.IGNORECASE))


def _auto_inject_into_body(content: str, tag: str, file_path: str) -> Tuple[str, bool]:
    """
    Auto-inject AI_INJECT tags inside the <body> tag if missing injection tags.

    Args:
        content (str): Original file content
        tag (str): Tag name to use for injection
        file_path (str): Path to the file being processed

    Returns:
        Tuple[str, bool]: (modified_content, was_modified)
    """
    import re

    # Check if injection tags already exist
    if _detect_html_injection_tags(content, tag):
        return content, False

    # Check if body tag exists
    if not _detect_body_tag(content):
        return content, False

    # Find the opening body tag and inject right after it
    body_pattern = r'(<body[^>]*>)'
    match = re.search(body_pattern, content, re.IGNORECASE)

    if not match:
        return content, False

    # Get the position after the opening body tag
    insert_pos = match.end()

    # Determine indentation by looking at the line containing the body tag
    lines = content[:insert_pos].split('\n')
    body_line = lines[-1] if lines else ''

    # Calculate base indentation (spaces before <body>)
    body_indent = len(body_line) - len(body_line.lstrip())
    indent = ' ' * (body_indent + 2)  # Add 2 spaces for inner content

    # Create injection tags with proper indentation
    start_tag = f"\n{indent}<!-- AI_INJECT:{tag}:start -->"
    end_tag = f"\n{indent}<!-- AI_INJECT:{tag}:end -->"

    # Insert the tags
    modified_content = (
        content[:insert_pos] +
        start_tag +
        end_tag +
        content[insert_pos:]
    )

    console.print(f"[cyan]🪄 Auto-injected fallback tag into <body> for tag '[bold]{tag}[/bold]' in file {file_path}[/cyan]")

    return modified_content, True


def _process_html_tags(source_code: List[str], tag: str) -> List[Tuple[int, str]]:
    """
    Process HTML injection tags and return tag positions.

    Args:
        source_code (List[str]): Lines of source code
        tag (str): Tag to look for

    Returns:
        List[Tuple[int, str]]: List of (line_number, tag_name) tuples
    """
    tags = []
    content = ''.join(source_code)

    # Look for HTML-style injection tags (cc:start/cc:end format)
    start_pattern = f"<!-- cc:start:{tag} -->"
    end_pattern = f"<!-- cc:end:{tag} -->"

    if start_pattern in content and end_pattern in content:
        # Find the line number of the start tag
        for i, line in enumerate(source_code):
            if start_pattern in line:
                tags.append((i, tag))
                break

    # Also look for AI_INJECT style tags (for body injection)
    ai_inject_start = f"<!-- AI_INJECT:{tag}:start -->"
    ai_inject_end = f"<!-- AI_INJECT:{tag}:end -->"

    if ai_inject_start in content and ai_inject_end in content:
        # Find the line number of the AI_INJECT start tag
        for i, line in enumerate(source_code):
            if ai_inject_start in line:
                tags.append((i, tag))
                break

    return tags


def inject_code(source_path, prompt_text, use_ai=True, preview=False, model="auto", auto_model_routing=False, refresh_cache=False, apply=False, force=False, recursive=False, extensions=None, tag="default", inject_body=False, tuning_config=None):
    """
    Inject code into source files with recursive scanning and extension filtering
    AugmentCode STEP 3: Auto-logs every injection to SQLite log store

    Args:
        source_path (str): Path to the source file or directory
        prompt_text (str): Prompt text for the AI
        use_ai (bool, optional): Whether to use AI for code generation
        preview (bool, optional): Whether to preview changes before applying
        model (str, optional): Model to use for AI code generation
        auto_model_routing (bool, optional): Whether to use auto model routing
        refresh_cache (bool, optional): Whether to bypass the cache and regenerate results
        apply (bool, optional): Whether to apply changes to the file
        force (bool, optional): Whether to skip confirmation when applying changes
        recursive (bool, optional): Whether to recursively scan directories
        extensions (list, optional): List of file extensions to filter by

    Returns:
        dict: Result of the injection operation
    """
    # Track execution time for AugmentCode logging
    start_time = datetime.now()

    # Apply intelligent prompt shaping based on historical feedback
    original_prompt = prompt_text
    shaped_prompt = _apply_intelligent_prompt_shaping(prompt_text, str(source_path), [tag] if tag else [])

    # Use the shaped prompt for injection
    prompt_text = shaped_prompt

    logging.info(f"Injecting code into {source_path}")
    console.print(f"[bold cyan]Injecting code into:[/bold cyan] [yellow]{source_path}[/yellow]")

    if shaped_prompt != original_prompt:
        console.print(f"[dim]Original prompt: {original_prompt}[/dim]")
        console.print(f"[dim]Shaped prompt: {shaped_prompt}[/dim]")

    # Gather source files using recursive scan and extension filtering
    # In force mode, don't filter by tags since we'll auto-inject them
    try:
        filter_by_tags = not force  # Only filter by tags if not in force mode
        source_files = gather_source_files(source_path, recursive, extensions or ['py'], filter_tags=filter_by_tags)
        source_files = [Path(f) for f in source_files]  # Convert to Path objects
    except Exception as e:
        error_msg = f"Error gathering source files: {e}"
        logging.error(error_msg)
        console.print(f"[bold red]Error:[/bold red] {error_msg}")
        return {"success": False, "error": str(e)}

    # Check if any files were found
    if not source_files:
        if force:
            error_msg = f"No files found with extension(s): {', '.join(extensions or ['py'])}"
        else:
            error_msg = "No files found with injection tags"
        return {"success": False, "error": error_msg}

    # Display found files
    console.print(f"[green]Found {len(source_files)} file(s) to process:[/green]")
    for file_path in source_files:
        console.print(f"  [cyan]•[/cyan] {file_path}")

    # Check if we should use parallel processing
    use_parallel = len(source_files) > 1

    if use_parallel:
        # Use parallel injection for multiple files
        console.print(f"\n[bold blue]🧠 Injecting {len(source_files)} files using parallel processing...[/bold blue]")

        # Import parallel processor
        from codecrusher.parallel_injector import ParallelInjectionEngine

        # Create parallel engine
        parallel_engine = ParallelInjectionEngine(
            max_workers=min(4, len(source_files)),  # Limit to 4 threads max
            use_cache=not refresh_cache
        )

        # Process files in parallel
        try:
            parallel_results = parallel_engine.process_files_parallel(
                file_paths=source_files,
                prompt_text=prompt_text,
                use_ai=use_ai,
                model=model,
                auto_model_routing=auto_model_routing,
                refresh_cache=refresh_cache,
                apply=apply,
                force=force,
                preview=preview,
                tag=tag,
                inject_body=inject_body
            )

            return parallel_results

        except Exception as e:
            error_msg = f"Error in parallel processing: {e}"
            logging.error(error_msg)
            console.print(f"[bold red]Parallel processing failed:[/bold red] {error_msg}")
            console.print("[yellow]Falling back to sequential processing...[/yellow]")
            # Fall through to sequential processing

    # Sequential processing (single file or fallback)
    console.print(f"\n[bold blue]Processing {len(source_files)} file(s) sequentially...[/bold blue]")

    all_results = []
    total_injections = 0
    total_skipped = 0

    for i, file_path in enumerate(source_files, 1):
        console.print(f"\n[bold magenta]Processing file [{i}/{len(source_files)}]:[/bold magenta] [yellow]{file_path}[/yellow]")

        # Read source file
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.readlines()
        except Exception as e:
            error_msg = f"Error reading file {file_path}: {e}"
            logging.error(error_msg)
            console.print(f"[bold red]Error:[/bold red] {error_msg}")
            all_results.append({"file": str(file_path), "success": False, "error": str(e)})
            continue

        # Process this file (existing logic continues below)
        file_result = _process_single_file(
            file_path, source_code, prompt_text, use_ai, preview, model,
            auto_model_routing, refresh_cache, apply, force, tag, inject_body
        )

        all_results.append(file_result)
        if file_result.get("success"):
            total_injections += file_result.get("injections", 0)
            total_skipped += file_result.get("skipped_count", 0)

    # Return combined results
    successful_files = [r for r in all_results if r.get("success")]
    failed_files = [r for r in all_results if not r.get("success")]

    # Calculate execution time for AugmentCode logging
    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()

    # Determine overall success and prepare logging data
    overall_success = len(successful_files) > 0
    injection_type = "apply" if apply else "preview"

    # Collect all output from successful injections
    all_output = []
    all_models_used = []
    for result in successful_files:
        if result.get("suggestions"):
            all_output.extend([str(suggestion) for suggestion in result["suggestions"]])
        if result.get("model_used"):
            all_models_used.append(result["model_used"])

    # Use the most common model or the specified model
    model_used = max(set(all_models_used), key=all_models_used.count) if all_models_used else model
    combined_output = "\n".join(all_output)

    # Log the overall injection operation to AugmentCode store
    # Use original prompt for logging to track what user actually requested
    _log_injection_automatically(
        file_path=str(source_path),
        prompt_text=original_prompt,  # Log original prompt, not shaped one
        model_used=model_used,
        injection_type=injection_type,
        output=combined_output,
        tags=[tag] if tag else [],
        success=overall_success,
        execution_time=execution_time
    )

    return {
        "success": overall_success,
        "total_files": len(source_files),
        "successful_files": len(successful_files),
        "failed_files": len(failed_files),
        "total_injections": total_injections,
        "total_skipped": total_skipped,
        "results": all_results,
        "timestamp": datetime.now().isoformat(),
        "execution_time": execution_time,
        "model_used": model_used
    }


def _process_single_file(file_path, source_code, prompt_text, use_ai, preview, model, auto_model_routing, refresh_cache, apply, force, tag, inject_body=False):
    """
    Process a single file for code injection.
    AugmentCode STEP 3: Auto-logs individual file injections

    Args:
        file_path (Path): Path to the file being processed
        source_code (list): Lines of source code
        prompt_text (str): Prompt text for AI
        use_ai (bool): Whether to use AI
        preview (bool): Preview mode
        model (str): AI model
        auto_model_routing (bool): Auto model routing
        refresh_cache (bool): Refresh cache
        apply (bool): Apply changes
        force (bool): Force apply
        tag (str): Injection tag to use
        inject_body (bool): Whether to inject into body tag

    Returns:
        dict: Result of processing this file
    """
    # Track execution time for individual file processing
    file_start_time = datetime.now()

    # Apply intelligent prompt shaping for this specific file
    original_prompt = prompt_text
    shaped_prompt = _apply_intelligent_prompt_shaping(prompt_text, str(file_path), [tag] if tag else [])

    # Use the shaped prompt for this file
    prompt_text = shaped_prompt

    if shaped_prompt != original_prompt:
        console.print(f"[dim]🧠 File-specific prompt shaping applied for {file_path.name}[/dim]")

    # Check if this is an HTML file and handle auto-insertion
    file_extension = file_path.suffix.lower()
    is_html_file = file_extension in ['.html', '.htm']

    # Handle HTML files with force mode
    if is_html_file and force:
        content = ''.join(source_code)

        # Try body injection first if inject_body is enabled or as default fallback
        if inject_body or True:  # Default to True for fallback behavior
            modified_content, was_modified = _auto_inject_into_body(content, tag, str(file_path))

            if was_modified:
                # Update source_code with the modified content
                source_code = modified_content.splitlines(keepends=True)
            else:
                # Fallback to full content wrapping if body injection didn't work
                modified_content, was_modified = _auto_insert_html_tags(content, tag)
                if was_modified:
                    # Update source_code with the modified content
                    source_code = modified_content.splitlines(keepends=True)
                    console.print(f"[green]✅ Auto-inserted HTML injection tags for tag '[bold]{tag}[/bold]'[/green]")
        else:
            # Use the original full content wrapping approach
            modified_content, was_modified = _auto_insert_html_tags(content, tag)
            if was_modified:
                # Update source_code with the modified content
                source_code = modified_content.splitlines(keepends=True)
                console.print(f"[green]✅ Auto-inserted HTML injection tags for tag '[bold]{tag}[/bold]'[/green]")

    # Find injection tags
    tags = []

    if is_html_file:
        # Use HTML-style tag detection
        tags = _process_html_tags(source_code, tag)
    else:
        # Use traditional AI_INJECT tag detection
        for i, line in enumerate(source_code):
            if "AI_INJECT:" in line:
                found_tag = line.split("AI_INJECT:")[1].strip()
                tags.append((i, found_tag))

    if not tags:
        if force and not is_html_file:
            # For non-HTML files with force mode, create a default injection point
            console.print(f"[yellow]Force mode enabled: Creating injection point for tag '[bold]{tag}[/bold]'[/yellow]")
            # Add a comment with the injection tag at the end of the file
            source_code.append(f"# AI_INJECT: {tag}\n")
            tags.append((len(source_code) - 1, tag))
        else:
            error_msg = "No injection tags found in source file"
            logging.warning(error_msg)
            console.print(f"[bold yellow]Warning:[/bold yellow] {error_msg}")
            if is_html_file:
                console.print(f"[dim]Add HTML tags like:[/dim] [green]<!-- cc:start:{tag} --> ... <!-- cc:end:{tag} -->[/green]")
                console.print(f"[dim]Or use --force to auto-insert tags[/dim]")
            else:
                console.print("[dim]Add tags to your code like:[/dim] [green]# AI_INJECT: function_name[/green]")
            return {"success": False, "error": error_msg}

    # Display found tags
    console.print(f"[green]Found {len(tags)} injection tags:[/green]")
    for _, found_tag in tags:
        console.print(f"  [cyan]•[/cyan] {found_tag}")

    logging.info(f"Found {len(tags)} injection tags")

    # Generate code for each tag
    injections = {}
    skipped_tags = []  # Track skipped tags due to hallucination
    for line_num, tag in tags:
        logging.info(f"Processing tag: {tag}")

        if use_ai:
            # Generate code using AI
            tag_prompt = f"{prompt_text} for tag '{tag}'"

            # Use spinner for AI calls
            with Halo(text=f"Generating code for tag '{tag}'...", spinner="dots"):
                # Set the provider based on the model
                if model == "auto":
                    provider = "auto"
                # FIXED: Add defensive check for None model before calling .startswith()
                elif model and (model.startswith("llama") or model.startswith("mixtral")):
                    provider = f"groq/{model}"
                else:
                    provider = "groq"

                # Call generate_code with the correct parameters including file path for context detection
                ai_code, model_used = generate_code(
                    tag_prompt,
                    tag=tag,
                    provider=provider,
                    auto_route=auto_model_routing or (model == "auto"),
                    use_cache=not refresh_cache,  # Skip cache if refresh_cache is True
                    refresh_cache=refresh_cache,   # Pass refresh_cache for force mode
                    file_path=file_path  # Pass file path for context-aware orchestration
                )

            # Validate the AI response
            is_valid, reason = is_valid_code_response(ai_code)

            # SURGICAL FIX: Enhanced Debug Output
            # Get score for debug output
            from source.ai_engine import score_output
            ai_score = score_output(ai_code) if ai_code else 0

            # Display model used with more details
            model_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
            model_table.add_row("[cyan]Model Used:[/cyan]", f"[bold]{model_used}[/bold]")
            model_table.add_row("[cyan]Score:[/cyan]", f"[bold]{ai_score}[/bold]")

            # Add code stats
            if ai_code:
                lines_count = len(ai_code.splitlines())
                model_table.add_row("[cyan]Lines Generated:[/cyan]", f"[bold]{lines_count}[/bold]")
                model_table.add_row("[cyan]Characters:[/cyan]", f"[bold]{len(ai_code)}[/bold]")

                # Add validation status
                if is_valid:
                    model_table.add_row("[cyan]Validation:[/cyan]", f"[bold green]✅ Valid code response[/bold green]")
                    if ai_score >= 75:
                        model_table.add_row("[cyan]Quality:[/cyan]", f"[bold green]Production Ready[/bold green]")
                    elif ai_score >= 50:
                        model_table.add_row("[cyan]Quality:[/cyan]", f"[bold yellow]Good Quality[/bold yellow]")
                    else:
                        model_table.add_row("[cyan]Quality:[/cyan]", f"[bold red]Low Quality[/bold red]")
                else:
                    model_table.add_row("[cyan]Validation:[/cyan]", f"[bold red]❌ {reason}[/bold red]")

                # Check if escalation was used (model_used contains escalation info)
                if "escalated_from" in str(model_used):
                    model_table.add_row("[cyan]Escalated:[/cyan]", f"[yellow]Yes[/yellow]")

            # Determine panel style based on validation
            panel_title = f"✅ Code Generated for Tag: {tag}"
            panel_style = "green"

            if not is_valid:
                panel_title = f"⚠️ Low-Quality Response for Tag: {tag}"
                panel_style = "yellow"

            console.print(Panel(model_table, title=f"[bold {panel_style}]{panel_title}[/bold {panel_style}]",
                               expand=False, border_style=panel_style))

            # Handle invalid or empty responses
            if not ai_code:
                error_msg = f"Failed to generate code for tag: {tag}"
                logging.warning(error_msg)
                console.print(Panel(f"[bold red]{error_msg}[/bold red]", border_style="red"))
                continue

            if not is_valid:
                warning_msg = f"⚠️ Detected hallucinated or non-code response — skipping injection for tag: {tag}"
                logging.warning(warning_msg)
                console.print(f"[bold yellow]{warning_msg}[/bold yellow]")
                console.print(f"[dim yellow]Reason: {reason}[/dim yellow]")

                # Retry with another model if available
                if auto_model_routing:
                    console.print(f"[yellow]Attempting retry with a stronger model...[/yellow]")

                    # Try with a more powerful model (e.g., LLaMA 3 70B)
                    retry_model = "llama3-70b-8192"
                    retry_provider = f"groq/{retry_model}"

                    console.print(f"[yellow]Retrying with model: {retry_model}[/yellow]")

                    try:
                        # Call generate_code with the retry model
                        with Halo(text=f"Retrying generation for tag '{tag}'...", spinner="dots"):
                            retry_code, retry_model_used = generate_code(
                                tag_prompt,
                                tag=tag,
                                provider=retry_provider,
                                auto_route=False,  # Direct use of the retry model
                                use_cache=False,   # Force regeneration
                                refresh_cache=True # Skip cache
                            )

                        # Validate the retry response
                        retry_valid, retry_reason = is_valid_code_response(retry_code)

                        if retry_valid and retry_code:
                            console.print(f"[green]✅ Retry successful with model: {retry_model_used}[/green]")

                            # Format the retry code
                            code_lines = retry_code.splitlines()
                            code_lines = [line + "\n" for line in code_lines]

                            injections[line_num] = code_lines
                            continue  # Skip to the next tag
                        else:
                            console.print(f"[yellow]Retry also failed: {retry_reason}[/yellow]")
                    except Exception as retry_error:
                        console.print(f"[yellow]Error during retry: {retry_error}[/yellow]")

                # Skip this tag's injection if retry failed or wasn't attempted
                console.print(f"[yellow]Skipping injection for tag: {tag}[/yellow]")
                skipped_tags.append((tag, reason))  # Track skipped tag and reason
                continue

            # Format the generated code
            code_lines = ai_code.splitlines()
            code_lines = [line + "\n" for line in code_lines]

            injections[line_num] = code_lines
        else:
            # Use placeholder for non-AI mode
            injections[line_num] = [f"# TODO: Implement {tag}\n"]

    # Apply injections
    new_code = source_code.copy()
    offset = 0

    for line_num, code_lines in sorted(injections.items()):
        # Insert code after the tag line
        new_code[line_num + offset + 1:line_num + offset + 1] = code_lines
        offset += len(code_lines)

    # Always show a preview of the changes
    logging.info("Previewing changes")
    console.print("\n[bold magenta]Previewing Changes[/bold magenta]")

    diff = difflib.unified_diff(source_code, new_code, fromfile="original", tofile="modified", lineterm="")
    diff_text = "\n".join(diff)

    # Highlight the diff output
    highlighted_diff = ""
    for line in diff_text.splitlines():
        # FIXED: Add defensive checks for None line before calling .startswith()
        if line and line.startswith("+"):
            highlighted_diff += f"[green]{line}[/green]\n"
        elif line and line.startswith("-"):
            highlighted_diff += f"[red]{line}[/red]\n"
        elif line and line.startswith("@@"):
            highlighted_diff += f"[cyan]{line}[/cyan]\n"
        else:
            highlighted_diff += f"{line}\n"

    console.print(highlighted_diff)

    # Determine if we should apply changes
    should_apply = apply

    # If preview mode is enabled, ask for confirmation regardless of apply flag
    if preview and should_apply:
        # Ask for confirmation with styled prompt
        console.print("\n[yellow]Apply these changes?[/yellow] [dim](y/n)[/dim]", end=" ")
        response = input()

        if response.lower() != 'y':
            logging.info("Changes rejected by user")
            console.print("[yellow]Changes rejected by user[/yellow]")
            return {"success": False, "error": "Changes rejected by user"}

    # If apply flag is set but force is not, ask for confirmation
    elif should_apply and not force:
        # Ask for confirmation with styled prompt
        console.print("\n[yellow]Are you sure you want to apply these changes to the file?[/yellow] [dim](y/n)[/dim]", end=" ")
        response = input()

        if response.lower() != 'y':
            logging.info("Changes rejected by user")
            console.print("[yellow]Changes rejected by user[/yellow]")
            return {"success": False, "error": "Changes rejected by user"}

    # If not applying changes, just return the result
    if not should_apply:
        console.print("[yellow]🔍 Preview mode only (use --apply to write changes to file)[/yellow]")

        # Calculate execution time for AugmentCode logging
        file_end_time = datetime.now()
        file_execution_time = (file_end_time - file_start_time).total_seconds()

        # Prepare data for automatic logging (preview mode)
        preview_model_used = model_used if 'model_used' in locals() else model
        combined_output = "\n".join([str(suggestion) for suggestion in injections.values()])

        # Log preview injection to AugmentCode store
        _log_injection_automatically(
            file_path=str(file_path),
            prompt_text=original_prompt,  # Log original prompt for tracking
            model_used=preview_model_used,
            injection_type="preview",
            output=combined_output,
            tags=[tag] if tag else [],
            success=True,
            execution_time=file_execution_time
        )

        # Return result without writing to file
        return {
            "file": str(file_path),
            "success": True,
            "tags": [tag for _, tag in tags],
            "injections": len(injections),
            "timestamp": datetime.now().isoformat(),
            "model_used": preview_model_used,
            "suggestions": list(injections.values()),
            "preview_only": True,
            "skipped_tags": skipped_tags,  # List of skipped tags due to hallucination
            "skipped_count": len(skipped_tags),  # Number of skipped tags
            "execution_time": file_execution_time
        }

    # Write changes to file if apply flag is set
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_code)
        console.print("[green]✅ Code changes applied to file.[/green]")
    except Exception as e:
        error_msg = f"Error writing to source file: {e}"
        logging.error(error_msg)
        console.print(f"[bold red]Error:[/bold red] {error_msg}")
        return {"success": False, "error": str(e)}

    logging.info(f"Successfully injected code into {file_path}")

    # Create a summary table
    summary_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    summary_table.add_row("[cyan]File:[/cyan]", f"[yellow]{file_path}[/yellow]")
    summary_table.add_row("[cyan]Tags Processed:[/cyan]", f"[bold]{len(tags)}[/bold]")
    summary_table.add_row("[cyan]Injections Made:[/cyan]", f"[bold]{len(injections)}[/bold]")

    # Show skipped tags due to hallucination
    if skipped_tags:
        skipped_count = len(skipped_tags)
        summary_table.add_row("[cyan]Tags Skipped:[/cyan]", f"[bold yellow]{skipped_count}[/bold yellow] (hallucinated responses)")

        # List the skipped tags and reasons
        skipped_list = "\n".join([f"• {tag}: {reason[:50]}..." for tag, reason in skipped_tags])
        if skipped_count <= 3:  # Only show details for a small number of skipped tags
            summary_table.add_row("[cyan]Skipped Details:[/cyan]", f"[yellow]{skipped_list}[/yellow]")

    summary_table.add_row("[cyan]Model Used:[/cyan]", f"[bold]{model_used if 'model_used' in locals() else model}[/bold]")

    # Show whether changes were applied
    if should_apply:
        summary_table.add_row("[cyan]Changes Applied:[/cyan]", "[bold green]Yes[/bold green] (written to file)")
    else:
        summary_table.add_row("[cyan]Changes Applied:[/cyan]", "[bold yellow]No[/bold yellow] (preview only)")

    summary_table.add_row("[cyan]Timestamp:[/cyan]", f"[dim]{datetime.now().isoformat()}[/dim]")

    # Display success message with summary
    title = "[bold green]✅ Code Injection Complete[/bold green]"
    if not should_apply:
        title = "[bold yellow]✅ Code Generation Complete (Preview Only)[/bold yellow]"

    console.print(Panel(
        summary_table,
        title=title,
        border_style="green" if should_apply else "yellow"
    ))

    # Calculate execution time for AugmentCode logging
    file_end_time = datetime.now()
    file_execution_time = (file_end_time - file_start_time).total_seconds()

    # Prepare data for automatic logging
    final_model_used = model_used if 'model_used' in locals() else model
    injection_type = "apply" if should_apply else "preview"
    combined_output = "\n".join([str(suggestion) for suggestion in injections.values()])

    # Log individual file injection to AugmentCode store
    _log_injection_automatically(
        file_path=str(file_path),
        prompt_text=original_prompt,  # Log original prompt for tracking
        model_used=final_model_used,
        injection_type=injection_type,
        output=combined_output,
        tags=[tag] if tag else [],
        success=True,
        execution_time=file_execution_time
    )

    # Return result
    return {
        "file": str(file_path),
        "success": True,
        "tags": [tag for _, tag in tags],
        "injections": len(injections),
        "timestamp": datetime.now().isoformat(),
        "model_used": final_model_used,
        "suggestions": list(injections.values()),
        "applied": should_apply,  # Indicate whether changes were applied to the file
        "skipped_tags": skipped_tags,  # List of skipped tags due to hallucination
        "skipped_count": len(skipped_tags),  # Number of skipped tags
        "execution_time": file_execution_time
    }
