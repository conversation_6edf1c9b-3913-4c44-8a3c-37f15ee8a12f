{"name": "codecrusher-vscode", "displayName": "CodeCrusher", "description": "VS Code integration for CodeCrusher - AI-powered code injection tool", "version": "0.1.0", "publisher": "codecrusher", "engines": {"vscode": "^1.60.0"}, "categories": ["Other", "Programming Languages", "Snippets"], "activationEvents": ["onCommand:codecrusher.inject", "onCommand:codecrusher.showLogs", "onCommand:codecrusher.copySettingsToGlobal", "onCommand:codecrusher.runInjection", "onCommand:codecrusher.viewTelemetry", "onCommand:codecrusher.replayInjection", "onCommand:codecrusher.openPanel", "onCommand:codecrusher.scanNow", "onCommand:codecrusher.openTrends", "onCommand:codecrusher.openScan", "onCommand:codecrusher.showInjectionDetails", "onCommand:codecrusher.resendInjection", "onView:codecrusher.sidebar", "onLanguage:python", "onLanguage:javascript", "onLanguage:typescript", "onLanguage:java", "onLanguage:csharp", "onLanguage:go", "onLanguage:html", "onLanguage:css", "onLanguage:rust"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "codecrusher.inject", "title": "Inject with CodeCrusher"}, {"command": "codecrusher.showLogs", "title": "CodeCrusher: Show Live Logs"}, {"command": "codecrusher.copySettingsToGlobal", "title": "CodeCrusher: Copy Workspace Settings to Global"}, {"command": "codecrusher.runInjection", "title": "CodeCrusher: Run Injection"}, {"command": "codecrusher.viewTelemetry", "title": "CodeCrusher: View Telemetry"}, {"command": "codecrusher.replayInjection", "title": "CodeCrusher: Replay Injection"}, {"command": "codecrusher.insertTag", "title": "CodeCrusher: Insert Injection Tag"}, {"command": "codecrusher.showStatus", "title": "CodeCrusher: Show Status"}, {"command": "codecrusher.openPanel", "title": "CodeCrusher: Open Panel"}, {"command": "codecrusher.scanNow", "title": "CodeCrusher: Scan Project for Anomalies"}, {"command": "codecrusher.openTrends", "title": "CodeCrusher: Open Trends Report"}, {"command": "codecrusher.openScan", "title": "CodeCrusher: Open Scan Results"}, {"command": "codecrusher.showInjectionDetails", "title": "CodeCrusher: Show Injection Details"}, {"command": "codecrusher.resendInjection", "title": "CodeCrusher: <PERSON>send Injection to AI"}], "menus": {"editor/context": [{"command": "codecrusher.inject", "group": "codecrusher@1", "when": "resourceExtname =~ /\\.(py|js|ts|jsx|tsx|java|c|cpp|cs|go|rs|html|css)$/"}, {"command": "codecrusher.runInjection", "group": "codecrusher@2", "when": "editorTextFocus"}, {"command": "codecrusher.insertTag", "group": "codecrusher@3", "when": "editorTextFocus"}, {"command": "codecrusher.resendInjection", "group": "codecrusher@4", "when": "editorTextFocus"}], "view/title": [{"command": "codecrusher.scanNow", "when": "view == codecrusher.sidebar", "group": "navigation"}, {"command": "codecrusher.openTrends", "when": "view == codecrusher.sidebar", "group": "navigation"}]}, "viewsContainers": {"activitybar": [{"id": "codecrusher-sidebar", "title": "CodeCrusher", "icon": "media/codecrusher-icon.svg"}]}, "views": {"codecrusher-sidebar": [{"id": "codecrusher.sidebar", "name": "CodeCrusher", "type": "webview"}]}, "configuration": {"title": "CodeCrusher", "properties": {"codecrusher.pythonPath": {"type": "string", "default": "python", "description": "Path to Python executable for running CodeCrusher CLI"}, "codecrusher.cliPath": {"type": "string", "default": "codecrusher", "description": "Path to CodeCrusher CLI executable (use 'codecrusher' if installed globally)"}, "codecrusher.defaultProvider": {"type": "string", "default": "groq", "enum": ["groq", "mistral", "openai"], "description": "Default AI provider to use"}, "codecrusher.defaultModel": {"type": "string", "default": "auto", "enum": ["auto", "mistral", "gemma", "mixtral", "llama3-8b", "llama3-70b"], "enumDescriptions": ["Automatically choose the best model for the task", "Mistral AI model - Fast and efficient", "Google Gemma model - Balanced performance", "Mixtral model - High quality output", "Llama 3 8B - Fast and lightweight", "Llama 3 70B - Most powerful"], "description": "Default AI model for CodeCrusher injections"}, "codecrusher.useCache": {"type": "boolean", "default": true, "description": "Whether to use cache by default"}, "codecrusher.showStatusBar": {"type": "boolean", "default": true, "description": "Show CodeCrusher status in the status bar"}, "codecrusher.telemetryRefreshInterval": {"type": "number", "default": 60, "description": "Interval in seconds to refresh telemetry data in status bar"}, "codecrusher.defaultMode": {"type": "string", "default": "preview", "enum": ["preview", "apply"], "enumDescriptions": ["Show changes in a preview document (safe mode)", "Apply changes directly to the file (immediate mode)"], "description": "Default injection mode when using CodeCrusher"}, "codecrusher.enableFallback": {"type": "boolean", "default": true, "description": "Enable fallback to other models if the primary model fails"}, "codecrusher.logServerPort": {"type": "number", "default": 11434, "description": "Port for CodeCrusher WebSocket log server"}, "codecrusher.autoShowLogs": {"type": "boolean", "default": false, "description": "Automatically show live logs panel when injection starts"}, "codecrusher.dashboardUrl": {"type": "string", "default": "http://localhost:8000", "description": "URL of the CodeCrusher web dashboard for settings sync"}, "codecrusher.syncInterval": {"type": "number", "default": 120, "description": "Interval in seconds to sync settings with dashboard (0 to disable)"}, "codecrusher.enableSettingsSync": {"type": "boolean", "default": true, "description": "Enable automatic synchronization of settings with web dashboard"}}}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/glob": "^7.1.3", "@types/mocha": "^8.2.2", "@types/node": "^14.14.37", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^4.22.0", "@typescript-eslint/parser": "^4.22.0", "eslint": "^7.24.0", "glob": "^7.1.6", "mocha": "^8.3.2", "ts-loader": "^9.5.2", "typescript": "^4.9.5", "vscode-test": "^1.5.2", "webpack": "^5.99.9", "webpack-cli": "^4.10.0"}, "dependencies": {"axios": "^0.21.1"}}