"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeCrusherDiagnostics = exports.registerDiagnostics = void 0;
const vscode = require("vscode");
const api_1 = require("./api");
/**
 * Register diagnostics provider for CodeCrusher injections
 */
function registerDiagnostics(context) {
    const diagnosticsProvider = new CodeCrusherDiagnostics();
    context.subscriptions.push(diagnosticsProvider);
    // Update diagnostics when active editor changes
    context.subscriptions.push(vscode.window.onDidChangeActiveTextEditor(editor => {
        if (editor) {
            diagnosticsProvider.updateDiagnostics(editor.document);
        }
    }));
    // Update diagnostics when document changes
    context.subscriptions.push(vscode.workspace.onDidChangeTextDocument(event => {
        diagnosticsProvider.updateDiagnostics(event.document);
    }));
    // Update diagnostics for current editor
    if (vscode.window.activeTextEditor) {
        diagnosticsProvider.updateDiagnostics(vscode.window.activeTextEditor.document);
    }
    return diagnosticsProvider;
}
exports.registerDiagnostics = registerDiagnostics;
/**
 * Manages diagnostics for CodeCrusher injections
 */
class CodeCrusherDiagnostics {
    constructor() {
        this._telemetryEntries = [];
        this._diagnosticCollection = vscode.languages.createDiagnosticCollection('codecrusher');
        // Refresh telemetry data every 5 minutes
        this._refreshInterval = setInterval(() => this.refreshTelemetryData(), 5 * 60 * 1000);
        // Initial refresh
        this.refreshTelemetryData();
        // Update diagnostics when active editor changes
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor) {
                this.updateDiagnostics(editor.document);
            }
        });
        // Update diagnostics when document changes
        vscode.workspace.onDidChangeTextDocument(event => {
            this.updateDiagnostics(event.document);
        });
        // Update diagnostics for current editor
        if (vscode.window.activeTextEditor) {
            this.updateDiagnostics(vscode.window.activeTextEditor.document);
        }
    }
    /**
     * Refresh telemetry data
     */
    refreshTelemetryData() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                // Check if server is running
                const isServerRunning = yield api_1.CodeCrusherApi.isServerRunning();
                if (!isServerRunning) {
                    this._telemetryEntries = [];
                    this.clearDiagnostics();
                    return;
                }
                // Get telemetry data
                const telemetryData = yield api_1.CodeCrusherApi.getTelemetry(500);
                this._telemetryEntries = telemetryData.entries;
                // Update diagnostics for current editor
                if (vscode.window.activeTextEditor) {
                    this.updateDiagnostics(vscode.window.activeTextEditor.document);
                }
            }
            catch (error) {
                console.error('Error refreshing telemetry data:', error);
                this._telemetryEntries = [];
                this.clearDiagnostics();
            }
        });
    }
    /**
     * Clear all diagnostics
     */
    clearDiagnostics() {
        this._diagnosticCollection.clear();
    }
    /**
     * Update diagnostics for the given document
     */
    updateDiagnostics(document) {
        if (this._telemetryEntries.length === 0) {
            this._diagnosticCollection.delete(document.uri);
            return;
        }
        const diagnostics = [];
        const filePath = document.uri.fsPath;
        // Find telemetry entries for this file
        const fileEntries = this._telemetryEntries.filter(entry => entry.file_path && entry.line_number &&
            (entry.file_path === filePath || filePath.endsWith(entry.file_path)));
        for (const entry of fileEntries) {
            if (!entry.line_number) {
                continue;
            }
            // Line numbers in telemetry are 1-based, VS Code is 0-based
            const lineNumber = entry.line_number - 1;
            if (lineNumber < 0 || lineNumber >= document.lineCount) {
                continue;
            }
            const line = document.lineAt(lineNumber);
            const range = new vscode.Range(new vscode.Position(lineNumber, 0), new vscode.Position(lineNumber, line.text.length));
            // Create diagnostic
            const diagnostic = new vscode.Diagnostic(range, this.getDiagnosticMessage(entry), this.getDiagnosticSeverity(entry));
            // Add related information
            diagnostic.source = 'CodeCrusher';
            diagnostic.code = 'codecrusher.injection';
            // Add to diagnostics
            diagnostics.push(diagnostic);
        }
        // Update diagnostics
        this._diagnosticCollection.set(document.uri, diagnostics);
    }
    /**
     * Get diagnostic message based on telemetry entry
     */
    getDiagnosticMessage(entry) {
        const parts = [];
        // Add model info
        parts.push(`CodeCrusher Injection (${entry.model})`);
        // Add fallback info if applicable
        if (entry.fallback) {
            parts.push('Fallback used');
        }
        // Add error info if applicable
        if (entry.error) {
            parts.push(`Error: ${entry.error.substring(0, 30)}...`);
        }
        // Add tag info if available
        if (!entry.tags || entry.tags.length === 0) {
            parts.push('Missing tags');
        }
        return parts.join(' | ');
    }
    /**
     * Get diagnostic severity based on telemetry entry
     */
    getDiagnosticSeverity(entry) {
        if (entry.error) {
            return vscode.DiagnosticSeverity.Error;
        }
        if (entry.fallback) {
            return vscode.DiagnosticSeverity.Warning;
        }
        if (!entry.tags || entry.tags.length === 0) {
            return vscode.DiagnosticSeverity.Information;
        }
        return vscode.DiagnosticSeverity.Hint;
    }
    /**
     * Dispose of resources
     */
    dispose() {
        if (this._refreshInterval) {
            clearInterval(this._refreshInterval);
        }
        this._diagnosticCollection.clear();
        this._diagnosticCollection.dispose();
    }
}
exports.CodeCrusherDiagnostics = CodeCrusherDiagnostics;
//# sourceMappingURL=diagnostics.js.map