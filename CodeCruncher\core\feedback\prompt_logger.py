"""
Prompt Log + Rating Store
Persistent storage for prompt history, ratings, and feedback
"""

import sqlite3
import json
import hashlib
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class PromptLogEntry:
    """Schema for prompt log entries"""
    id: str
    timestamp: str
    file: str
    injection_type: str
    model: str
    prompt: str
    output_hash: str
    rating: Optional[int] = None
    feedback: Optional[str] = None
    tags: Optional[List[str]] = None
    learned: bool = False
    execution_time: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None

class PromptLogger:
    """Persistent store for prompt logs and ratings"""
    
    def __init__(self, db_path: str = "data/prompt_logs.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database with schema"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS prompt_logs (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    file TEXT NOT NULL,
                    injection_type TEXT NOT NULL,
                    model TEXT NOT NULL,
                    prompt TEXT NOT NULL,
                    output_hash TEXT NOT NULL,
                    rating INTEGER,
                    feedback TEXT,
                    tags TEXT,  -- JSON array
                    learned BOOLEAN DEFAULT FALSE,
                    execution_time REAL,
                    success BOOLEAN DEFAULT TRUE,
                    error_message TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON prompt_logs(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_model ON prompt_logs(model)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_rating ON prompt_logs(rating)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_injection_type ON prompt_logs(injection_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_success ON prompt_logs(success)")
            
            conn.commit()
    
    def log_prompt(self, 
                   file: str,
                   injection_type: str,
                   model: str,
                   prompt: str,
                   output: str,
                   execution_time: Optional[float] = None,
                   success: bool = True,
                   error_message: Optional[str] = None,
                   tags: Optional[List[str]] = None) -> str:
        """Log a prompt execution"""
        
        entry_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        output_hash = hashlib.sha256(output.encode()).hexdigest()
        
        entry = PromptLogEntry(
            id=entry_id,
            timestamp=timestamp,
            file=file,
            injection_type=injection_type,
            model=model,
            prompt=prompt,
            output_hash=output_hash,
            execution_time=execution_time,
            success=success,
            error_message=error_message,
            tags=tags or []
        )
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO prompt_logs 
                (id, timestamp, file, injection_type, model, prompt, output_hash, 
                 execution_time, success, error_message, tags)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                entry.id, entry.timestamp, entry.file, entry.injection_type,
                entry.model, entry.prompt, entry.output_hash,
                entry.execution_time, entry.success, entry.error_message,
                json.dumps(entry.tags)
            ))
            conn.commit()
        
        logger.info(f"Logged prompt execution: {entry_id} for {file} using {model}")
        return entry_id
    
    def add_rating(self, entry_id: str, rating: int, feedback: Optional[str] = None):
        """Add user rating and feedback to a logged prompt"""
        if not 1 <= rating <= 5:
            raise ValueError("Rating must be between 1 and 5")
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE prompt_logs 
                SET rating = ?, feedback = ?
                WHERE id = ?
            """, (rating, feedback, entry_id))
            conn.commit()
        
        logger.info(f"Added rating {rating} to prompt {entry_id}")
    
    def mark_learned(self, entry_id: str):
        """Mark a prompt as learned from"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE prompt_logs 
                SET learned = TRUE
                WHERE id = ?
            """, (entry_id,))
            conn.commit()
    
    def get_entry(self, entry_id: str) -> Optional[PromptLogEntry]:
        """Get a specific prompt log entry"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM prompt_logs WHERE id = ?
            """, (entry_id,))
            row = cursor.fetchone()
            
            if row:
                return PromptLogEntry(
                    id=row['id'],
                    timestamp=row['timestamp'],
                    file=row['file'],
                    injection_type=row['injection_type'],
                    model=row['model'],
                    prompt=row['prompt'],
                    output_hash=row['output_hash'],
                    rating=row['rating'],
                    feedback=row['feedback'],
                    tags=json.loads(row['tags']) if row['tags'] else [],
                    learned=bool(row['learned']),
                    execution_time=row['execution_time'],
                    success=bool(row['success']),
                    error_message=row['error_message']
                )
        return None
    
    def get_recent_entries(self, limit: int = 50) -> List[PromptLogEntry]:
        """Get recent prompt log entries"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM prompt_logs 
                ORDER BY timestamp DESC 
                LIMIT ?
            """, (limit,))
            
            entries = []
            for row in cursor.fetchall():
                entries.append(PromptLogEntry(
                    id=row['id'],
                    timestamp=row['timestamp'],
                    file=row['file'],
                    injection_type=row['injection_type'],
                    model=row['model'],
                    prompt=row['prompt'],
                    output_hash=row['output_hash'],
                    rating=row['rating'],
                    feedback=row['feedback'],
                    tags=json.loads(row['tags']) if row['tags'] else [],
                    learned=bool(row['learned']),
                    execution_time=row['execution_time'],
                    success=bool(row['success']),
                    error_message=row['error_message']
                ))
            
            return entries
    
    def get_low_rated_entries(self, rating_threshold: int = 2) -> List[PromptLogEntry]:
        """Get entries with low ratings for learning"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("""
                SELECT * FROM prompt_logs 
                WHERE rating IS NOT NULL AND rating <= ?
                ORDER BY timestamp DESC
            """, (rating_threshold,))
            
            entries = []
            for row in cursor.fetchall():
                entries.append(PromptLogEntry(
                    id=row['id'],
                    timestamp=row['timestamp'],
                    file=row['file'],
                    injection_type=row['injection_type'],
                    model=row['model'],
                    prompt=row['prompt'],
                    output_hash=row['output_hash'],
                    rating=row['rating'],
                    feedback=row['feedback'],
                    tags=json.loads(row['tags']) if row['tags'] else [],
                    learned=bool(row['learned']),
                    execution_time=row['execution_time'],
                    success=bool(row['success']),
                    error_message=row['error_message']
                ))
            
            return entries
    
    def get_stats(self) -> Dict[str, Any]:
        """Get statistics about prompt logs"""
        with sqlite3.connect(self.db_path) as conn:
            stats = {}
            
            # Total entries
            cursor = conn.execute("SELECT COUNT(*) FROM prompt_logs")
            stats['total_entries'] = cursor.fetchone()[0]
            
            # Success rate
            cursor = conn.execute("SELECT COUNT(*) FROM prompt_logs WHERE success = TRUE")
            success_count = cursor.fetchone()[0]
            stats['success_rate'] = success_count / stats['total_entries'] if stats['total_entries'] > 0 else 0
            
            # Average rating
            cursor = conn.execute("SELECT AVG(rating) FROM prompt_logs WHERE rating IS NOT NULL")
            avg_rating = cursor.fetchone()[0]
            stats['average_rating'] = round(avg_rating, 2) if avg_rating else None
            
            # Model usage
            cursor = conn.execute("""
                SELECT model, COUNT(*) as count 
                FROM prompt_logs 
                GROUP BY model 
                ORDER BY count DESC
            """)
            stats['model_usage'] = dict(cursor.fetchall())
            
            # Injection type distribution
            cursor = conn.execute("""
                SELECT injection_type, COUNT(*) as count 
                FROM prompt_logs 
                GROUP BY injection_type 
                ORDER BY count DESC
            """)
            stats['injection_types'] = dict(cursor.fetchall())
            
            return stats
