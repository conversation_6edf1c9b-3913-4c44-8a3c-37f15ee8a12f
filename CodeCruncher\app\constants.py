"""
Constants for CodeCrusher API
"""

# Supported file extensions
SUPPORTED_EXTENSIONS = [
    "py", "js", "ts", "jsx", "tsx", "java", "cpp", "c", "cs", "php", 
    "rb", "go", "rs", "swift", "kt", "scala", "html", "css", "scss", 
    "sass", "less", "vue", "svelte", "md", "json", "yaml", "yml", "xml"
]

# Default extensions for scanning
DEFAULT_EXTENSIONS = ["py", "js", "ts", "jsx", "tsx"]

# Available AI models
AVAILABLE_MODELS = [
    "gpt-4-turbo",
    "gpt-4",
    "gpt-3.5-turbo",
    "mixtral-8x7b",
    "llama3-70b", 
    "llama3-8b",
    "gemma-7b",
    "claude-3-sonnet",
    "claude-3-haiku"
]

# Default model
DEFAULT_MODEL = "mixtral-8x7b"

# Model tiers for escalation
MODEL_TIERS = {
    "standard": ["llama3-8b", "gemma-7b"],
    "advanced": ["mixtral-8x7b", "llama3-70b"],
    "premium": ["gpt-4-turbo", "gpt-4", "claude-3-sonnet"]
}

# Injection modes
INJECTION_MODES = ["preview", "apply"]

# Default injection settings
DEFAULT_SETTINGS = {
    "fallback_sensitivity": 0.7,
    "auto_apply_threshold": 0.85,
    "retry_attempts": 3,
    "timeout_seconds": 300
}
