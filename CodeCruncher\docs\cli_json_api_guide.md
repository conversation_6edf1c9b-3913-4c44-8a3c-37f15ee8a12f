# CodeCrusher CLI JSON Output & REST API Guide

This guide explains how to use the JSON output mode for CLI commands and the local REST API server in CodeCrusher.

## JSON Output Mode

CodeCrusher CLI commands now support a `--json` flag that outputs clean, structured JSON suitable for programmatic consumption. This makes it easier to integrate CodeCrusher with other tools and scripts.

### Status Command

```bash
codecrusher status --json
```

This outputs clean, structured JSON with information about the current status of CodeCrusher, including model usage, fallback events, caching, tags, and anomalies.

Example output:
```json
{
  "time_period": "Last 24h",
  "entry_count": 42,
  "models_used": {
    "llama3": {
      "count": 30,
      "errors": 2,
      "success_rate": 93.3
    },
    "mistral": {
      "count": 12,
      "errors": 1,
      "success_rate": 91.7
    }
  },
  "fallback_stats": {
    "total": 3,
    "auto": 2,
    "forced": 1,
    "top_cause": "timeout"
  },
  "cache_stats": {
    "hits": 15,
    "misses": 27,
    "hit_rate": 35.7
  },
  "top_tags": {
    "bugfix": 10,
    "feature": 8,
    "refactor": 5
  },
  "anomalies": [
    "⚠️ High error rate (10.0%) in last 6h",
    "🔥 Token output spike (15000 tokens)"
  ]
}
```

### Scan Command

```bash
codecrusher scan --json
```

This outputs clean, structured JSON with information about anomalies detected in the telemetry data.

Example output:
```json
{
  "time_period": "24h",
  "model_filter": "All models",
  "tag_filter": "All tags",
  "entries_analyzed": 42,
  "scan_duration_seconds": 0.123,
  "anomalies_count": 2,
  "anomalies": [
    {
      "type": "high_error_rate",
      "description": "High error rate (10.0%) in last 6h",
      "timestamp": "2023-05-01T12:34:56",
      "model": "llama3",
      "value": 10.0,
      "threshold": 5.0,
      "tags": []
    },
    {
      "type": "token_spike",
      "description": "Token output spike (15000 tokens)",
      "timestamp": "2023-05-01T10:11:12",
      "model": "mistral",
      "value": 15000,
      "threshold": 10000,
      "tags": []
    }
  ]
}
```

### Trends Command

```bash
codecrusher trends --json
```

This outputs clean, structured JSON with historical trend data.

Example output:
```json
{
  "time_period": {
    "since": "2023-04-24T00:00:00",
    "until": "2023-05-01T00:00:00"
  },
  "filters": {
    "model": "all",
    "tag": "all"
  },
  "metrics": {
    "2023-04-24": {
      "total_injections": 10,
      "fallbacks": 1,
      "fallback_rate": 10.0,
      "errors": 1,
      "error_types": {
        "timeout": 1
      },
      "models": {
        "llama3": 7,
        "mistral": 3
      },
      "tokens_in": 1200,
      "tokens_out": 4500,
      "tags": {
        "bugfix": 4,
        "feature": 3,
        "refactor": 3
      }
    },
    "2023-04-25": {
      "total_injections": 15,
      "fallbacks": 2,
      "fallback_rate": 13.3,
      "errors": 1,
      "error_types": {
        "rate_limit": 1
      },
      "models": {
        "llama3": 10,
        "mistral": 5
      },
      "tokens_in": 1800,
      "tokens_out": 6750,
      "tags": {
        "bugfix": 6,
        "feature": 5,
        "refactor": 4
      }
    }
  }
}
```

## Local REST API Server

CodeCrusher includes a local REST API server that exposes telemetry, injection metadata, and anomaly detection data for IDE plugins and CI/CD tools.

### Starting the Server

```bash
codecrusher serve run
```

This starts a local HTTP server on port 9000 (by default) that provides REST API endpoints for accessing telemetry data, status information, and anomaly detection.

Options:
- `--host`: Host to bind to (default: 127.0.0.1)
- `--port`: Port to bind to (default: 9000)
- `--no-open-browser`: Don't open browser after starting server
- `--allow-remote`: Allow remote connections (binds to 0.0.0.0 instead of localhost)

### API Endpoints

#### Status Endpoint

```
GET /api/status
```

Parameters:
- `since`: Time period to analyze (e.g., 24h, 7d) (default: 24h)
- `model`: Filter by model
- `refresh`: Force refresh of cache (default: false)

Returns the current status of CodeCrusher, including model usage, fallback events, caching, tags, and anomalies.

#### Scan Endpoint

```
GET /api/scan
```

Parameters:
- `since`: Time period to analyze (e.g., 24h, 7d) (default: 24h)
- `model`: Filter by model
- `tag`: Filter by tag
- `limit`: Limit the number of anomalies to return (default: 10)
- `verbose`: Include suggested actions in the response (default: false)
- `refresh`: Force refresh of cache (default: false)

Returns a JSON report of anomalies detected in the telemetry data.

#### Trends Endpoint

```
GET /api/trends
```

Parameters:
- `since`: Show data since this time period (e.g., 7d, 30d, 2023-01-01) (default: 7d)
- `until`: Show data until this time period (e.g., now, 1d, 2023-01-31) (default: now)
- `model`: Filter by model
- `tag`: Filter by tag
- `refresh`: Force refresh of cache (default: false)

Returns historical trend data.

#### Telemetry Endpoint

```
GET /api/telemetry
```

Parameters:
- `limit`: Maximum number of entries to return (default: 100)
- `since`: Only return entries since this timestamp
- `model`: Filter by model
- `tag`: Filter by tag
- `operation_type`: Filter by operation type
- `cached`: Filter by cached status
- `fallback`: Filter by fallback status
- `error`: Filter by error status
- `refresh`: Force refresh of cache (default: false)

Returns raw or filtered telemetry logs.

### Examples

#### Using JSON Output with jq

```bash
# Get status and extract cache hit rate
codecrusher status --json | jq '.cache_stats.hit_rate'

# Get anomalies and filter by model
codecrusher scan --json | jq '.anomalies[] | select(.model == "llama3")'

# Get trends and extract total injections per day
codecrusher trends --json | jq '.metrics | to_entries | map({date: .key, injections: .value.total_injections})'
```

#### Using the REST API with curl

```bash
# Get status
curl http://localhost:9000/api/status

# Get anomalies for a specific model
curl http://localhost:9000/api/anomalies?model=llama3

# Get trends for the last 30 days
curl http://localhost:9000/api/trends?since=30d

# Get telemetry data filtered by tag
curl http://localhost:9000/api/telemetry?tag=bugfix&limit=50
```

#### Using the REST API with Python

```python
import requests
import json

# Get status
response = requests.get("http://localhost:9000/api/status")
status = response.json()
print(f"Cache hit rate: {status['cache_stats']['hit_rate']}%")

# Get anomalies
response = requests.get("http://localhost:9000/api/anomalies?model=llama3")
anomalies = response.json()["anomalies"]
for anomaly in anomalies:
    print(f"{anomaly['type']}: {anomaly['description']}")

# Get trends
response = requests.get("http://localhost:9000/api/trends?since=30d")
trends = response.json()
for date, metrics in trends["metrics"].items():
    print(f"{date}: {metrics['total_injections']} injections")
```

### Security Notes

- The server binds to localhost (127.0.0.1) by default, which means it's only accessible from the local machine.
- Use the `--allow-remote` flag to allow remote connections (binds to 0.0.0.0).
- The server does not implement authentication or authorization, so it should only be used in a trusted environment.
- Use the `--port` option to change the default port (9000) if needed.
