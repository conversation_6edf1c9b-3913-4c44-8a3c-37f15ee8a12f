import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: number;
  email: string;
  role: string;
  created_at: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (userData: User, token: string) => void;
  logout: () => void;
  getAuthHeaders: () => Record<string, string>;
  checkTokenExpiry: () => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if token is expired
  const checkTokenExpiry = (): boolean => {
    const expiryStr = localStorage.getItem('codecrusher_token_expires');
    if (!expiryStr) return false;
    
    const expiry = new Date(expiryStr);
    const now = new Date();
    
    return now < expiry;
  };

  // Load authentication state from localStorage
  useEffect(() => {
    const loadAuthState = () => {
      try {
        const storedToken = localStorage.getItem('codecrusher_token');
        const storedUser = localStorage.getItem('codecrusher_user');
        
        if (storedToken && storedUser && checkTokenExpiry()) {
          const userData = JSON.parse(storedUser);
          setUser(userData);
          setToken(storedToken);
        } else {
          // Clear expired or invalid data
          clearAuthData();
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        clearAuthData();
      } finally {
        setIsLoading(false);
      }
    };

    loadAuthState();
  }, []);

  // Clear authentication data
  const clearAuthData = () => {
    localStorage.removeItem('codecrusher_token');
    localStorage.removeItem('codecrusher_token_type');
    localStorage.removeItem('codecrusher_token_expires');
    localStorage.removeItem('codecrusher_user');
    setUser(null);
    setToken(null);
  };

  // Login function
  const login = (userData: User, authToken: string) => {
    setUser(userData);
    setToken(authToken);
  };

  // Logout function
  const logout = async () => {
    try {
      // Send logout request to backend if token exists
      if (token) {
        await fetch('/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      // Clear local data regardless of backend response
      clearAuthData();
    }
  };

  // Get authentication headers for API requests
  const getAuthHeaders = (): Record<string, string> => {
    if (!token || !checkTokenExpiry()) {
      return {};
    }
    
    return {
      'Authorization': `Bearer ${token}`,
    };
  };

  // Check token expiry periodically
  useEffect(() => {
    if (!token) return;

    const interval = setInterval(() => {
      if (!checkTokenExpiry()) {
        console.log('Token expired, logging out');
        logout();
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [token]);

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated: !!user && !!token && checkTokenExpiry(),
    isLoading,
    login,
    logout,
    getAuthHeaders,
    checkTokenExpiry,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
