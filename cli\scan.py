"""
Scan command for CodeCrusher CLI
"""

import typer
import logging
import os
from typing import Optional, List
from pathlib import Path
from rich.panel import Panel
from rich.table import Table

from .common import setup_logging, console

# Create the scan app
scan_app = typer.Typer(
    help="🔍 Scan source code for AI_INJECT tags and issues",
    short_help="Scan code"
)

@scan_app.command("run")
def run(
    path: str = typer.Option(
        ..., "--path", "-p",
        help="📁 Directory or file to scan"
    ),
    extensions: List[str] = typer.Option(
        [".py", ".js", ".ts", ".java", ".c", ".cpp", ".cs"], "--extensions", "-e",
        help="🔍 File extensions to scan (default: common code files)"
    ),
    recursive: bool = typer.Option(
        True, "--recursive/--no-recursive",
        help="🔄 Scan directories recursively"
    ),
    verbose: bool = False,
    provider: str = "auto",
    model: str = "auto",
    cache: bool = True,
):
    """
    🔍 Scan source code for AI_INJECT tags and issues.

    This command scans a directory or file for AI_INJECT tags and reports
    on potential issues or opportunities for AI-powered improvements.
    """
    # Set up logging
    setup_logging(verbose)

    # Display welcome message
    console.print(Panel(
        "[bold]CodeCrusher: Enterprise-grade AI-powered code scanner[/bold]\n"
        "Identifying opportunities for AI-powered improvements.",
        title="[bold cyan]🚀 Starting CodeCrusher Scanner[/bold cyan]",
        border_style="cyan"
    ))
    logging.info("Starting CodeCrusher Scanner...")

    # Validate path
    target_path = Path(path)
    if not target_path.exists():
        console.print(f"[bold red]❌ Error:[/bold red] Path {path} does not exist")
        raise typer.Exit(code=1)

    # Create a configuration table
    config_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    config_table.add_row("[cyan]Target Path:[/cyan]", f"[yellow]{path}[/yellow]")
    config_table.add_row("[cyan]Extensions:[/cyan]", f"[yellow]{', '.join(extensions)}[/yellow]")
    config_table.add_row("[cyan]Recursive:[/cyan]", f"[yellow]{'Yes' if recursive else 'No'}[/yellow]")

    console.print(Panel(config_table, title="[bold]Scan Configuration[/bold]", border_style="blue"))

    # Scan for files
    files_to_scan = []

    if target_path.is_file():
        # Single file scan
        if any(target_path.name.endswith(ext) for ext in extensions):
            files_to_scan.append(target_path)
    else:
        # Directory scan
        if recursive:
            for root, _, files in os.walk(target_path):
                for file in files:
                    if any(file.endswith(ext) for ext in extensions):
                        files_to_scan.append(Path(root) / file)
        else:
            for file in target_path.iterdir():
                if file.is_file() and any(file.name.endswith(ext) for ext in extensions):
                    files_to_scan.append(file)

    console.print(f"[bold]Found {len(files_to_scan)} files to scan[/bold]")

    # Scan each file for AI_INJECT tags
    tags_found = []
    files_with_tags = 0

    for file_path in files_to_scan:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.readlines()

            file_tags = []
            for i, line in enumerate(content):
                if "AI_INJECT:" in line:
                    tag = line.split("AI_INJECT:")[1].strip()
                    file_tags.append((tag, i + 1))

            if file_tags:
                files_with_tags += 1
                relative_path = file_path.relative_to(os.getcwd())
                console.print(f"[green]✅ Found {len(file_tags)} tags in:[/green] [yellow]{relative_path}[/yellow]")

                for tag, line_num in file_tags:
                    console.print(f"  [cyan]•[/cyan] Line {line_num}: [bold]{tag}[/bold]")
                    tags_found.append((str(relative_path), tag, line_num))

        except Exception as e:
            console.print(f"[red]❌ Error scanning {file_path}: {str(e)}[/red]")

    # Display summary
    summary_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    summary_table.add_row("[cyan]Files Scanned:[/cyan]", f"[bold]{len(files_to_scan)}[/bold]")
    summary_table.add_row("[cyan]Files with Tags:[/cyan]", f"[bold]{files_with_tags}[/bold]")
    summary_table.add_row("[cyan]Total Tags Found:[/cyan]", f"[bold]{len(tags_found)}[/bold]")

    console.print(Panel(
        summary_table,
        title="[bold green]✅ Scan Complete[/bold green]",
        border_style="green"
    ))

    # If tags were found, display them in a table
    if tags_found:
        tags_table = Table(title="AI_INJECT Tags Found", box=None)
        tags_table.add_column("File", style="cyan")
        tags_table.add_column("Line", style="yellow")
        tags_table.add_column("Tag", style="green")

        for file_path, tag, line_num in tags_found:
            tags_table.add_row(str(file_path), str(line_num), tag)

        console.print(tags_table)

    logging.info("Scan completed.")
