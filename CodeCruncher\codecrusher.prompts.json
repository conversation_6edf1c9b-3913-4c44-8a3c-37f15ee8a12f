{"refactor": "Please refactor the following code for readability and maintainability:\n\n{{code}}\n\nInstructions: {{prompt}}\n\nFocus on:\n- Clean code principles\n- Better variable names\n- Improved structure\n- Code reusability", "document": "Add comprehensive documentation and comments to the following code:\n\n{{code}}\n\nDocumentation requirements:\n- Clear docstrings for functions and classes\n- Inline comments for complex logic\n- Type hints where appropriate\n- Usage examples", "optimize": "Optimize the following code for better performance:\n\n{{code}}\n\nOptimization request: {{prompt}}\n\nConsider:\n- Algorithm efficiency\n- Memory usage\n- Database query optimization\n- Caching strategies", "test": "Generate comprehensive unit tests for the following code:\n\n{{code}}\n\nTest requirements: {{prompt}}\n\nInclude:\n- Positive test cases\n- Edge cases\n- Error conditions\n- Mock dependencies where needed", "debug": "Debug and fix issues in the following code:\n\n{{code}}\n\nProblem description: {{prompt}}\n\nAnalyze for:\n- Logic errors\n- Runtime exceptions\n- Edge case failures\n- Performance bottlenecks", "security": "Improve the security of the following code:\n\n{{code}}\n\nSecurity focus: {{prompt}}\n\nAddress:\n- Input validation\n- SQL injection prevention\n- XSS protection\n- Authentication/authorization", "feature": "Add the requested feature to the existing codebase:\n\n{{code}}\n\nFeature request: {{prompt}}\n\nEnsure:\n- Integration with existing code\n- Proper error handling\n- Consistent coding style\n- Scalable implementation", "explain": "Explain how the following code works:\n\n{{code}}\n\nExplanation focus: {{prompt}}\n\nProvide:\n- Step-by-step breakdown\n- Purpose of each component\n- Data flow explanation\n- Design pattern identification", "transform": "Transform the following code according to requirements:\n\n{{code}}\n\nTransformation request: {{prompt}}\n\nMaintain:\n- Original functionality\n- Code quality standards\n- Proper error handling\n- Documentation", "localize": "Prepare the following code for internationalization:\n\n{{code}}\n\nLocalization requirements: {{prompt}}\n\nImplement:\n- String externalization\n- Locale-aware formatting\n- RTL language support\n- Translation framework integration", "default": "Process the following code according to the request:\n\n{{code}}\n\nRequest: {{prompt}}\n\nProvide high-quality, production-ready code that follows best practices."}