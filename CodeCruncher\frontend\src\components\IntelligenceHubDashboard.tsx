import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Brain,
  TrendingUp,
  Database,
  Activity,
  Star,
  FileText,
  Zap,
  BarChart3,
  Target,
  Cpu,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useIntelligenceWebSocket } from '@/hooks/useIntelligenceWebSocket';

interface IntelligenceData {
  total_entries: number;
  models: string[];
  files_processed: number;
  average_rating: number;
  average_score: number;
  model_performance: Record<string, any>;
  tone_effectiveness: Record<string, number>;
  best_model: string;
  recent_activity: number;
}

interface ModelPerformance {
  total_entries: number;
  average_score: number;
  average_rating: number;
  fallback_usage: number;
}

export default function IntelligenceHubDashboard() {
  const [intelligenceData, setIntelligenceData] = useState<IntelligenceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // WebSocket connection for real-time updates
  const {
    connectionStatus,
    lastMessage,
    isConnected,
    retry
  } = useIntelligenceWebSocket({
    url: '/ws/intelligence',
    onMessage: (message) => {
      console.log('🧠 Intelligence update received:', message);

      if (message.type === 'welcome' || message.type === 'update') {
        if (message.data) {
          setIntelligenceData(message.data);
          setLoading(false);
          setError(null);
        }
      } else if (message.type === 'intelligence_update' || message.type === 'new_feedback') {
        // Refresh data when new feedback is received
        fetchIntelligenceData();
      }
    },
    onStatusChange: (status) => {
      console.log('🧠 Intelligence WebSocket status:', status);
      if (status === 'error') {
        setError('WebSocket connection failed');
      }
    }
  });

  useEffect(() => {
    fetchIntelligenceData();
    // Set up periodic refresh as fallback
    const interval = setInterval(fetchIntelligenceData, 60000); // Refresh every 60 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchIntelligenceData = async () => {
    try {
      // Try to fetch real data from the backend API
      const response = await fetch('/api/intel/summary');

      if (response.ok) {
        const result = await response.json();
        const data = result.intelligence_data;

        // Use real data if available
        setIntelligenceData(data);
        setLoading(false);
        return;
      }

      // Fallback to mock data if API is not available
      console.warn('Intelligence API not available, using mock data');
      const mockData: IntelligenceData = {
        total_entries: 9,
        models: ['GPT-4', 'TestAPI', 'TestModel', 'Mixtral'],
        files_processed: 6,
        average_rating: 4.0,
        average_score: 85.2,
        model_performance: {
          'GPT-4': { total_entries: 2, average_score: 95, average_rating: 5.0, fallback_usage: 1 },
          'TestAPI': { total_entries: 1, average_score: 95, average_rating: 5.0, fallback_usage: 0 },
          'TestModel': { total_entries: 1, average_score: 88, average_rating: 4.0, fallback_usage: 0 },
          'Mixtral': { total_entries: 5, average_score: 78, average_rating: 3.4, fallback_usage: 2 }
        },
        tone_effectiveness: {
          'assertive': 5.0,
          'neutral': 4.5,
          'formal': 4.5,
          'friendly': 3.2
        },
        best_model: 'GPT-4',
        recent_activity: 3
      };

      setIntelligenceData(mockData);
      setLoading(false);
    } catch (err) {
      console.error('Failed to fetch intelligence data:', err);
      setError('Failed to fetch intelligence data');
      setLoading(false);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-lg text-gray-600">Loading Intelligence Data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error || !intelligenceData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-red-600 text-lg mb-2">⚠️ Error Loading Data</div>
              <div className="text-gray-600">{error || 'Unknown error occurred'}</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2 flex items-center justify-center gap-3">
            <Brain className="h-10 w-10 text-blue-600" />
            Intelligence Hub
          </h1>
          <p className="text-lg text-gray-600">Real-time AI performance analytics and insights</p>

          {/* WebSocket Status */}
          <div className="flex items-center justify-center gap-2 mt-3">
            {isConnected ? (
              <>
                <Wifi className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600 font-medium">Live Updates Active</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-orange-500" />
                <span className="text-sm text-orange-600 font-medium">
                  {connectionStatus === 'connecting' ? 'Connecting...' : 'Using Cached Data'}
                </span>
                {connectionStatus === 'error' && (
                  <button
                    onClick={retry}
                    className="ml-2 text-xs bg-orange-100 hover:bg-orange-200 text-orange-700 px-2 py-1 rounded"
                  >
                    Retry
                  </button>
                )}
              </>
            )}
          </div>
        </div>

        {/* Key Metrics Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Entries</CardTitle>
              <Database className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{intelligenceData.total_entries}</div>
              <p className="text-xs text-gray-500 mt-1">Feedback entries tracked</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Average Rating</CardTitle>
              <Star className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{intelligenceData.average_rating.toFixed(1)}/5.0</div>
              <div className="flex items-center mt-1">
                {renderStars(intelligenceData.average_rating)}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Files Processed</CardTitle>
              <FileText className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{intelligenceData.files_processed}</div>
              <p className="text-xs text-gray-500 mt-1">Unique files analyzed</p>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Best Model</CardTitle>
              <Target className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900">{intelligenceData.best_model}</div>
              <p className="text-xs text-gray-500 mt-1">Top performing AI</p>
            </CardContent>
          </Card>
        </div>

        {/* Model Performance and Tone Effectiveness */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Model Performance */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cpu className="h-5 w-5 text-blue-600" />
                Model Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(intelligenceData.model_performance).map(([model, perf]: [string, any]) => (
                <div key={model} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900">{model}</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {perf.total_entries} entries
                      </Badge>
                      <div className="flex items-center">
                        {renderStars(perf.average_rating)}
                        <span className="ml-1 text-sm text-gray-600">
                          {perf.average_rating.toFixed(1)}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Quality Score</span>
                      <span className={`font-medium ${getPerformanceColor(perf.average_score)}`}>
                        {perf.average_score}%
                      </span>
                    </div>
                    <Progress value={perf.average_score} className="h-2" />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Tone Effectiveness */}
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-600" />
                Tone Effectiveness
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(intelligenceData.tone_effectiveness)
                .sort(([,a], [,b]) => b - a)
                .map(([tone, rating]) => (
                <div key={tone} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-900 capitalize">{tone}</span>
                    <div className="flex items-center">
                      {renderStars(rating)}
                      <span className="ml-1 text-sm text-gray-600">
                        {rating.toFixed(1)}
                      </span>
                    </div>
                  </div>
                  <Progress value={(rating / 5) * 100} className="h-2" />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* System Status */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-purple-600" />
              Intelligence System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="font-medium text-gray-900">Intel Directory</span>
                </div>
                <p className="text-sm text-gray-600">~/.codecrusher/intel/</p>
                <p className="text-xs text-green-600 mt-1">Active</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="font-medium text-gray-900">Feedback Log</span>
                </div>
                <p className="text-sm text-gray-600">3,819 bytes</p>
                <p className="text-xs text-green-600 mt-1">Synced</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span className="font-medium text-gray-900">Intel API</span>
                </div>
                <p className="text-sm text-gray-600">Ready</p>
                <p className="text-xs text-green-600 mt-1">Operational</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-orange-600" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <span className="font-medium text-gray-900">example.py</span>
                  <span className="text-sm text-gray-600 ml-2">• Mixtral</span>
                </div>
                <div className="flex items-center">
                  {renderStars(4)}
                  <span className="ml-1 text-sm text-gray-600">4.0</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <span className="font-medium text-gray-900">auth_service.py</span>
                  <span className="text-sm text-gray-600 ml-2">• GPT-4</span>
                </div>
                <div className="flex items-center">
                  {renderStars(5)}
                  <span className="ml-1 text-sm text-gray-600">5.0</span>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <span className="font-medium text-gray-900">test_intel_api.py</span>
                  <span className="text-sm text-gray-600 ml-2">• TestAPI</span>
                </div>
                <div className="flex items-center">
                  {renderStars(5)}
                  <span className="ml-1 text-sm text-gray-600">5.0</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center py-6">
          <p className="text-sm text-gray-500">
            🎉 Intelligence Hub - Real-time AI analytics and performance insights
          </p>
        </div>
      </div>
    </div>
  );
}
