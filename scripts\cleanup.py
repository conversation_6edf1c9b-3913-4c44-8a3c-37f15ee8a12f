#!/usr/bin/env python3
"""
Process Cleanup Script for CodeCrusher Development
Kills hanging uvicorn, node, and vite processes to prevent port conflicts.
"""

import os
import signal
import psutil
import sys
from typing import List


def kill_processes_by_name(target_names: List[str]) -> None:
    """
    Kill processes by name patterns.
    
    Args:
        target_names: List of process name patterns to match
    """
    killed_count = 0
    
    print("🧹 CodeCrusher Process Cleanup")
    print("=" * 40)
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_name = proc.info['name'].lower()
            cmdline = ' '.join(proc.info['cmdline'] or []).lower()
            
            # Check if process matches any target
            for target in target_names:
                if (target in proc_name or 
                    target in cmdline or
                    any(target in arg.lower() for arg in (proc.info['cmdline'] or []))):
                    
                    print(f"🔪 Killing: {proc.info['name']} [PID: {proc.info['pid']}]")
                    
                    # Try graceful termination first
                    try:
                        proc.terminate()
                        proc.wait(timeout=3)
                    except psutil.TimeoutExpired:
                        # Force kill if graceful termination fails
                        proc.kill()
                    
                    killed_count += 1
                    break
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            # Process already gone or no permission
            continue
        except Exception as e:
            print(f"⚠️  Error processing {proc.info.get('name', 'unknown')}: {e}")
    
    print(f"\n✅ Cleanup complete. Killed {killed_count} processes.")


def kill_processes_by_port(ports: List[int]) -> None:
    """
    Kill processes listening on specific ports.
    
    Args:
        ports: List of port numbers to check
    """
    print(f"\n🔍 Checking for processes on ports: {ports}")
    
    for port in ports:
        for conn in psutil.net_connections():
            if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                try:
                    proc = psutil.Process(conn.pid)
                    print(f"🔪 Killing process on port {port}: {proc.name()} [PID: {conn.pid}]")
                    proc.terminate()
                    proc.wait(timeout=3)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    try:
                        proc.kill()
                    except:
                        pass
                except Exception as e:
                    print(f"⚠️  Error killing process on port {port}: {e}")


def main():
    """Main cleanup function."""
    # Target process names and patterns
    target_processes = [
        "uvicorn",
        "node",
        "vite",
        "npm",
        "yarn",
        "pnpm",
        "fastapi",
        "python.exe",  # Windows Python processes
        "node.exe",    # Windows Node processes
    ]
    
    # Common development ports
    target_ports = [8000, 8001, 5173, 3000, 4000, 5000]
    
    try:
        # Kill by process name
        kill_processes_by_name(target_processes)
        
        # Kill by port (additional safety)
        kill_processes_by_port(target_ports)
        
        print("\n🎉 All cleanup operations completed!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Cleanup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Check if psutil is available
    try:
        import psutil
    except ImportError:
        print("❌ psutil not found. Install with: pip install psutil")
        sys.exit(1)
    
    main()
