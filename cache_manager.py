# FILE: cache_manager.py

import os
import json
import hashlib
import threading
from pathlib import Path
from datetime import datetime

# Support both home directory and local cache
HOME_CACHE_FILE = os.path.expanduser("~/.codecrusher_cache.json")
LOCAL_CACHE_FILE = Path(".cache/codecrusher_cache.json")

# Default to home directory cache
CACHE_FILE = HOME_CACHE_FILE
CACHE_LOCK = threading.Lock()


def ensure_cache_dir():
    """
    Ensure the cache directory and file exist
    """
    # For local cache
    if isinstance(CACHE_FILE, Path):
        CACHE_FILE.parent.mkdir(parents=True, exist_ok=True)

    # Create cache file if it doesn't exist
    if not os.path.exists(CACHE_FILE):
        with open(CACHE_FILE, "w", encoding="utf-8") as f:
            json.dump({}, f)


def _generate_key(tag, prompt, provider=None, model_id=None):
    """
    Generate a cache key based on tag, prompt, provider, and model_id

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        provider (str, optional): The AI provider name
        model_id (str, optional): The specific model ID

    Returns:
        str: A unique hash key
    """
    # For backward compatibility
    if model_id is None and provider is not None:
        base = f"{tag}:{prompt}:{provider}"
        return hashlib.sha256(base.encode('utf-8')).hexdigest()
    # New format with model_id
    elif model_id is not None:
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()
        return f"{tag}_{prompt_hash}_{model_id}"
    # Fallback
    else:
        base = f"{tag}:{prompt}"
        return hashlib.sha256(base.encode('utf-8')).hexdigest()


def check_cache(tag, prompt, provider=None, model_id=None):
    """
    Check if a result exists in the cache

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        provider (str, optional): The AI provider name
        model_id (str, optional): The specific model ID

    Returns:
        dict or None: The cached result or None if not found
    """
    ensure_cache_dir()
    key = _generate_key(tag, prompt, provider, model_id)
    with CACHE_LOCK:
        try:
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data = json.load(f)
            if key in cache_data:
                if model_id:
                    print(f"[{datetime.now()}] 🔁 Cache hit for tag='{tag}', model='{model_id}'")
                else:
                    print(f"[{datetime.now()}] 🔁 Cache hit for tag='{tag}', provider='{provider}'")
                return cache_data[key]
            else:
                if model_id:
                    print(f"[{datetime.now()}] 🆕 Cache miss for tag='{tag}', model='{model_id}'")
                else:
                    print(f"[{datetime.now()}] 🆕 Cache miss for tag='{tag}', provider='{provider}'")
        except Exception as e:
            print(f"[{datetime.now()}] ⚠️ Cache read error: {e}")
    return None


def save_cache(tag, prompt, provider=None, content=None, model_id=None, result=None):
    """
    Save a result to the cache

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        provider (str, optional): The AI provider name
        content (str, optional): The content to cache (legacy)
        model_id (str, optional): The specific model ID
        result (dict, optional): The result object with score, model info, etc.
    """
    ensure_cache_dir()
    key = _generate_key(tag, prompt, provider, model_id)
    with CACHE_LOCK:
        try:
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data = json.load(f)
        except:
            cache_data = {}

        # Support both legacy and new format
        if result is not None:
            cache_data[key] = result
            display_model = model_id or result.get('model', 'unknown')
        else:
            cache_data[key] = content
            display_model = provider or 'unknown'

        try:
            with open(CACHE_FILE, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, indent=2)
            print(f"[{datetime.now()}] 💾 Cached result for tag='{tag}', model='{display_model}'")
        except Exception as e:
            print(f"[{datetime.now()}] ❌ Cache write error: {e}")


# New functions for multi-model caching with improved structure
def get_prompt_key(tag, prompt):
    """
    Generate a prompt key for the cache

    Args:
        tag (str): The tag name
        prompt (str): The prompt text

    Returns:
        str: A unique prompt key
    """
    # Create a key in the format "inject::filename::tag" or use hash for uniqueness
    if ":" in tag:
        # Tag already has a structure like "inject::filename::tag"
        return tag
    else:
        # Create a structured key
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()[:8]
        return f"inject::{prompt_hash}::{tag}"

def load_cache():
    """
    Load the cache from disk

    Returns:
        dict: The cache data
    """
    ensure_cache_dir()
    try:
        with CACHE_LOCK:
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception as e:
        print(f"[{datetime.now()}] ⚠️ Cache load error: {e}")
        return {}

def save_cache_data(cache_data):
    """
    Save the cache data to disk

    Args:
        cache_data (dict): The cache data to save
    """
    ensure_cache_dir()
    try:
        with CACHE_LOCK:
            with open(CACHE_FILE, "w", encoding="utf-8") as f:
                json.dump(cache_data, f, indent=2)
    except Exception as e:
        print(f"[{datetime.now()}] ❌ Cache write error: {e}")

def save_result(tag, prompt, model_id, result):
    """
    Save a model result to the cache using the new structure

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        model_id (str): The model ID
        result (dict): The result object with score, output, etc.
    """
    cache = load_cache()
    prompt_key = get_prompt_key(tag, prompt)

    # Initialize the prompt key if it doesn't exist
    if prompt_key not in cache:
        cache[prompt_key] = {}

    # Extract the output and score from the result
    output = result.get("output", "")
    score = result.get("score", 0)

    # Save the result in the new structure
    cache[prompt_key][model_id] = {
        "output": output,
        "score": score,
        "timestamp": datetime.now().isoformat()
    }

    # Save the updated cache
    save_cache_data(cache)
    print(f"[{datetime.now()}] 💾 Cached result for prompt_key='{prompt_key}', model='{model_id}', score={score}")

def get_cached_result(tag, prompt, model_id):
    """
    Get a cached result for a specific model using the new structure

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        model_id (str): The model ID

    Returns:
        dict or None: The cached result or None if not found
    """
    cache = load_cache()
    prompt_key = get_prompt_key(tag, prompt)

    # Check if the prompt key and model ID exist in the cache
    if prompt_key in cache and model_id in cache[prompt_key]:
        cached_data = cache[prompt_key][model_id]
        print(f"[{datetime.now()}] 🔁 Cache hit for prompt_key='{prompt_key}', model='{model_id}', score={cached_data.get('score', 0)}")

        # Convert to the format expected by the AI engine
        return {
            "model": model_id,
            "id": model_id,
            "output": cached_data.get("output", ""),
            "score": cached_data.get("score", 0),
            "timestamp": cached_data.get("timestamp", datetime.now().isoformat())
        }

    print(f"[{datetime.now()}] 🆕 Cache miss for prompt_key='{prompt_key}', model='{model_id}'")
    return None

# Additional utility functions
def clear_cache():
    """
    Clear the entire cache
    """
    ensure_cache_dir()
    with CACHE_LOCK:
        try:
            with open(CACHE_FILE, "w", encoding="utf-8") as f:
                json.dump({}, f)
            print(f"[{datetime.now()}] 🧹 Cache cleared successfully")
        except Exception as e:
            print(f"[{datetime.now()}] ❌ Cache clear error: {e}")


def cache_exists(tag, prompt, model_id):
    """
    Check if a result exists in the cache without retrieving it

    Args:
        tag (str): The tag name
        prompt (str): The prompt text
        model_id (str): The model ID

    Returns:
        bool: True if the result exists in the cache, False otherwise
    """
    cache = load_cache()
    prompt_key = get_prompt_key(tag, prompt)

    # Check if the prompt key and model ID exist in the cache
    exists = prompt_key in cache and model_id in cache[prompt_key]

    if exists:
        print(f"[{datetime.now()}] 🔍 Cache entry exists for prompt_key='{prompt_key}', model='{model_id}'")
    else:
        print(f"[{datetime.now()}] 🔍 Cache entry does not exist for prompt_key='{prompt_key}', model='{model_id}'")

    return exists


# Maintain backward compatibility with existing code
def save_to_cache(tag, prompt, provider, content):
    """Legacy function for backward compatibility."""
    save_cache(tag, prompt, provider, content)


def get_cached_code(tag_name, prompt_text, model="mistral"):
    """Legacy function for backward compatibility."""
    return check_cache(tag_name, prompt_text, model)