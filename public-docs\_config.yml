# GitHub Pages Configuration for CodeCrusher Documentation

# Site settings
title: "CodeCrusher"
description: "Enterprise-grade AI-powered code injector with self-improving intelligence"
url: "https://codegx-technology.github.io"
baseurl: "/codecruncher"

# Author information
author:
  name: "CodeCrusher Team"
  email: "<EMAIL>"
  url: "https://github.com/Codegx-Technology"

# Repository information
repository: "Codegx-Technology/CodeCruncher"
github_username: "Codegx-Technology"

# Build settings
markdown: kramdown
highlighter: rouge
theme: minima

# Plugins
plugins:
  - jekyll-feed
  - jekyll-sitemap
  - jekyll-seo-tag

# Exclude from processing
exclude:
  - README.md
  - Gemfile
  - Gemfile.lock
  - node_modules
  - vendor/bundle/
  - vendor/cache/
  - vendor/gems/
  - vendor/ruby/

# Include files
include:
  - _pages

# Collections
collections:
  pages:
    output: true
    permalink: /:name/

# Default layouts
defaults:
  - scope:
      path: ""
      type: "pages"
    values:
      layout: "default"

# SEO settings
lang: en_US
twitter:
  username: codecrusher
  card: summary_large_image

# Social links
social:
  name: CodeCrusher
  links:
    - https://github.com/Codegx-Technology/CodeCruncher

# Google Analytics (optional)
# google_analytics: UA-XXXXXXXX-X

# Disqus comments (optional)
# disqus:
#   shortname: codecrusher

# Custom variables
codecrusher:
  version: "1.0.0"
  pypi_url: "https://pypi.org/project/codecrusher/"
  github_url: "https://github.com/Codegx-Technology/CodeCruncher"
  docs_url: "https://codegx-technology.github.io/codecruncher/"
  
# Navigation
navigation:
  - title: "Overview"
    url: "#overview"
  - title: "Installation"
    url: "#installation"
  - title: "Usage"
    url: "#usage"
  - title: "Features"
    url: "#features"
  - title: "Architecture"
    url: "#architecture"
  - title: "Contributing"
    url: "#contributing"

# Features list
features:
  - title: "Self-Improving Intelligence"
    description: "AI that learns from your feedback and gets better over time"
    icon: "fas fa-brain"
  - title: "Multi-Model Fallback"
    description: "Intelligent routing across 4 AI models with automatic escalation"
    icon: "fas fa-layer-group"
  - title: "Professional CLI"
    description: "Rich console interface with interactive preview and progress tracking"
    icon: "fas fa-terminal"
  - title: "Quality Analytics"
    description: "Comprehensive quality scoring and improvement tracking"
    icon: "fas fa-chart-line"

# Statistics
stats:
  quality_score: "92.5/100"
  win_rate: "75%"
  models: "4"
  languages: "4+"
