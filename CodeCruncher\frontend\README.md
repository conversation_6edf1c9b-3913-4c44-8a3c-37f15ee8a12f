# CodeCrusher Frontend Dashboard

A modern React frontend for CodeCrusher's AI-powered code injection capabilities.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- CodeCrusher FastAPI backend running on localhost:8001

### Installation

1. **Navigate to frontend directory:**
```bash
cd frontend
```

2. **Install dependencies:**
```bash
npm install
```

3. **Start development server:**
```bash
npm run dev
```

The frontend will be available at `http://localhost:5173`

## 🎯 Features

### Modern UI Components
- **Shadcn/ui** components with Tailwind CSS styling
- **Responsive design** that works on desktop and mobile
- **Dark/light theme** support (configurable)
- **Real-time status** indicators and health checks

### CodeCrusher Integration
- **Full parameter support** for all CLI options
- **Source path validation** before execution
- **Real-time execution logs** with emoji indicators
- **Execution statistics** and timing information
- **Error handling** with detailed error messages

### Smart Configuration
- **Auto-detection** of available models and extensions
- **Form validation** with helpful error messages
- **Persistent settings** (localStorage)
- **Quick presets** for common operations

## 🔧 Configuration Options

### Injection Parameters
- **Source Path**: File or directory to process
- **AI Prompt**: Description of desired changes
- **Tag**: Version identifier for the injection
- **Extensions**: File types to process (py, js, ts, etc.)
- **Recursive**: Scan subdirectories
- **Apply/Preview**: Apply changes or preview only
- **Auto Model Routing**: Use best AI model automatically
- **Refresh Cache**: Bypass cached results
- **Force Mode**: Inject without AI_INJECT tags

### UI Features
- **Real-time health monitoring** of backend status
- **Execution logs** with color-coded output
- **Statistics display** with success/failure metrics
- **Responsive layout** for different screen sizes

## 📋 API Integration

The frontend integrates with the CodeCrusher FastAPI backend:

### Endpoints Used
- `GET /health` - Backend health and CodeCrusher availability
- `POST /inject` - Main code injection endpoint
- `GET /models` - Available AI models
- `GET /extensions` - Supported file extensions
- `POST /validate` - Source path validation

### Request Flow
1. **Health Check**: Verify backend and CodeCrusher availability
2. **Source Validation**: Validate path before execution
3. **Injection Request**: Send parameters to backend
4. **Real-time Updates**: Display progress and results
5. **Statistics Display**: Show execution metrics

## 🎨 UI Components

### Built with Shadcn/ui
- **Button**: Primary actions and controls
- **Input/Textarea**: Form inputs with validation
- **Card**: Content containers and panels
- **Switch**: Toggle controls for boolean options
- **Select**: Dropdown selections for models/extensions
- **Badge**: Status indicators and labels
- **Alert**: Important notifications and warnings

### Custom Features
- **Health Status Indicator**: Real-time backend status
- **Execution Log Terminal**: Styled console output
- **Configuration Panel**: Organized form controls
- **Results Summary**: Execution statistics and timing

## 🚀 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

### Project Structure
```
frontend/
├── src/
│   ├── components/ui/    # Reusable UI components
│   ├── lib/             # Utility functions
│   ├── App.tsx          # Main application component
│   ├── main.tsx         # Application entry point
│   └── index.css        # Global styles
├── public/              # Static assets
└── package.json         # Dependencies and scripts
```

### Adding New Features
1. Create components in `src/components/`
2. Add utilities in `src/lib/`
3. Update `App.tsx` for new functionality
4. Follow TypeScript best practices

## 🔗 Backend Integration

### API Configuration
The frontend uses dynamic API configuration with environment variable support:

#### Environment Variables
Create a `.env` file in the frontend directory (copy from `.env.example`):

```bash
# Backend API Server URL
VITE_SERVER_URL=http://localhost:8000

# WebSocket URL (optional - auto-derived if not set)
VITE_WS_URL=ws://localhost:8000

# Development settings
VITE_DEV_MODE=true
```

#### URL Priority System
The frontend uses this priority order for API URLs:
1. `VITE_SERVER_URL` (highest priority)
2. `VITE_API_URL` (fallback)
3. `window.location.origin` (production fallback)
4. `http://localhost:8000` (default)

#### Usage Examples
```bash
# Development (default)
npm run dev

# Custom backend URL
VITE_SERVER_URL=http://your-backend:8000 npm run dev

# Production build with environment variables
VITE_SERVER_URL=https://api.codecrusher.com npm run build
```

### CORS Configuration
Ensure the backend CORS settings allow frontend origin:
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 📱 Responsive Design

The dashboard is fully responsive and works on:
- **Desktop**: Full feature set with side-by-side panels
- **Tablet**: Stacked layout with optimized spacing
- **Mobile**: Single-column layout with touch-friendly controls

## 🎯 Usage Examples

### Basic Code Injection
1. Enter source path (e.g., `./src`)
2. Write AI prompt (e.g., "Add error handling")
3. Configure options (recursive, extensions, etc.)
4. Click "Preview Changes" to see what would be modified
5. Click "Apply Changes" to execute the injection

### Advanced Configuration
- Use **Force Mode** to inject without AI_INJECT tags
- Enable **Refresh Cache** to bypass cached results
- Select specific **Extensions** for targeted processing
- Use **Custom Tags** for version tracking

## 🔧 Troubleshooting

### Common Issues
- **Backend not available**: Check if FastAPI server is running
- **CORS errors**: Verify backend CORS configuration
- **CodeCrusher not found**: Ensure CLI is installed and in PATH
- **Build errors**: Check Node.js version and dependencies

### Debug Mode
Enable detailed logging by setting:
```bash
VITE_DEBUG=true npm run dev
```

## 🌟 Future Enhancements

- **File upload** for direct file processing
- **Batch operations** for multiple projects
- **History tracking** of previous injections
- **Settings persistence** across sessions
- **Dark mode** toggle
- **Real-time collaboration** features

Perfect for developers who want a modern, intuitive interface for CodeCrusher's powerful AI code injection capabilities!
