#!/usr/bin/env python3
"""
CodeCrusher post-commit hook

This hook logs telemetry summaries or injection metadata to a central log
and adds branch information to telemetry data.
"""

import os
import sys
import json
import subprocess
import time
import re
import requests
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from datetime import datetime

# Constants
TELEMETRY_DIR = os.path.expanduser("~/.codecrusher")
TELEMETRY_FILE = os.path.join(TELEMETRY_DIR, "telemetry.jsonl")
COMMIT_LOG_FILE = os.path.join(TELEMETRY_DIR, "commit_log.jsonl")
HOOK_CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "post-commit.config.json")

# Injection markers
INJECTION_MARKERS = [
    r"# AI_INJECT: (\w+)",  # Python
    r"// AI_INJECT: (\w+)",  # JS, TS, Java, C#, etc.
    r"/\* AI_INJECT: (\w+) \*/",  # CSS, C, etc.
    r"<!-- AI_INJECT: (\w+) -->",  # HTML, XML, etc.
    r"-- AI_INJECT: (\w+)",  # SQL
    r"' AI_INJECT: (\w+)",  # VB
]

def ensure_telemetry_dir():
    """Ensure the telemetry directory exists."""
    os.makedirs(TELEMETRY_DIR, exist_ok=True)

def load_hook_config() -> Dict[str, Any]:
    """Load hook configuration."""
    if not os.path.exists(HOOK_CONFIG_FILE):
        return {
            "remote_sync": False,
            "remote_sync_url": ""
        }

    try:
        with open(HOOK_CONFIG_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception:
        return {
            "remote_sync": False,
            "remote_sync_url": ""
        }

def extract_injection_tags(content: str) -> Set[str]:
    """
    Extract injection tags from file content.

    Args:
        content: File content

    Returns:
        Set of injection tags
    """
    tags = set()

    for marker in INJECTION_MARKERS:
        for match in re.finditer(marker, content):
            tags.add(match.group(1))

    return tags

def get_commit_info() -> Dict[str, Any]:
    """Get information about the current commit."""
    # Get commit hash
    hash_result = subprocess.run(
        ["git", "rev-parse", "HEAD"],
        capture_output=True,
        text=True
    )

    if hash_result.returncode != 0:
        print(f"Error getting commit hash: {hash_result.stderr}")
        return {}

    commit_hash = hash_result.stdout.strip()

    # Get commit message
    message_result = subprocess.run(
        ["git", "log", "-1", "--pretty=%B"],
        capture_output=True,
        text=True
    )

    if message_result.returncode != 0:
        print(f"Error getting commit message: {message_result.stderr}")
        return {}

    commit_message = message_result.stdout.strip()

    # Get branch name
    branch_result = subprocess.run(
        ["git", "rev-parse", "--abbrev-ref", "HEAD"],
        capture_output=True,
        text=True
    )

    if branch_result.returncode != 0:
        print(f"Error getting branch name: {branch_result.stderr}")
        return {}

    branch_name = branch_result.stdout.strip()

    # Get author
    author_result = subprocess.run(
        ["git", "log", "-1", "--pretty=%an <%ae>"],
        capture_output=True,
        text=True
    )

    if author_result.returncode != 0:
        print(f"Error getting author: {author_result.stderr}")
        return {}

    author = author_result.stdout.strip()

    # Get changed files
    files_result = subprocess.run(
        ["git", "diff-tree", "--no-commit-id", "--name-only", "-r", "HEAD"],
        capture_output=True,
        text=True
    )

    if files_result.returncode != 0:
        print(f"Error getting changed files: {files_result.stderr}")
        return {}

    changed_files = [f for f in files_result.stdout.strip().split("\n") if f]

    # Extract injection tags from changed files
    injection_tags = set()
    for file_path in changed_files:
        if not os.path.exists(file_path):
            continue

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                file_tags = extract_injection_tags(content)
                injection_tags.update(file_tags)
        except Exception:
            # Skip files that can't be read
            continue

    return {
        "hash": commit_hash,
        "message": commit_message,
        "branch": branch_name,
        "author": author,
        "timestamp": datetime.now().isoformat(),
        "changed_files": changed_files,
        "injection_tags": list(injection_tags)
    }

def get_recent_telemetry(since_timestamp: Optional[str] = None) -> List[Dict[str, Any]]:
    """Get recent telemetry entries."""
    if not os.path.exists(TELEMETRY_FILE):
        return []

    entries = []

    with open(TELEMETRY_FILE, "r", encoding="utf-8") as f:
        for line in f:
            try:
                entry = json.loads(line.strip())

                if since_timestamp:
                    if entry.get("timestamp", "") >= since_timestamp:
                        entries.append(entry)
                else:
                    entries.append(entry)
            except json.JSONDecodeError:
                continue

    return entries

def get_last_commit_timestamp() -> Optional[str]:
    """Get the timestamp of the last commit from the commit log."""
    if not os.path.exists(COMMIT_LOG_FILE):
        return None

    try:
        with open(COMMIT_LOG_FILE, "r", encoding="utf-8") as f:
            for line in f:
                try:
                    entry = json.loads(line.strip())
                    return entry.get("timestamp")
                except json.JSONDecodeError:
                    continue
    except Exception:
        return None

    return None

def update_telemetry_with_branch(branch: str) -> None:
    """Update recent telemetry entries with branch information."""
    if not os.path.exists(TELEMETRY_FILE):
        return

    # Get the timestamp of the last commit
    last_commit_timestamp = get_last_commit_timestamp()

    # Get recent telemetry entries
    recent_entries = get_recent_telemetry(last_commit_timestamp)

    if not recent_entries:
        return

    # Create a temporary file
    temp_file = f"{TELEMETRY_FILE}.tmp"

    # Read all entries
    all_entries = []
    with open(TELEMETRY_FILE, "r", encoding="utf-8") as f:
        for line in f:
            try:
                entry = json.loads(line.strip())
                all_entries.append(entry)
            except json.JSONDecodeError:
                all_entries.append(line.strip())

    # Update recent entries with branch information
    for entry in all_entries:
        if isinstance(entry, dict):
            entry_timestamp = entry.get("timestamp", "")

            if last_commit_timestamp and entry_timestamp >= last_commit_timestamp:
                # Add branch information
                if "tags" not in entry:
                    entry["tags"] = []

                # Add branch tag if not already present
                branch_tag = f"branch:{branch}"
                if branch_tag not in entry["tags"]:
                    entry["tags"].append(branch_tag)

    # Write updated entries back to the file
    with open(temp_file, "w", encoding="utf-8") as f:
        for entry in all_entries:
            if isinstance(entry, dict):
                f.write(json.dumps(entry) + "\n")
            else:
                f.write(entry + "\n")

    # Replace the original file
    os.replace(temp_file, TELEMETRY_FILE)

def sync_telemetry_to_remote(commit_info: Dict[str, Any], remote_url: str) -> bool:
    """
    Sync telemetry metadata to a remote server.

    Args:
        commit_info: Commit information
        remote_url: URL of the remote server

    Returns:
        True if successful, False otherwise
    """
    try:
        # Get recent telemetry entries
        last_commit_timestamp = get_last_commit_timestamp()
        recent_entries = get_recent_telemetry(last_commit_timestamp)

        # Prepare payload
        payload = {
            "commit": commit_info,
            "telemetry": recent_entries
        }

        # Send to remote server
        response = requests.post(
            remote_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=5
        )

        if response.status_code == 200:
            print(f"Successfully synced telemetry to {remote_url}")
            return True
        else:
            print(f"Failed to sync telemetry to {remote_url}: {response.status_code}")
            return False

    except Exception as e:
        print(f"Error syncing telemetry to remote: {str(e)}")
        return False

def log_commit(commit_info: Dict[str, Any]) -> None:
    """Log commit information to the commit log file."""
    ensure_telemetry_dir()

    with open(COMMIT_LOG_FILE, "a", encoding="utf-8") as f:
        f.write(json.dumps(commit_info) + "\n")

def main():
    print("Running CodeCrusher post-commit hook...")

    # Load hook configuration
    config = load_hook_config()
    remote_sync = config.get("remote_sync", False)
    remote_sync_url = config.get("remote_sync_url", "")

    # Get commit information
    commit_info = get_commit_info()

    if not commit_info:
        print("Failed to get commit information.")
        return 1

    # Log commit information
    log_commit(commit_info)

    # Update telemetry with branch information
    branch = commit_info.get("branch", "unknown")
    update_telemetry_with_branch(branch)

    print(f"Updated telemetry with branch information: {branch}")

    # Sync telemetry to remote if enabled
    if remote_sync and remote_sync_url:
        print(f"Syncing telemetry to remote: {remote_sync_url}")
        if sync_telemetry_to_remote(commit_info, remote_sync_url):
            print("Telemetry sync successful.")
        else:
            print("Telemetry sync failed.")

    # Print injection tags if any
    injection_tags = commit_info.get("injection_tags", [])
    if injection_tags:
        print(f"Found {len(injection_tags)} injection tags in committed files:")
        for tag in injection_tags:
            print(f"  - {tag}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
