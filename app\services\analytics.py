"""
Analytics Service for CodeCrusher

This module provides analytics functionality for tracking model performance,
user feedback, and system metrics across different AI models and scenarios.

Features:
- Feedback analytics recording and aggregation
- Model performance tracking and comparison
- Real-time analytics updates
- JSON-based storage with optional database integration
- Performance metrics calculation and insights
"""

import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

from models.feedback import Feedback, ModelPerformanceStats, calculate_model_stats, RatingType, ModelType

logger = logging.getLogger(__name__)

class AnalyticsService:
    """
    Analytics service for tracking and analyzing model performance.
    
    This service handles feedback recording, analytics calculation,
    and performance insights generation.
    """
    
    def __init__(self, storage_path: str = "logs/analytics"):
        """
        Initialize analytics service.
        
        Args:
            storage_path: Path for storing analytics data
        """
        self.storage_path = Path(storage_path)
        self.feedback_file = self.storage_path / "feedback_analytics.jsonl"
        self.stats_file = self.storage_path / "model_stats.json"
        self.daily_stats_file = self.storage_path / "daily_stats.json"
        
        # Ensure storage directory exists
        self.storage_path.mkdir(parents=True, exist_ok=True)
    
    def record_feedback_analytics(self, data: Dict[str, Any]) -> str:
        """
        Record feedback analytics data.
        
        Args:
            data: Feedback data dictionary
            
        Returns:
            Feedback ID for tracking
        """
        try:
            # Create Feedback object with validation
            feedback = Feedback.from_dict(data)
            
            # Generate feedback ID if not provided
            if not feedback.feedback_id:
                feedback.feedback_id = f"analytics_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(data)) % 10000:04d}"
            
            # Record to JSONL file
            with open(self.feedback_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(feedback.to_dict(), ensure_ascii=False) + "\n")
            
            # Update real-time stats
            self._update_model_stats()
            
            logger.info(f"Recorded feedback analytics: {feedback.feedback_id} - {feedback.model} - {feedback.rating}")
            return feedback.feedback_id
            
        except Exception as e:
            logger.error(f"Failed to record feedback analytics: {e}")
            raise
    
    def get_all_feedback(self, limit: Optional[int] = None) -> List[Feedback]:
        """
        Get all feedback records.
        
        Args:
            limit: Maximum number of records to return
            
        Returns:
            List of Feedback objects
        """
        feedback_list = []
        
        if not self.feedback_file.exists():
            return feedback_list
        
        try:
            with open(self.feedback_file, "r", encoding="utf-8") as f:
                lines = f.readlines()
                
                # Apply limit if specified
                if limit:
                    lines = lines[-limit:]
                
                for line in lines:
                    if line.strip():
                        try:
                            data = json.loads(line)
                            feedback = Feedback.from_dict(data)
                            feedback_list.append(feedback)
                        except (json.JSONDecodeError, ValueError) as e:
                            logger.warning(f"Failed to parse feedback line: {e}")
                            continue
            
            return feedback_list
            
        except Exception as e:
            logger.error(f"Failed to get feedback records: {e}")
            return []
    
    def get_model_stats(self, days: int = 30) -> Dict[str, ModelPerformanceStats]:
        """
        Get model performance statistics.
        
        Args:
            days: Number of days to include in analysis
            
        Returns:
            Dictionary of model statistics
        """
        try:
            # Get feedback from specified time period
            cutoff_date = datetime.now() - timedelta(days=days)
            all_feedback = self.get_all_feedback()
            
            # Filter by date
            recent_feedback = [
                f for f in all_feedback 
                if f.timestamp >= cutoff_date
            ]
            
            # Calculate stats
            model_stats = calculate_model_stats(recent_feedback)
            
            # Add time period info
            for stats in model_stats.values():
                stats.period_start = cutoff_date
                stats.period_end = datetime.now()
            
            return model_stats
            
        except Exception as e:
            logger.error(f"Failed to get model stats: {e}")
            return {}
    
    def get_model_leaderboard(self, metric: str = "satisfaction_rate") -> List[Dict[str, Any]]:
        """
        Get model leaderboard sorted by specified metric.
        
        Args:
            metric: Metric to sort by (satisfaction_rate, avg_rating, success_rate, etc.)
            
        Returns:
            List of model stats sorted by metric
        """
        try:
            model_stats = self.get_model_stats()
            
            # Convert to list and sort
            leaderboard = []
            for model_name, stats in model_stats.items():
                stats_dict = stats.to_dict()
                stats_dict["rank"] = 0  # Will be set after sorting
                leaderboard.append(stats_dict)
            
            # Sort by metric (descending)
            if metric in ["satisfaction_rate", "avg_rating", "success_rate", "retry_success_rate", "auto_fix_success_rate"]:
                leaderboard.sort(key=lambda x: x.get(metric, 0), reverse=True)
            elif metric in ["avg_latency_ms"]:
                leaderboard.sort(key=lambda x: x.get(metric, float('inf')))
            else:
                leaderboard.sort(key=lambda x: x.get(metric, 0), reverse=True)
            
            # Add ranks
            for i, stats in enumerate(leaderboard, 1):
                stats["rank"] = i
            
            return leaderboard
            
        except Exception as e:
            logger.error(f"Failed to get model leaderboard: {e}")
            return []
    
    def get_analytics_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive analytics summary.
        
        Returns:
            Dictionary with analytics summary
        """
        try:
            all_feedback = self.get_all_feedback()
            model_stats = self.get_model_stats()
            
            # Overall statistics
            total_feedback = len(all_feedback)
            positive_feedback = sum(1 for f in all_feedback if f.is_positive_feedback())
            negative_feedback = sum(1 for f in all_feedback if f.is_negative_feedback())
            
            overall_satisfaction = (positive_feedback / total_feedback * 100) if total_feedback > 0 else 0
            
            # Model performance insights
            best_model = None
            worst_model = None
            
            if model_stats:
                leaderboard = self.get_model_leaderboard("satisfaction_rate")
                if leaderboard:
                    best_model = leaderboard[0]["model"]
                    worst_model = leaderboard[-1]["model"]
            
            # Recent trends (last 7 days vs previous 7 days)
            now = datetime.now()
            recent_feedback = [f for f in all_feedback if f.timestamp >= now - timedelta(days=7)]
            previous_feedback = [f for f in all_feedback if now - timedelta(days=14) <= f.timestamp < now - timedelta(days=7)]
            
            recent_satisfaction = (sum(1 for f in recent_feedback if f.is_positive_feedback()) / len(recent_feedback) * 100) if recent_feedback else 0
            previous_satisfaction = (sum(1 for f in previous_feedback if f.is_positive_feedback()) / len(previous_feedback) * 100) if previous_feedback else 0
            
            satisfaction_trend = recent_satisfaction - previous_satisfaction
            
            return {
                "total_feedback": total_feedback,
                "overall_satisfaction_rate": round(overall_satisfaction, 1),
                "positive_feedback_count": positive_feedback,
                "negative_feedback_count": negative_feedback,
                "best_performing_model": best_model,
                "worst_performing_model": worst_model,
                "satisfaction_trend_7d": round(satisfaction_trend, 1),
                "total_models_tracked": len(model_stats),
                "recent_feedback_count": len(recent_feedback),
                "analytics_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get analytics summary: {e}")
            return {}
    
    def _update_model_stats(self):
        """Update cached model statistics."""
        try:
            model_stats = self.get_model_stats()
            
            # Convert to serializable format
            stats_dict = {}
            for model_name, stats in model_stats.items():
                stats_dict[model_name] = stats.to_dict()
            
            # Save to file
            with open(self.stats_file, "w", encoding="utf-8") as f:
                json.dump(stats_dict, f, indent=2, ensure_ascii=False)
            
            logger.debug("Updated cached model statistics")
            
        except Exception as e:
            logger.error(f"Failed to update model stats cache: {e}")
    
    def get_model_comparison(self, model1: str, model2: str) -> Dict[str, Any]:
        """
        Compare performance between two models.
        
        Args:
            model1: First model name
            model2: Second model name
            
        Returns:
            Comparison results
        """
        try:
            model_stats = self.get_model_stats()
            
            stats1 = model_stats.get(model1)
            stats2 = model_stats.get(model2)
            
            if not stats1 or not stats2:
                return {"error": "One or both models not found"}
            
            comparison = {
                "model1": model1,
                "model2": model2,
                "satisfaction_rate_diff": stats1.get_satisfaction_rate() - stats2.get_satisfaction_rate(),
                "avg_rating_diff": stats1.avg_rating - stats2.avg_rating,
                "latency_diff_ms": stats1.avg_latency_ms - stats2.avg_latency_ms,
                "retry_success_diff": stats1.retry_success_rate - stats2.retry_success_rate,
                "winner": model1 if stats1.get_satisfaction_rate() > stats2.get_satisfaction_rate() else model2,
                "comparison_timestamp": datetime.now().isoformat()
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Failed to compare models: {e}")
            return {"error": str(e)}
    
    def record_injection_performance(self, injection_data: Dict[str, Any]) -> str:
        """
        Record performance data from code injection.
        
        Args:
            injection_data: Injection performance data
            
        Returns:
            Analytics record ID
        """
        try:
            # Extract performance metrics
            feedback_data = {
                "feedback_id": f"injection_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(str(injection_data)) % 10000:04d}",
                "rating": "neutral",  # Default until user provides feedback
                "model": injection_data.get("model", "unknown"),
                "intent": injection_data.get("intent", "auto"),
                "prompt": injection_data.get("prompt"),
                "file_path": injection_data.get("file_path"),
                "injection_id": injection_data.get("injection_id"),
                "latency_ms": injection_data.get("latency_ms", 0),
                "token_count": injection_data.get("token_count"),
                "diff": injection_data.get("diff"),
                "before_code": injection_data.get("before_code"),
                "after_code": injection_data.get("after_code"),
                "prompt_quality_score": injection_data.get("prompt_quality_score"),
                "confidence_score": injection_data.get("confidence_score"),
                "timestamp": datetime.now(),
                "unix_timestamp": int(datetime.now().timestamp())
            }
            
            return self.record_feedback_analytics(feedback_data)
            
        except Exception as e:
            logger.error(f"Failed to record injection performance: {e}")
            raise

# Global analytics service instance
analytics_service = AnalyticsService()

# Convenience functions for easy import
def record_feedback_analytics(data: Dict[str, Any]) -> str:
    """Record feedback analytics data."""
    return analytics_service.record_feedback_analytics(data)

def get_model_stats(days: int = 30) -> Dict[str, ModelPerformanceStats]:
    """Get model performance statistics."""
    return analytics_service.get_model_stats(days)

def get_model_leaderboard(metric: str = "satisfaction_rate") -> List[Dict[str, Any]]:
    """Get model leaderboard."""
    return analytics_service.get_model_leaderboard(metric)

def get_analytics_summary() -> Dict[str, Any]:
    """Get analytics summary."""
    return analytics_service.get_analytics_summary()

def record_injection_performance(injection_data: Dict[str, Any]) -> str:
    """Record injection performance data."""
    return analytics_service.record_injection_performance(injection_data)
