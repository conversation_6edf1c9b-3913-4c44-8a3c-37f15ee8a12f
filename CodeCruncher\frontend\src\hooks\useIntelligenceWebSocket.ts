import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface IntelligenceMessage {
  type: 'welcome' | 'update' | 'intelligence_update' | 'new_feedback';
  message?: string;
  data?: any;
  timestamp?: string;
  entry?: any;
}

interface UseIntelligenceWebSocketProps {
  url: string;
  onMessage?: (message: IntelligenceMessage) => void;
  onStatusChange?: (status: 'connecting' | 'connected' | 'disconnected' | 'error') => void;
  autoReconnect?: boolean;
  maxRetries?: number;
}

export function useIntelligenceWebSocket({
  url,
  onMessage,
  onStatusChange,
  autoReconnect = true,
  maxRetries = 5
}: UseIntelligenceWebSocketProps) {
  const { getAuthHeaders, isAuthenticated } = useAuth();
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [lastMessage, setLastMessage] = useState<IntelligenceMessage | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  const wsRef = useRef<WebSocket | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateStatus = useCallback((status: typeof connectionStatus) => {
    setConnectionStatus(status);
    onStatusChange?.(status);
  }, [onStatusChange]);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    updateStatus('connecting');

    try {
      // Convert relative URLs to absolute WebSocket URLs using window.location.host
      let wsUrl = url.startsWith('/')
        ? `ws://${window.location.host}${url}`
        : url;

      // Add authentication token as query parameter if authenticated
      if (isAuthenticated) {
        const authHeaders = getAuthHeaders();
        const token = authHeaders.Authorization?.replace('Bearer ', '');
        if (token) {
          const separator = wsUrl.includes('?') ? '&' : '?';
          wsUrl += `${separator}token=${encodeURIComponent(token)}`;
        }
      }

      console.log('🔗 Connecting to WebSocket:', wsUrl.replace(/token=[^&]+/, 'token=***'));
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('🧠 Intelligence WebSocket connected');
        updateStatus('connected');
        setRetryCount(0);
      };

      ws.onmessage = (event) => {
        try {
          const message: IntelligenceMessage = JSON.parse(event.data);
          setLastMessage(message);
          onMessage?.(message);

          console.log('📊 Intelligence message received:', message.type);
        } catch (error) {
          console.error('Failed to parse intelligence message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('🧠 Intelligence WebSocket closed:', event.code, event.reason);
        updateStatus('disconnected');

        // Auto-reconnect if enabled and not manually closed
        if (autoReconnect && event.code !== 1000 && retryCount < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Exponential backoff, max 30s
          console.log(`🔄 Reconnecting intelligence WebSocket in ${delay}ms (attempt ${retryCount + 1}/${maxRetries})`);

          reconnectTimeoutRef.current = setTimeout(() => {
            setRetryCount(prev => prev + 1);
            connect();
          }, delay);
        }
      };

      ws.onerror = (error) => {
        console.error('🧠 Intelligence WebSocket error:', error);
        updateStatus('error');
      };

    } catch (error) {
      console.error('Failed to create intelligence WebSocket:', error);
      updateStatus('error');
    }
  }, [url, onMessage, updateStatus, autoReconnect, maxRetries, retryCount]);

  const disconnect = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    updateStatus('disconnected');
    setRetryCount(0);
  }, [updateStatus]);

  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      return true;
    }
    return false;
  }, []);

  const retry = useCallback(() => {
    disconnect();
    setTimeout(connect, 1000);
  }, [connect, disconnect]);

  // Auto-connect on mount
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  return {
    connectionStatus,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    retry,
    retryCount,
    isConnected: connectionStatus === 'connected',
    isConnecting: connectionStatus === 'connecting',
    hasError: connectionStatus === 'error'
  };
}
