"""
Telemetry logging module for CodeCrusher.

This module provides functions for logging telemetry data to a JSONL file.
"""

import os
import json
import uuid
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
import threading

# Define the telemetry file path
TELEMETRY_DIR = os.path.expanduser("~/.codecrusher")
TELEMETRY_FILE = os.path.join(TELEMETRY_DIR, "telemetry.jsonl")

# Lock for thread-safe file operations
TELEMETRY_LOCK = threading.Lock()

def ensure_telemetry_dir():
    """
    Ensure the telemetry directory exists
    """
    os.makedirs(TELEMETRY_DIR, exist_ok=True)

def log_telemetry(
    model: str,
    provider: str,
    latency_ms: Optional[int] = None,
    tokens_in: Optional[int] = None,
    tokens_out: Optional[int] = None,
    cached: bool = False,
    fallback: bool = False,
    error: Optional[str] = None,
    tags: Optional[List[str]] = None,
    operation_id: Optional[str] = None,
    parent_id: Optional[str] = None,
    operation_type: str = "injection",
    no_telemetry: bool = False
) -> str:
    """
    Log telemetry data to the telemetry file.

    Args:
        model: The model used
        provider: The provider used
        latency_ms: The latency in milliseconds
        tokens_in: The number of input tokens
        tokens_out: The number of output tokens
        cached: Whether the result was cached
        fallback: Whether a fallback model was used
        error: Any error that occurred
        tags: Tags associated with the operation
        operation_id: Optional ID for the operation (generated if not provided)
        parent_id: Optional ID of the parent operation (for replays)
        operation_type: Type of operation (injection, replay, etc.)
        no_telemetry: Whether to skip telemetry logging

    Returns:
        The operation ID
    """
    # Skip telemetry logging if no_telemetry is True
    if no_telemetry:
        return operation_id or str(uuid.uuid4())

    # Generate an operation ID if not provided
    if not operation_id:
        operation_id = str(uuid.uuid4())

    # Create the telemetry entry
    telemetry_entry = {
        "id": operation_id,
        "timestamp": datetime.now().isoformat(),
        "model": model,
        "provider": provider,
        "latency_ms": latency_ms,
        "tokens_in": tokens_in,
        "tokens_out": tokens_out,
        "cached": cached,
        "fallback": fallback,
        "error": error,
        "tags": tags or [],
        "operation_type": operation_type
    }

    # Add parent_id if provided
    if parent_id:
        telemetry_entry["parent_id"] = parent_id

    # Ensure the telemetry directory exists
    ensure_telemetry_dir()

    # Write the telemetry entry to the file
    with TELEMETRY_LOCK:
        try:
            with open(TELEMETRY_FILE, "a", encoding="utf-8") as f:
                f.write(json.dumps(telemetry_entry) + "\n")
        except Exception as e:
            print(f"Error writing telemetry: {e}")

    return operation_id

def count_tokens(text: str) -> int:
    """
    Count the number of tokens in a text.
    This is a simple approximation - in a real implementation, 
    you would use the tokenizer from the specific model.

    Args:
        text: The text to count tokens for

    Returns:
        The approximate number of tokens
    """
    if not text:
        return 0
    
    # Simple approximation: 4 characters per token
    # This is a rough estimate and will vary by model
    return len(text) // 4

def log_model_telemetry(
    model: str,
    provider: str,
    prompt: str,
    output: str,
    start_time: float,
    cached: bool = False,
    fallback: bool = False,
    error: Optional[str] = None,
    tags: Optional[List[str]] = None,
    operation_id: Optional[str] = None,
    parent_id: Optional[str] = None,
    operation_type: str = "injection",
    no_telemetry: bool = False
) -> str:
    """
    Log telemetry data for a model call.

    Args:
        model: The model used
        provider: The provider used
        prompt: The prompt text
        output: The model output
        start_time: The start time of the operation (time.time())
        cached: Whether the result was cached
        fallback: Whether a fallback model was used
        error: Any error that occurred
        tags: Tags associated with the operation
        operation_id: Optional ID for the operation (generated if not provided)
        parent_id: Optional ID of the parent operation (for replays)
        operation_type: Type of operation (injection, replay, etc.)
        no_telemetry: Whether to skip telemetry logging

    Returns:
        The operation ID
    """
    # Skip telemetry logging if no_telemetry is True
    if no_telemetry:
        return operation_id or str(uuid.uuid4())

    # Calculate latency
    end_time = time.time()
    latency_ms = int((end_time - start_time) * 1000)

    # Count tokens
    tokens_in = count_tokens(prompt)
    tokens_out = count_tokens(output)

    # Log telemetry
    return log_telemetry(
        model=model,
        provider=provider,
        latency_ms=latency_ms,
        tokens_in=tokens_in,
        tokens_out=tokens_out,
        cached=cached,
        fallback=fallback,
        error=error,
        tags=tags,
        operation_id=operation_id,
        parent_id=parent_id,
        operation_type=operation_type,
        no_telemetry=no_telemetry
    )

def get_telemetry_entries(
    limit: int = 100,
    filter_tags: Optional[List[str]] = None,
    filter_model: Optional[str] = None,
    filter_provider: Optional[str] = None,
    filter_operation_type: Optional[str] = None,
    filter_cached: Optional[bool] = None,
    filter_fallback: Optional[bool] = None,
    filter_error: Optional[bool] = None
) -> List[Dict[str, Any]]:
    """
    Get telemetry entries from the telemetry file.

    Args:
        limit: Maximum number of entries to return
        filter_tags: Filter by tags (all tags must be present)
        filter_model: Filter by model
        filter_provider: Filter by provider
        filter_operation_type: Filter by operation type
        filter_cached: Filter by cached status
        filter_fallback: Filter by fallback status
        filter_error: Filter by error status (True = has error, False = no error)

    Returns:
        List of telemetry entries
    """
    # Ensure the telemetry directory exists
    ensure_telemetry_dir()

    # Check if the telemetry file exists
    if not os.path.exists(TELEMETRY_FILE):
        return []

    entries = []
    with TELEMETRY_LOCK:
        try:
            with open(TELEMETRY_FILE, "r", encoding="utf-8") as f:
                for line in f:
                    if line.strip():
                        try:
                            entry = json.loads(line)
                            
                            # Apply filters
                            if filter_tags and not all(tag in entry.get("tags", []) for tag in filter_tags):
                                continue
                            if filter_model and entry.get("model") != filter_model:
                                continue
                            if filter_provider and entry.get("provider") != filter_provider:
                                continue
                            if filter_operation_type and entry.get("operation_type") != filter_operation_type:
                                continue
                            if filter_cached is not None and entry.get("cached") != filter_cached:
                                continue
                            if filter_fallback is not None and entry.get("fallback") != filter_fallback:
                                continue
                            if filter_error is not None:
                                has_error = entry.get("error") is not None and entry.get("error") != ""
                                if has_error != filter_error:
                                    continue
                            
                            entries.append(entry)
                            
                            # Check limit
                            if len(entries) >= limit:
                                break
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            print(f"Error reading telemetry: {e}")

    # Sort by timestamp (newest first)
    entries.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
    
    return entries[:limit]
