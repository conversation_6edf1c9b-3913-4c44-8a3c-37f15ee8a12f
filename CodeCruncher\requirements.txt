# CodeCrusher Core Dependencies

# CLI framework and rich console output
click>=8.0.0
rich>=13.0.0
halo>=0.0.31
colorama>=0.4.4

# HTTP requests for AI API calls
requests>=2.28.0
httpx>=0.24.0
aiohttp>=3.8.0

# AI model providers
groq>=0.4.0

# Data validation and settings
pydantic>=2.5.0

# Database for intelligence storage
sqlalchemy>=2.0.0
aiosqlite>=0.19.0

# Configuration and environment
python-dotenv>=0.20.0
pyyaml>=6.0.0

# Template engine for code generation
jinja2>=3.1.0

# Web API Dependencies
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
aiofiles>=23.2.1

# Development and Testing
pytest>=7.4.3
pytest-asyncio>=0.21.1
