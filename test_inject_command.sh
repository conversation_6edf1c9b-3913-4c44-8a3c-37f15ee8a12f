#!/bin/bash
# Test script for the inject run command

# Create a test file
echo "# Test file for CodeCrusher inject command

def hello_world():
    print('Hello, World!')

def factorial(n):
    if n == 0:
        return 1
    else:
        return n * factorial(n - 1)
" > test_file.py

echo "=== Testing inject run command with verbose flag ==="
python simple_inject.py --input test_file.py --prompt-text "Add type hints" --verbose

echo ""
echo "=== Testing inject run command with output file ==="
python simple_inject.py --input test_file.py --prompt-text "Add docstrings" --output test_output.py

echo ""
echo "=== Testing inject run command with non-existent file ==="
python simple_inject.py --input non_existent_file.py --prompt-text "Refactor this"

echo ""
echo "=== Testing inject run command with provider and model ==="
python simple_inject.py --input test_file.py --prompt-text "Optimize this code" --provider groq --model llama3 --cache

# Clean up
# rm test_file.py test_output.py
