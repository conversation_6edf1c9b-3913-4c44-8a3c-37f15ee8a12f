# Dashboard Guide

Learn how to use <PERSON><PERSON>rusher's web dashboard for real-time monitoring, team collaboration, and visual management of your AI code injection workflows.

## 🚀 Quick Start

### Launching the Dashboard

```bash
# Start the dashboard server
codecrusher dashboard --start

# Start on custom port
codecrusher dashboard --start --port 8080

# Start with team mode
codecrusher dashboard --start --team

# Access dashboard
# Open browser to: http://localhost:3002
```

### First-Time Setup

1. **Launch Dashboard**: `codecrusher dashboard --start`
2. **Open Browser**: Navigate to `http://localhost:3002`
3. **Create Account**: Sign up with email and password
4. **Configure Settings**: Set your preferences and API keys
5. **Start Monitoring**: Begin using CodeCrusher CLI to see real-time data

---

## 🎛️ Dashboard Overview

### Main Interface Components

#### 1. **Navigation Bar**
- **Home**: Overview and quick actions
- **Injections**: History and management of code injections
- **Analytics**: Performance metrics and insights
- **Models**: AI model configuration and status
- **Team**: Collaboration and team management
- **Settings**: Configuration and preferences

#### 2. **Real-Time Status Panel**
- **Active Injections**: Currently running AI operations
- **System Health**: API connectivity and performance
- **Queue Status**: Pending operations and processing time
- **Model Availability**: Real-time model status

#### 3. **Quick Actions**
- **New Injection**: Start AI code generation
- **Rate Recent**: Provide feedback on recent injections
- **Trigger Learning**: Apply intelligence improvements
- **Export Data**: Download logs and analytics

---

## 📊 Features Overview

### 🔍 **Real-Time Injection Monitoring**

Watch AI code generation as it happens:

- **Live Progress**: See injection progress in real-time
- **Model Selection**: View which models are being used
- **Quality Scoring**: Real-time quality assessment
- **Error Tracking**: Monitor failures and retries

### 📈 **Analytics Dashboard**

Comprehensive insights into your CodeCrusher usage:

- **Injection Statistics**: Success rates, timing, and frequency
- **Quality Trends**: Track improvement over time
- **Model Performance**: Compare different AI models
- **Learning Progress**: Monitor intelligence system improvements

### 🤖 **Model Management**

Configure and monitor AI models:

- **Model Status**: Real-time availability and performance
- **Usage Statistics**: Track model usage and costs
- **Fallback Configuration**: Set escalation preferences
- **Performance Metrics**: Response times and quality scores

### 👥 **Team Collaboration**

Work together with shared intelligence:

- **Team Workspaces**: Shared projects and configurations
- **Collaborative Learning**: Team-wide intelligence improvements
- **Activity Feeds**: See team member injections and ratings
- **Shared Settings**: Synchronized tuning parameters

---

## 🎯 Core Features

### Injection Management

#### **Injection History**
View and manage all your code injections:

```
Recent Injections
┌─────────────────┬──────────────┬─────────┬──────────┬─────────┐
│ File            │ Prompt       │ Model   │ Quality  │ Status  │
├─────────────────┼──────────────┼─────────┼──────────┼─────────┤
│ src/main.py     │ Add logging  │ llama3  │ 95/100   │ ✅ Done │
│ src/utils.py    │ Error handle │ mixtral │ 87/100   │ ✅ Done │
│ src/models.py   │ Add tests    │ gpt-4   │ 92/100   │ 🔄 Run  │
└─────────────────┴──────────────┴─────────┴──────────┴─────────┘
```

#### **Live Injection Viewer**
Watch code generation in real-time:

- **Progress Bar**: Visual progress indicator
- **Model Chain**: See fallback escalation
- **Code Preview**: Live preview of generated code
- **Quality Metrics**: Real-time quality assessment

### Analytics and Insights

#### **Performance Dashboard**
```
CodeCrusher Analytics
┌─────────────────────────────────────────────────────────────┐
│ 📊 This Week                                                │
│ • 47 Injections (+12% from last week)                      │
│ • 4.2/5 Average Rating (+0.3 improvement)                  │
│ • 89% Success Rate (+5% improvement)                       │
│ • 2.3s Average Response Time (-0.5s faster)                │
└─────────────────────────────────────────────────────────────┘
```

#### **Quality Trends**
- **Rating History**: Track quality improvements over time
- **Learning Impact**: See how feedback affects future injections
- **Model Comparison**: Compare performance across different models
- **Parameter Effectiveness**: Understand tuning impact

### Model Configuration

#### **Model Status Panel**
```
AI Models Status
┌─────────────────┬─────────┬──────────┬─────────────┬─────────┐
│ Model           │ Status  │ Latency  │ Success     │ Quality │
├─────────────────┼─────────┼──────────┼─────────────┼─────────┤
│ llama3-8b       │ 🟢 Up   │ 0.8s     │ 94%         │ 85/100  │
│ mixtral-8x7b    │ 🟢 Up   │ 1.2s     │ 96%         │ 91/100  │
│ llama3-70b      │ 🟢 Up   │ 2.1s     │ 98%         │ 95/100  │
│ gpt-4-turbo     │ 🟡 Slow │ 3.5s     │ 99%         │ 97/100  │
└─────────────────┴─────────┴──────────┴─────────────┴─────────┘
```

---

## 👥 Team Features

### Team Workspaces

#### **Creating a Team**
1. Navigate to **Team** section
2. Click **Create Team**
3. Enter team name and description
4. Invite team members by email
5. Configure team settings and permissions

#### **Team Dashboard**
- **Member Activity**: See team member injections and ratings
- **Shared Intelligence**: Team-wide learning improvements
- **Collaborative Settings**: Synchronized tuning parameters
- **Team Analytics**: Aggregate performance metrics

### Collaboration Features

#### **Shared Learning**
- **Team Feedback**: All team ratings contribute to learning
- **Synchronized Parameters**: Shared tuning configurations
- **Knowledge Base**: Team-specific prompt templates
- **Best Practices**: Shared successful injection patterns

#### **Activity Feeds**
```
Team Activity Feed
┌─────────────────────────────────────────────────────────────┐
│ 🔄 Alice injected error handling in auth.py (4⭐)          │
│ 📝 Bob rated validation logic in utils.py (5⭐)            │
│ 🧠 Team learning triggered - parameters updated            │
│ ✨ Charlie created new prompt template for testing         │
└─────────────────────────────────────────────────────────────┘
```

---

## ⚙️ Configuration

### Dashboard Settings

#### **General Settings**
- **Theme**: Light/Dark mode
- **Refresh Rate**: Real-time update frequency
- **Notifications**: Email and browser notifications
- **Language**: Interface language selection

#### **API Configuration**
- **Model Keys**: Configure AI provider API keys
- **Endpoints**: Custom API endpoints
- **Timeouts**: Request timeout settings
- **Rate Limits**: API usage limits

#### **Team Settings**
- **Permissions**: Member role management
- **Sharing**: Data sharing preferences
- **Notifications**: Team activity notifications
- **Backup**: Team data backup settings

### Integration Settings

#### **CLI Integration**
- **Auto-sync**: Automatic CLI data synchronization
- **Real-time Updates**: Live CLI operation monitoring
- **Logging Level**: Dashboard logging verbosity
- **Cache Settings**: Dashboard cache configuration

---

## 🔧 Advanced Features

### Custom Dashboards

#### **Widget Configuration**
Create custom dashboard layouts:

- **Injection Widgets**: Live injection monitoring
- **Analytics Widgets**: Custom performance charts
- **Model Widgets**: AI model status and metrics
- **Team Widgets**: Collaboration and activity feeds

#### **Dashboard Templates**
- **Developer Dashboard**: Focus on individual productivity
- **Team Lead Dashboard**: Team oversight and management
- **Analytics Dashboard**: Deep performance insights
- **Operations Dashboard**: System health and monitoring

### API Integration

#### **REST API Access**
```bash
# Get injection history
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3002/api/injections

# Get team analytics
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:3002/api/team/analytics

# Trigger learning
curl -X POST -H "Authorization: Bearer $TOKEN" \
  http://localhost:3002/api/learning/trigger
```

#### **WebSocket Integration**
Real-time data streaming for custom applications:

```javascript
// Connect to real-time feed
const ws = new WebSocket('ws://localhost:3002/ws/live');

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Live update:', data);
};
```

---

## 🚨 Troubleshooting

### Common Issues

#### **Dashboard Won't Start**
```bash
# Check if port is in use
netstat -an | grep 3002

# Start on different port
codecrusher dashboard --start --port 8080

# Check logs
codecrusher dashboard --logs
```

#### **Real-Time Updates Not Working**
- Check WebSocket connection in browser developer tools
- Verify firewall settings allow WebSocket connections
- Restart dashboard server
- Clear browser cache and cookies

#### **Team Features Not Available**
- Ensure you're logged in with team account
- Check team permissions and role
- Verify team workspace is properly configured
- Contact team administrator

#### **Slow Performance**
- Reduce refresh rate in settings
- Disable unnecessary widgets
- Clear dashboard cache
- Check system resources

### Debug Mode

```bash
# Start dashboard in debug mode
codecrusher dashboard --start --debug

# View detailed logs
codecrusher dashboard --logs --verbose

# Test API connectivity
codecrusher dashboard --test-apis
```

---

## 📱 Mobile Access

### Mobile-Responsive Interface
The dashboard is fully responsive and works on mobile devices:

- **Touch-Friendly**: Optimized for touch interactions
- **Responsive Layout**: Adapts to different screen sizes
- **Mobile Navigation**: Simplified navigation for mobile
- **Offline Support**: Basic functionality when offline

### Mobile Features
- **Push Notifications**: Injection completion alerts
- **Quick Actions**: Essential functions accessible on mobile
- **Simplified Views**: Mobile-optimized dashboard layouts
- **Touch Gestures**: Swipe and tap interactions

---

## 🔗 Integration with CLI

### Seamless CLI Integration

The dashboard automatically syncs with CLI operations:

- **Live Monitoring**: See CLI injections in real-time
- **Automatic Logging**: All CLI operations logged to dashboard
- **Shared Configuration**: Settings sync between CLI and dashboard
- **Unified Analytics**: Combined CLI and dashboard metrics

### CLI Commands for Dashboard

```bash
# Open dashboard in browser
codecrusher dashboard --open

# Sync CLI data to dashboard
codecrusher dashboard --sync

# Export dashboard data
codecrusher dashboard --export data.json

# Import dashboard configuration
codecrusher dashboard --import config.json
```

---

## 🔗 Next Steps

- **[Learn Advanced Usage](usage.md)** - Master CLI integration
- **[Configure Tuning](tuning.md)** - Optimize dashboard settings
- **[Troubleshooting](troubleshooting.md)** - Solve dashboard issues

---

**[← Tuning Guide](tuning.md)** | **[Next: Contributing Guide →](contributing.md)**
