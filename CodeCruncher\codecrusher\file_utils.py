"""
File utility functions for CodeCrusher.

This module provides utility functions for working with files in CodeCrusher.
"""

import os
from pathlib import Path
from typing import Tu<PERSON>, Dict, Optional

# Mapping of file extensions to comment syntax
COMMENT_SYNTAX = {
    # Single-line comment prefix, multi-line comment start, multi-line comment end
    # Programming languages
    'py': ('#', '"""', '"""'),
    'js': ('//', '/*', '*/'),
    'jsx': ('//', '/*', '*/'),
    'ts': ('//', '/*', '*/'),
    'tsx': ('//', '/*', '*/'),
    'java': ('//', '/*', '*/'),
    'c': ('//', '/*', '*/'),
    'cpp': ('//', '/*', '*/'),
    'cs': ('//', '/*', '*/'),
    'go': ('//', '/*', '*/'),
    'rs': ('//', '/*', '*/'),
    'rb': ('#', '=begin', '=end'),
    'php': ('//', '/*', '*/'),
    'swift': ('//', '/*', '*/'),
    'kt': ('//', '/*', '*/'),
    'scala': ('//', '/*', '*/'),
    'groovy': ('//', '/*', '*/'),
    'dart': ('//', '/*', '*/'),
    'r': ('#', "'''", "'''"),
    'sh': ('#', "'''", "'''"),
    'bash': ('#', "'''", "'''"),
    'zsh': ('#', "'''", "'''"),
    'ps1': ('#', '<#', '#>'),
    'lua': ('--', '--[[', ']]'),
    'sql': ('--', '/*', '*/'),
    'pl': ('#', '=pod', '=cut'),
    'pm': ('#', '=pod', '=cut'),
    'hs': ('--', '{-', '-}'),
    'elm': ('--', '{-', '-}'),
    'erl': ('%', '%%', '%%'),
    'ex': ('#', '@doc """', '"""'),
    'exs': ('#', '@doc """', '"""'),
    'clj': (';', '(comment', ')'),
    'lisp': (';', '#|', '|#'),
    'scm': (';', '#|', '|#'),
    'rkt': (';', '#|', '|#'),
    'ml': ('(*', '(*', '*)'),
    'mli': ('(*', '(*', '*)'),
    'fs': ('//', '(*', '*)'),
    'fsi': ('//', '(*', '*)'),
    'fsx': ('//', '(*', '*)'),
    'v': ('//', '/*', '*/'),
    'vhd': ('--', '/*', '*/'),
    'vhdl': ('--', '/*', '*/'),
    'tcl': ('#', 'if 0 {', '}'),
    'tk': ('#', 'if 0 {', '}'),
    'f': ('!', '!', '!'),
    'f90': ('!', '!', '!'),
    'f95': ('!', '!', '!'),
    'f03': ('!', '!', '!'),
    'f08': ('!', '!', '!'),
    'jl': ('#', '#=', '=#'),
    'cr': ('#', '=begin', '=end'),
    'nim': ('#', '#[', ']#'),
    'coffee': ('#', '###', '###'),
    
    # Markup and config languages
    'html': ('<!--', '<!--', '-->'),
    'htm': ('<!--', '<!--', '-->'),
    'xml': ('<!--', '<!--', '-->'),
    'svg': ('<!--', '<!--', '-->'),
    'css': ('/*', '/*', '*/'),
    'scss': ('//', '/*', '*/'),
    'sass': ('//', '/*', '*/'),
    'less': ('//', '/*', '*/'),
    'yaml': ('#', '# ---', '# ---'),
    'yml': ('#', '# ---', '# ---'),
    'json': (None, '/*', '*/'),  # JSON doesn't support comments officially
    'md': ('<!--', '<!--', '-->'),
    'markdown': ('<!--', '<!--', '-->'),
    'tex': ('%', '\\begin{comment}', '\\end{comment}'),
    'latex': ('%', '\\begin{comment}', '\\end{comment}'),
    'rst': ('..', '.. ', ''),
    'toml': ('#', '# ---', '# ---'),
    'ini': (';', '; ---', '; ---'),
    'cfg': (';', '; ---', '; ---'),
    'conf': ('#', '# ---', '# ---'),
    'properties': ('#', '# ---', '# ---'),
    'gitignore': ('#', '# ---', '# ---'),
    'dockerignore': ('#', '# ---', '# ---'),
    'editorconfig': (';', '; ---', '; ---'),
    
    # Default fallback
    'default': ('#', '/*', '*/')
}

def get_comment_syntax(file_path: str) -> Tuple[Optional[str], str, str]:
    """
    Get the comment syntax for a file based on its extension.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Tuple of (single-line comment prefix, multi-line comment start, multi-line comment end)
    """
    # Get the file extension
    _, ext = os.path.splitext(file_path)
    ext = ext.lstrip('.').lower()
    
    # Return the comment syntax for the extension, or the default if not found
    return COMMENT_SYNTAX.get(ext, COMMENT_SYNTAX['default'])

def is_code_file(file_path: str) -> bool:
    """
    Check if a file is a code file based on its extension.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if the file is a code file, False otherwise
    """
    # Get the file extension
    _, ext = os.path.splitext(file_path)
    ext = ext.lstrip('.').lower()
    
    # Check if the extension is in the COMMENT_SYNTAX dictionary
    return ext in COMMENT_SYNTAX

def format_annotation(tags: list, file_path: str, is_code: bool = True) -> str:
    """
    Format an annotation for a file based on its type.
    
    Args:
        tags: List of tags to include in the annotation
        file_path: Path to the file
        is_code: Whether the content is code or plain text
        
    Returns:
        Formatted annotation string
    """
    # Sort tags alphabetically
    sorted_tags = sorted(tags) if tags else []
    
    if not is_code:
        # For non-code content, use a plaintext header
        return f"[Injected by CodeCrusher | Tags: {' '.join(sorted_tags)}]\n\n"
    
    # Get the comment syntax for the file
    single_line, multi_line_start, multi_line_end = get_comment_syntax(file_path)
    
    # If no single-line comment syntax is available, use multi-line
    if not single_line:
        return f"{multi_line_start}\nInjected by CodeCrusher\nTags: {' '.join(sorted_tags)}\n{multi_line_end}\n\n"
    
    # Use single-line comments
    return f"{single_line} Injected by CodeCrusher\n{single_line} Tags: {' '.join(sorted_tags)}\n\n"
