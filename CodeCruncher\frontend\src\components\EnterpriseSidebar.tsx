import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  BarChart3,
  FolderOpen,
  Settings,
  Users,
  Code,
  GitBranch,
  Database,
  Shield,
  Zap,
  ChevronLeft,
  ChevronRight,
  Activity,
  FileText,
  Workflow,
  Plug
} from 'lucide-react';

interface EnterpriseSidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

export const EnterpriseSidebar: React.FC<EnterpriseSidebarProps> = ({ isOpen, onToggle }) => {
  const navigate = useNavigate();

  const navigationItems = [
    { icon: FolderOpen, label: 'Dashboard', active: false, onClick: () => navigate('/dashboard') },
    { icon: Users, label: 'Team Workspace', active: false, onClick: () => navigate('/teams') },
    { icon: Code, label: 'Backend', active: false, onClick: () => navigate('/backend') },
    { icon: GitBranch, label: 'Status', active: false, onClick: () => navigate('/status') },
    { icon: Database, label: 'Enhanced', active: false, onClick: () => navigate('/enhanced') },
    { icon: Shield, label: 'Clean View', active: false, onClick: () => navigate('/clean') },
    { icon: Activity, label: 'Streamlined', active: false, onClick: () => navigate('/streamlined') },
    { icon: FileText, label: 'Log Demo', active: false, onClick: () => navigate('/demo') },
    { icon: Workflow, label: 'Style Test', active: false, onClick: () => navigate('/styletest') },
    { icon: Plug, label: 'Stable View', active: false, onClick: () => navigate('/stable') },
    { icon: Settings, label: 'Enterprise', active: false, onClick: () => navigate('/enterprise') },
  ];

  return (
    <>
      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full bg-gradient-to-b from-slate-900 via-gray-900 to-slate-900 border-r border-gray-700/50 backdrop-blur-xl transition-all duration-300 z-40 ${
        isOpen ? 'w-64' : 'w-16'
      }`}>
        {/* Premium Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-transparent to-purple-900/10"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-gray-900/50 to-transparent"></div>

        {/* Header */}
        <div className="relative p-4 border-b border-gray-700/50">
          <div className="flex items-center justify-between">
            {isOpen && (
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-br from-blue-600 to-purple-700 rounded-lg">
                  <Zap className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-white font-bold text-sm">CodeCrusher</h3>
                  <p className="text-gray-400 text-xs">Enterprise</p>
                </div>
              </div>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggle}
              className="p-2 hover:bg-gray-800/50 text-gray-400 hover:text-white transition-all duration-200"
            >
              {isOpen ? (
                <ChevronLeft className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="relative p-2 space-y-1">
          {navigationItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                onClick={item.onClick}
                className={`w-full justify-start p-3 rounded-lg transition-all duration-300 group ${
                  item.active
                    ? 'bg-gradient-to-r from-blue-600/20 to-purple-600/20 border border-blue-500/30 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <Icon className={`h-4 w-4 ${item.active ? 'text-blue-400' : 'group-hover:text-blue-400'} transition-colors duration-300`} />
                {isOpen && (
                  <span className="ml-3 font-medium text-sm">{item.label}</span>
                )}
              </Button>
            );
          })}
        </nav>

        {/* Bottom Section */}
        {isOpen && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="p-3 bg-gradient-to-r from-emerald-900/20 to-green-900/20 border border-emerald-500/30 rounded-lg">
              <div className="flex items-center space-x-2 text-emerald-400">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span className="text-xs font-medium">All Systems Online</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-30 lg:hidden"
          onClick={onToggle}
        />
      )}
    </>
  );
};
