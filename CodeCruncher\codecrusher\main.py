"""
CodeCrusher main module - AI-powered code injector
"""

import argparse
import logging
import os
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from halo import Halo
from dotenv import load_dotenv
from codecrusher.injector import inject_code
from codecrusher.logger import setup_logging
# Cache manager imports removed - now handled by parallel injection engine

# Import feedback system
try:
    from core.feedback.prompt_logger import PromptLogger
    from core.feedback.feedback_engine import FeedbackEngine
    from core.feedback.auto_refine import AutoRefineEngine
    FEEDBACK_AVAILABLE = True
except ImportError:
    FEEDBACK_AVAILABLE = False

# Load environment variables from .env file
load_dotenv()

# Initialize rich console
console = Console()


def main():
    """
    Main entry point for the CodeCrusher CLI
    """
    parser = argparse.ArgumentParser(
        description="🧠 CodeCrusher: Enterprise-grade AI code injector",
        epilog="""
Examples:
  codecrusher --source app.py --prompt-text "Add error handling" --preview
  codecrusher --source ./src --recursive --ext py,js --prompt-text "Add logging" --apply
  codecrusher --source ./api --recursive --prompt-text "Optimize performance" --auto-model-routing --apply
  codecrusher --source main.py --prompt-text "Add tests" --force --tag refactor-v2 --apply
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    # Required arguments (but not for subcommands)
    parser.add_argument(
        '--source',
        help='Path to source file or folder'
    )
    parser.add_argument(
        '--prompt-text',
        help='Prompt text to inject with AI'
    )

    # Modes (mutually exclusive)
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--apply',
        action='store_true',
        help='Apply changes to source file(s)'
    )
    mode_group.add_argument(
        '--preview',
        action='store_true',
        help='Preview only, don\'t write changes'
    )

    # Model selection
    parser.add_argument(
        '--model',
        help='Specify model name (e.g., mistral, mixtral, llama3)'
    )
    parser.add_argument(
        '--auto-model-routing',
        action='store_true',
        help='Use best model based on prompt'
    )

    # Control scanning
    parser.add_argument(
        '--recursive',
        action='store_true',
        help='Scan subfolders recursively'
    )
    parser.add_argument(
        '--ext',
        default='py',
        help='Filter by file extension(s), comma-separated (e.g., py,js,ts)'
    )

    # Caching and tagging
    parser.add_argument(
        '--tag',
        default='default',
        help='Tag to uniquely identify this injection version'
    )
    parser.add_argument(
        '--refresh-cache',
        action='store_true',
        help='Ignore cache and re-inject'
    )
    parser.add_argument(
        '--force',
        action='store_true',
        help='Auto-insert missing injection tags and inject even without existing tags'
    )

    parser.add_argument(
        '--inject-body',
        action='store_true',
        help='Auto-inject tags inside <body> tag when using --force (default: enabled)'
    )

    # Output
    parser.add_argument(
        '--summary',
        action='store_true',
        help='Print final injection summary'
    )

    # Feedback system
    parser.add_argument(
        '--enable-feedback',
        action='store_true',
        help='Enable feedback logging and auto-refinement'
    )
    parser.add_argument(
        '--rate',
        type=int,
        choices=[1, 2, 3, 4, 5],
        help='Rate the injection result (1-5 stars)'
    )
    parser.add_argument(
        '--feedback',
        help='Provide feedback text for improvement'
    )

    # Add subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Rate command
    rate_parser = subparsers.add_parser('rate', help='Rate the last injection')
    rate_parser.add_argument('--rating', type=int, choices=[1, 2, 3, 4, 5], help='Rating from 1-5 stars')
    rate_parser.add_argument('--feedback', help='Optional feedback text')
    rate_parser.add_argument('--log-id', type=int, help='Specific log ID to rate')
    rate_parser.add_argument('--list-recent', action='store_true', help='List recent injections')

    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show injection statistics')

    # History command
    history_parser = subparsers.add_parser('history', help='Show injection history')
    history_parser.add_argument('--limit', type=int, default=10, help='Number of entries to show')
    history_parser.add_argument('--file', help='Filter by filename')
    history_parser.add_argument('--tag', help='Filter by tag')

    # Tune command for manual shaping configuration
    tune_parser = subparsers.add_parser('tune', help='Configure manual prompt shaping settings')
    tune_parser.add_argument('--tone', choices=['neutral', 'friendly', 'assertive', 'formal'],
                           help='Set tone modifier for prompts')
    tune_parser.add_argument('--fallback-sensitivity', choices=['low', 'medium', 'high'],
                           help='Set fallback sensitivity threshold')
    tune_parser.add_argument('--shaping-strength', choices=['off', 'soft', 'smart', 'aggressive'],
                           help='Set prompt shaping aggressiveness')
    tune_parser.add_argument('--show', action='store_true', help='Show current configuration')
    tune_parser.add_argument('--reset', action='store_true', help='Reset to default configuration')

    args = parser.parse_args()

    # Handle subcommands
    if args.command == 'rate':
        from codecrusher.commands.rate import rate
        import click
        # Create a click context and invoke the command
        ctx = click.Context(rate)
        ctx.params = {
            'rating': getattr(args, 'rating', None),
            'feedback': getattr(args, 'feedback', None),
            'log_id': getattr(args, 'log_id', None),
            'list_recent': getattr(args, 'list_recent', False)
        }
        rate.invoke(ctx)
        return
    elif args.command == 'stats':
        from codecrusher.commands.rate import stats
        import click
        ctx = click.Context(stats)
        stats.invoke(ctx)
        return
    elif args.command == 'history':
        _show_history(args)
        return
    elif args.command == 'tune':
        _handle_tune_command(args)
        return

    # Validate required arguments for main injection functionality
    if not args.source:
        parser.error("--source is required for injection operations")
    if not args.prompt_text:
        parser.error("--prompt-text is required for injection operations")
    if not args.apply and not args.preview:
        parser.error("Either --apply or --preview is required for injection operations")

    # Process extensions argument (convert comma-separated string to list)
    if isinstance(args.ext, str):
        args.ext = [ext.strip() for ext in args.ext.split(',')]

    # Set AI mode to True by default (since we're always using AI)
    args.ai = True

    setup_logging()
    console.print(Panel(
        "[bold]CodeCrusher: Enterprise-grade AI code injector[/bold]\n"
        "Intelligent AI-powered code injection with parallel processing and caching.",
        title="[bold cyan]CodeCrusher[/bold cyan]",
        border_style="cyan"
    ))
    logging.info("Starting CodeCrusher...")

    # Initialize feedback system if available and enabled
    feedback_system = None
    if FEEDBACK_AVAILABLE and args.enable_feedback:
        try:
            prompt_logger = PromptLogger()
            feedback_engine = FeedbackEngine(prompt_logger)
            auto_refine_engine = AutoRefineEngine(prompt_logger, feedback_engine)
            feedback_system = {
                'logger': prompt_logger,
                'engine': feedback_engine,
                'refiner': auto_refine_engine
            }
            console.print("[green]✅ Feedback system enabled[/green]")
        except Exception as e:
            console.print(f"[yellow]⚠️ Feedback system initialization failed: {e}[/yellow]")
            feedback_system = None

    # Create a configuration table
    config_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
    config_table.add_row("[cyan]Source:[/cyan]", f"[yellow]{args.source}[/yellow]")
    config_table.add_row("[cyan]Prompt:[/cyan]", f"[yellow]{args.prompt_text}[/yellow]")

    # Display mode
    if args.apply:
        config_table.add_row("[cyan]Mode:[/cyan]", "[bold green]Apply[/bold green] (changes will be written)")
    else:
        config_table.add_row("[cyan]Mode:[/cyan]", "[bold yellow]Preview[/bold yellow] (dry-run only)")

    # Display model selection
    if args.auto_model_routing:
        config_table.add_row("[cyan]Model:[/cyan]", "[bold green]Auto-routing[/bold green] (best model selection)")
    elif args.model:
        config_table.add_row("[cyan]Model:[/cyan]", f"[bold green]{args.model}[/bold green]")
    else:
        config_table.add_row("[cyan]Model:[/cyan]", "[bold green]auto[/bold green]")

    # Display scanning configuration
    if args.recursive:
        config_table.add_row("[cyan]Scanning:[/cyan]", "[bold green]Recursive[/bold green]")
    else:
        config_table.add_row("[cyan]Scanning:[/cyan]", "[bold yellow]Single level[/bold yellow]")

    extensions_str = ', '.join(args.ext)
    config_table.add_row("[cyan]Extensions:[/cyan]", f"[bold]{extensions_str}[/bold]")

    # Display tag and cache configuration
    config_table.add_row("[cyan]Tag:[/cyan]", f"[bold]{args.tag}[/bold]")

    if args.refresh_cache:
        config_table.add_row("[cyan]Cache:[/cyan]", "[bold red]Bypassed[/bold red]")
    else:
        config_table.add_row("[cyan]Cache:[/cyan]", "[bold green]Enabled[/bold green]")

    if args.force:
        config_table.add_row("[cyan]Force Mode:[/cyan]", "[bold red]Enabled[/bold red] (auto-insert missing tags)")

    console.print(Panel(config_table, title="[bold]Configuration[/bold]", border_style="blue"))

    # Load tuning configuration
    tuning_config = None
    try:
        from tuning_config import get_config_manager
        tuning_manager = get_config_manager()
        tuning_config = tuning_manager.load_config()
        console.print("[green]✅ Loaded tuning configuration[/green]")
    except Exception as e:
        console.print(f"[yellow]⚠️ Could not load tuning config: {e}[/yellow]")

    # Enhance prompt using surgical feedback loop
    enhanced_prompt = args.prompt_text
    enhancement_metadata = {}

    if tuning_config:
        try:
            from prompt_enhancer import PromptEnhancer
            enhancer = PromptEnhancer()

            # Determine injection type from prompt
            injection_type = _detect_injection_type(args.prompt_text)

            enhanced_prompt, enhancement_metadata = enhancer.enhance_prompt(
                original_prompt=args.prompt_text,
                filename=args.source,
                tags=[args.tag] if args.tag else [],
                injection_type=injection_type
            )

            if enhanced_prompt != args.prompt_text:
                console.print(f"[cyan]🔧 Enhanced prompt using {enhancement_metadata['rules_applied']} learned rules[/cyan]")
                console.print(f"[dim]Confidence: {enhancement_metadata['enhancement_confidence']:.1%}[/dim]")
        except Exception as e:
            console.print(f"[yellow]⚠️ Prompt enhancement failed: {e}[/yellow]")

    # Call the main injection function
    console.print("\n[bold blue]Starting injection process...[/bold blue]")

    try:
        # Use spinner for AI calls
        with Halo(text="Processing files with AI...", spinner="dots"):
            result = inject_code(
                source_path=args.source,
                prompt_text=enhanced_prompt,  # Use enhanced prompt
                use_ai=args.ai,
                preview=args.preview,
                model=args.model,
                auto_model_routing=args.auto_model_routing,
                refresh_cache=args.refresh_cache,
                apply=args.apply,
                force=args.force,
                recursive=args.recursive,
                extensions=args.ext,
                tag=args.tag,
                inject_body=args.inject_body,
                tuning_config=tuning_config  # Pass tuning config
            )
    except Exception as e:
        console.print(Panel(
            f"[bold red]❌ Error during injection:[/bold red] {str(e)}",
            title="[bold red]Operation Failed[/bold red]",
            border_style="red"
        ))
        logging.error(f"Injection failed: {str(e)}")
        return

    # Display completion information
    if result.get("success", False):
        # Get statistics
        total_files = result.get("total_files", 0)
        successful_files = result.get("successful_files", 0)
        failed_files = result.get("failed_files", 0)
        cached_files = result.get("cached_files", 0)

        # Display completion message
        if args.apply:
            console.print(Panel(
                f"[bold green]Injection complete![/bold green]\n"
                f"[green]Files processed: {successful_files}/{total_files}[/green]",
                title="[bold green]Operation Complete[/bold green]",
                border_style="green"
            ))
        else:
            console.print(Panel(
                f"[bold yellow]Preview complete![/bold yellow]\n"
                f"[yellow]Files would be modified: {successful_files}/{total_files}[/yellow]\n"
                f"[dim]Use --apply to write changes[/dim]",
                title="[bold yellow]Preview Complete[/bold yellow]",
                border_style="yellow"
            ))

        # Display summary if requested or if there are multiple files
        if args.summary or total_files > 1:
            _display_injection_summary(result, args)

        # Log injection to surgical feedback loop
        _log_injection_to_surgical_loop(result, args, enhanced_prompt, enhancement_metadata)

        # Collect feedback if system is enabled
        if feedback_system and (args.rate or args.feedback):
            _collect_feedback(feedback_system, result, args)

        # Run automatic feedback analysis if enabled
        if tuning_config and tuning_config.auto_feedback_analysis:
            _run_automatic_feedback_analysis()

    else:
        console.print(Panel(
            f"[bold red]❌ Operation failed:[/bold red] {result.get('error', 'Unknown error')}",
            title="[bold red]Operation Failed[/bold red]",
            border_style="red"
        ))

    logging.info("Code injection completed.")


def _display_injection_summary(result: dict, args) -> None:
    """Display a comprehensive injection summary."""

    # Create summary table
    summary_table = Table(show_header=False, box=None, padding=(0, 1))

    # Basic statistics
    total_files = result.get("total_files", 0)
    successful_files = result.get("successful_files", 0)
    failed_files = result.get("failed_files", 0)
    cached_files = result.get("cached_files", 0)
    total_injections = result.get("total_injections", 0)

    summary_table.add_row("[cyan]Total Files:[/cyan]", f"[bold]{total_files}[/bold]")
    summary_table.add_row("[cyan]Successful:[/cyan]", f"[bold green]{successful_files}[/bold green]")

    if cached_files > 0:
        summary_table.add_row("[cyan]Cached:[/cyan]", f"[bold blue]{cached_files}[/bold blue]")

    if failed_files > 0:
        summary_table.add_row("[cyan]Failed:[/cyan]", f"[bold red]{failed_files}[/bold red]")

    if total_injections > 0:
        summary_table.add_row("[cyan]Injections:[/cyan]", f"[bold]{total_injections}[/bold]")

    # Calculate success rate
    success_rate = ((successful_files + cached_files) / total_files * 100) if total_files > 0 else 0
    summary_table.add_row("[cyan]Success Rate:[/cyan]", f"[bold]{success_rate:.1f}%[/bold]")

    # Display model information
    model_used = result.get("model_used", args.model or "auto")
    summary_table.add_row("[cyan]Model Used:[/cyan]", f"[bold]{model_used}[/bold]")

    # Display tag
    summary_table.add_row("[cyan]Tag:[/cyan]", f"[bold]{args.tag}[/bold]")

    console.print(Panel(
        summary_table,
        title="[bold]Injection Summary[/bold]",
        border_style="blue"
    ))


def _detect_injection_type(prompt: str) -> str:
    """Detect injection type from prompt text"""
    prompt_lower = prompt.lower()

    # Define keywords for different injection types
    type_keywords = {
        'bugfix': ['fix', 'bug', 'error', 'exception', 'crash', 'fail', 'broken', 'issue'],
        'optimize': ['optimize', 'performance', 'faster', 'speed', 'efficient', 'slow'],
        'refactor': ['refactor', 'clean', 'restructure', 'organize', 'improve structure'],
        'feature': ['add', 'implement', 'create', 'new', 'feature', 'functionality'],
        'security': ['secure', 'security', 'vulnerability', 'safe', 'protect'],
        'test': ['test', 'testing', 'unit test', 'coverage', 'validate'],
        'documentation': ['document', 'comment', 'docs', 'explain', 'describe']
    }

    # Count matches for each type
    type_scores = {}
    for injection_type, keywords in type_keywords.items():
        score = sum(1 for keyword in keywords if keyword in prompt_lower)
        if score > 0:
            type_scores[injection_type] = score

    # Return the type with highest score, or 'general' if no matches
    if type_scores:
        return max(type_scores, key=type_scores.get)
    return 'general'


def _show_history(args):
    """Show injection history"""
    try:
        from log_store import LogStore
        store = LogStore()

        # Get logs based on filters
        if args.file:
            logs = store.query_by_file(args.file, limit=args.limit)
        elif args.tag:
            logs = store.query_by_tag(args.tag, limit=args.limit)
        else:
            logs = store.get_recent_logs(limit=args.limit)

        if not logs:
            console.print("[yellow]No injection history found[/yellow]")
            return

        # Create history table
        from rich.table import Table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("ID", style="dim", width=6)
        table.add_column("File", style="cyan", width=20)
        table.add_column("Prompt", style="green", width=30)
        table.add_column("Model", style="blue", width=12)
        table.add_column("Type", style="yellow", width=12)
        table.add_column("Rating", style="red", width=8)
        table.add_column("Success", style="green", width=8)

        for log in logs:
            rating_str = f"⭐{log['user_rating']}" if log['user_rating'] else "—"
            success_str = "✅" if log['success'] else "❌"
            prompt_preview = log['prompt'][:27] + "..." if len(log['prompt']) > 30 else log['prompt']
            filename_preview = log['filename'][:17] + "..." if len(log['filename']) > 20 else log['filename']

            table.add_row(
                str(log['id']),
                filename_preview,
                prompt_preview,
                log['model'],
                log['injection_type'],
                rating_str,
                success_str
            )

        from rich.panel import Panel
        console.print(Panel(table, title=f"[bold]Injection History ({len(logs)} entries)[/bold]"))

    except Exception as e:
        console.print(f"[red]❌ Failed to load history: {e}[/red]")


def _log_injection_to_surgical_loop(result: dict, args, enhanced_prompt: str, enhancement_metadata: dict):
    """Log injection to the surgical feedback loop"""
    try:
        from log_store import log_injection

        # Determine injection type
        injection_type = _detect_injection_type(args.prompt_text)

        # Log the injection
        log_id = log_injection(
            filename=args.source,
            prompt=enhanced_prompt,
            model=result.get('model_used', args.model or 'auto'),
            injection_type=injection_type,
            output=str(result.get('output', '')),
            tags=[args.tag] if args.tag else [],
            execution_time=result.get('execution_time'),
            success=result.get('success', False)
        )

        # Store log ID in result for potential rating
        result['surgical_log_id'] = log_id

        logger.info(f"Logged injection to surgical feedback loop: {log_id}")

    except Exception as e:
        logger.warning(f"Failed to log injection to surgical loop: {e}")


def _run_automatic_feedback_analysis():
    """Run automatic feedback analysis to update shaping rules"""
    try:
        from feedback_analyzer import analyze_feedback

        # Run analysis with conservative settings
        analysis_result = analyze_feedback(rating_threshold=2, min_occurrences=2)

        if analysis_result['rules_created'] > 0:
            console.print(f"[cyan]🧠 Created {analysis_result['rules_created']} new shaping rules from feedback patterns[/cyan]")

        logger.info(f"Automatic feedback analysis: {analysis_result}")

    except Exception as e:
        logger.warning(f"Automatic feedback analysis failed: {e}")


def _collect_feedback(feedback_system: dict, result: dict, args) -> None:
    """Collect user feedback for the injection."""
    try:
        # Get the entry ID from result (would need to be added to inject_code function)
        entry_id = result.get('feedback_entry_id')

        if not entry_id:
            # Log the prompt execution first
            entry_id = feedback_system['logger'].log_prompt(
                file=args.source,
                injection_type="general",  # Could be enhanced to detect type
                model=result.get('model_used', args.model or 'auto'),
                prompt=args.prompt_text,
                output=str(result.get('output', '')),
                execution_time=result.get('execution_time'),
                success=result.get('success', False),
                tags=[args.tag] if args.tag else []
            )

        # Collect rating and feedback
        if args.rate or args.feedback:
            feedback_system['engine'].collect_feedback(
                entry_id=entry_id,
                rating=args.rate,
                feedback_text=args.feedback
            )

            console.print(f"[green]✅ Feedback collected (Entry: {entry_id[:8]}...)[/green]")

            # Show improvement suggestions if rating is low
            if args.rate and args.rate <= 2:
                suggestions = feedback_system['engine'].get_improvement_suggestions("general", [args.tag])
                if suggestions:
                    console.print("\n[yellow]💡 Improvement suggestions based on feedback:[/yellow]")
                    for i, suggestion in enumerate(suggestions[:3], 1):
                        console.print(f"  {i}. {suggestion}")

    except Exception as e:
        console.print(f"[red]❌ Failed to collect feedback: {e}[/red]")


def _handle_tune_command(args):
    """Handle the tune command for manual shaping configuration."""
    try:
        import config

        # Show current configuration
        if args.show:
            config.print_config_info()
            return

        # Reset configuration
        if args.reset:
            if config.reset_config():
                console.print("[green]✅ Configuration reset to defaults[/green]")
                config.print_config_info()
            else:
                console.print("[red]❌ Failed to reset configuration[/red]")
            return

        # Update configuration
        updates = {}
        if args.tone:
            updates['tone'] = args.tone
        if args.fallback_sensitivity:
            updates['fallback_sensitivity'] = args.fallback_sensitivity
        if args.shaping_strength:
            updates['shaping_strength'] = args.shaping_strength

        if updates:
            if config.update_config(updates):
                console.print("[green]✅ Configuration updated successfully[/green]")

                # Show what was changed
                for key, value in updates.items():
                    console.print(f"  [cyan]{key}:[/cyan] [bold]{value}[/bold]")

                console.print()
                config.print_config_info()
            else:
                console.print("[red]❌ Failed to update configuration[/red]")
        else:
            # No arguments provided, show current config
            console.print("[yellow]No configuration changes specified. Current configuration:[/yellow]")
            config.print_config_info()
            console.print("\n[dim]Use --help to see available options[/dim]")

    except ImportError:
        console.print("[red]❌ Configuration module not available[/red]")
    except Exception as e:
        console.print(f"[red]❌ Error handling tune command: {e}[/red]")


if __name__ == "__main__":
    main()
