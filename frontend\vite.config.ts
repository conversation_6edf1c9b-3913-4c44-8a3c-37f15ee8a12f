import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 3000,
    host: '127.0.0.1', // Force IPv4 for frontend server
    proxy: {
      // API endpoints proxy - Force IPv4 backend
      '/api': {
        target: 'http://127.0.0.1:8001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      // Direct backend endpoints (inject, health, etc.)
      '/inject': {
        target: 'http://127.0.0.1:8001',
        changeOrigin: true
      },
      '/health': {
        target: 'http://127.0.0.1:8001',
        changeOrigin: true
      },
      '/run-injection': {
        target: 'http://127.0.0.1:8001',
        changeOrigin: true
      },
      // Proxy WebSocket connections to FastAPI with enhanced configuration
      '/ws': {
        target: 'http://127.0.0.1:8001',
        ws: true,
        changeOrigin: true,
        secure: false,
        timeout: 60000, // 60 second timeout
        proxyTimeout: 60000, // 60 second proxy timeout
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('WebSocket proxy error:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('WebSocket proxy request:', req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('WebSocket proxy response:', proxyRes.statusCode);
          });
        }
      }
    }
  }
})
