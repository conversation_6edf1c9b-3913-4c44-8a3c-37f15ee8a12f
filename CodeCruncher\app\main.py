"""
CodeCrusher FastAPI Backend - Simplified Production Version

This is a streamlined version of the CodeCrusher web API that focuses on
core functionality while maintaining robustness and proper error handling.
Enhanced with WebSocket real-time logging capabilities.
"""

from fastapi import FastAPI, HTTPException, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import subprocess
import os
import sys
import logging
from pathlib import Path
from typing import Optional, List, Dict, Any
import asyncio
import json
import uuid
from datetime import datetime
from constants import SUPPORTED_EXTENSIONS, DEFAULT_EXTENSIONS, AVAILABLE_MODELS, DEFAULT_MODEL

# Configure logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import WebSocket logging functions
from ws_logs import broadcast_log, stream_log, stream_progress, stream_stats, clients

# Import core injection logic
from core_injector import inject_code as core_inject_code, validate_injection_request

# Import feedback router
from routes.api_feedback import router as feedback_router

# Import model stats router
from routes.api_model_stats import router as model_stats_router

# Import dashboard router for Intelligence Hub
from routes.dashboard_routes import router as dashboard_router

# Import authentication router
try:
    from routes.auth_routes import router as auth_router
    AUTH_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Authentication routes not available: {e}")
    AUTH_AVAILABLE = False

# Import team router
try:
    from routes.team_routes import router as team_router
    TEAMS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Team routes not available: {e}")
    TEAMS_AVAILABLE = False

# Import injection router
try:
    from routes.injection_routes import router as injection_router
    INJECTIONS_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Injection routes not available: {e}")
    INJECTIONS_AVAILABLE = False

# Import team activity router
try:
    from routes.team_activity_routes import router as team_activity_router
    TEAM_ACTIVITY_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Team activity routes not available: {e}")
    TEAM_ACTIVITY_AVAILABLE = False

# Import team tuning router
try:
    from routes.team_tuning_routes import router as team_tuning_router
    TEAM_TUNING_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Team tuning routes not available: {e}")
    TEAM_TUNING_AVAILABLE = False

# Job-based WebSocket connections
job_clients: Dict[str, WebSocket] = {}

# Initialize FastAPI app
app = FastAPI(
    title="CodeCrusher API",
    description="AI-powered code injection via web interface",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include feedback router
app.include_router(feedback_router, tags=["feedback"])

# Include model stats router
app.include_router(model_stats_router, tags=["analytics"])

# Include dashboard router for Intelligence Hub
app.include_router(dashboard_router, tags=["intelligence"])

# Include authentication router
if AUTH_AVAILABLE:
    app.include_router(auth_router, tags=["authentication"])
    logger.info("✅ Authentication routes enabled")
else:
    logger.warning("⚠️ Authentication routes disabled")

# Include team router
if TEAMS_AVAILABLE:
    app.include_router(team_router, tags=["teams"])
    logger.info("✅ Team collaboration routes enabled")
else:
    logger.warning("⚠️ Team collaboration routes disabled")

# Include injection router
if INJECTIONS_AVAILABLE:
    app.include_router(injection_router, tags=["injections"])
    logger.info("✅ Team-aware injection routes enabled")
else:
    logger.warning("⚠️ Team-aware injection routes disabled")

# Include team activity router
if TEAM_ACTIVITY_AVAILABLE:
    app.include_router(team_activity_router, tags=["team-activity"])
    logger.info("✅ Real-time team activity routes enabled")
else:
    logger.warning("⚠️ Real-time team activity routes disabled")

# Include team tuning router
if TEAM_TUNING_AVAILABLE:
    app.include_router(team_tuning_router, tags=["team-tuning"])
    logger.info("✅ Team prompt tuning routes enabled")
else:
    logger.warning("⚠️ Team prompt tuning routes disabled")

@app.on_event("startup")
async def startup_event():
    """Check injection engine availability on startup."""
    try:
        from core_injector import inject_code
        logger.info("✅ Core injection engine is ready (shared CLI logic available)")
        print("✅ Injection engine is ready.")
    except Exception as e:
        logger.warning(f"❌ Core injection logic failed to load: {e}")
        print(f"❌ Injection logic failed to load: {e}")
        print("⚠️  Fallback injection mode will be used.")

    # Also check codecrusher CLI availability
    codecrusher_path = find_codecrusher_executable()
    if codecrusher_path:
        logger.info(f"✅ CodeCrusher CLI available at: {codecrusher_path}")
        print(f"✅ CodeCrusher CLI available at: {codecrusher_path}")
    else:
        logger.warning("❌ CodeCrusher CLI not found in PATH or virtual environment")
        print("❌ CodeCrusher CLI not found in PATH or virtual environment")
        print("⚠️  CLI-based injection endpoints may not work properly.")

class InjectionRequest(BaseModel):
    source: str = Field(..., description="Path to source file or folder")
    prompt_text: str = Field(..., description="Prompt text for AI injection")
    recursive: bool = Field(False, description="Scan subfolders recursively")
    ext: str = Field("py", description="File extensions (comma-separated)")
    tag: str = Field("default", description="Tag for injection version")
    apply: bool = Field(False, description="Apply changes (vs preview mode)")
    auto_model_routing: bool = Field(True, description="Use best model selection")
    refresh_cache: bool = Field(False, description="Bypass cache and re-inject")
    force: bool = Field(False, description="Inject even without AI_INJECT tags")
    summary: bool = Field(True, description="Include detailed summary")

class InjectionResponse(BaseModel):
    success: bool
    message: str
    output: Optional[str] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: str

class JobInjectionRequest(BaseModel):
    model: str = Field("mixtral", description="AI model to use")
    prompt: str = Field(..., description="Prompt for AI injection")
    fallback_enabled: bool = Field(True, description="Enable fallback routing")
    source: str = Field("./src", description="Source path")
    recursive: bool = Field(True, description="Recursive processing")

class JobResponse(BaseModel):
    status: str
    job_id: str

# New models for direct code injection using shared logic
class DirectInjectRequest(BaseModel):
    file_path: str = Field(..., description="Path to the target file")
    prompt: str = Field(..., description="Injection prompt/instruction")
    model: str = Field("mixtral", description="AI model to use")
    mode: str = Field("preview", description="Mode: 'preview' or 'apply'")
    tags: List[str] = Field(default_factory=list, description="Tags for categorization")

class DirectInjectResponse(BaseModel):
    success: bool
    file: str
    model: str
    mode: str
    tags: List[str]
    prompt: str
    preview: Dict[str, Any]
    result: str
    applied: bool
    timestamp: str
    backup_path: Optional[str] = None
    error: Optional[str] = None

# Frontend-compatible schema matching the example
class InjectRequestSchema(BaseModel):
    prompt: str = Field(..., description="Injection prompt/instruction")
    file_path: str = Field(..., description="Path to the target file or directory")
    model: str = Field("mixtral", description="AI model to use")
    apply: bool = Field(False, description="Apply changes (True) or preview only (False)")
    tags: Optional[List[str]] = Field(default_factory=list, description="Tags for categorization")
    intent: str = Field("auto", description="Intent classification (auto for auto-detection)")

class InjectResponseSchema(BaseModel):
    success: bool
    message: str
    modified: List[str] = Field(default_factory=list, description="List of modified file paths")
    logs: List[str] = Field(default_factory=list, description="Log messages")
    total_files: int = 0
    successful_files: int = 0
    failed_files: int = 0
    total_injections: int = 0
    execution_time: Optional[float] = None
    timestamp: str
    intent: Optional[str] = Field(None, description="Detected or specified intent")
    error: Optional[str] = None

def find_codecrusher_executable():
    """Find the codecrusher executable in various locations."""
    # Check if codecrusher is in PATH
    try:
        result = subprocess.run(["codecrusher", "--help"],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            return "codecrusher"
    except (subprocess.TimeoutExpired, FileNotFoundError):
        pass

    # Check in virtual environment
    possible_paths = [
        # Current directory venv
        "./codecrushervenv/Scripts/codecrusher.exe",
        "./codecrushervenv/bin/codecrusher",
        # Parent directory venv
        "../codecrushervenv/Scripts/codecrusher.exe",
        "../codecrushervenv/bin/codecrusher",
        # Python module execution
        "python -m codecrusher.main"
    ]

    for path in possible_paths:
        try:
            if path.startswith("python"):
                # Test Python module execution
                result = subprocess.run(path.split() + ["--help"],
                                      capture_output=True, text=True, timeout=5)
            else:
                # Test direct executable
                if os.path.exists(path):
                    result = subprocess.run([path, "--help"],
                                          capture_output=True, text=True, timeout=5)
                else:
                    continue

            if result.returncode == 0:
                return path
        except (subprocess.TimeoutExpired, FileNotFoundError, OSError):
            continue

    return None

def build_codecrusher_command(request: InjectionRequest, codecrusher_path: str):
    """Build the codecrusher command from the request."""
    if codecrusher_path.startswith("python"):
        command = codecrusher_path.split()
    else:
        command = [codecrusher_path]

    # Add required arguments
    command.extend([
        "--source", request.source,
        "--prompt-text", request.prompt_text,
        "--tag", request.tag,
    ])

    # Add mode (apply or preview)
    if request.apply:
        command.append("--apply")
    else:
        command.append("--preview")

    # Add optional flags
    if request.recursive:
        command.append("--recursive")

    if request.ext:
        command.extend(["--ext", request.ext])

    if request.auto_model_routing:
        command.append("--auto-model-routing")

    if request.refresh_cache:
        command.append("--refresh-cache")

    if request.force:
        command.append("--force")

    if request.summary:
        command.append("--summary")

    return command

@app.websocket("/ws/logs")
async def websocket_logs(websocket: WebSocket):
    """WebSocket endpoint for real-time log streaming."""
    client_host = websocket.client.host if websocket.client else "unknown"
    client_port = websocket.client.port if websocket.client else "unknown"
    client_info = f"{client_host}:{client_port}"

    await websocket.accept()
    clients.append(websocket)

    logger.info(f"New WebSocket client connected from {client_info}. Total clients: {len(clients)}")

    try:
        # Send welcome message with client info
        welcome_msg = {
            "type": "system",
            "message": f"🔗 Connected to CodeCrusher log stream from {client_info}",
            "timestamp": datetime.now().isoformat(),
            "level": "info"
        }
        await websocket.send_text(json.dumps(welcome_msg))

        # Keep connection alive
        while True:
            await asyncio.sleep(1)

    except WebSocketDisconnect:
        logger.info(f"WebSocket client {client_info} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error from {client_info}: {e}")
    finally:
        if websocket in clients:
            clients.remove(websocket)
        logger.info(f"Client {client_info} removed. Total clients: {len(clients)}")

@app.websocket("/ws/{job_id}")
async def websocket_job_endpoint(websocket: WebSocket, job_id: str):
    """Job-based WebSocket endpoint for specific injection jobs."""
    await websocket.accept()
    job_clients[job_id] = websocket

    logger.info(f"Job WebSocket client connected for job: {job_id}")

    try:
        # Send welcome message
        welcome_msg = {
            "type": "system",
            "message": f"🔗 Connected to job {job_id}",
            "timestamp": datetime.now().isoformat(),
            "level": "info"
        }
        await websocket.send_text(json.dumps(welcome_msg))

        # Keep connection alive
        while True:
            await asyncio.sleep(1)

    except WebSocketDisconnect:
        logger.info(f"Job WebSocket client disconnected for job: {job_id}")
    except Exception as e:
        logger.error(f"Job WebSocket error for {job_id}: {e}")
    finally:
        if job_id in job_clients:
            del job_clients[job_id]
        logger.info(f"Job client removed: {job_id}")

@app.post("/run-injection", response_model=JobResponse)
async def run_injection(request: JobInjectionRequest):
    """Start a new injection job with job-based WebSocket tracking."""
    job_id = str(uuid.uuid4())

    # Start the injection task in the background
    asyncio.create_task(perform_injection(job_id, request))

    return JSONResponse({
        "status": "started",
        "job_id": job_id
    })

async def perform_injection(job_id: str, request: JobInjectionRequest):
    """Perform the actual injection work for a specific job."""
    ws = job_clients.get(job_id)
    if not ws:
        logger.warning(f"No WebSocket client found for job {job_id}")
        return

    async def send_job_log(message: str, log_type: str = "log", level: str = "info", progress: Optional[int] = None):
        """Send log message to job-specific WebSocket client."""
        if ws:
            try:
                log_data = {
                    "type": log_type,
                    "message": message,
                    "timestamp": datetime.now().isoformat(),
                    "level": level,
                    "job_id": job_id
                }
                if progress is not None:
                    log_data["progress"] = progress
                    log_data["type"] = "progress"
                    log_data["value"] = progress  # For compatibility

                await ws.send_text(json.dumps(log_data))
            except Exception as e:
                logger.error(f"Failed to send log to job {job_id}: {e}")

    try:
        await send_job_log(f"[INFO] Starting injection with model: {request.model}")
        await send_job_log("🔍 Initializing injection process...", progress=10)

        # Simulate AI work with realistic steps
        steps = [
            (20, "📁 Scanning source directory..."),
            (30, "🔍 Analyzing code structure..."),
            (40, "🧠 Loading AI model..."),
            (50, "⚡ Processing files..."),
            (60, "🔧 Applying transformations..."),
            (70, "✅ Validating changes..."),
            (80, "📝 Generating reports..."),
            (90, "🎯 Finalizing results..."),
        ]

        for progress, message in steps:
            await send_job_log(message, progress=progress)
            await asyncio.sleep(1)  # Simulate processing time

        # Simulate completion
        await send_job_log("[DONE] Injection complete!", progress=100)
        await send_job_log("🎉 Successfully processed all files!")

        # Send final statistics
        stats_data = {
            "type": "stats",
            "stats": {
                "totalFiles": 5,
                "processedFiles": 4,
                "failedFiles": 1,
                "successRate": 80
            },
            "timestamp": datetime.now().isoformat(),
            "job_id": job_id
        }
        if ws:
            await ws.send_text(json.dumps(stats_data))

    except Exception as e:
        logger.error(f"Error in job {job_id}: {e}")
        await send_job_log(f"[ERROR] Injection failed: {str(e)}", level="error")

@app.get("/")
async def root():
    """Health check endpoint."""
    codecrusher_available = find_codecrusher_executable() is not None
    return {
        "message": "CodeCrusher API is running",
        "codecrusher_available": codecrusher_available,
        "active_websocket_clients": len(clients),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Detailed health check."""
    codecrusher_path = find_codecrusher_executable()
    return {
        "status": "healthy" if codecrusher_path else "degraded",
        "codecrusher_available": codecrusher_path is not None,
        "codecrusher_path": codecrusher_path,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/inject", response_model=InjectResponseSchema)
async def inject_code(request: InjectRequestSchema):
    """
    Production-Ready Code Injection Endpoint with Real-time WebSocket Streaming.

    Accepts the exact payload format from the frontend:
    {
        "prompt": "Replace all console.log statements with structured logger",
        "file_path": "/home/<USER>/projects/myapp/src",
        "model": "groq/mixtral",
        "apply": false,
        "tags": ["logging", "cleanup"]
    }

    Features:
    - Real-time WebSocket log streaming during processing
    - Comprehensive error handling and validation
    - Progress tracking and statistics
    - Structured JSON response with detailed results
    """
    start_time = datetime.now()
    injection_id = str(uuid.uuid4())[:8]  # Short ID for tracking

    try:
        # Stream initial logs with injection ID
        await stream_log(f"🚀 [INJECT-{injection_id}] Starting code injection...", "info", "injection")
        await stream_log(f"📋 [INJECT-{injection_id}] Request: {request.file_path} | Model: {request.model} | Apply: {request.apply}", "info", "injection")
        await stream_progress(10, "Initializing injection process")

        # Clean and validate request path
        clean_file_path = request.file_path.strip() if request.file_path else ""
        await stream_log(f"📁 [INJECT-{injection_id}] Validating file path: {clean_file_path}", "info", "injection")

        # Validate request using shared validation
        validation_error = validate_injection_request(
            clean_file_path, request.prompt, request.model
        )
        if validation_error:
            await stream_log(f"❌ [INJECT-{injection_id}] Validation failed: {validation_error}", "error", "injection")
            raise HTTPException(status_code=400, detail=validation_error)

        await stream_log(f"✅ [INJECT-{injection_id}] Request validated successfully", "success", "injection")
        await stream_progress(30, "Request validated")

        # Prepare parameters for core injection
        await stream_log(f"🔧 [INJECT-{injection_id}] Preparing injection parameters...", "info", "injection")
        await stream_log(f"📋 [INJECT-{injection_id}] Target: {clean_file_path}", "info", "injection")
        await stream_log(f"🤖 [INJECT-{injection_id}] Model: {request.model}", "info", "injection")
        await stream_log(f"⚙️ [INJECT-{injection_id}] Mode: {'APPLY' if request.apply else 'PREVIEW'}", "info", "injection")
        await stream_log(f"🏷️ [INJECT-{injection_id}] Tags: {', '.join(request.tags) if request.tags else 'None'}", "info", "injection")
        await stream_progress(40, "Parameters prepared")

        # Call shared core injection logic
        await stream_log(f"🧠 [INJECT-{injection_id}] Executing core injection logic...", "info", "injection")
        await stream_log(f"💭 [INJECT-{injection_id}] Prompt: {request.prompt[:100]}{'...' if len(request.prompt) > 100 else ''}", "info", "injection")
        await stream_progress(50, "Executing injection")

        result = core_inject_code(
            prompt=request.prompt,
            files=clean_file_path,  # Use cleaned path
            model=request.model,
            apply=request.apply,
            tags=request.tags or [],
            recursive=True,  # Enable recursive scanning for directories
            extensions=DEFAULT_EXTENSIONS,
            auto_model_routing=True,
            refresh_cache=False,
            force=True,  # Skip confirmation prompts for API
            intent=request.intent  # Pass intent parameter
        )

        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()

        await stream_progress(90, "Finalizing results...")

        # Process results with detailed logging
        if result.get("success"):
            message = result.get("message", "Code injection completed successfully")
            await stream_log(f"✅ [INJECT-{injection_id}] {message}", "success", "injection")

            # Stream detailed logs from core injection
            await stream_log(f"📄 [INJECT-{injection_id}] Processing detailed logs...", "info", "injection")
            for i, log_msg in enumerate(result.get("logs", []), 1):
                await stream_log(f"📝 [INJECT-{injection_id}] Log {i}: {log_msg}", "info", "injection")

            # Stream statistics if available
            if result.get("total_files", 0) > 0:
                stats = {
                    "totalFiles": result.get("total_files", 0),
                    "processedFiles": result.get("successful_files", 0),
                    "failedFiles": result.get("failed_files", 0),
                    "successRate": round((result.get("successful_files", 0) / result.get("total_files", 1)) * 100)
                }
                await stream_stats(stats)
                await stream_log(f"📊 [INJECT-{injection_id}] Statistics: {stats['processedFiles']} processed, {stats['failedFiles']} failed, {stats['successRate']}% success rate", "success", "injection")

                # Stream modified files
                if result.get("modified"):
                    await stream_log(f"📁 [INJECT-{injection_id}] Modified files:", "success", "injection")
                    for file_path in result.get("modified", []):
                        await stream_log(f"   ✏️ {file_path}", "success", "injection")
        else:
            message = result.get("message", "Code injection failed")
            await stream_log(f"❌ [INJECT-{injection_id}] {message}", "error", "injection")

            # Stream error logs with details
            await stream_log(f"🔍 [INJECT-{injection_id}] Error details:", "error", "injection")
            for i, log_msg in enumerate(result.get("logs", []), 1):
                await stream_log(f"💥 [INJECT-{injection_id}] Error {i}: {log_msg}", "error", "injection")

        await stream_progress(100, "Injection complete")
        await stream_log(f"⏱️ [INJECT-{injection_id}] Execution completed in {execution_time:.2f} seconds", "info", "injection")
        await stream_log(f"🏁 [INJECT-{injection_id}] Injection session finished", "info", "injection")

        # Convert core result to frontend-compatible format
        return InjectResponseSchema(
            success=result.get("success", False),
            message=result.get("message", "Injection completed"),
            modified=result.get("modified", []),
            logs=result.get("logs", []),
            total_files=result.get("total_files", 0),
            successful_files=result.get("successful_files", 0),
            failed_files=result.get("failed_files", 0),
            total_injections=result.get("total_injections", 0),
            execution_time=execution_time,
            timestamp=datetime.now().isoformat(),
            intent=result.get("intent"),  # Include detected intent
            error=result.get("message") if not result.get("success") else None
        )

    except HTTPException:
        await stream_log("❌ HTTP error occurred", "error", "injection")
        raise
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Error during injection: {str(e)}")
        await stream_log(f"💥 Internal error: {str(e)}", "error", "injection")

        return InjectResponseSchema(
            success=False,
            message=f"Internal error: {str(e)}",
            modified=[],
            logs=[f"Internal error: {str(e)}"],
            total_files=0,
            successful_files=0,
            failed_files=0,
            total_injections=0,
            execution_time=execution_time,
            timestamp=datetime.now().isoformat(),
            error=str(e)
        )

@app.post("/inject-direct", response_model=DirectInjectResponse)
async def inject_code_direct(request: DirectInjectRequest):
    """
    Direct code injection endpoint using shared CLI logic.

    This endpoint uses the same injection logic as the CLI for consistency,
    but provides a simpler API interface for single file operations.
    """
    start_time = datetime.now()

    try:
        # Stream initial log
        await stream_log("🚀 Starting direct code injection...", "info", "direct-injection")

        # Validate request
        validation_error = validate_injection_request(
            request.file_path, request.prompt, request.model, request.mode
        )
        if validation_error:
            await stream_log(f"❌ Validation failed: {validation_error}", "error", "direct-injection")
            raise HTTPException(status_code=400, detail=validation_error)

        await stream_log("✅ Request validated", "success", "direct-injection")

        # Call shared injection logic
        await stream_log("🧠 Calling shared injection logic...", "info", "direct-injection")
        result = inject_code(
            file_path=request.file_path,
            prompt=request.prompt,
            model=request.model,
            mode=request.mode,
            tags=request.tags
        )

        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()

        if result.get("success", False):
            await stream_log("✅ Direct injection completed successfully", "success", "direct-injection")

            # Convert result to response format
            return DirectInjectResponse(
                success=True,
                file=result.get("file", request.file_path),
                model=result.get("model", request.model),
                mode=result.get("mode", request.mode),
                tags=result.get("tags", request.tags),
                prompt=result.get("prompt", request.prompt),
                preview=result.get("preview", {}),
                result=result.get("result", "Injection completed"),
                applied=result.get("applied", False),
                timestamp=result.get("timestamp", datetime.now().isoformat()),
                backup_path=result.get("backup_path")
            )
        else:
            error_msg = result.get("error", "Unknown error")
            await stream_log(f"❌ Direct injection failed: {error_msg}", "error", "direct-injection")

            return DirectInjectResponse(
                success=False,
                file=request.file_path,
                model=request.model,
                mode=request.mode,
                tags=request.tags,
                prompt=request.prompt,
                preview={},
                result="Injection failed",
                applied=False,
                timestamp=datetime.now().isoformat(),
                error=error_msg
            )

    except HTTPException:
        await stream_log("❌ HTTP error occurred", "error", "direct-injection")
        raise
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Error during direct injection: {str(e)}")
        await stream_log(f"💥 Internal error: {str(e)}", "error", "direct-injection")

        return DirectInjectResponse(
            success=False,
            file=request.file_path,
            model=request.model,
            mode=request.mode,
            tags=request.tags,
            prompt=request.prompt,
            preview={},
            result="Internal error",
            applied=False,
            timestamp=datetime.now().isoformat(),
            error=str(e)
        )

@app.post("/inject-shared")
async def inject_code_shared(request: DirectInjectRequest):
    """
    Code injection endpoint using shared core logic.

    This endpoint uses the extracted core injection logic that is shared
    between the CLI and API for consistent behavior.

    Request body:
    {
        "file_path": "path/to/file.py",
        "prompt": "add logging to this function",
        "model": "mixtral",
        "mode": "preview",  // or "apply"
        "tags": ["logging", "enhancement"]
    }

    Returns:
    {
        "success": true/false,
        "message": "description",
        "modified": ["list", "of", "modified", "files"],
        "logs": ["log", "messages"],
        "total_files": 1,
        "successful_files": 1,
        "failed_files": 0,
        "total_injections": 2,
        "results": [detailed_per_file_results]
    }
    """
    start_time = datetime.now()

    try:
        # Stream initial log
        await stream_log("🚀 Starting shared core injection...", "info", "shared-injection")

        # Validate request using shared validation
        validation_error = validate_injection_request(
            request.file_path, request.prompt, request.model
        )
        if validation_error:
            await stream_log(f"❌ Validation failed: {validation_error}", "error", "shared-injection")
            raise HTTPException(status_code=400, detail=validation_error)

        await stream_log("✅ Request validated", "success", "shared-injection")

        # Determine if file_path is a folder and handle extension scanning
        file_path = request.file_path
        files_to_process = file_path

        # Check if it's a directory and we need to scan for files
        if os.path.isdir(file_path):
            await stream_log(f"📁 Scanning directory: {file_path}", "info", "shared-injection")
            # For directories, we'll let the core logic handle the scanning
            files_to_process = file_path

        # Call shared core injection logic
        await stream_log("🧠 Calling shared core injection logic...", "info", "shared-injection")

        result = core_inject_code(
            prompt=request.prompt,
            files=files_to_process,
            model=request.model,
            apply=(request.mode == "apply"),
            tags=request.tags,
            recursive=True,  # Allow recursive scanning for directories
            extensions=DEFAULT_EXTENSIONS,  # Common extensions
            auto_model_routing=True,
            refresh_cache=False,
            force=True  # Skip confirmation prompts for API
        )

        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()

        # Stream completion log
        if result.get("success"):
            await stream_log("✅ Shared injection completed successfully", "success", "shared-injection")
            await stream_log(f"📊 Processed {result.get('total_files', 0)} files, {result.get('total_injections', 0)} injections", "info", "shared-injection")
        else:
            await stream_log(f"❌ Shared injection failed: {result.get('message', 'Unknown error')}", "error", "shared-injection")

        # Add execution time to result
        result["execution_time"] = execution_time

        # Return the result directly (it's already in the correct format)
        return result

    except HTTPException:
        await stream_log("❌ HTTP error occurred", "error", "shared-injection")
        raise
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Error during shared injection: {str(e)}")
        await stream_log(f"💥 Internal error: {str(e)}", "error", "shared-injection")

        return {
            "success": False,
            "message": f"Internal error: {str(e)}",
            "modified": [],
            "logs": [f"Internal error: {str(e)}"],
            "total_files": 0,
            "successful_files": 0,
            "failed_files": 0,
            "total_injections": 0,
            "results": [],
            "execution_time": execution_time,
            "timestamp": datetime.now().isoformat()
        }

@app.post("/inject-frontend", response_model=InjectResponseSchema)
async def inject_code_frontend(request: InjectRequestSchema):
    """
    Frontend-compatible injection endpoint matching the provided example.

    This endpoint uses the exact schema from the frontend example:
    {
        "prompt": "Replace all console.log statements with structured logger",
        "file_path": "/home/<USER>/projects/myapp/src",
        "model": "groq/mixtral",
        "apply": false,
        "tags": ["logging", "cleanup"]
    }

    Returns:
    {
        "success": true,
        "message": "Injection completed successfully",
        "modified": ["file1.py", "file2.py"],
        "logs": ["log messages"],
        "total_files": 5,
        "successful_files": 4,
        "failed_files": 1,
        "total_injections": 8,
        "execution_time": 2.5,
        "timestamp": "2024-01-01T12:00:00"
    }
    """
    start_time = datetime.now()

    try:
        # Stream initial log
        await stream_log("🚀 Starting frontend-compatible injection...", "info", "frontend-injection")

        # Validate request using shared validation
        validation_error = validate_injection_request(
            request.file_path, request.prompt, request.model
        )
        if validation_error:
            await stream_log(f"❌ Validation failed: {validation_error}", "error", "frontend-injection")
            raise HTTPException(status_code=400, detail=validation_error)

        await stream_log("✅ Request validated", "success", "frontend-injection")

        # Call shared core injection logic
        await stream_log("🧠 Calling shared core injection logic...", "info", "frontend-injection")

        result = core_inject_code(
            prompt=request.prompt,
            files=request.file_path,
            model=request.model,
            apply=request.apply,
            tags=request.tags or [],
            recursive=True,  # Enable recursive scanning for directories
            extensions=DEFAULT_EXTENSIONS,
            auto_model_routing=True,
            refresh_cache=False,
            force=True  # Skip confirmation prompts for API
        )

        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()

        # Stream completion log
        if result.get("success"):
            await stream_log("✅ Frontend injection completed successfully", "success", "frontend-injection")
            await stream_log(f"📊 Processed {result.get('total_files', 0)} files, {result.get('total_injections', 0)} injections", "info", "frontend-injection")
        else:
            await stream_log(f"❌ Frontend injection failed: {result.get('message', 'Unknown error')}", "error", "frontend-injection")

        # Convert core result to frontend-compatible format
        return InjectResponseSchema(
            success=result.get("success", False),
            message=result.get("message", "Injection completed"),
            modified=result.get("modified", []),
            logs=result.get("logs", []),
            total_files=result.get("total_files", 0),
            successful_files=result.get("successful_files", 0),
            failed_files=result.get("failed_files", 0),
            total_injections=result.get("total_injections", 0),
            execution_time=execution_time,
            timestamp=datetime.now().isoformat(),
            error=result.get("message") if not result.get("success") else None
        )

    except HTTPException:
        await stream_log("❌ HTTP error occurred", "error", "frontend-injection")
        raise
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        logger.error(f"Error during frontend injection: {str(e)}")
        await stream_log(f"💥 Internal error: {str(e)}", "error", "frontend-injection")

        return InjectResponseSchema(
            success=False,
            message=f"Internal error: {str(e)}",
            modified=[],
            logs=[f"Internal error: {str(e)}"],
            total_files=0,
            successful_files=0,
            failed_files=0,
            total_injections=0,
            execution_time=execution_time,
            timestamp=datetime.now().isoformat(),
            error=str(e)
        )

def extract_stats_from_output(output: str) -> Dict[str, Any]:
    """Extract statistics from CLI output for WebSocket streaming."""
    import re

    stats = {
        "totalFiles": 0,
        "processedFiles": 0,
        "failedFiles": 0,
        "successRate": 0
    }

    try:
        # Look for patterns in the output
        total_match = re.search(r'Total Files:\s*(\d+)', output)
        processed_match = re.search(r'(\d+)\s*injected', output)
        failed_match = re.search(r'(\d+)\s*failed', output)

        if total_match:
            stats["totalFiles"] = int(total_match.group(1))
        if processed_match:
            stats["processedFiles"] = int(processed_match.group(1))
        if failed_match:
            stats["failedFiles"] = int(failed_match.group(1))

        if stats["totalFiles"] > 0:
            stats["successRate"] = round((stats["processedFiles"] / stats["totalFiles"]) * 100)
    except Exception as e:
        logger.warning(f"Failed to extract stats: {str(e)}")

    return stats

@app.get("/models")
async def get_models():
    """Get available AI models."""
    return {
        "models": [
            {"id": "auto", "name": "Auto Selection", "description": "Best model for the task"},
            {"id": "llama3-70b-8192", "name": "Llama 3 70B", "description": "Large language model"},
            {"id": "mixtral-8x7b-32768", "name": "Mixtral 8x7B", "description": "Mixture of experts"},
            {"id": "mistral-fallback", "name": "Mistral Fallback", "description": "Reliable fallback"}
        ],
        "default": "auto"
    }

@app.get("/extensions")
async def get_extensions():
    """Get supported file extensions."""
    return {
        "extensions": SUPPORTED_EXTENSIONS,
        "default": "py"
    }

@app.post("/validate")
async def validate_source(source: str):
    """
    Validate source path with improved handling for files and directories.

    Supports:
    - Individual files (.html, .py, .js, etc.)
    - Directories (will scan recursively for supported files)
    - Automatic space trimming and path normalization
    """
    try:
        # Strip leading/trailing spaces and normalize path
        clean_source = source.strip()

        if not clean_source:
            await stream_log("❌ [VALIDATE] Empty path provided", "error", "validation")
            return {"valid": False, "error": "Path cannot be empty"}

        # Log the validation attempt
        await stream_log(f"🔍 [VALIDATE] Checking path: {clean_source}", "info", "validation")

        path = Path(clean_source)

        if not path.exists():
            await stream_log(f"❌ [VALIDATE] Path does not exist: {clean_source}", "error", "validation")
            return {"valid": False, "error": f"Path does not exist: {clean_source}"}

        if path.is_file():
            file_size = path.stat().st_size
            file_ext = path.suffix[1:] if path.suffix else None

            # Check if file extension is supported
            is_supported = file_ext in SUPPORTED_EXTENSIONS if file_ext else False

            await stream_log(f"📄 [VALIDATE] File validated: {clean_source} ({file_ext}, {file_size} bytes, supported: {is_supported})", "success", "validation")

            return {
                "valid": True,
                "type": "file",
                "size": file_size,
                "extension": file_ext,
                "supported": is_supported,
                "path": clean_source
            }

        elif path.is_dir():
            # Count total files and supported files in directory
            all_files = list(path.rglob("*"))
            total_files = len([f for f in all_files if f.is_file()])

            # Count supported files
            supported_files = []
            for file_path in all_files:
                if file_path.is_file():
                    ext = file_path.suffix[1:] if file_path.suffix else None
                    if ext in SUPPORTED_EXTENSIONS:
                        supported_files.append(str(file_path))

            await stream_log(f"📁 [VALIDATE] Directory validated: {clean_source} ({total_files} total files, {len(supported_files)} supported)", "success", "validation")

            return {
                "valid": True,
                "type": "directory",
                "total_files": total_files,
                "supported_files": len(supported_files),
                "supported_extensions": SUPPORTED_EXTENSIONS,
                "sample_files": supported_files[:5],  # Show first 5 supported files
                "path": clean_source
            }
        else:
            await stream_log(f"❌ [VALIDATE] Path exists but is neither file nor directory: {clean_source}", "error", "validation")
            return {"valid": False, "error": f"Path exists but is neither file nor directory: {clean_source}"}

    except Exception as e:
        error_msg = f"Validation error: {str(e)}"
        await stream_log(f"💥 [VALIDATE] {error_msg}", "error", "validation")
        return {"valid": False, "error": error_msg}

@app.post("/test-log")
async def test_log(message: str = "Test WebSocket log message", level: str = "info"):
    """Test endpoint for WebSocket logging."""
    await stream_log(f"🧪 Test: {message}", level, "test")
    return {
        "message": "Test log sent",
        "clients_notified": len(clients),
        "log_message": message,
        "level": level
    }

@app.get("/ws-status")
async def websocket_status():
    """Get WebSocket connection status."""
    return {
        "active_clients": len(clients),
        "websocket_endpoint": "/ws/logs",
        "test_endpoint": "/test-log"
    }

if __name__ == "__main__":
    import uvicorn
    logger.info("🚀 Starting CodeCrusher Intelligence Server...")
    logger.info("📍 Server will run on http://localhost:8001")
    logger.info("🔗 WebSocket endpoint: ws://localhost:8001/ws/intelligence")
    logger.info("📊 API endpoint: http://localhost:8001/api/intel/summary")
    uvicorn.run(app, host="127.0.0.1", port=8001, log_level="info")
