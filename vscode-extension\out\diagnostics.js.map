{"version": 3, "file": "diagnostics.js", "sourceRoot": "", "sources": ["../src/diagnostics.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,iCAAiC;AACjC,+BAAuD;AAEvD;;GAEG;AACH,SAAgB,mBAAmB,CAAC,OAAgC;IAChE,MAAM,mBAAmB,GAAG,IAAI,sBAAsB,EAAE,CAAC;IACzD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAEhD,gDAAgD;IAChD,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;QAC/C,IAAI,MAAM,EAAE;YACR,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;SAC1D;IACL,CAAC,CAAC,CACL,CAAC;IAEF,2CAA2C;IAC3C,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;QAC7C,mBAAmB,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC,CAAC,CACL,CAAC;IAEF,wCAAwC;IACxC,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;QAChC,mBAAmB,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;KAClF;IAED,OAAO,mBAAmB,CAAC;AAC/B,CAAC;AA1BD,kDA0BC;AAED;;GAEG;AACH,MAAa,sBAAsB;IAK/B;QAHQ,sBAAiB,GAAqB,EAAE,CAAC;QAI7C,IAAI,CAAC,qBAAqB,GAAG,MAAM,CAAC,SAAS,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAC;QAExF,yCAAyC;QACzC,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEtF,kBAAkB;QAClB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,gDAAgD;QAChD,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,MAAM,CAAC,EAAE;YAC/C,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;aAC3C;QACL,CAAC,CAAC,CAAC;QAEH,2CAA2C;QAC3C,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,wCAAwC;QACxC,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;YAChC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;SACnE;IACL,CAAC;IAED;;OAEG;IACU,oBAAoB;;YAC7B,IAAI;gBACA,6BAA6B;gBAC7B,MAAM,eAAe,GAAG,MAAM,oBAAc,CAAC,eAAe,EAAE,CAAC;gBAC/D,IAAI,CAAC,eAAe,EAAE;oBAClB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;oBAC5B,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,OAAO;iBACV;gBAED,qBAAqB;gBACrB,MAAM,aAAa,GAAG,MAAM,oBAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC7D,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,OAAO,CAAC;gBAE/C,wCAAwC;gBACxC,IAAI,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE;oBAChC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;iBACnE;aACJ;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACzD,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBAC5B,IAAI,CAAC,gBAAgB,EAAE,CAAC;aAC3B;QACL,CAAC;KAAA;IAED;;OAEG;IACK,gBAAgB;QACpB,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,QAA6B;QAClD,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAChD,OAAO;SACV;QAED,MAAM,WAAW,GAAwB,EAAE,CAAC;QAC5C,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;QAErC,uCAAuC;QACvC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACtD,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,WAAW;YACpC,CAAC,KAAK,CAAC,SAAS,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CACvE,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBACpB,SAAS;aACZ;YAED,4DAA4D;YAC5D,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;YAEzC,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACpD,SAAS;aACZ;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,CAC1B,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,EAClC,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CACpD,CAAC;YAEF,oBAAoB;YACpB,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,UAAU,CACpC,KAAK,EACL,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAChC,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CACpC,CAAC;YAEF,0BAA0B;YAC1B,UAAU,CAAC,MAAM,GAAG,aAAa,CAAC;YAClC,UAAU,CAAC,IAAI,GAAG,uBAAuB,CAAC;YAE1C,qBAAqB;YACrB,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAChC;QAED,qBAAqB;QACrB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;IAC9D,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,KAAqB;QAC9C,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,iBAAiB;QACjB,KAAK,CAAC,IAAI,CAAC,0BAA0B,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAErD,kCAAkC;QAClC,IAAI,KAAK,CAAC,QAAQ,EAAE;YAChB,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;SAC/B;QAED,+BAA+B;QAC/B,IAAI,KAAK,CAAC,KAAK,EAAE;YACb,KAAK,CAAC,IAAI,CAAC,UAAU,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;SAC3D;QAED,4BAA4B;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC9B;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,KAAqB;QAC/C,IAAI,KAAK,CAAC,KAAK,EAAE;YACb,OAAO,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;SAC1C;QAED,IAAI,KAAK,CAAC,QAAQ,EAAE;YAChB,OAAO,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC;SAC5C;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YACxC,OAAO,MAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC;SAChD;QAED,OAAO,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACxC;QACD,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,CAAC;IACzC,CAAC;CACJ;AAlLD,wDAkLC"}