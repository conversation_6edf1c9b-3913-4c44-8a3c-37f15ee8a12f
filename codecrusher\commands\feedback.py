"""
Feedback management commands for CodeCrusher
"""

import click
import json
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from pathlib import Path

console = Console()

try:
    from core.feedback.prompt_logger import PromptLogger
    from core.feedback.feedback_engine import FeedbackEngine
    from core.feedback.auto_refine import AutoRefineEngine
    from core.feedback.injection_memory import InjectionMemory, InjectionContext
    from core.feedback.manual_tuning import ManualTuningPanel
    from core.feedback.auto_testing import AutoTestingEngine
    FEEDBACK_AVAILABLE = True
except ImportError:
    FEEDBACK_AVAILABLE = False


@click.group()
def feedback():
    """Manage feedback and self-improving features"""
    if not FEEDBACK_AVAILABLE:
        console.print("[red]❌ Feedback system not available. Install required dependencies.[/red]")
        return


@feedback.command()
@click.option('--limit', default=20, help='Number of recent entries to show')
def history(limit):
    """Show recent prompt execution history"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        prompt_logger = PromptLogger()
        entries = prompt_logger.get_recent_entries(limit)

        if not entries:
            console.print("[yellow]No prompt history found.[/yellow]")
            return

        # Create history table
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("ID", style="dim", width=8)
        table.add_column("Timestamp", style="cyan", width=16)
        table.add_column("File", style="green", width=20)
        table.add_column("Model", style="blue", width=12)
        table.add_column("Type", style="yellow", width=10)
        table.add_column("Rating", style="red", width=6)
        table.add_column("Success", style="green", width=7)

        for entry in entries:
            rating_str = f"⭐{entry.rating}" if entry.rating else "—"
            success_str = "✅" if entry.success else "❌"
            timestamp_str = entry.timestamp[:16].replace('T', ' ')

            table.add_row(
                entry.id[:8],
                timestamp_str,
                entry.file[:18] + "..." if len(entry.file) > 18 else entry.file,
                entry.model,
                entry.injection_type,
                rating_str,
                success_str
            )

        console.print(Panel(table, title=f"[bold]Recent Prompt History ({len(entries)} entries)[/bold]"))

    except Exception as e:
        console.print(f"[red]❌ Failed to load history: {e}[/red]")


@feedback.command()
@click.argument('entry_id')
@click.option('--rating', type=int, help='Rating (1-5 stars)')
@click.option('--feedback-text', help='Feedback text')
def rate(entry_id, rating, feedback_text):
    """Rate a specific prompt execution"""
    if not FEEDBACK_AVAILABLE:
        return

    if not rating and not feedback_text:
        console.print("[red]❌ Please provide either --rating or --feedback-text[/red]")
        return

    if rating and not (1 <= rating <= 5):
        console.print("[red]❌ Rating must be between 1 and 5[/red]")
        return

    try:
        prompt_logger = PromptLogger()
        feedback_engine = FeedbackEngine(prompt_logger)

        # Check if entry exists
        entry = prompt_logger.get_entry(entry_id)
        if not entry:
            console.print(f"[red]❌ Entry {entry_id} not found[/red]")
            return

        # Collect feedback
        success = feedback_engine.collect_feedback(entry_id, rating, feedback_text)

        if success:
            console.print(f"[green]✅ Feedback collected for entry {entry_id[:8]}...[/green]")

            # Show improvement suggestions if rating is low
            if rating and rating <= 2:
                suggestions = feedback_engine.get_improvement_suggestions(entry.injection_type, entry.tags)
                if suggestions:
                    console.print("\n[yellow]💡 Improvement suggestions:[/yellow]")
                    for i, suggestion in enumerate(suggestions[:3], 1):
                        console.print(f"  {i}. {suggestion}")
        else:
            console.print("[red]❌ Failed to collect feedback[/red]")

    except Exception as e:
        console.print(f"[red]❌ Failed to rate entry: {e}[/red]")


@feedback.command()
def stats():
    """Show feedback statistics and patterns"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        prompt_logger = PromptLogger()
        feedback_engine = FeedbackEngine(prompt_logger)

        # Get statistics
        stats = prompt_logger.get_stats()
        feedback_summary = feedback_engine.get_feedback_summary()

        # Create stats table
        stats_table = Table(show_header=False, box=None, padding=(0, 1))
        stats_table.add_row("[cyan]Total Entries:[/cyan]", f"[bold]{stats['total_entries']}[/bold]")
        stats_table.add_row("[cyan]Success Rate:[/cyan]", f"[bold]{stats['success_rate']:.1%}[/bold]")

        if stats['average_rating']:
            stars = "⭐" * int(stats['average_rating'])
            stats_table.add_row("[cyan]Average Rating:[/cyan]", f"[bold]{stats['average_rating']:.1f} {stars}[/bold]")

        console.print(Panel(stats_table, title="[bold]Overall Statistics[/bold]"))

        # Model usage
        if stats['model_usage']:
            model_table = Table(show_header=True, header_style="bold blue")
            model_table.add_column("Model", style="cyan")
            model_table.add_column("Usage Count", style="green")

            for model, count in stats['model_usage'].items():
                model_table.add_row(model, str(count))

            console.print(Panel(model_table, title="[bold]Model Usage[/bold]"))

        # Feedback patterns
        patterns = feedback_summary.get('feedback_patterns', {})
        if patterns:
            pattern_table = Table(show_header=True, header_style="bold yellow")
            pattern_table.add_column("Pattern", style="cyan")
            pattern_table.add_column("Frequency", style="green")
            pattern_table.add_column("Confidence", style="blue")
            pattern_table.add_column("Improvement Hint", style="yellow")

            for pattern_name, pattern_data in patterns.items():
                pattern_table.add_row(
                    pattern_name.replace('_', ' ').title(),
                    str(pattern_data['frequency']),
                    f"{pattern_data['confidence']:.1%}",
                    pattern_data['improvement_hint'][:50] + "..." if len(pattern_data['improvement_hint']) > 50 else pattern_data['improvement_hint']
                )

            console.print(Panel(pattern_table, title="[bold]Learned Patterns[/bold]"))

    except Exception as e:
        console.print(f"[red]❌ Failed to load statistics: {e}[/red]")


@feedback.command()
@click.option('--injection-type', default='general', help='Injection type to analyze')
def suggestions(injection_type):
    """Get improvement suggestions based on learned patterns"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        prompt_logger = PromptLogger()
        feedback_engine = FeedbackEngine(prompt_logger)

        suggestions = feedback_engine.get_improvement_suggestions(injection_type)

        if suggestions:
            console.print(f"\n[bold]💡 Improvement suggestions for '{injection_type}':[/bold]")
            for i, suggestion in enumerate(suggestions, 1):
                console.print(f"  {i}. {suggestion}")
        else:
            console.print(f"[yellow]No suggestions available for '{injection_type}' yet.[/yellow]")

    except Exception as e:
        console.print(f"[red]❌ Failed to get suggestions: {e}[/red]")


@feedback.command()
@click.option('--rating-threshold', default=2, help='Rating threshold for low-rated entries')
def analyze(rating_threshold):
    """Analyze low-rated entries for patterns"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        prompt_logger = PromptLogger()
        auto_refine_engine = AutoRefineEngine(prompt_logger, FeedbackEngine(prompt_logger))

        # Get analysis
        analysis = auto_refine_engine.analyze_prompt_patterns()

        console.print(Panel(
            f"[bold]Analysis Results[/bold]\n"
            f"Total analyzed: {analysis['total_analyzed']}\n"
            f"Successful: {analysis['successful_count']}\n"
            f"Failed: {analysis['failed_count']}\n"
            f"Success rate: {analysis['success_rate']:.1%}",
            title="[bold]Pattern Analysis[/bold]"
        ))

        # Template performance
        if analysis.get('template_performance'):
            template_table = Table(show_header=True, header_style="bold green")
            template_table.add_column("Template", style="cyan")
            template_table.add_column("Success Rate", style="green")
            template_table.add_column("Usage Count", style="blue")

            for template_name, perf in analysis['template_performance'].items():
                template_table.add_row(
                    template_name,
                    f"{perf['success_rate']:.1%}",
                    str(perf['usage_count'])
                )

            console.print(Panel(template_table, title="[bold]Template Performance[/bold]"))

    except Exception as e:
        console.print(f"[red]❌ Failed to analyze patterns: {e}[/red]")


@feedback.command()
@click.option('--format', 'output_format', default='json', type=click.Choice(['json', 'csv']), help='Export format')
@click.option('--output', help='Output file path')
def export(output_format, output):
    """Export feedback data"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        prompt_logger = PromptLogger()
        entries = prompt_logger.get_recent_entries(limit=1000)  # Export up to 1000 entries

        if not entries:
            console.print("[yellow]No data to export.[/yellow]")
            return

        # Prepare data
        export_data = []
        for entry in entries:
            export_data.append({
                'id': entry.id,
                'timestamp': entry.timestamp,
                'file': entry.file,
                'injection_type': entry.injection_type,
                'model': entry.model,
                'prompt': entry.prompt,
                'rating': entry.rating,
                'feedback': entry.feedback,
                'tags': entry.tags,
                'success': entry.success,
                'execution_time': entry.execution_time
            })

        # Export
        if not output:
            output = f"feedback_export.{output_format}"

        output_path = Path(output)

        if output_format == 'json':
            with open(output_path, 'w') as f:
                json.dump(export_data, f, indent=2)
        elif output_format == 'csv':
            import csv
            with open(output_path, 'w', newline='') as f:
                if export_data:
                    writer = csv.DictWriter(f, fieldnames=export_data[0].keys())
                    writer.writeheader()
                    writer.writerows(export_data)

        console.print(f"[green]✅ Exported {len(export_data)} entries to {output_path}[/green]")

    except Exception as e:
        console.print(f"[red]❌ Failed to export data: {e}[/red]")


@feedback.command()
def tune():
    """Open manual tuning panel for AI behavior configuration"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        tuning_panel = ManualTuningPanel()
        tuning_panel.interactive_tuning_session()
    except Exception as e:
        console.print(f"[red]❌ Failed to open tuning panel: {e}[/red]")


@feedback.command()
@click.option('--cycles', default=3, help='Number of test cycles to run')
@click.option('--scenarios', help='Comma-separated list of scenario names to test')
def autotest(cycles, scenarios):
    """Run auto-testing simulation to validate self-improving loop"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        # Initialize all components
        prompt_logger = PromptLogger()
        feedback_engine = FeedbackEngine(prompt_logger)
        auto_refine_engine = AutoRefineEngine(prompt_logger, feedback_engine)
        injection_memory = InjectionMemory(prompt_logger, feedback_engine)
        tuning_panel = ManualTuningPanel()

        auto_testing = AutoTestingEngine(
            prompt_logger, feedback_engine, auto_refine_engine,
            injection_memory, tuning_panel
        )

        # Parse scenarios if provided
        scenario_list = None
        if scenarios:
            scenario_list = [s.strip() for s in scenarios.split(',')]

        console.print(f"[bold]Starting auto-testing with {cycles} cycles...[/bold]")

        # Run test suite
        results = auto_testing.run_test_suite(cycles=cycles, scenarios=scenario_list)

        # Display results
        analysis = results['analysis']
        overall = analysis['overall']

        console.print(Panel(
            f"[bold]Auto-Testing Results[/bold]\n"
            f"Total tests: {overall['total_tests']}\n"
            f"Successful improvements: {overall['successful_improvements']}\n"
            f"Success rate: {overall['success_rate']:.1%}\n"
            f"Average improvement score: {overall['average_improvement_score']:.2f}\n"
            f"Learning effectiveness: {analysis['learning_effectiveness']:.2f}",
            title="[bold green]Test Summary[/bold green]"
        ))

        # Show scenario breakdown
        if analysis.get('by_scenario'):
            scenario_table = Table(show_header=True, header_style="bold blue")
            scenario_table.add_column("Scenario", style="cyan")
            scenario_table.add_column("Tests", style="green")
            scenario_table.add_column("Success Rate", style="yellow")
            scenario_table.add_column("Avg Improvement", style="magenta")

            for scenario_name, scenario_data in analysis['by_scenario'].items():
                scenario_table.add_row(
                    scenario_name,
                    str(scenario_data['tests']),
                    f"{scenario_data['success_rate']:.1%}",
                    f"{scenario_data['avg_improvement_score']:.2f}"
                )

            console.print(Panel(scenario_table, title="[bold]Scenario Performance[/bold]"))

    except Exception as e:
        console.print(f"[red]❌ Auto-testing failed: {e}[/red]")


@feedback.command()
@click.option('--file-path', required=True, help='File path for memory lookup')
@click.option('--injection-type', default='general', help='Type of injection')
@click.option('--tags', help='Comma-separated tags')
@click.option('--model', default='mistral', help='Model to use')
def memory(file_path, injection_type, tags, model):
    """Query injection memory for similar historical data"""
    if not FEEDBACK_AVAILABLE:
        return

    try:
        prompt_logger = PromptLogger()
        feedback_engine = FeedbackEngine(prompt_logger)
        injection_memory = InjectionMemory(prompt_logger, feedback_engine)

        # Create context
        context = InjectionContext(
            file_path=file_path,
            file_extension=Path(file_path).suffix[1:] if Path(file_path).suffix else 'py',
            injection_type=injection_type,
            tags=tags.split(',') if tags else [],
            model=model
        )

        # Query similar injections
        similar_injections = injection_memory.query_similar_injections(context)

        if not similar_injections:
            console.print("[yellow]No similar injections found in memory.[/yellow]")
            return

        # Display results
        memory_table = Table(show_header=True, header_style="bold magenta")
        memory_table.add_column("File", style="cyan", width=20)
        memory_table.add_column("Type", style="green", width=12)
        memory_table.add_column("Model", style="blue", width=12)
        memory_table.add_column("Rating", style="yellow", width=8)
        memory_table.add_column("Success", style="red", width=8)
        memory_table.add_column("Tags", style="dim", width=20)

        for entry in similar_injections[:10]:  # Show top 10
            rating_str = f"⭐{entry.rating}" if entry.rating else "—"
            success_str = "✅" if entry.success else "❌"
            tags_str = ", ".join(entry.tags[:3]) if entry.tags else "—"

            memory_table.add_row(
                entry.file[:18] + "..." if len(entry.file) > 18 else entry.file,
                entry.injection_type,
                entry.model,
                rating_str,
                success_str,
                tags_str
            )

        console.print(Panel(
            memory_table,
            title=f"[bold]Similar Injections Found ({len(similar_injections)})[/bold]"
        ))

        # Show memory stats
        memory_stats = injection_memory.get_memory_stats()
        console.print(f"\n[dim]Memory effectiveness: {memory_stats['memory_effectiveness']:.1%}[/dim]")

    except Exception as e:
        console.print(f"[red]❌ Failed to query memory: {e}[/red]")


if __name__ == '__main__':
    feedback()
