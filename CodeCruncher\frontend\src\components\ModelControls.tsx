import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Brain, Zap } from 'lucide-react';

interface ModelControlsProps {
  model: string;
  setModel: (model: string) => void;
  fallbackEnabled: boolean;
  setFallbackEnabled: (enabled: boolean) => void;
  className?: string;
}

export default function ModelControls({ 
  model, 
  setModel, 
  fallbackEnabled, 
  setFallbackEnabled,
  className = ""
}: ModelControlsProps) {
  const models = [
    { value: "auto", label: "🤖 Auto Selection", description: "Best model for the task" },
    { value: "mixtral", label: "🧠 Mixtral", description: "Mixture of experts model" },
    { value: "gemma", label: "💎 Gemma", description: "Google's efficient model" },
    { value: "llama3-8b", label: "🦙 LLaMA 3 8B", description: "Fast and efficient" },
    { value: "llama3-70b", label: "🦙 LLaMA 3 70B", description: "Large and powerful" },
    { value: "gpt-4", label: "🚀 GPT-4", description: "OpenAI's flagship model" }
  ];

  return (
    <Card className={className}>
      <CardContent className="pt-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          {/* Model Selector */}
          <div className="flex-1 min-w-48">
            <Label htmlFor="model-select" className="flex items-center gap-2 mb-2">
              <Brain className="h-4 w-4" />
              <span>AI Model:</span>
            </Label>
            <Select value={model} onValueChange={setModel}>
              <SelectTrigger id="model-select">
                <SelectValue placeholder="Select AI Model" />
              </SelectTrigger>
              <SelectContent>
                {models.map((modelOption) => (
                  <SelectItem key={modelOption.value} value={modelOption.value}>
                    <div className="flex flex-col">
                      <span>{modelOption.label}</span>
                      <span className="text-xs text-gray-500">{modelOption.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Fallback Toggle */}
          <div className="flex items-center space-x-2">
            <Switch 
              id="fallback-toggle"
              checked={fallbackEnabled} 
              onCheckedChange={setFallbackEnabled}
            />
            <Label htmlFor="fallback-toggle" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              <span>Enable Fallback</span>
            </Label>
          </div>
        </div>

        {/* Model Info Display */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm">
            <div className="flex items-center justify-between">
              <span className="font-medium">Selected Model:</span>
              <span className="text-blue-600">
                {models.find(m => m.value === model)?.label || model}
              </span>
            </div>
            <div className="flex items-center justify-between mt-1">
              <span className="font-medium">Fallback Routing:</span>
              <span className={fallbackEnabled ? "text-green-600" : "text-gray-500"}>
                {fallbackEnabled ? "Enabled" : "Disabled"}
              </span>
            </div>
            {fallbackEnabled && (
              <div className="mt-2 text-xs text-gray-600">
                💡 If the selected model fails, the system will automatically try alternative models
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
