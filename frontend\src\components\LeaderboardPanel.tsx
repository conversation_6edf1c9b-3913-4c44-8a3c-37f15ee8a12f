import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Trophy, Medal, Award, TrendingUp, TrendingDown, Download,
  RefreshCw, Filter, Clock, ThumbsUp, ThumbsDown, Meh
} from 'lucide-react';

interface LeaderboardEntry {
  rank: number;
  model: string;
  metric_value: number;
  total_feedback: number;
  satisfaction_rate: number;
  performance_grade: string;
  up_pct: number;  // Percentage of upvotes
  meh_pct: number; // Percentage of neutral/meh votes
  down_pct: number; // Percentage of downvotes
  positive_feedback_count?: number;
  negative_feedback_count?: number;
  neutral_feedback_count?: number;
  avg_rating?: number;
  avg_latency_ms?: number;
  retry_success_rate?: number;
  intent_distribution?: Record<string, number>;
}

interface FilterOptions {
  metric: string;
  intent: string;
  grade: string;
  minFeedback: number;
}

const LeaderboardPanel: React.FC = () => {
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [filters, setFilters] = useState<FilterOptions>({
    metric: 'satisfaction_rate',
    intent: 'all',
    grade: 'all',
    minFeedback: 0
  });

  const availableMetrics = [
    { value: 'satisfaction_rate', label: 'Satisfaction Rate' },
    { value: 'avg_rating', label: 'Average Rating' },
    { value: 'success_rate', label: 'Success Rate' },
    { value: 'retry_success_rate', label: 'Retry Success Rate' },
    { value: 'avg_latency_ms', label: 'Average Latency (lower is better)' }
  ];

  const availableIntents = [
    { value: 'all', label: 'All Intents' },
    { value: 'refactor', label: 'Refactor' },
    { value: 'optimize', label: 'Optimize' },
    { value: 'debug', label: 'Debug' },
    { value: 'document', label: 'Document' },
    { value: 'test', label: 'Test' },
    { value: 'feature', label: 'Feature' }
  ];

  const availableGrades = [
    { value: 'all', label: 'All Grades' },
    { value: 'A+', label: 'A+' },
    { value: 'A', label: 'A' },
    { value: 'B+', label: 'B+' },
    { value: 'B', label: 'B' },
    { value: 'C', label: 'C' },
    { value: 'D', label: 'D' }
  ];

  const fetchLeaderboard = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        metric: filters.metric,
        limit: '20'
      });

      const response = await fetch(`/api/model_leaderboard?${params}`);
      if (!response.ok) throw new Error('Failed to fetch leaderboard');

      let data = await response.json();

      // Apply client-side filters
      if (filters.intent !== 'all') {
        data = data.filter((entry: LeaderboardEntry) =>
          entry.intent_distribution && entry.intent_distribution[filters.intent] > 0
        );
      }

      if (filters.grade !== 'all') {
        data = data.filter((entry: LeaderboardEntry) =>
          entry.performance_grade === filters.grade
        );
      }

      if (filters.minFeedback > 0) {
        data = data.filter((entry: LeaderboardEntry) =>
          entry.total_feedback >= filters.minFeedback
        );
      }

      // Re-rank after filtering
      data.forEach((entry: LeaderboardEntry, index: number) => {
        entry.rank = index + 1;
      });

      setLeaderboard(data);
      setLastUpdated(new Date());
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    fetchLeaderboard();

    let interval: NodeJS.Timeout | null = null;

    if (autoRefresh) {
      interval = setInterval(() => {
        console.log('Auto-refreshing leaderboard...');
        fetchLeaderboard();
      }, 30000); // 30 seconds
    }

    // Cleanup interval on unmount or when autoRefresh changes
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [fetchLeaderboard, autoRefresh]);

  const getRankBadge = (rank: number) => {
    switch (rank) {
      case 1:
        return (
          <Badge className="bg-yellow-500 text-white flex items-center gap-1">
            <Trophy className="h-3 w-3" />
            Gold
          </Badge>
        );
      case 2:
        return (
          <Badge className="bg-gray-400 text-white flex items-center gap-1">
            <Medal className="h-3 w-3" />
            Silver
          </Badge>
        );
      case 3:
        return (
          <Badge className="bg-amber-600 text-white flex items-center gap-1">
            <Award className="h-3 w-3" />
            Bronze
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="flex items-center gap-1">
            #{rank}
          </Badge>
        );
    }
  };

  const getScoreColor = (score: number) => {
    if (score < 0.5) return 'text-red-600';
    if (score >= 0.8) return 'text-green-600';
    return 'text-gray-700';
  };

  const getScoreProgress = (score: number, metric: string) => {
    // For latency, lower is better, so invert the progress
    if (metric === 'avg_latency_ms') {
      const maxLatency = 5000; // 5 seconds as max
      const normalizedScore = Math.max(0, (maxLatency - score) / maxLatency * 100);
      return Math.min(100, normalizedScore);
    }

    // For other metrics, higher is better
    if (metric.includes('rate') || metric.includes('satisfaction')) {
      return score; // Already in percentage
    }

    return score * 100; // Convert to percentage
  };

  const RatingBreakdownBar: React.FC<{ entry: LeaderboardEntry }> = ({ entry }) => {
    const { up_pct, meh_pct, down_pct } = entry;

    return (
      <div className="w-full">
        <div className="flex h-3 rounded-full overflow-hidden bg-gray-200 mb-1">
          {up_pct > 0 && (
            <div
              className="bg-green-400 flex items-center justify-center"
              style={{ width: `${up_pct}%` }}
              title={`${up_pct}% positive`}
            >
              {up_pct > 20 && <ThumbsUp className="h-2 w-2 text-white" />}
            </div>
          )}
          {meh_pct > 0 && (
            <div
              className="bg-yellow-300 flex items-center justify-center"
              style={{ width: `${meh_pct}%` }}
              title={`${meh_pct}% neutral`}
            >
              {meh_pct > 20 && <Meh className="h-2 w-2 text-white" />}
            </div>
          )}
          {down_pct > 0 && (
            <div
              className="bg-red-400 flex items-center justify-center"
              style={{ width: `${down_pct}%` }}
              title={`${down_pct}% negative`}
            >
              {down_pct > 20 && <ThumbsDown className="h-2 w-2 text-white" />}
            </div>
          )}
        </div>
        <div className="flex justify-between text-xs text-gray-500">
          <span className="flex items-center gap-1">
            <ThumbsUp className="h-3 w-3 text-green-500" />
            {up_pct.toFixed(1)}%
          </span>
          <span className="flex items-center gap-1">
            <Meh className="h-3 w-3 text-yellow-500" />
            {meh_pct.toFixed(1)}%
          </span>
          <span className="flex items-center gap-1">
            <ThumbsDown className="h-3 w-3 text-red-500" />
            {down_pct.toFixed(1)}%
          </span>
        </div>
      </div>
    );
  };

  const exportData = (format: 'csv' | 'json') => {
    const dataToExport = leaderboard.map(entry => ({
      rank: entry.rank,
      model: entry.model,
      metric_value: entry.metric_value,
      satisfaction_rate: entry.satisfaction_rate,
      performance_grade: entry.performance_grade,
      total_feedback: entry.total_feedback,
      avg_rating: entry.avg_rating,
      avg_latency_ms: entry.avg_latency_ms,
      retry_success_rate: entry.retry_success_rate
    }));

    if (format === 'json') {
      const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `leaderboard_${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } else {
      const headers = Object.keys(dataToExport[0] || {});
      const csvContent = [
        headers.join(','),
        ...dataToExport.map(row => headers.map(header => row[header as keyof typeof row]).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `leaderboard_${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      URL.revokeObjectURL(url);
    }
  };

  const formatMetricValue = (value: number, metric: string) => {
    if (metric === 'avg_latency_ms') {
      return value < 1000 ? `${Math.round(value)}ms` : `${(value / 1000).toFixed(1)}s`;
    }
    if (metric.includes('rate') || metric.includes('satisfaction')) {
      return `${value.toFixed(1)}%`;
    }
    return value.toFixed(3);
  };

  if (loading && leaderboard.length === 0) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading leaderboard...
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              Model Performance Leaderboard
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              {lastUpdated && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {lastUpdated.toLocaleTimeString()}
                </div>
              )}
              {autoRefresh && (
                <div className="flex items-center gap-1 text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs">Auto-refreshing every 30s</span>
                </div>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={autoRefresh ? 'bg-green-50 border-green-200 text-green-700' : ''}
              >
                <RefreshCw className={`h-4 w-4 mr-1 ${autoRefresh && loading ? 'animate-spin' : ''}`} />
                Auto-refresh {autoRefresh ? 'ON' : 'OFF'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="text-sm font-medium mb-1 block">Metric</label>
              <Select value={filters.metric} onValueChange={(value) => setFilters({...filters, metric: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableMetrics.map(metric => (
                    <SelectItem key={metric.value} value={metric.value}>
                      {metric.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Intent</label>
              <Select value={filters.intent} onValueChange={(value) => setFilters({...filters, intent: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableIntents.map(intent => (
                    <SelectItem key={intent.value} value={intent.value}>
                      {intent.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Grade</label>
              <Select value={filters.grade} onValueChange={(value) => setFilters({...filters, grade: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {availableGrades.map(grade => (
                    <SelectItem key={grade.value} value={grade.value}>
                      {grade.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-1 block">Min Feedback</label>
              <input
                type="number"
                min="0"
                value={filters.minFeedback}
                onChange={(e) => setFilters({...filters, minFeedback: Number(e.target.value)})}
                className="w-full border rounded px-2 py-1 text-sm"
                placeholder="0"
              />
            </div>
          </div>

          {/* Export Buttons */}
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => exportData('csv')}>
              <Download className="h-4 w-4 mr-1" />
              Export CSV
            </Button>
            <Button variant="outline" size="sm" onClick={() => exportData('json')}>
              <Download className="h-4 w-4 mr-1" />
              Export JSON
            </Button>
            <Button variant="outline" size="sm" onClick={fetchLeaderboard}>
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="text-red-600">Error: {error}</div>
          </CardContent>
        </Card>
      )}

      {/* Leaderboard */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Rankings by {availableMetrics.find(m => m.value === filters.metric)?.label}
            <Badge variant="outline">{leaderboard.length} models</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {leaderboard.map((entry) => {
              const progressValue = getScoreProgress(entry.metric_value, filters.metric);

              return (
                <div key={entry.model} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      {getRankBadge(entry.rank)}
                      <div>
                        <h3 className="font-semibold text-lg">{entry.model}</h3>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Badge variant="outline">{entry.performance_grade}</Badge>
                          <span>{entry.total_feedback} feedback</span>
                        </div>
                      </div>
                    </div>

                    <div className="text-right">
                      <div className={`text-2xl font-bold ${getScoreColor(entry.metric_value / (filters.metric.includes('rate') || filters.metric.includes('satisfaction') ? 1 : 100))}`}>
                        {formatMetricValue(entry.metric_value, filters.metric)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {filters.metric === 'avg_latency_ms' ? 'Lower is better' : 'Score'}
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-3">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>Performance</span>
                      <span>{progressValue.toFixed(1)}%</span>
                    </div>
                    <Progress
                      value={progressValue}
                      className="h-2"
                    />
                  </div>

                  {/* Rating Distribution Bar */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-600">Rating Distribution</div>
                    <RatingBreakdownBar entry={entry} />
                  </div>

                  {/* Additional Metrics */}
                  {entry.avg_rating !== undefined && (
                    <div className="mt-3 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <div className="text-gray-500">Avg Rating</div>
                        <div className="font-medium">{entry.avg_rating.toFixed(2)}</div>
                      </div>
                      {entry.avg_latency_ms !== undefined && (
                        <div>
                          <div className="text-gray-500">Latency</div>
                          <div className="font-medium">{formatMetricValue(entry.avg_latency_ms, 'avg_latency_ms')}</div>
                        </div>
                      )}
                      {entry.retry_success_rate !== undefined && (
                        <div>
                          <div className="text-gray-500">Retry Success</div>
                          <div className="font-medium">{(entry.retry_success_rate * 100).toFixed(1)}%</div>
                        </div>
                      )}
                      <div>
                        <div className="text-gray-500">Satisfaction</div>
                        <div className="font-medium">{entry.satisfaction_rate.toFixed(1)}%</div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {leaderboard.length === 0 && !loading && (
              <div className="text-center py-8 text-gray-500">
                No models found matching the current filters.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LeaderboardPanel;
