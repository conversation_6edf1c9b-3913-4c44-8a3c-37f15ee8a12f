"""
AI Engine - Unified AI Model Interface with Smart Routing
"""

import asyncio
import os
import time
import traceback
from rich import print as rich_print
from dotenv import load_dotenv
from .providers import mistral, groq
from .logger import log
from codecrusher.cache_manager import save_result, get_cached_result

# Load environment variables from .env file
load_dotenv()

# Model tier order: (name, id)
MODEL_TIERS = [
    # SURGICAL FIX: Updated model list with correct IDs for escalation
    ("LLaMA 3 8B", "llama3-8b-8192"),
    ("Mixtral 8x7B", "mixtral-8x7b-32768"),  # Added for escalation
    ("LLaMA 3 70B", "llama3-70b-8192"),
    ("LLaMA 3.3 70B", "llama-3.3-70b-versatile"),
    # ("OpenAI GPT-4", "gpt-4"),  # optional later
]

# --- CONTEXT-AWARE AI ORCHESTRATION SYSTEM ---

class ContextType:
    """Enumeration of supported context types"""
    HTML = "html"
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    CSS = "css"
    MARKDOWN = "markdown"
    JSON = "json"
    YAML = "yaml"
    UNKNOWN = "unknown"

class QualityThreshold:
    """Quality score thresholds for different contexts"""
    EXCELLENT = 85
    GOOD = 70
    ACCEPTABLE = 50
    POOR = 30

# --- Score Output ---
class ContextAwareScorer:
    """Advanced context-aware scoring system for AI outputs"""

    CONTEXT_WEIGHTS = {
        ContextType.HTML: {
            'structure': 0.3,
            'semantics': 0.25,
            'responsiveness': 0.2,
            'accessibility': 0.15,
            'completeness': 0.1,
        },
        ContextType.PYTHON: {
            'syntax': 0.25,
            'structure': 0.25,
            'best_practices': 0.2,
            'documentation': 0.15,
            'efficiency': 0.15,
        },
        ContextType.JAVASCRIPT: {
            'syntax': 0.25,
            'modern_features': 0.2,
            'structure': 0.2,
            'error_handling': 0.15,
            'performance': 0.2,
        },
        ContextType.CSS: {
            'syntax': 0.2,
            'layout_techniques': 0.3,
            'responsiveness': 0.25,
            'organization': 0.15,
            'efficiency': 0.1,
        },
    }

    QUALITY_INDICATORS = {
        ContextType.HTML: {
            'excellent': [
                r'<!DOCTYPE\s+html>',
                r'<html[^>]*lang=',
                r'<meta[^>]*viewport[^>]*>',
                r'<section[^>]*>',
                r'<header[^>]*>',
                r'<main[^>]*>',
                r'<footer[^>]*>',
                r'class="[^"]*"',
                r'aria-[a-z]+="[^"]*"',
            ],
            'good': [
                r'<div[^>]*class="[^"]*"[^>]*>',
                r'<h[1-6][^>]*>',
                r'<p[^>]*>',
                r'<button[^>]*>',
                r'<form[^>]*>',
            ],
            'issues': [
                r'<div[^>]*>\s*<div[^>]*>',  # Excessive div nesting
                r'style="[^"]*"',  # Inline styles
                r'<br\s*/?>',  # Line breaks for spacing
            ],
        },
        ContextType.PYTHON: {
            'excellent': [
                r'def\s+\w+\([^)]*\)\s*->\s*\w+:',  # Type hints
                r'"""[^"]*"""',  # Docstrings
                r'class\s+\w+\([^)]*\):',
                r'from\s+typing\s+import',
                r'try:\s*\n.*except\s+\w+:',  # Error handling
            ],
            'good': [
                r'def\s+\w+\([^)]*\):',
                r'if\s+__name__\s*==\s*["\']__main__["\']:',
                r'import\s+\w+',
                r'class\s+\w+:',
            ],
            'issues': [
                r'print\([^)]*\)',  # Debug prints
                r'pass\s*$',  # Empty pass statements
                r'#\s*TODO',  # TODO comments
            ],
        },
        ContextType.JAVASCRIPT: {
            'excellent': [
                r'const\s+\w+\s*=',
                r'let\s+\w+\s*=',
                r'=>',  # Arrow functions
                r'async\s+function',
                r'await\s+',
                r'try\s*\{.*catch\s*\(',  # Error handling
            ],
            'good': [
                r'function\s+\w+\s*\(',
                r'document\.',
                r'addEventListener\(',
                r'querySelector\(',
            ],
            'issues': [
                r'var\s+\w+\s*=',  # Old var declarations
                r'console\.log\(',  # Debug logs
                r'alert\(',  # Alert usage
            ],
        },
    }

    @classmethod
    def score_by_context(cls, output: str, context: ContextType) -> dict:
        """
        Score output based on detected context with detailed breakdown

        Args:
            output (str): AI output to score
            context (ContextType): Detected context type

        Returns:
            dict: Detailed scoring breakdown
        """
        if not output:
            return {'total_score': 0, 'breakdown': {}, 'issues': ['Empty output']}

        # Base scoring using legacy system
        base_score = score_output_legacy(output)

        # Context-specific scoring
        context_score = 0
        breakdown = {}
        issues = []

        if context in cls.QUALITY_INDICATORS:
            indicators = cls.QUALITY_INDICATORS[context]

            # Count excellent indicators
            excellent_count = 0
            for pattern in indicators.get('excellent', []):
                import re
                if re.search(pattern, output, re.IGNORECASE | re.MULTILINE):
                    excellent_count += 1

            # Count good indicators
            good_count = 0
            for pattern in indicators.get('good', []):
                import re
                if re.search(pattern, output, re.IGNORECASE | re.MULTILINE):
                    good_count += 1

            # Count issues
            issue_count = 0
            for pattern in indicators.get('issues', []):
                import re
                if re.search(pattern, output, re.IGNORECASE | re.MULTILINE):
                    issue_count += 1
                    issues.append(f"Found issue pattern: {pattern}")

            # Calculate context score
            context_score = (excellent_count * 15) + (good_count * 8) - (issue_count * 5)

            breakdown = {
                'base_score': base_score,
                'context_score': context_score,
                'excellent_indicators': excellent_count,
                'good_indicators': good_count,
                'issues_found': issue_count,
            }

        # Combine scores
        total_score = max(0, base_score + context_score)

        return {
            'total_score': total_score,
            'breakdown': breakdown,
            'issues': issues,
            'context': context,
        }

def score_output(output: str, context: ContextType = None, file_path: str = None) -> int:
    """
    Enhanced scoring function with context awareness

    Args:
        output (str): AI output to score
        context (ContextType): Optional context type
        file_path (str): Optional file path for context detection

    Returns:
        int: Quality score
    """
    # Detect context if not provided
    if context is None:
        context = ContextDetector.detect_context(file_path=file_path, content=output)

    # Use context-aware scoring
    result = ContextAwareScorer.score_by_context(output, context)
    return result['total_score']

def score_output_legacy(output: str) -> int:
    """
    Scores AI output based on presence of expected code elements and quality indicators.

    Enhanced with:
    - Better detection of code-like structures
    - Penalties for hallucinated or explanatory responses
    - Type hint detection
    - Logical structure scoring
    - Detection of question-like responses

    Returns:
        int: Score value (higher is better)
    """
    if not output:
        return -100

    score = 0
    lines = output.lower().splitlines()

    # Basic code keywords with their point values
    keywords = {
        # Core code structures (boosters)
        "def ": 15,
        "class ": 15,
        "return ": 10,
        "import ": 5,
        "from ": 5,

        # Comments and documentation
        "#": 2,
        "\"\"\"": 3,
        "'''": 3,

        # Control flow
        "if ": 3,
        "elif ": 2,
        "else:": 2,
        "for ": 3,
        "while ": 3,
        "break": 1,
        "continue": 1,

        # Error handling
        "try:": 3,
        "except": 2,
        "finally:": 2,
        "raise ": 2,

        # Negative indicators
        "todo": -5,
        "fixme": -10,
        "pass": -2
    }

    # Score boosters from prompt 2
    has_def_class_import = False
    has_type_hints = False
    has_logical_structure = False
    # SURGICAL FIX: HTML-specific scoring flags
    has_html_structure = False
    has_css_properties = False

    # Score penalties from prompt 2
    has_paragraph_text = False
    has_question_marks = False
    has_you_please_phrasing = False
    has_long_sentences = False

    # Hallucination indicators (prompt 1)
    hallucination_phrases = [
        "it seems like you're trying",
        "could you please clarify",
        "tags are often used",
        "i'd be happy to help",
        "i'm not sure what you're asking",
        "i don't have enough information",
        "can you provide more details",
        "i need more context",
        "without more information",
        "i'd need to know more"
    ]

    # Check for hallucination phrases
    output_lower = output.lower()
    for phrase in hallucination_phrases:
        if phrase in output_lower:
            score -= 50  # Heavy penalty for hallucination indicators
            log(f"Detected hallucination phrase: '{phrase}'", "warning")

    # Process each line
    indentation_levels = set()
    for line in lines:
        # Count indentation levels for structure detection
        if line.strip():
            indent = len(line) - len(line.lstrip())
            indentation_levels.add(indent)

        # Check for type hints (Score booster)
        if "->" in line or ":" in line and any(typ in line for typ in ["str", "int", "float", "bool", "list", "dict", "tuple", "set", "any", "none", "optional"]):
            has_type_hints = True

        # Check for def/class/import lines (Score booster)
        if any(key in line for key in ["def ", "class ", "import ", "from "]):
            has_def_class_import = True

        # SURGICAL FIX: HTML-specific score boosters
        # Check for HTML structure elements (Score booster)
        if any(key in line.lower() for key in ["<section", "<div", "<h1", "<h2", "<h3", "<button", "<form"]):
            has_html_structure = True

        # Check for CSS properties (Score booster)
        if any(key in line for key in ["class=", "style=", "background", "color", "margin", "padding", "flex", "grid"]):
            has_css_properties = True

        # Check for question marks (Score penalty)
        if "?" in line:
            has_question_marks = True

        # Check for you/please phrasing (Score penalty)
        if " you " in line or "please" in line:
            has_you_please_phrasing = True

        # Check for long sentences (Score penalty)
        words = line.split()
        if len(words) > 20 and not any(key in line for key in ["def ", "class ", "import ", "#", "\"\"\"", "'''"]):
            has_long_sentences = True

        # Apply keyword scoring
        for key, value in keywords.items():
            if key in line:
                score += value

    # Check for logical structure based on indentation levels
    if len(indentation_levels) >= 3:
        has_logical_structure = True

    # SURGICAL FIX: Enhanced Verbose Rejection Filter
    # Check for paragraph-style text with improved detection
    text_block_count = 0
    current_block_length = 0
    natural_language_tokens = 0
    total_tokens = 0

    for line in lines:
        if line.strip():
            current_block_length += 1

            # Count natural language vs code tokens
            words = line.split()
            total_tokens += len(words)

            # Count natural language indicators
            natural_indicators = ["the", "and", "or", "but", "with", "for", "this", "that", "these", "those", "a", "an"]
            natural_language_tokens += sum(1 for word in words if word.lower() in natural_indicators)
        else:
            if current_block_length > 3:
                text_block_count += 1
            current_block_length = 0

    # Check the last block
    if current_block_length > 3:
        text_block_count += 1

    # Enhanced paragraph detection
    if text_block_count >= 2:
        has_paragraph_text = True

    # If >50% natural language tokens, treat as verbose/paragraph text
    if total_tokens > 0 and (natural_language_tokens / total_tokens) > 0.5:
        has_paragraph_text = True
        score -= 15  # Additional penalty for high natural language ratio
        log("Score penalty: High natural language token ratio (-15)", "debug")

    # Apply score boosters (from prompt 2)
    if has_def_class_import:
        score += 3
        log("Score booster: Contains def/class/import lines (+3)", "debug")

    if has_type_hints:
        score += 2
        log("Score booster: Contains type hints (+2)", "debug")

    if has_logical_structure:
        score += 2
        log("Score booster: Contains logical structure/indentation (+2)", "debug")

    # SURGICAL FIX: HTML-specific score boosters
    if has_html_structure:
        score += 5
        log("Score booster: Contains HTML structure elements (+5)", "debug")

    if has_css_properties:
        score += 3
        log("Score booster: Contains CSS properties (+3)", "debug")

    # Apply score penalties (from prompt 2)
    if has_paragraph_text:
        score -= 5
        log("Score penalty: Contains paragraph-style text blocks (-5)", "debug")

    if has_question_marks or has_you_please_phrasing:
        score -= 3
        log("Score penalty: Contains question marks or you/please phrasing (-3)", "debug")

    if has_long_sentences:
        score -= 2
        log("Score penalty: Contains long non-code sentences (-2)", "debug")

    # Penalize overly short or verbose responses
    if len(lines) < 3:
        score -= 20
        log("Score penalty: Response too short (-20)", "debug")
    elif len(lines) > 50:
        score -= 10
        log("Score penalty: Response too verbose (-10)", "debug")

    # Add a small bonus for longer responses (but not too much to avoid verbosity)
    length_bonus = min(len(output) // 50, 10)  # Max 10 points for length
    score += length_bonus

    log(f"Final score: {score}", "debug")

    return score

class ContextDetector:
    """Intelligent context detection based on file extension and content"""

    EXTENSION_MAP = {
        '.html': ContextType.HTML,
        '.htm': ContextType.HTML,
        '.py': ContextType.PYTHON,
        '.js': ContextType.JAVASCRIPT,
        '.jsx': ContextType.JAVASCRIPT,
        '.ts': ContextType.JAVASCRIPT,
        '.tsx': ContextType.JAVASCRIPT,
        '.css': ContextType.CSS,
        '.scss': ContextType.CSS,
        '.sass': ContextType.CSS,
        '.md': ContextType.MARKDOWN,
        '.markdown': ContextType.MARKDOWN,
        '.json': ContextType.JSON,
        '.yml': ContextType.YAML,
        '.yaml': ContextType.YAML,
    }

    CONTENT_PATTERNS = {
        ContextType.HTML: [
            r'<!DOCTYPE\s+html>',
            r'<html[^>]*>',
            r'<head[^>]*>',
            r'<body[^>]*>',
            r'<div[^>]*>',
            r'<section[^>]*>',
        ],
        ContextType.PYTHON: [
            r'def\s+\w+\s*\(',
            r'class\s+\w+\s*\(',
            r'import\s+\w+',
            r'from\s+\w+\s+import',
            r'if\s+__name__\s*==\s*["\']__main__["\']',
        ],
        ContextType.JAVASCRIPT: [
            r'function\s+\w+\s*\(',
            r'const\s+\w+\s*=',
            r'let\s+\w+\s*=',
            r'var\s+\w+\s*=',
            r'=>',
            r'document\.',
            r'window\.',
        ],
        ContextType.CSS: [
            r'\.\w+\s*\{',
            r'#\w+\s*\{',
            r'@media\s*\(',
            r'@import\s+',
            r':\s*\w+;',
        ],
    }

    @classmethod
    def detect_context(cls, file_path: str = None, content: str = None, prompt: str = None) -> ContextType:
        """
        Detect context from file extension, content, and prompt

        Args:
            file_path (str): Path to the file
            content (str): File content
            prompt (str): User prompt

        Returns:
            ContextType: Detected context type
        """
        # Primary detection: file extension
        if file_path:
            import os
            ext = os.path.splitext(file_path)[1].lower()
            if ext in cls.EXTENSION_MAP:
                return cls.EXTENSION_MAP[ext]

        # Secondary detection: content patterns
        if content:
            import re
            for context_type, patterns in cls.CONTENT_PATTERNS.items():
                for pattern in patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        return context_type

        # Tertiary detection: prompt analysis
        if prompt:
            prompt_lower = prompt.lower()
            if any(keyword in prompt_lower for keyword in ['html', 'webpage', 'landing', 'website', 'responsive']):
                return ContextType.HTML
            elif any(keyword in prompt_lower for keyword in ['python', 'function', 'class', 'def', 'import']):
                return ContextType.PYTHON
            elif any(keyword in prompt_lower for keyword in ['javascript', 'js', 'react', 'component', 'dom']):
                return ContextType.JAVASCRIPT
            elif any(keyword in prompt_lower for keyword in ['css', 'style', 'tailwind', 'bootstrap']):
                return ContextType.CSS

        return ContextType.UNKNOWN

class PromptTemplateEngine:
    """Context-aware prompt templates for optimal model performance"""

    TEMPLATES = {
        ContextType.HTML: {
            'prefix': "Generate clean, semantic HTML5 code with modern best practices. ",
            'suffix': " Use proper indentation, semantic tags, and ensure mobile responsiveness. Output only valid HTML without explanations.",
            'quality_indicators': ['semantic tags', 'proper structure', 'accessibility', 'responsiveness'],
        },
        ContextType.PYTHON: {
            'prefix': "Write clean, efficient Python code following PEP 8 standards. ",
            'suffix': " Include proper type hints, docstrings, and error handling. Output only Python code without explanations.",
            'quality_indicators': ['type hints', 'docstrings', 'error handling', 'clean structure'],
        },
        ContextType.JAVASCRIPT: {
            'prefix': "Create modern JavaScript code using ES6+ features and best practices. ",
            'suffix': " Use const/let appropriately, arrow functions where suitable, and proper error handling. Output only JavaScript code without explanations.",
            'quality_indicators': ['modern syntax', 'proper scoping', 'error handling', 'clean functions'],
        },
        ContextType.CSS: {
            'prefix': "Generate clean, efficient CSS with modern layout techniques. ",
            'suffix': " Use flexbox/grid for layouts, proper naming conventions, and responsive design. Output only CSS without explanations.",
            'quality_indicators': ['modern layout', 'responsive design', 'clean selectors', 'efficient rules'],
        },
        ContextType.MARKDOWN: {
            'prefix': "Create well-structured Markdown content with proper formatting. ",
            'suffix': " Use appropriate headers, lists, links, and formatting. Output only Markdown without explanations.",
            'quality_indicators': ['proper headers', 'clear structure', 'good formatting', 'readable content'],
        },
    }

    @classmethod
    def enhance_prompt(cls, original_prompt: str, context: ContextType) -> str:
        """
        Enhance prompt based on detected context

        Args:
            original_prompt (str): Original user prompt
            context (ContextType): Detected context type

        Returns:
            str: Enhanced prompt optimized for the context
        """
        if context not in cls.TEMPLATES:
            return original_prompt

        template = cls.TEMPLATES[context]
        enhanced = f"{template['prefix']}{original_prompt}{template['suffix']}"

        return enhanced

    @classmethod
    def create_refinement_prompt(cls, original_prompt: str, previous_output: str, context: ContextType, issues: list) -> str:
        """
        Create a refinement prompt for improving previous output

        Args:
            original_prompt (str): Original user prompt
            previous_output (str): Previous model output
            context (ContextType): Context type
            issues (list): List of identified issues

        Returns:
            str: Refinement prompt
        """
        if context not in cls.TEMPLATES:
            context = ContextType.UNKNOWN

        template = cls.TEMPLATES.get(context, cls.TEMPLATES[ContextType.HTML])

        issues_text = ", ".join(issues) if issues else "general quality improvements"

        refinement_prompt = f"""
{template['prefix']}Improve the following code by addressing these issues: {issues_text}.

Original request: {original_prompt}

Previous output to improve:
{previous_output}

{template['suffix']}
        """.strip()

        return refinement_prompt

# --- Structured Prompt Detection (Legacy Support) ---
def is_structured_prompt(prompt: str) -> bool:
    """
    Legacy function - now uses ContextDetector
    """
    context = ContextDetector.detect_context(prompt=prompt)
    return context != ContextType.UNKNOWN

class IntelligentModelOrchestrator:
    """Advanced model orchestration with context-aware routing and refinement"""

    # Context-specific model preferences
    CONTEXT_MODEL_PREFERENCES = {
        ContextType.HTML: [
            ("llama-3.3-70b-versatile", 0.9),  # Best for HTML generation
            ("llama3-70b-8192", 0.8),
            ("llama3-8b-8192", 0.6),
        ],
        ContextType.PYTHON: [
            ("llama3-70b-8192", 0.9),  # Best for Python
            ("llama-3.3-70b-versatile", 0.8),
            ("llama3-8b-8192", 0.7),
        ],
        ContextType.JAVASCRIPT: [
            ("llama-3.3-70b-versatile", 0.85),
            ("llama3-70b-8192", 0.8),
            ("llama3-8b-8192", 0.6),
        ],
        ContextType.CSS: [
            ("llama-3.3-70b-versatile", 0.9),
            ("llama3-70b-8192", 0.7),
            ("llama3-8b-8192", 0.5),
        ],
    }

    @classmethod
    def select_optimal_model(cls, context: ContextType, previous_failures: list = None) -> str:
        """
        Select the optimal model based on context and previous failures

        Args:
            context (ContextType): Detected context type
            previous_failures (list): List of models that have failed

        Returns:
            str: Selected model ID
        """
        previous_failures = previous_failures or []

        # Get context-specific preferences
        preferences = cls.CONTEXT_MODEL_PREFERENCES.get(context, [])

        # If no context-specific preferences, use default order
        if not preferences:
            preferences = [(model_id, 1.0) for _, model_id in MODEL_TIERS]

        # Filter out failed models and select best available
        for model_id, confidence in preferences:
            if model_id not in previous_failures:
                log(f"Selected model {model_id} for context {context} (confidence: {confidence})", "info")
                return model_id

        # Fallback to first available model
        for _, model_id in MODEL_TIERS:
            if model_id not in previous_failures:
                log(f"Fallback to model {model_id}", "warning")
                return model_id

        # Last resort - return first model even if it failed before
        return MODEL_TIERS[0][1]

    @classmethod
    def should_escalate(cls, score: int, context: ContextType, attempt_count: int) -> bool:
        """
        Determine if escalation is needed based on score, context, and attempt count

        Args:
            score (int): Current output score
            context (ContextType): Context type
            attempt_count (int): Number of attempts made

        Returns:
            bool: Whether to escalate to next model
        """
        # Context-specific thresholds
        thresholds = {
            ContextType.HTML: QualityThreshold.GOOD,  # Higher standard for HTML
            ContextType.PYTHON: QualityThreshold.ACCEPTABLE,
            ContextType.JAVASCRIPT: QualityThreshold.ACCEPTABLE,
            ContextType.CSS: QualityThreshold.GOOD,
        }

        threshold = thresholds.get(context, QualityThreshold.ACCEPTABLE)

        # Don't escalate if we've tried too many times
        if attempt_count >= 3:
            return False

        # Escalate if score is below threshold
        return score < threshold

    @classmethod
    async def generate_with_refinement(cls, prompt: str, context: ContextType, tag: str = "",
                                     file_path: str = None, max_attempts: int = 3) -> tuple:
        """
        Generate output with intelligent refinement and escalation

        Args:
            prompt (str): Original prompt
            context (ContextType): Detected context
            tag (str): Tag name
            file_path (str): File path for context
            max_attempts (int): Maximum refinement attempts

        Returns:
            tuple: (best_output, model_used, final_score)
        """
        attempts = []
        failed_models = []

        for attempt in range(max_attempts):
            # Select optimal model for this attempt
            model_id = cls.select_optimal_model(context, failed_models)

            # Enhance prompt based on context and previous attempts
            if attempt == 0:
                enhanced_prompt = PromptTemplateEngine.enhance_prompt(prompt, context)
            else:
                # Create refinement prompt using previous output
                previous_output = attempts[-1]['output']
                issues = attempts[-1]['issues']
                enhanced_prompt = PromptTemplateEngine.create_refinement_prompt(
                    prompt, previous_output, context, issues
                )

            log(f"Attempt {attempt + 1}: Using model {model_id}", "info")

            try:
                # Generate output
                import asyncio
                output = await asyncio.wait_for(
                    groq.generate_async(enhanced_prompt, tag=tag, model_id=model_id),
                    timeout=30
                )

                # Clean the output to remove markdown artifacts and explanations
                cleaned_output = clean_ai_output(output)

                # Score the cleaned output with context awareness
                score_result = ContextAwareScorer.score_by_context(cleaned_output, context)
                score = score_result['total_score']
                issues = score_result['issues']

                log(f"Model {model_id} scored: {score}", "info")

                # Store attempt result with cleaned output
                attempts.append({
                    'model_id': model_id,
                    'output': cleaned_output,  # Use cleaned output
                    'score': score,
                    'issues': issues,
                    'enhanced_prompt': enhanced_prompt,
                })

                # Check if we should escalate
                if not cls.should_escalate(score, context, attempt + 1):
                    log(f"Quality threshold met with score {score}", "success")
                    break

                # Add to failed models for next iteration
                failed_models.append(model_id)
                log(f"Score {score} below threshold, escalating...", "warning")

            except Exception as e:
                log(f"Model {model_id} failed: {e}", "error")
                failed_models.append(model_id)
                attempts.append({
                    'model_id': model_id,
                    'output': "",
                    'score': 0,
                    'issues': [f"Model error: {e}"],
                    'enhanced_prompt': enhanced_prompt,
                })

        # Select best attempt
        if attempts:
            best_attempt = max(attempts, key=lambda x: x['score'])
            return best_attempt['output'], best_attempt['model_id'], best_attempt['score']

        # Fallback if all attempts failed
        return "ERROR: All model attempts failed", "error", 0

# --- Model Escalation Logic (Legacy Support) ---
def get_escalation_models(current_model: str) -> list:
    """
    Legacy function - now uses IntelligentModelOrchestrator
    """
    # Simple escalation for backward compatibility
    escalation_map = {
        "llama3-8b-8192": ["llama3-70b-8192", "llama-3.3-70b-versatile"],
        "llama3-70b-8192": ["llama-3.3-70b-versatile"],
        "llama-3.3-70b-versatile": []
    }
    return escalation_map.get(current_model, [])

# --- Response Validation ---
def is_valid_code_response(output: str, min_score: int = 0) -> tuple:
    """
    Validates if the AI response is actual code and not a hallucinated or explanatory response.

    Args:
        output (str): The AI-generated output to validate
        min_score (int): Minimum score threshold for valid responses

    Returns:
        tuple: (is_valid, reason) - Whether the response is valid and reason if not
    """
    if not output:
        return False, "Empty response"

    # Score the output
    score = score_output(output)

    # Check if score is below threshold
    if score < min_score:
        return False, f"Low-quality AI result (score: {score})"

    # Check for hallucination phrases
    hallucination_phrases = [
        "it seems like you're trying",
        "could you please clarify",
        "tags are often used",
        "i'd be happy to help",
        "i'm not sure what you're asking",
        "i don't have enough information",
        "can you provide more details",
        "i need more context",
        "without more information",
        "i'd need to know more"
    ]

    output_lower = output.lower()
    for phrase in hallucination_phrases:
        if phrase in output_lower:
            return False, f"Detected hallucination phrase: '{phrase}'"

    # Check for question marks (strong indicator of non-code)
    question_count = output.count("?")
    if question_count > 2:  # Allow 1-2 questions in comments
        return False, f"Too many questions in response ({question_count} question marks)"

    # SURGICAL FIX: Enhanced Code Structure Detection
    # Check for code-like structures (Python, HTML, CSS, JavaScript, etc.)
    python_markers = ["def ", "class ", "import ", "from ", "return", "if ", "for ", "while "]
    html_markers = ["<html", "<div", "<section", "<h1", "<h2", "<h3", "<p>", "<a ", "<button", "<form", "<input", "<style", "<script"]
    css_markers = ["{", "}", ":", ";", "margin", "padding", "color", "background", "font-", "display", "flex", "grid"]
    js_markers = ["function", "const ", "let ", "var ", "=>", "document.", "window.", "console."]

    # Check for any code structures
    has_python = any(marker in output for marker in python_markers)
    has_html = any(marker in output.lower() for marker in html_markers)
    has_css = any(marker in output for marker in css_markers) and ("{" in output or ":" in output)
    has_js = any(marker in output for marker in js_markers)

    if not (has_python or has_html or has_css or has_js):
        # No code markers found, but check if it's a simple variable or expression
        if len(output.strip().splitlines()) < 3 and "=" in output:
            # Might be a simple variable assignment, which is valid
            pass
        else:
            return False, "No code structures detected"

    # If we got here, the response is valid
    return True, "Valid code response"

# --- Output Cleaning ---
def clean_ai_output(output: str) -> str:
    """
    Clean AI output by removing markdown code fences and explanatory text

    Args:
        output (str): Raw AI output

    Returns:
        str: Cleaned output with only code content
    """
    if not output:
        return output

    lines = output.split('\n')
    cleaned_lines = []
    in_code_block = False
    skip_explanations = False

    for line in lines:
        stripped = line.strip()

        # Skip markdown code fences
        if stripped in ['```', '```html', '```css', '```javascript', '```python', '```js', '```py']:
            in_code_block = not in_code_block
            continue

        # Skip explanatory text before and after code blocks
        if not in_code_block:
            # Skip lines that look like explanations
            if any(phrase in stripped.lower() for phrase in [
                'here is', 'this is', 'example of', 'you can', 'note that',
                'additionally', 'also', 'furthermore', 'however', 'therefore',
                'to customize', 'you\'ll need', 'you may want', 'this example'
            ]):
                continue

            # Skip lines that are just comments about the code
            if stripped.startswith('//') and any(word in stripped.lower() for word in ['config', 'example', 'sample']):
                continue

        # Keep the line if we're in a code block or it looks like actual code
        if in_code_block or stripped.startswith('<') or stripped.startswith('{') or stripped.startswith('def ') or stripped.startswith('class '):
            cleaned_lines.append(line)

    # Join back and clean up extra whitespace
    cleaned = '\n'.join(cleaned_lines).strip()

    # Remove any remaining explanatory paragraphs at the start or end
    paragraphs = cleaned.split('\n\n')
    code_paragraphs = []

    for paragraph in paragraphs:
        # Keep paragraphs that contain code-like content
        if any(char in paragraph for char in ['<', '>', '{', '}', '(', ')', ';', '=']):
            code_paragraphs.append(paragraph)

    return '\n\n'.join(code_paragraphs).strip()



# --- Parallel Racing Logic ---
import traceback
import asyncio
from asyncio import TimeoutError

# Default timeout for model calls (in seconds)
DEFAULT_MODEL_TIMEOUT = 15  # Reduced timeout to prevent long waits

async def race_models(prompt: str, tag: str, timeout: int = DEFAULT_MODEL_TIMEOUT, early_return: bool = True) -> tuple:
    """
    Run all models in parallel and return best output and model used.

    Enhanced with:
    - Detailed debug logging
    - Timeouts for each model call
    - Early return option for first valid response
    - Improved error handling
    - Better async coordination

    Args:
        prompt (str): The prompt to send to the AI
        tag (str): The tag name
        timeout (int): Timeout in seconds for each model call (default: 30)
        early_return (bool): Whether to return immediately when a good response is received (default: True)

    Returns:
        tuple: (output, model_id) - The best output and the model that produced it
    """
    log(f"Starting race_models with {len(MODEL_TIERS)} models, timeout={timeout}s, early_return={early_return}", "info")

    async def call_model(model_name, model_id):
        """Call a model with timeout and detailed logging"""
        # Log the start of the model call
        log(f"[DEBUG] Starting model call for {model_name} ({model_id})", "debug")

        # Check cache first
        cached_result = get_cached_result(tag, prompt, model_id)
        if cached_result:
            log(f"[DEBUG] Cache hit for {model_name}", "debug")
            rich_print(f"[gray]Cached result used from {model_id}[/gray]")
            log(f"Using cached result for {model_name}", "info")
            return model_name, model_id, cached_result.get("output", ""), cached_result.get("score", 0)

        try:
            log(f"Calling {model_name}...", "info")
            start = time.time()

            # Call the model with timeout
            try:
                output = await asyncio.wait_for(
                    groq.generate_async(prompt, tag=tag, model_id=model_id),
                    timeout=timeout
                )
            except TimeoutError:
                log(f"⏱️ {model_name} timed out after {timeout} seconds", "warning")
                return model_name, model_id, "", -500

            elapsed = time.time() - start

            # Check if we got a valid response
            if not output or not isinstance(output, str):
                log(f"❌ {model_name} returned invalid output", "warning")
                return model_name, model_id, "", -200

            # Score the output
            score = score_output(output)
            log(f"{model_name} responded in {elapsed:.2f}s with score {score}", "info")

            # Create result object for cache
            result = {
                "model": model_name,
                "id": model_id,
                "output": output,
                "score": score,
                "time": elapsed,
                "timestamp": time.time()
            }

            # Save to cache
            save_result(tag, prompt, model_id, result)

            return model_name, model_id, output, score

        except Exception as e:
            # Get detailed stack trace
            stack_trace = traceback.format_exc()
            log(f"[DEBUG] Exception in {model_name}: {e}\n{stack_trace}", "error")
            log(f"{model_name} failed: {e}", "warning")
            return model_name, model_id, "", -999

    # Create tasks for all models
    tasks = {
        asyncio.create_task(call_model(name, model_id), name=f"task_{model_id}"): (name, model_id)
        for name, model_id in MODEL_TIERS
    }

    valid_results = []

    # Process results as they come in
    while tasks:
        # Wait for the first task to complete or timeout
        done, _ = await asyncio.wait(
            tasks.keys(),
            return_when=asyncio.FIRST_COMPLETED
        )

        # Process completed tasks
        for task in done:
            model_name, model_id, output, score = task.result()

            # Remove the task from our tracking dict
            tasks.pop(task)

            # Check if we got a valid result
            if output and score > -100:
                log(f"Valid result from {model_name} with score {score}", "info")
                valid_results.append((model_name, model_id, output, score))

                # If early return is enabled and we have a good score, return immediately
                if early_return and score > 50:
                    log(f"Early return with {model_name} (score: {score})", "info")

                    # Cancel remaining tasks
                    for pending_task in tasks:
                        pending_task.cancel()

                    return output, model_id

    # If we get here, all tasks have completed
    log(f"All models completed. Valid results: {len(valid_results)}/{len(MODEL_TIERS)}", "info")

    # Handle the case where no valid results were found
    if not valid_results:
        error_msg = "All models failed or returned poor quality output"
        log(error_msg, "error")
        raise RuntimeError(error_msg)

    # Find the best result based on score
    best_result = max(valid_results, key=lambda x: x[3])  # x[3] is the score
    best_model_name, best_model_id, best_output, best_score = best_result

    log(f"Best model: {best_model_name} (score {best_score})", "success")

    # Return both the output and the model ID
    return best_output, best_model_id

# Example usage of race_models
async def example_race_models():
    """Example usage of race_models function"""
    prompt = "Write a Python function to calculate the factorial of a number."
    tag = "example"

    try:
        output, model_id = await race_models(prompt, tag)
        print(f"Best model: {model_id}")
        print(f"Output snippet: {output[:100]}...")
        return output, model_id
    except Exception as e:
        print(f"Error: {e}")
        return None, None

# --- Main Entry ---
async def generate_code_async(prompt: str, tag: str="", provider="auto", model_ids=None,
                        fallback=True, auto_route=False, use_cache=True, refresh_cache=False,
                        timeout=DEFAULT_MODEL_TIMEOUT, file_path: str = None):
    """
    Enhanced async code generation with context-aware AI orchestration.

    Args:
        prompt (str): The prompt to send to the AI
        tag (str, optional): The tag name
        provider (str, optional): The AI provider to use
        model_ids (list, optional): List of model IDs to use (overrides provider)
        fallback (bool, optional): Whether to use fallback models
        auto_route (bool, optional): Whether to use auto-routing
        use_cache (bool, optional): Whether to use the cache
        refresh_cache (bool, optional): Whether to bypass the cache
        timeout (int, optional): Timeout in seconds for each model call
        file_path (str, optional): File path for context detection

    Returns:
        tuple: (output, model_id) - The generated code and the model that produced it
    """
    log(f"Generating code for tag: {tag} using {provider}...", "info")
    print(f"DEBUG: Starting generate_code_async with provider={provider}, model_ids={model_ids}, auto_route={auto_route}")

    # CONTEXT-AWARE ORCHESTRATION: Detect context from file path and prompt
    context = ContextDetector.detect_context(file_path=file_path, prompt=prompt)
    log(f"Detected context: {context}", "info")

    # Check cache with enhanced threshold logic
    effective_use_cache = use_cache and not refresh_cache
    cache_threshold = QualityThreshold.EXCELLENT if not refresh_cache else 0

    print(f"DEBUG: effective_use_cache={effective_use_cache}, context={context}, cache_threshold={cache_threshold}")

    # INTELLIGENT CACHE MANAGEMENT
    if effective_use_cache:
        # Try to find high-quality cached result for this context
        for _, model_id in MODEL_TIERS:
            cached_result = get_cached_result(tag, prompt, model_id)
            if cached_result:
                cached_score = cached_result.get("score", 0)

                # Use cache only if score meets threshold
                if cached_score >= cache_threshold:
                    log(f"High-quality cache hit: {model_id} (score={cached_score})", "success")
                    rich_print(f"[green]✅ High-quality cache hit: {model_id} (score={cached_score})[/green]")
                    return cached_result.get("output", ""), model_id
                else:
                    log(f"Cache score {cached_score} below threshold {cache_threshold}, skipping", "debug")

    # INTELLIGENT MODEL ORCHESTRATION
    if auto_route or provider == "auto" or (model_ids and len(model_ids) > 1):
        log("[Context-Aware Orchestration] Using intelligent model selection...", "info")

        try:
            # Use the new intelligent orchestration system
            output, model_id, final_score = await IntelligentModelOrchestrator.generate_with_refinement(
                prompt=prompt,
                context=context,
                tag=tag,
                file_path=file_path,
                max_attempts=3
            )

            log(f"Intelligent orchestration completed: {model_id} (score={final_score})", "success")

            # Cache the result if it's good quality
            if use_cache and final_score >= QualityThreshold.ACCEPTABLE:
                result = {
                    "model": "groq",
                    "id": model_id,
                    "output": output,
                    "score": final_score,
                    "timestamp": time.time(),
                    "context": context,
                    "orchestrated": True
                }
                save_result(tag, prompt, model_id, result)

            return output, model_id

        except Exception as e:
            log(f"Intelligent orchestration failed: {e}", "error")
            # Fall back to legacy system
            pass

    try:
        # Auto-routing or explicit model_ids - use race_models
        if auto_route or (model_ids and len(model_ids) > 1) or provider == "auto":
            log("[Auto Route] Racing multiple models...", "info")

            # Use provided model_ids or all models from MODEL_TIERS
            models_to_race = model_ids if model_ids else [model_id for _, model_id in MODEL_TIERS]

            # Log the models we're racing
            log(f"Racing models: {models_to_race}", "info")

            # Use the improved race_models with timeout and early return
            return await race_models(prompt, tag, timeout=timeout, early_return=True)

        # Single model case
        # Determine model_id based on provider
        model_id = None
        # FIXED: Add defensive check for None provider before calling .startswith()
        if provider and provider.startswith("groq/"):
            model_id = provider.split("/", 1)[1]
        elif provider == "groq":
            model_id = MODEL_TIERS[0][1]  # Use first model in tier list
        elif model_ids and len(model_ids) == 1:
            model_id = model_ids[0]
        elif provider is None:
            # FIXED: Add logging warning when provider is None
            log("Warning: provider is None when determining model_id", "warning")
            print("DEBUG: provider is None, using default model")
            model_id = MODEL_TIERS[0][1]  # Use default model

        # Check cache if use_cache is enabled and we have a model_id
        if effective_use_cache and model_id:
            cached_result = get_cached_result(tag, prompt, model_id)
            if cached_result:
                cached_score = cached_result.get("score", 0)

                # SURGICAL FIX: Cache Override Logic
                # When refresh_cache is True (from --force) AND cached result has score < 50, bypass cache
                if refresh_cache and cached_score < 50:
                    log(f"Bypassing low-quality cache (score={cached_score}) due to --force", "warning")
                    rich_print(f"[yellow]Bypassing low-quality cache (score={cached_score}) due to --force[/yellow]")
                    # Continue to generate fresh result instead of using cache
                elif refresh_cache:
                    log(f"Bypassing cache due to refresh_cache flag", "info")
                    rich_print(f"[yellow]Bypassing cache due to refresh_cache flag[/yellow]")
                    # Continue to generate fresh result instead of using cache
                else:
                    rich_print(f"[gray]Cached result used from {model_id} (score={cached_score})[/gray]")
                    log(f"Using cached result for {model_id} (score={cached_score})", "info")
                    return cached_result.get("output", ""), model_id

        # Generate new result based on provider
        if provider == "mistral":
            output = mistral.generate(prompt, tag=tag)
            score = score_output(output)
            log(f"Mistral output scored: {score}", "info")

            if use_cache:
                # Save to cache
                result = {
                    "model": "mistral",
                    "id": "mistral",
                    "output": output,
                    "score": score,
                    "timestamp": time.time()
                }
                save_result(tag, prompt, "mistral", result)
            return output, "mistral"

        # FIXED: Add defensive check for None provider before calling .startswith()
        if (provider and provider.startswith("groq/")) or provider == "groq":
            if not model_id:
                model_id = MODEL_TIERS[0][1]  # Default to first model

            # Use async version if available
            try:
                print(f"DEBUG: Calling groq.generate_async with model_id={model_id}, timeout={timeout}")
                output = await asyncio.wait_for(
                    groq.generate_async(prompt, tag=tag, model_id=model_id),
                    timeout=timeout
                )
                print(f"DEBUG: groq.generate_async completed successfully")
            except (asyncio.TimeoutError, AttributeError, Exception) as e:
                # Fall back to sync version if async fails or times out
                print(f"DEBUG: Async call failed with error: {type(e).__name__}: {str(e)}")
                log(f"Async call failed or timed out, falling back to sync for {model_id}", "warning")

                try:
                    print(f"DEBUG: Calling groq.generate (sync) with model_id={model_id}")
                    # Set a timeout for the sync call as well (using a thread with timeout)
                    output = groq.generate(prompt, tag=tag, model_id=model_id)
                    print(f"DEBUG: groq.generate (sync) completed successfully")
                except Exception as sync_error:
                    print(f"DEBUG: Sync call also failed: {sync_error}")
                    log(f"Sync call also failed: {sync_error}", "error")
                    # Don't hang here, raise the error to trigger fallback
                    raise

            # Clean the output to remove markdown artifacts and explanations
            cleaned_output = clean_ai_output(output)

            score = score_output(cleaned_output, context, file_path)
            log(f"Groq/{model_id} output scored: {score}", "info")

            # SURGICAL FIX: Model Escalation Fallback
            # If score < 50 and this is a structured prompt, try escalation
            if score < 50 and is_structured_prompt(prompt):
                escalation_models = get_escalation_models(model_id)
                log(f"Low score ({score}) for structured prompt, attempting escalation", "warning")

                for escalation_model in escalation_models:
                    log(f"Escalating to model: {escalation_model}", "info")
                    rich_print(f"[yellow]Escalating to {escalation_model} due to low score ({score})[/yellow]")

                    try:
                        # Try the escalation model
                        escalation_output = await asyncio.wait_for(
                            groq.generate_async(prompt, tag=tag, model_id=escalation_model),
                            timeout=timeout
                        )

                        escalation_score = score_output(escalation_output)
                        log(f"Escalation model {escalation_model} scored: {escalation_score}", "info")

                        # If escalation model performs better, use it
                        if escalation_score >= 50:  # Reasonable threshold for good quality
                            log(f"Escalation successful with {escalation_model} (score: {escalation_score})", "success")
                            rich_print(f"[green]✅ Escalation successful with {escalation_model} (score: {escalation_score})[/green]")

                            if use_cache:
                                # Save escalated result to cache (cleaned)
                                cleaned_escalation_output = clean_ai_output(escalation_output)
                                result = {
                                    "model": "groq",
                                    "id": escalation_model,
                                    "output": cleaned_escalation_output,
                                    "score": escalation_score,
                                    "timestamp": time.time(),
                                    "escalated_from": model_id
                                }
                                save_result(tag, prompt, escalation_model, result)
                            return clean_ai_output(escalation_output), escalation_model

                    except Exception as escalation_error:
                        log(f"Escalation to {escalation_model} failed: {escalation_error}", "warning")
                        continue

                # If all escalations failed, continue with original result
                log("All escalation attempts failed, using original result", "warning")

            if use_cache:
                # Save to cache (cleaned output)
                result = {
                    "model": "groq",
                    "id": model_id,
                    "output": cleaned_output,
                    "score": score,
                    "timestamp": time.time()
                }
                save_result(tag, prompt, model_id, result)
            return cleaned_output, model_id

        # Default to groq with first model if we get here
        model_id = MODEL_TIERS[0][1]
        output = groq.generate(prompt, tag=tag, model_id=model_id)
        score = score_output(output)
        log(f"Default model {model_id} output scored: {score}", "info")

        if use_cache:
            # Save to cache
            result = {
                "model": "groq",
                "id": model_id,
                "output": output,
                "score": score,
                "timestamp": time.time()
            }
            save_result(tag, prompt, model_id, result)

        return output, model_id
    except Exception as e:
        log(f"Primary provider failed: {e}", "error")
        log(traceback.format_exc(), "error")

        if fallback and provider != "mistral":
            log("Falling back to mistral...", "warning")
            print(f"DEBUG: Falling back to mistral due to error: {e}")
            try:
                # Check if MISTRAL_API_KEY is set
                mistral_api_key = os.getenv("MISTRAL_API_KEY")
                if not mistral_api_key:
                    log("MISTRAL_API_KEY not found in environment variables", "error")
                    print("DEBUG: MISTRAL_API_KEY not found in environment variables")
                    # Return a helpful error message instead of hanging
                    return "ERROR: MISTRAL_API_KEY not found in environment variables. Please set it in your .env file.", "error"

                # Set a timeout for the mistral call as well
                output = mistral.generate(prompt, tag=tag)
                print(f"DEBUG: Mistral fallback successful")
                return output, "mistral-fallback"
            except Exception as fallback_error:
                log(f"Fallback to mistral also failed: {fallback_error}", "error")
                print(f"DEBUG: Mistral fallback also failed: {fallback_error}")
                # Return a helpful error message instead of hanging
                return f"ERROR: All AI providers failed. Primary error: {e}. Fallback error: {fallback_error}", "error"

        # Return a helpful error message instead of hanging
        print(f"DEBUG: No fallback available, returning error message")
        return f"ERROR: AI provider failed: {e}", "error"

def generate_code(prompt: str, tag: str="", provider="auto", model_ids=None,
                 fallback=True, auto_route=False, use_cache=True, refresh_cache=False,
                 file_path: str = None):
    """
    Enhanced AI code generation function with context-aware orchestration.
    Synchronous wrapper around the async generate_code_async function.

    Args:
        prompt (str): The prompt to send to the AI
        tag (str, optional): The tag name
        provider (str, optional): The AI provider to use
        model_ids (list, optional): List of model IDs to use (overrides provider)
        fallback (bool, optional): Whether to use fallback models
        auto_route (bool, optional): Whether to use auto-routing
        use_cache (bool, optional): Whether to use the cache
        refresh_cache (bool, optional): Whether to bypass the cache
        file_path (str, optional): File path for context detection

    Returns:
        tuple: (output, model_id) - The generated code and the model that produced it
    """
    try:
        # Check if we're already in an event loop (e.g., called from FastAPI)
        loop = asyncio.get_running_loop()
        # If we're in an event loop, we need to handle this differently
        log("Running in existing event loop, using create_task", "debug")

        # Create a task and run it in the current loop
        import concurrent.futures
        import threading

        # Use a thread pool to run the async function
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(
                lambda: asyncio.run(generate_code_async(
                    prompt=prompt,
                    tag=tag,
                    provider=provider,
                    model_ids=model_ids,
                    fallback=fallback,
                    auto_route=auto_route,
                    use_cache=use_cache,
                    refresh_cache=refresh_cache,
                    timeout=DEFAULT_MODEL_TIMEOUT,
                    file_path=file_path
                ))
            )
            return future.result(timeout=60)  # 60 second timeout

    except RuntimeError as e:
        if "no running event loop" in str(e).lower():
            # No event loop running, safe to use asyncio.run()
            log("No event loop running, using asyncio.run", "debug")
            return asyncio.run(generate_code_async(
                prompt=prompt,
                tag=tag,
                provider=provider,
                model_ids=model_ids,
                fallback=fallback,
                auto_route=auto_route,
                use_cache=use_cache,
                refresh_cache=refresh_cache,
                timeout=DEFAULT_MODEL_TIMEOUT,
                file_path=file_path
            ))
        else:
            # Some other RuntimeError, re-raise it
            raise
    except Exception as e:
        log(f"Error in generate_code: {e}", "error")
        # Fallback to simple sync generation
        try:
            if provider == "mistral":
                output = mistral.generate(prompt, tag=tag)
                return output, "mistral-sync"
            else:
                # Use sync groq generation
                output = groq.generate(prompt, tag=tag, model_id=MODEL_TIERS[0][1])
                return output, MODEL_TIERS[0][1]
        except Exception as fallback_error:
            log(f"Fallback sync generation failed: {fallback_error}", "error")
            return f"ERROR: All generation methods failed: {e}, {fallback_error}", "error"
