"""
Trends command for CodeCrusher.

This module provides the trends command for CodeCrusher, which generates
historical trend reports from telemetry data, enabling users to analyze
long-term patterns in injections, model usage, fallback rates, and errors.
"""

import typer
import os
import json
import csv
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple, Union
from collections import defaultdict, Counter
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn

# Import telemetry logger and anomaly engine
from codecrusher.telemetry_logger import get_telemetry_entries, TELEMETRY_FILE
from codecrusher.anomaly_engine import detect_anomalies

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

# Cache for telemetry data to speed up repeated queries
_telemetry_cache = {}

def parse_time_period(period: str) -> Optional[datetime]:
    """
    Parse a time period string into a datetime object.

    Args:
        period: Time period string (e.g., "24h", "7d", "30d", "2023-01-01")

    Returns:
        datetime object or None if invalid
    """
    now = datetime.now()

    if period.lower() == "now":
        return now

    try:
        # Parse time units
        if period.endswith('h'):
            hours = int(period[:-1])
            return now - timedelta(hours=hours)
        elif period.endswith('d'):
            days = int(period[:-1])
            return now - timedelta(days=days)
        elif period.endswith('w'):
            weeks = int(period[:-1])
            return now - timedelta(weeks=weeks)
        elif period.endswith('m'):
            months = int(period[:-1])
            # Approximate months as 30 days
            return now - timedelta(days=30 * months)
        else:
            # Try to parse as ISO format
            return datetime.fromisoformat(period)
    except (ValueError, TypeError):
        console.print(f"[bold red]❌ Error:[/bold red] Invalid time period format: {period}")
        return None

def filter_entries_by_date_range(
    entries: List[Dict[str, Any]],
    since: datetime,
    until: datetime
) -> List[Dict[str, Any]]:
    """
    Filter entries by date range.

    Args:
        entries: List of telemetry entries
        since: Start time for filtering
        until: End time for filtering

    Returns:
        List[Dict[str, Any]]: Filtered entries
    """
    filtered_entries = []

    for entry in entries:
        timestamp = entry.get("timestamp", "")
        try:
            dt = datetime.fromisoformat(timestamp)
            if since <= dt <= until:
                filtered_entries.append(entry)
        except (ValueError, TypeError):
            # Skip entries with invalid timestamps
            pass

    return filtered_entries

def filter_entries_by_model(entries: List[Dict[str, Any]], model: str) -> List[Dict[str, Any]]:
    """
    Filter entries by model.

    Args:
        entries: List of telemetry entries
        model: Model name to filter by

    Returns:
        List[Dict[str, Any]]: Filtered entries
    """
    return [e for e in entries if model.lower() in e.get("model", "").lower()]

def filter_entries_by_tag(entries: List[Dict[str, Any]], tag: str) -> List[Dict[str, Any]]:
    """
    Filter entries by tag.

    Args:
        entries: List of telemetry entries
        tag: Tag to filter by

    Returns:
        List[Dict[str, Any]]: Filtered entries
    """
    return [e for e in entries if tag in e.get("tags", [])]

def group_entries_by_day(entries: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Group entries by day.

    Args:
        entries: List of telemetry entries

    Returns:
        Dict[str, List[Dict[str, Any]]]: Entries grouped by day
    """
    grouped = defaultdict(list)

    for entry in entries:
        timestamp = entry.get("timestamp", "")
        try:
            dt = datetime.fromisoformat(timestamp)
            day_key = dt.strftime("%Y-%m-%d")
            grouped[day_key].append(entry)
        except (ValueError, TypeError):
            # Skip entries with invalid timestamps
            pass

    return grouped

def calculate_daily_metrics(grouped_entries: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Dict[str, Any]]:
    """
    Calculate daily metrics from grouped entries.

    Args:
        grouped_entries: Entries grouped by day

    Returns:
        Dict[str, Dict[str, Any]]: Daily metrics
    """
    daily_metrics = {}

    for day, entries in grouped_entries.items():
        # Count total injections
        total_injections = len(entries)

        # Count fallbacks
        fallbacks = sum(1 for e in entries if e.get("fallback", False))
        fallback_rate = (fallbacks / total_injections * 100) if total_injections > 0 else 0

        # Count errors
        errors = sum(1 for e in entries if e.get("error"))
        error_types = Counter([e.get("error", "unknown") for e in entries if e.get("error")])

        # Count by model
        models = Counter([e.get("model", "unknown") for e in entries])

        # Calculate token usage
        tokens_in = sum(e.get("tokens_in", 0) or 0 for e in entries)
        tokens_out = sum(e.get("tokens_out", 0) or 0 for e in entries)

        # Count tags
        tags = Counter()
        for entry in entries:
            for tag in entry.get("tags", []):
                tags[tag] += 1

        # Store metrics
        daily_metrics[day] = {
            "total_injections": total_injections,
            "fallbacks": fallbacks,
            "fallback_rate": fallback_rate,
            "errors": errors,
            "error_types": dict(error_types),
            "models": dict(models),
            "tokens_in": tokens_in,
            "tokens_out": tokens_out,
            "tags": dict(tags)
        }

    return daily_metrics

def generate_ascii_sparkline(values: List[float], width: int = 20) -> str:
    """
    Generate an ASCII sparkline from a list of values.

    Args:
        values: List of values to visualize
        width: Width of the sparkline

    Returns:
        str: ASCII sparkline
    """
    if not values:
        return " " * width

    # Normalize values to range [0, 1]
    min_val = min(values)
    max_val = max(values)

    if min_val == max_val:
        # All values are the same
        return "─" * width

    normalized = [(v - min_val) / (max_val - min_val) for v in values]

    # Map normalized values to ASCII characters
    chars = " ▁▂▃▄▅▆▇█"
    result = ""

    # Resample to desired width
    step = len(normalized) / width
    for i in range(width):
        idx = int(i * step)
        if idx >= len(normalized):
            idx = len(normalized) - 1
        val = normalized[idx]
        char_idx = int(val * (len(chars) - 1))
        result += chars[char_idx]

    return result

def export_to_csv(metrics: Dict[str, Dict[str, Any]], filename: str) -> None:
    """
    Export metrics to CSV file.

    Args:
        metrics: Daily metrics
        filename: Output filename
    """
    # Flatten the metrics for CSV export
    flattened = []

    # First pass: collect all possible field names
    all_fields = set(["date", "total_injections", "fallbacks", "fallback_rate",
                     "errors", "tokens_in", "tokens_out"])

    for day, data in metrics.items():
        # Add model fields
        for model in data["models"].keys():
            all_fields.add(f"model_{model}")

        # Add error type fields (sanitized)
        for error in data["error_types"].keys():
            error_key = error[:20].replace(" ", "_")  # Truncate and sanitize
            all_fields.add(f"error_{error_key}")

    # Second pass: create rows with all fields
    for day, data in metrics.items():
        row = {"date": day}
        row.update({
            "total_injections": data["total_injections"],
            "fallbacks": data["fallbacks"],
            "fallback_rate": data["fallback_rate"],
            "errors": data["errors"],
            "tokens_in": data["tokens_in"],
            "tokens_out": data["tokens_out"]
        })

        # Add all models (with 0 for missing ones)
        for model, count in data["models"].items():
            row[f"model_{model}"] = count

        # Add all error types (with 0 for missing ones)
        for error, count in data["error_types"].items():
            error_key = error[:20].replace(" ", "_")  # Truncate and sanitize
            row[f"error_{error_key}"] = count

        # Ensure all fields are present (with 0 for missing ones)
        for field in all_fields:
            if field not in row:
                row[field] = 0

        flattened.append(row)

    # Write to CSV
    if flattened:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = sorted(all_fields)
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for row in flattened:
                writer.writerow(row)

def export_to_json(metrics: Dict[str, Dict[str, Any]], filename: str) -> None:
    """
    Export metrics to JSON file.

    Args:
        metrics: Daily metrics
        filename: Output filename
    """
    with open(filename, 'w') as jsonfile:
        json.dump(metrics, jsonfile, indent=2)

@app.command("run")
def run_trends(
    since: str = typer.Option("7d", "--since", "-s", help="Show data since this time period (e.g., 7d, 30d, 2023-01-01)"),
    until: str = typer.Option("now", "--until", "-u", help="Show data until this time period (e.g., now, 1d, 2023-01-31)"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Filter by model"),
    tag: Optional[str] = typer.Option(None, "--tag", "-t", help="Filter by tag"),
    export_csv: Optional[str] = typer.Option(None, "--csv", help="Export data to CSV file"),
    export_json: Optional[str] = typer.Option(None, "--export-json", help="Export data to JSON file"),
    json: bool = typer.Option(False, "--json", help="Output clean, structured JSON for programmatic consumption"),
    cache: bool = typer.Option(True, "--cache/--no-cache", help="Use cached data for faster processing"),
):
    """
    Generate historical trend reports from telemetry data.

    This command analyzes telemetry data and generates reports on long-term
    patterns in injections, model usage, fallback rates, and errors.

    Examples:
        codecrusher trends
        codecrusher trends --since 30d
        codecrusher trends --since 2023-01-01 --until 2023-01-31
        codecrusher trends --model mistral
        codecrusher trends --tag bugfix
        codecrusher trends --csv trends_report.csv
        codecrusher trends --export-json trends_report.json
        codecrusher trends --json

    The --json flag outputs clean, structured JSON suitable for programmatic consumption,
    while --export-json exports the data to a JSON file.
    """
    # Parse time periods
    since_dt = parse_time_period(since)
    until_dt = parse_time_period(until)

    if not since_dt or not until_dt:
        return False

    # Ensure since is before until
    if since_dt > until_dt:
        console.print("[bold red]❌ Error:[/bold red] Start date must be before end date")
        return False

    # Format time period for display
    time_period = f"{since_dt.strftime('%Y-%m-%d')} to {until_dt.strftime('%Y-%m-%d')}"

    # Cache key for this query
    cache_key = f"{since}_{until}_{model or 'all'}_{tag or 'all'}"

    # Check cache
    if cache and cache_key in _telemetry_cache:
        console.print("[cyan]Using cached data...[/cyan]")
        entries = _telemetry_cache[cache_key]
    else:
        # Get all telemetry entries
        with Progress(
            SpinnerColumn(),
            TextColumn("[cyan]Loading telemetry data...[/cyan]"),
            console=console
        ) as progress:
            progress.add_task("Loading", total=None)
            entries = get_telemetry_entries(limit=100000)

        # Check if telemetry file exists
        if not os.path.exists(TELEMETRY_FILE):
            console.print("[yellow]No telemetry data found. Run some commands first.[/yellow]")
            return False

        # Filter by date range
        entries = filter_entries_by_date_range(entries, since_dt, until_dt)

        # Filter by model if specified
        if model:
            entries = filter_entries_by_model(entries, model)

        # Filter by tag if specified
        if tag:
            entries = filter_entries_by_tag(entries, tag)

        # Cache the filtered entries
        if cache:
            _telemetry_cache[cache_key] = entries

    # Check if we have data
    if not entries:
        console.print("[yellow]No telemetry data found for the specified filters[/yellow]")
        return False

    # Group entries by day
    grouped_entries = group_entries_by_day(entries)

    # Calculate daily metrics
    with Progress(
        SpinnerColumn(),
        TextColumn("[cyan]Analyzing trends...[/cyan]"),
        console=console
    ) as progress:
        progress.add_task("Analyzing", total=None)
        daily_metrics = calculate_daily_metrics(grouped_entries)

    # Sort days chronologically
    sorted_days = sorted(daily_metrics.keys())

    # Display report header
    console.print(Panel(
        f"[bold]CodeCrusher Trend Report[/bold]\n\n"
        f"[cyan]Time Period:[/cyan] {time_period}\n"
        f"[cyan]Model Filter:[/cyan] {model or 'All models'}\n"
        f"[cyan]Tag Filter:[/cyan] {tag or 'All tags'}\n"
        f"[cyan]Total Days:[/cyan] {len(daily_metrics)}\n"
        f"[cyan]Total Entries:[/cyan] {len(entries)}",
        title="Trend Report Configuration",
        border_style="blue"
    ))

    # Prepare JSON data
    json_output = {
        "time_period": {
            "since": since_dt.isoformat() if since_dt else "",
            "until": until_dt.isoformat() if until_dt else "",
        },
        "filters": {
            "model": model or "all",
            "tag": tag or "all",
        },
        "metrics": daily_metrics
    }

    # Output JSON if requested
    if json:
        print(json.dumps(json_output))
        return True

    # Export data if requested
    if export_csv:
        export_to_csv(daily_metrics, export_csv)
        console.print(f"[green]Data exported to CSV: {export_csv}[/green]")

    if export_json:
        export_to_json(daily_metrics, export_json)
        console.print(f"[green]Data exported to JSON: {export_json}[/green]")

    # Display trend report
    display_trend_report(daily_metrics, sorted_days)

    return True

def display_trend_report(metrics: Dict[str, Dict[str, Any]], sorted_days: List[str]) -> None:
    """
    Display trend report in the console.

    Args:
        metrics: Daily metrics
        sorted_days: List of days sorted chronologically
    """
    if not sorted_days:
        console.print("[yellow]No data available for the specified time period[/yellow]")
        return

    # Extract data for sparklines
    daily_injections = [metrics[day]["total_injections"] for day in sorted_days]
    daily_fallback_rates = [metrics[day]["fallback_rate"] for day in sorted_days]
    daily_errors = [metrics[day]["errors"] for day in sorted_days]
    daily_tokens_in = [metrics[day]["tokens_in"] for day in sorted_days]
    daily_tokens_out = [metrics[day]["tokens_out"] for day in sorted_days]

    # Calculate totals
    total_injections = sum(daily_injections)
    total_fallbacks = sum(metrics[day]["fallbacks"] for day in sorted_days)
    total_errors = sum(daily_errors)
    total_tokens_in = sum(daily_tokens_in)
    total_tokens_out = sum(daily_tokens_out)

    # Calculate averages
    avg_injections = total_injections / len(sorted_days) if sorted_days else 0
    avg_fallback_rate = (total_fallbacks / total_injections * 100) if total_injections > 0 else 0

    # Create daily injections table
    injections_table = Table(
        title="Daily Injections",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )

    injections_table.add_column("Metric", style="bold")
    injections_table.add_column("Value")
    injections_table.add_column("Trend", no_wrap=True)
    injections_table.add_column("Min/Max")

    # Add rows
    injections_table.add_row(
        "Total Injections",
        f"{total_injections}",
        generate_ascii_sparkline(daily_injections),
        f"{min(daily_injections)}/{max(daily_injections)}"
    )

    injections_table.add_row(
        "Avg. Injections/Day",
        f"{avg_injections:.1f}",
        "",
        ""
    )

    injections_table.add_row(
        "Fallback Rate",
        f"{avg_fallback_rate:.1f}%",
        generate_ascii_sparkline(daily_fallback_rates),
        f"{min(daily_fallback_rates):.1f}%/{max(daily_fallback_rates):.1f}%"
    )

    injections_table.add_row(
        "Errors",
        f"{total_errors}",
        generate_ascii_sparkline(daily_errors),
        f"{min(daily_errors)}/{max(daily_errors)}"
    )

    injections_table.add_row(
        "Tokens (In/Out)",
        f"{total_tokens_in:,}/{total_tokens_out:,}",
        generate_ascii_sparkline(daily_tokens_out),
        f"{min(daily_tokens_out):,}/{max(daily_tokens_out):,}"
    )

    console.print(injections_table)

    # Create model usage table
    model_usage_table = Table(
        title="Model Usage Trends",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )

    model_usage_table.add_column("Model", style="bold")
    model_usage_table.add_column("Total")
    model_usage_table.add_column("Trend", no_wrap=True)
    model_usage_table.add_column("Success Rate")

    # Aggregate model usage
    model_usage = defaultdict(lambda: {"total": 0, "errors": 0, "trend": []})

    for day in sorted_days:
        day_metrics = metrics[day]
        for model, count in day_metrics["models"].items():
            model_usage[model]["total"] += count
            model_usage[model]["trend"].append(count)

            # Count errors for this model on this day
            # We don't have model-specific error tracking in our data structure
            # So we'll just count errors generally
            model_errors = 0
            if model in day_metrics.get("error_types", {}):
                model_errors = day_metrics["error_types"][model]
            model_usage[model]["errors"] += model_errors

    # Add rows for top models
    for model, data in sorted(model_usage.items(), key=lambda x: x[1]["total"], reverse=True)[:5]:
        success_rate = ((data["total"] - data["errors"]) / data["total"] * 100) if data["total"] > 0 else 0
        success_color = "green" if success_rate >= 90 else "yellow" if success_rate >= 70 else "red"

        model_usage_table.add_row(
            model,
            f"{data['total']}",
            generate_ascii_sparkline(data["trend"]),
            f"[{success_color}]{success_rate:.1f}%[/{success_color}]"
        )

    console.print(model_usage_table)

    # Create error types table
    error_table = Table(
        title="Error Types",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )

    error_table.add_column("Error Type", style="bold")
    error_table.add_column("Count")
    error_table.add_column("Percentage")

    # Aggregate error types
    error_types = Counter()

    for day in sorted_days:
        for error_type, count in metrics[day]["error_types"].items():
            error_types[error_type] += count

    # Add rows for top error types
    for error_type, count in error_types.most_common(5):
        percentage = (count / total_errors * 100) if total_errors > 0 else 0
        error_table.add_row(
            error_type[:30] + "..." if len(error_type) > 33 else error_type,
            f"{count}",
            f"{percentage:.1f}%"
        )

    if error_types:
        console.print(error_table)

    # Create tag usage table
    tag_table = Table(
        title="Top Tags",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )

    tag_table.add_column("Tag", style="bold")
    tag_table.add_column("Count")
    tag_table.add_column("Trend", no_wrap=True)

    # Aggregate tag usage
    tag_usage = defaultdict(lambda: {"total": 0, "trend": []})

    for day in sorted_days:
        day_tags = metrics[day]["tags"]
        for tag, count in day_tags.items():
            tag_usage[tag]["total"] += count
            tag_usage[tag]["trend"].append(count)

    # Add rows for top tags
    for tag, data in sorted(tag_usage.items(), key=lambda x: x[1]["total"], reverse=True)[:5]:
        tag_table.add_row(
            tag,
            f"{data['total']}",
            generate_ascii_sparkline(data["trend"])
        )

    if tag_usage:
        console.print(tag_table)

    # Display date range
    if len(sorted_days) > 1:
        console.print(f"\n[dim]Date range: {sorted_days[0]} to {sorted_days[-1]} ({len(sorted_days)} days)[/dim]")
