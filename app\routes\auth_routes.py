"""
Authentication routes for Code<PERSON>rusher
Provides user registration, login, logout, and profile management
"""

import logging
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import HTTPAuthorizationCredentials

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from auth import (
    UserCreate, UserLogin, UserResponse, Token,
    hash_password, verify_password, create_token_response,
    get_current_user_email, security, validate_email, validate_password
)
from database import get_db, DatabaseManager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])

@router.post("/register", response_model=Dict[str, Any])
async def register_user(user_data: UserCreate, db: DatabaseManager = Depends(get_db)):
    """
    Register a new user account.

    Args:
        user_data: User registration data (email, password, role)
        db: Database manager instance

    Returns:
        Success message with user info
    """
    try:
        # Validate email format
        if not validate_email(user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid email format"
            )

        # Validate password strength
        is_valid, message = validate_password(user_data.password)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )

        # Check if user already exists
        existing_user = db.get_user_by_email(user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )

        # Hash password and create user
        hashed_password = hash_password(user_data.password)
        user_id = db.create_user(
            email=user_data.email,
            hashed_password=hashed_password,
            role=user_data.role
        )

        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create user account"
            )

        logger.info(f"User registered successfully: {user_data.email}")

        return {
            "message": "User registered successfully",
            "user_id": user_id,
            "email": user_data.email,
            "role": user_data.role,
            "status": "success"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during registration"
        )

@router.post("/login", response_model=Token)
async def login_user(login_data: UserLogin, db: DatabaseManager = Depends(get_db)):
    """
    Authenticate user and return access token.

    Args:
        login_data: User login credentials (email, password)
        db: Database manager instance

    Returns:
        JWT access token with expiration info
    """
    try:
        # Get user from database
        user = db.get_user_by_email(login_data.email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verify password
        if not verify_password(login_data.password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Update last login timestamp
        db.update_last_login(user["id"])

        # Create and return token
        token_response = create_token_response(user["email"], user["role"])

        logger.info(f"User logged in successfully: {user['email']}")

        return token_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during login"
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user_email: str = Depends(get_current_user_email),
    db: DatabaseManager = Depends(get_db)
):
    """
    Get current authenticated user information.

    Args:
        current_user_email: Email from JWT token
        db: Database manager instance

    Returns:
        Current user information
    """
    try:
        user = db.get_user_by_email(current_user_email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        return UserResponse(
            id=user["id"],
            email=user["email"],
            role=user["role"],
            created_at=datetime.fromisoformat(user["created_at"])
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get current user error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )

@router.post("/logout")
async def logout_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user_email: str = Depends(get_current_user_email)
):
    """
    Logout user by invalidating their token.

    Args:
        credentials: JWT token from Authorization header
        current_user_email: Email from JWT token

    Returns:
        Logout confirmation
    """
    try:
        # In a more sophisticated implementation, you would:
        # 1. Add the token to a blacklist
        # 2. Store token hashes in database and mark as invalid
        # 3. Implement token refresh mechanism

        # For now, we'll just log the logout
        logger.info(f"User logged out: {current_user_email}")

        return {
            "message": "Logged out successfully",
            "status": "success"
        }

    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to logout"
        )

@router.get("/users", response_model=Dict[str, Any])
async def list_users(
    current_user_email: str = Depends(get_current_user_email),
    db: DatabaseManager = Depends(get_db)
):
    """
    List all users (admin functionality).

    Args:
        current_user_email: Email from JWT token
        db: Database manager instance

    Returns:
        List of all users
    """
    try:
        # Get current user to check permissions
        current_user = db.get_user_by_email(current_user_email)
        if not current_user or current_user["role"] != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required"
            )

        users = db.get_all_users()

        # Remove sensitive information
        safe_users = []
        for user in users:
            safe_users.append({
                "id": user["id"],
                "email": user["email"],
                "role": user["role"],
                "created_at": user["created_at"],
                "last_login": user.get("last_login"),
                "is_active": user["is_active"]
            })

        return {
            "users": safe_users,
            "count": len(safe_users),
            "status": "success"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"List users error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list users"
        )

@router.put("/profile")
async def update_profile(
    updates: Dict[str, Any],
    current_user_email: str = Depends(get_current_user_email),
    db: DatabaseManager = Depends(get_db)
):
    """
    Update user profile information.

    Args:
        updates: Profile updates to apply
        current_user_email: Email from JWT token
        db: Database manager instance

    Returns:
        Update confirmation
    """
    try:
        user = db.get_user_by_email(current_user_email)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        # For now, we'll just store updates as user preferences
        # In a full implementation, you'd update the users table for core fields

        updated_count = 0
        for key, value in updates.items():
            if key not in ["password", "email", "id"]:  # Prevent updating sensitive fields
                if db.set_user_preference(user["id"], f"profile_{key}", str(value)):
                    updated_count += 1

        logger.info(f"Profile updated for user: {current_user_email}")

        return {
            "message": "Profile updated successfully",
            "updated_fields": updated_count,
            "status": "success"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update profile error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )
