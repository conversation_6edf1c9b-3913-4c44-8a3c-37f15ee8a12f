# CodeCrusher DevTools Integrations

This package provides integrations between CodeCrusher and popular developer tools like VS Code and Git.

## Features

### VS Code Extension

- **Injection Tagging**: Auto-suggest and insert injection tags based on context and file type
- **Telemetry Peek**: View telemetry data for injections directly in VS Code
- **Command Palette Integration**: Run CodeCrusher commands directly from VS Code
- **Status Bar Widget**: See current injection stats, cache hit rate, and model uptime

### Git Hooks Integration

- **Pre-commit Hook**: Automatically scan staged code for injection points
- **Post-commit Hook**: Push telemetry summaries to a central log
- **Branch-aware Tagging**: Track injections per branch for better team collaboration

### CLI Enhancements for DevTools

- **JSON Output Modes**: All commands support JSON output for easier parsing
- **Local Server Mode**: Expose telemetry and injection info via REST API

## Installation

### VS Code Extension

1. Install the extension dependencies:

```bash
cd vscode-extension
npm install
```

2. Build the extension:

```bash
npm run package
```

3. Install the extension in VS Code:

```bash
code --install-extension codecrusher-vscode-0.1.0.vsix
```

### Git Hooks

1. Install the Git hooks:

```bash
python git-hooks/install.py
```

### CLI Enhancements

1. Install the required dependencies:

```bash
pip install -r requirements-devtools.txt
```

## Usage

### VS Code Extension

1. Open a file in VS Code
2. Use the command palette (`Ctrl+Shift+P`) to run CodeCrusher commands
3. Hover over injection tags to see telemetry data
4. Use the status bar widget to monitor CodeCrusher status

### Git Hooks

The Git hooks run automatically when you commit code:

- The pre-commit hook scans staged files for injection points
- The post-commit hook logs telemetry data and adds branch information

### Local Server Mode

Start the local server:

```bash
python cc_li.py serve run
```

This starts a FastAPI server at http://127.0.0.1:9000 with the following endpoints:

- `/api/telemetry`: Get telemetry data
- `/api/status`: Get CodeCrusher status
- `/api/tags`: Get all tags used in telemetry data
- `/api/models`: Get all models used in telemetry data
- `/api/anomalies`: Get anomalies in telemetry data

## Configuration

### VS Code Extension

Configure the extension in VS Code settings:

- `codecrusher.pythonPath`: Path to Python executable
- `codecrusher.cliPath`: Path to CodeCrusher CLI script
- `codecrusher.defaultProvider`: Default AI provider
- `codecrusher.defaultModel`: Default model
- `codecrusher.useCache`: Whether to use cache by default
- `codecrusher.showStatusBar`: Show status bar widget
- `codecrusher.telemetryRefreshInterval`: Refresh interval for telemetry data

### Git Hooks

The Git hooks use the same configuration as the CodeCrusher CLI.

### Local Server Mode

Configure the server with command-line options:

- `--host`: Host to bind to (default: 127.0.0.1)
- `--port`: Port to bind to (default: 8008)
- `--open-browser/--no-open-browser`: Open browser after starting server

## Development

### VS Code Extension

1. Clone the repository
2. Install dependencies: `npm install`
3. Build the extension: `npm run compile`
4. Launch the extension in debug mode: `F5`

### Git Hooks

1. Clone the repository
2. Install the hooks: `python git-hooks/install.py`
3. Make changes to the hooks
4. Test the hooks by making a commit

### Local Server Mode

1. Clone the repository
2. Install dependencies: `pip install -r requirements-devtools.txt`
3. Start the server: `python cc_li.py serve run`
4. Access the API at http://127.0.0.1:9000/docs
