# ⬇️ AI_INJECT: import_cache_manager
import argparse
import os
import sys
import requests
import difflib
import yaml
import json
from datetime import datetime
from dotenv import load_dotenv
from colorama import Fore, Style, init
from cache_manager import check_cache, save_cache, ensure_cache_dir

# Import rich CLI utilities
try:
    from cli_utils import (
        print_success, print_warning, print_error, print_info,
        spinner_wrap, log_with_timestamp, confirm_action, console,
        validate_inputs
    )
    USE_RICH = True
except ImportError:
    # Fallback to colorama if rich is not available
    USE_RICH = False
    init(autoreset=True)

    # Helper function for colored logging with colorama
    def print_info(msg, color=Fore.WHITE):
        print(f"{color}[{datetime.now()}] {msg}{Style.RESET_ALL}")

    # Define aliases for colorama functions to match rich interface
    def print_success(msg):
        print_info(msg, color=Fore.GREEN)

    def print_warning(msg):
        print_info(msg, color=Fore.YELLOW)

    def print_error(msg):
        print_info(msg, color=Fore.RED)

    def log_with_timestamp(msg, level="info"):
        if level == "success":
            print_info(msg, color=Fore.GREEN)
        elif level == "warning":
            print_info(msg, color=Fore.YELLOW)
        elif level == "error":
            print_info(msg, color=Fore.RED)
        else:  # info
            print_info(msg, color=Fore.CYAN)

    def confirm_action(prompt):
        response = input(f"{Fore.YELLOW}{prompt} (y/n): {Style.RESET_ALL}").lower()
        return response == 'y'

    # Mock spinner for colorama fallback
    class MockSpinner:
        def __init__(self, text):
            self.text = text

        def __enter__(self):
            print_info(f"🔄 {self.text}...", color=Fore.CYAN)
            return self

        def __exit__(self, exc_type, exc_val, exc_tb):
            if exc_type:
                print_error(f"Failed: {exc_val}")
            else:
                print_success(f"Completed: {self.text}")

    def spinner_wrap(task_description):
        return MockSpinner(task_description)

    # Fallback implementation of validate_inputs
    def validate_inputs(source_path, prompt_text, tags):
        errors = []
        if not os.path.exists(source_path):
            print_error(f"Source file not found: {source_path}")
            errors.append("Source file not found")
        if not prompt_text:
            print_error("Prompt text is empty")
            errors.append("Empty prompt text")
        if not tags:
            print_error("No injection tags provided")
            errors.append("No tags")

        if errors:
            print_error("Aborting due to validation errors.")
            return False
        return True

# Load configuration from file
def load_config(custom_config_path=None):
    """
    Load configuration from a config file

    Args:
        custom_config_path (str, optional): Path to a custom config file

    Returns:
        dict: Configuration values or an empty dict if no config found
    """
    config_paths = []

    # Add custom config path if provided
    if custom_config_path:
        config_paths.append(custom_config_path)

    # Add default config paths
    config_paths.extend([
        os.path.expanduser("~/.codecrusherrc"),
        os.path.join(os.getcwd(), "codecrusher.config.json"),  # New JSON config in root
        os.path.join(os.getcwd(), "codecrusher", "config.yaml")
    ])

    for path in config_paths:
        if os.path.exists(path):
            try:
                with open(path, 'r') as f:
                    # Determine file format based on extension
                    if path.endswith(('.yaml', '.yml', '.codecrusherrc')):
                        config_data = yaml.safe_load(f) or {}
                    elif path.endswith('.json'):
                        config_data = json.load(f) or {}
                    else:
                        # Try YAML first, then JSON if that fails
                        try:
                            config_data = yaml.safe_load(f) or {}
                        except yaml.YAMLError:
                            # Reset file pointer to beginning
                            f.seek(0)
                            try:
                                config_data = json.load(f) or {}
                            except json.JSONDecodeError:
                                raise ValueError("File is neither valid YAML nor JSON")

                    # Validate config data
                    validate_config(config_data, path)

                    log_with_timestamp(f"📄 Loaded config from: {path}", level="info")
                    return config_data
            except Exception as e:
                print_warning(f"Error loading config from {path}: {e}")
                if path == custom_config_path:
                    # If custom config path was specified but failed, don't fall back
                    print_error(f"Custom config file specified but could not be loaded.")
                    return {}

    print_info("No config file found, using defaults.")
    return {}

def validate_config(config_data, config_path):
    """
    Validate configuration data and warn about missing or invalid keys

    Args:
        config_data (dict): The configuration data to validate
        config_path (str): Path to the config file (for error messages)
    """
    # Define expected keys and their types
    expected_keys = {
        'provider': str,
        'model': str,
        'max_retries': int,
        'timeout': int,
        'use_cache': bool,
        'default_prompt_text': str,
        'default_tags': list
    }

    # Check for missing keys
    for key, expected_type in expected_keys.items():
        if key not in config_data:
            print_warning(f"Config file {config_path} is missing key: {key}")
        elif not isinstance(config_data[key], expected_type):
            print_warning(f"Config file {config_path} has invalid type for key: {key}. Expected {expected_type.__name__}")

# Load environment variables and config
load_dotenv()
config = load_config()

# Get API keys with priority: env vars > config file
MISTRAL_API_KEY = os.getenv("MISTRAL_API_KEY") or config.get("api_keys", {}).get("mistral")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY") or config.get("api_keys", {}).get("openai")

# --- AI Function ---
def generate_code(prompt, tag=None, provider="mistral"):
    """
    Generate code using the specified AI provider.

    Args:
        prompt (str): The prompt to send to the AI
        tag (str, optional): The tag name (for logging)
        provider (str, optional): The AI provider to use

    Returns:
        str: The generated code or None if generation failed
    """
    print_info(f"Generating code for tag: {tag}")

    # ✅ Wrap long AI call with spinner
    with spinner_wrap("Calling AI to generate code..."):
        if provider.lower() == "mistral":
            return generate_code_from_mistral(prompt)
        elif provider.lower() == "openai":
            return generate_code_from_openai(prompt)
        else:
            print_error(f"Unsupported provider: {provider}")
            return None

def generate_code_from_mistral(prompt_text):
    """
    Generate code using the Mistral AI API.

    Args:
        prompt_text (str): The prompt to send to the AI

    Returns:
        str: The generated code or None if generation failed
    """
    if not MISTRAL_API_KEY:
        print_error("MISTRAL_API_KEY not found in .env or config")
        return None

    try:
        url = "https://api.mistral.ai/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {MISTRAL_API_KEY}",
            "Content-Type": "application/json"
        }
        # Use model from config or default to mistral-medium
        model = config.get("model", "mistral-medium")
        temperature = config.get("temperature", 0.7)
        timeout = config.get("timeout", 30)  # Default timeout of 30 seconds

        print_info(f"Using model: {model} with temperature: {temperature}")

        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a senior software engineer. Generate clean code blocks only."},
                {"role": "user", "content": prompt_text}
            ],
            "temperature": temperature
        }

        response = requests.post(url, headers=headers, json=payload, timeout=timeout)
        response.raise_for_status()

        content = response.json()["choices"][0]["message"]["content"]
        print_success("AI response generated successfully")
        return content
    except Exception as e:
        print_error(f"Error generating code: {e}")
        return None

def generate_code_from_openai(prompt_text):
    """
    Generate code using the OpenAI API.

    Args:
        prompt_text (str): The prompt to send to the AI

    Returns:
        str: The generated code or None if generation failed
    """
    if not OPENAI_API_KEY:
        print_error("OPENAI_API_KEY not found in .env or config")
        return None

    try:
        url = "https://api.openai.com/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        # Use model from config or default to gpt-3.5-turbo
        model = config.get("model", "gpt-3.5-turbo")
        temperature = config.get("temperature", 0.7)
        timeout = config.get("timeout", 30)  # Default timeout of 30 seconds

        print_info(f"Using model: {model} with temperature: {temperature}")

        payload = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a senior software engineer. Generate clean code blocks only."},
                {"role": "user", "content": prompt_text}
            ],
            "temperature": temperature
        }

        response = requests.post(url, headers=headers, json=payload, timeout=timeout)
        response.raise_for_status()

        content = response.json()["choices"][0]["message"]["content"]
        print_success("AI response generated successfully")
        return content
    except Exception as e:
        print_error(f"Error generating code: {e}")
        return None

# --- Validation Logic ---
def validate_source_file(source_path):
    """
    Validate that the source file exists and is a readable .py file

    Args:
        source_path (str): Path to the source file

    Returns:
        bool: True if validation passes, False otherwise
    """
    # Check if file exists
    if not os.path.exists(source_path):
        print_error(f"Source file not found: {source_path}")
        return False

    # Check if it's a file (not a directory)
    if not os.path.isfile(source_path):
        print_error(f"Source path is not a file: {source_path}")
        return False

    # Check if it's a .py file
    if not source_path.endswith('.py'):
        print_error(f"Source file must be a Python (.py) file: {source_path}")
        return False

    # Check if it's readable
    try:
        with open(source_path, 'r', encoding='utf-8') as f:
            f.read(1)  # Try to read 1 byte
        return True
    except Exception as e:
        print_error(f"Source file is not readable: {source_path} - {str(e)}")
        return False

def validate_prompt_text(prompt_text, prompt_path):
    """
    Validate that either prompt_text is provided or prompt_path is valid

    Args:
        prompt_text (str): The prompt text to validate
        prompt_path (str): Path to the prompt file

    Returns:
        bool: True if validation passes, False otherwise
    """
    # If prompt_path is provided, check if it exists and is readable
    if prompt_path:
        if not os.path.exists(prompt_path):
            print_error(f"Prompt file not found: {prompt_path}")
            return False

        if not os.path.isfile(prompt_path):
            print_error(f"Prompt path is not a file: {prompt_path}")
            return False

        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    print_warning(f"Prompt file is empty: {prompt_path}")
                    return False
            return True
        except Exception as e:
            print_error(f"Prompt file is not readable: {prompt_path} - {str(e)}")
            return False

    # If prompt_text is provided, check if it's not empty
    elif prompt_text:
        if not prompt_text.strip():
            print_error("Prompt text cannot be empty")
            return False
        return True

    # Neither prompt_text nor prompt_path is provided
    else:
        print_error("Either --prompt-text or --prompt must be provided")
        return False

def validate_tag_presence(source_path):
    """
    Validate that the source file contains at least one pair of inject tags

    Args:
        source_path (str): Path to the source file

    Returns:
        bool: True if validation passes, False otherwise
    """
    try:
        with open(source_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Check for injection tags
        if "// INJECT:" not in content:
            print_warning(f"No injection tags found in source file: {source_path}")
            print_info("Tags should be in the format: '// INJECT: tag_name'")
            return False

        return True
    except Exception as e:
        print_error(f"Error checking for tags in source file: {str(e)}")
        return False

def validate_cli_args(args):
    """
    Validate all CLI arguments

    Args:
        args (Namespace): The parsed command-line arguments

    Returns:
        bool: True if all validations pass, False otherwise
    """
    # Skip validation if showing config
    if args.show_config:
        return True

    with spinner_wrap("Validating input arguments"):
        # Validate source file
        if not validate_source_file(args.source):
            return False

        # Validate prompt text or prompt file
        if not validate_prompt_text(args.prompt_text, args.prompt):
            return False

        # Validate tag presence
        if not validate_tag_presence(args.source):
            return False

    return True

# --- Injection Logic ---
def inject_code(source_path, prompt_text=None, provider="mistral", preview=False, refresh=False, output_path=None, prompt_path=None, recursive=False, extensions=None):
    """
    Inject code into a source file or directory using AI-generated content or a prompt file

    Args:
        source_path (str): Path to the source file or directory
        prompt_text (str, optional): Prompt text to send to AI provider
        provider (str, optional): AI provider to use
        preview (bool, optional): Whether to preview changes before applying
        refresh (bool, optional): Whether to refresh the cache
        output_path (str, optional): Path to the output file
        prompt_path (str, optional): Path to the prompt file
        recursive (bool, optional): Whether to recursively scan directories
        extensions (list, optional): List of file extensions to filter by
    """
    import os
    from pathlib import Path

    # Set default extensions if not provided
    if extensions is None:
        extensions = ["py"]

    # Normalize extensions (remove dots if present)
    extensions = [ext.lstrip('.') for ext in extensions]

    # Determine if source_path is a file or directory
    source_path_obj = Path(source_path)

    if source_path_obj.is_file():
        # Single file mode - process the file directly
        files_to_process = [source_path_obj]
    elif source_path_obj.is_dir():
        # Directory mode - gather files based on recursive flag and extensions
        files_to_process = []

        if recursive:
            # Recursive scanning
            for root, dirs, files in os.walk(source_path_obj):
                # Skip common directories that should be ignored
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'build', 'dist']]

                for file in files:
                    file_path = Path(root) / file
                    if any(file.endswith(f'.{ext}') for ext in extensions):
                        files_to_process.append(file_path)
        else:
            # Non-recursive scanning
            for file_path in source_path_obj.iterdir():
                if file_path.is_file() and any(file_path.name.endswith(f'.{ext}') for ext in extensions):
                    files_to_process.append(file_path)
    else:
        print_error(f"Source path is neither a file nor directory: {source_path}")
        return

    if not files_to_process:
        print_warning(f"No files found matching extensions: {', '.join(extensions)}")
        return

    print_info(f"Found {len(files_to_process)} file(s) to process")
    if len(files_to_process) > 1:
        print_info("Files to process:")
        for file_path in files_to_process:
            print_info(f"  - {file_path}")

    # Process each file
    for file_path in files_to_process:
        print_info(f"\nProcessing file: {file_path}")

        # Read source file
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.readlines()
        except Exception as e:
            print_error(f"Error reading file {file_path}: {e}")
            continue

        # Process the file (existing logic continues below)
        _process_single_file(file_path, source_code, prompt_text, provider, preview, refresh, output_path, prompt_path)

def _process_single_file(file_path, source_code, prompt_text, provider, preview, refresh, output_path, prompt_path):
    """
    Process a single file for code injection.

    Args:
        file_path (Path): Path to the file being processed
        source_code (list): Lines of source code
        prompt_text (str): Prompt text for AI
        provider (str): AI provider
        preview (bool): Preview mode
        refresh (bool): Refresh cache
        output_path (str): Output file path
        prompt_path (str): Prompt file path
    """

    # Set default output path if not provided
    if output_path is None:
        output_path = str(file_path).replace(".py", ".v0.1.py")

    injections = {}
    is_ai_mode = prompt_text is not None

    if is_ai_mode:
        # Ensure cache directory exists
        ensure_cache_dir()

        for line in source_code:
            if line.strip().startswith("// INJECT:"):
                tag = line.strip()
                full_prompt = f"{prompt_text} for tag '{tag}'"

                # ⬇️ AI_INJECT: cache_check
                print_info(f"Processing tag: {tag}")

                # Check if we have a cached version
                with spinner_wrap(f"Checking cache for tag '{tag}'"):
                    cached_result = None if refresh else check_cache(tag, full_prompt, provider)

                if cached_result:
                    # Use cached content
                    ai_code = cached_result.get('content', cached_result.get('code', ''))  # Support both formats
                    print_success(f"[CACHE HIT] Using cached code for tag: {tag}")
                else:
                    # Get retry count from config or default to 1
                    retry_count = config.get("max_retries", config.get("retry_count", 1))

                    # Generate new content with retries
                    ai_code = None
                    for attempt in range(retry_count):
                        if attempt > 0:
                            print_warning(f"Retry attempt {attempt}/{retry_count-1} for tag: {tag}")

                        ai_code = generate_code(
                            prompt=full_prompt,
                            tag=tag,
                            provider=provider
                        )

                        if ai_code:
                            break
                    if ai_code:
                        with spinner_wrap(f"Saving generated code to cache for tag '{tag}'"):
                            save_cache(tag, full_prompt, provider, ai_code)
                        print_success(f"Injected code into tag: {tag}")
                    else:
                        print_warning(f"No AI response received for tag: {tag}")
                        continue

                # ⬇️ AI_INJECT: log_cache_use
                # Add newlines to each line if needed
                injections[tag] = [l if l.endswith('\n') else l + '\n' for l in ai_code.splitlines()]
    else:
        # Read prompt file (validation already done)
        with open(prompt_path, 'r', encoding='utf-8') as f:
            prompt_lines = f.readlines()

        current_tag = None
        buffer = []
        for line in prompt_lines:
            if line.strip().startswith("// INJECT:"):
                if current_tag:
                    injections[current_tag] = buffer
                current_tag = line.strip()
                buffer = []
            else:
                buffer.append(line)
        if current_tag:
            injections[current_tag] = buffer

    new_code = []
    injected_tags = set()

    for line in source_code:
        stripped = line.strip()
        if stripped in injections:
            new_code.extend(injections[stripped])
            injected_tags.add(stripped)
        else:
            new_code.append(line)

    if preview:
        print_info("[PREVIEW MODE] Showing injected diff only, no changes applied.")
        diff = difflib.unified_diff(source_code, new_code, fromfile="original", tofile="modified", lineterm="")

        if USE_RICH:
            from rich.syntax import Syntax
            from rich.panel import Panel

            console.print("\n🔍 Preview changes:\n")
            diff_text = ""
            for line in diff:
                diff_text += line + "\n"

            syntax = Syntax(diff_text, "diff", theme="monokai", line_numbers=True)
            console.print(Panel(syntax, title="Code Changes", border_style="cyan"))
        else:
            print(f"\n🔍 Preview changes:\n")
            for line in diff:
                if line.startswith('+'):
                    print(f"{Fore.GREEN}{line}{Style.RESET_ALL}")
                elif line.startswith('-'):
                    print(f"{Fore.RED}{line}{Style.RESET_ALL}")
                else:
                    print(line)

        if not confirm_action("Apply changes?"):
            print_error("Injection cancelled.")
            return

    with spinner_wrap(f"Writing changes to {output_path}"):
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.writelines(new_code)
        except IOError as e:
            print_error(f"Error writing to output file: {e}")
            return

    try:
        # Ensure logs directory exists
        os.makedirs("logs", exist_ok=True)
        with open("logs/injection_log.txt", 'a', encoding='utf-8') as log:
            for tag in injected_tags:
                log.write(f"[{datetime.now()}] Injected '{tag}' using {'AI' if is_ai_mode else 'Prompt'} into '{file_path}' -> '{output_path}'\n")
    except IOError as e:
        print_warning(f"Could not write to log file: {e}")

    print_success(f"Injected {len(injected_tags)} block(s) -> {output_path}")

# --- CLI Interface ---
if __name__ == "__main__":
    # Parse config file first if specified
    parser = argparse.ArgumentParser(add_help=False)
    parser.add_argument("--config", help="Path to a custom config file")
    args, _ = parser.parse_known_args()

    # Load config from specified file if provided
    if args.config:
        config = load_config(args.config)

    # Get default values from config
    default_provider = config.get("provider", config.get("ai_provider", "mistral"))
    default_model = config.get("model", "mistral-medium")
    default_use_cache = config.get("use_cache", True)
    default_prompt_text = config.get("default_prompt_text", "")
    default_tags = config.get("default_tags", [])
    default_timeout = config.get("timeout", 30)
    default_max_retries = config.get("max_retries", config.get("retry_count", 1))

    # Create the full parser with all arguments
    parser = argparse.ArgumentParser(description="CodeCrusher v3 - AI-powered code injector")

    # Add show-config argument first to check for it
    parser.add_argument(
        "--show-config",
        action="store_true",
        help="Show current configuration and exit"
    )

    # Only require source if not showing config
    if "--show-config" not in sys.argv:
        parser.add_argument("--source", required=True, help="Source code file or directory (use --recursive for directory scanning)")
    else:
        parser.add_argument("--source", help="Source code file or directory (use --recursive for directory scanning)")

    parser.add_argument("--prompt", help="Prompt file (for static injection)")
    parser.add_argument("--prompt-text", default=default_prompt_text, nargs="?", const="",
                        help=f"Prompt text to send to AI provider (default: {default_prompt_text if default_prompt_text else 'None'})")
    parser.add_argument("--provider", default=default_provider,
                        help=f"AI provider to use (default: {default_provider})")
    parser.add_argument("--model", default=default_model,
                        help=f"Model to use (default: {default_model})")
    parser.add_argument("--output", help="Output file (default: source.v0.1.py)")
    parser.add_argument("--preview", action="store_true", help="Preview changes before applying")
    parser.add_argument("--retry", type=int, default=default_max_retries,
                        help=f"Number of retry attempts if generation fails (default: {default_max_retries})")
    parser.add_argument("--timeout", type=int, default=default_timeout,
                        help=f"Timeout in seconds for API calls (default: {default_timeout})")
    parser.add_argument("--config", help="Path to a custom config file")
    parser.add_argument("--tags", nargs="+", default=default_tags,
                        help=f"Tags to use for injection (default: {default_tags if default_tags else 'None'})")

    # ⬇️ AI_INJECT: cli_flag_refresh
    parser.add_argument(
        "--refresh-cache",
        action="store_true",
        help="Force refresh AI cache even if prompt+tag exists"
    )

    # ⬇️ AI_INJECT: recursive_and_extension_support
    parser.add_argument(
        "--recursive",
        action="store_true",
        help="Recursively scan all subfolders in the source directory"
    )
    parser.add_argument(
        "--ext",
        nargs="+",
        default=["py"],
        help="List of file extensions to filter source files by (e.g., py js java). Default: py"
    )

    args = parser.parse_args()

    # Handle show-config flag
    if args.show_config:
        if USE_RICH:
            from rich.table import Table

            console.print("[cyan]📋 Current Configuration:[/cyan]")

            # Show config file locations
            locations_table = Table(title="Config File Locations")
            locations_table.add_column("Path", style="cyan")
            locations_table.add_column("Status", style="green")

            for path in [
                os.path.expanduser("~/.codecrusherrc"),
                os.path.join(os.getcwd(), "codecrusher.config.json"),
                os.path.join(os.getcwd(), "codecrusher", "config.yaml")
            ]:
                exists = "[green]✅ Found[/green]" if os.path.exists(path) else "[red]❌ Not found[/red]"
                locations_table.add_row(path, exists)

            console.print(locations_table)

            # Show loaded config values
            config_table = Table(title="Loaded Configuration Values")
            config_table.add_column("Key", style="yellow")
            config_table.add_column("Value", style="cyan")

            if not config:
                config_table.add_row("No configuration found", "")
            else:
                for key, value in config.items():
                    if key == "api_keys":
                        api_keys_str = ""
                        for provider, api_key in value.items():
                            masked_key = f"{api_key[:4]}...{api_key[-4:]}" if api_key and len(api_key) > 8 else "Not set"
                            api_keys_str += f"{provider}: {masked_key}\n"
                        config_table.add_row(key, api_keys_str)
                    else:
                        config_table.add_row(key, str(value))

            console.print(config_table)

            # Show effective settings
            settings_table = Table(title="Effective Settings")
            settings_table.add_column("Setting", style="yellow")
            settings_table.add_column("Value", style="green")

            settings_table.add_row("Provider", args.provider)
            settings_table.add_row("Model", args.model)
            settings_table.add_row("Use Cache", str(not args.refresh_cache))
            settings_table.add_row("Timeout", f"{args.timeout} seconds")
            settings_table.add_row("Max Retries", str(args.retry))
            settings_table.add_row("Default Prompt Text", args.prompt_text or "None")
            settings_table.add_row("Default Tags", str(args.tags or "None"))

            console.print(settings_table)
        else:
            print_info("📋 Current Configuration:")
            print(f"\n{Fore.CYAN}=== CodeCrusher Configuration ==={Style.RESET_ALL}")

            # Show config file locations
            print(f"\n{Fore.YELLOW}Config File Locations:{Style.RESET_ALL}")
            for path in [
                os.path.expanduser("~/.codecrusherrc"),
                os.path.join(os.getcwd(), "codecrusher.config.json"),
                os.path.join(os.getcwd(), "codecrusher", "config.yaml")
            ]:
                exists = "✅ (Found)" if os.path.exists(path) else "❌ (Not found)"
                print(f"  {path}: {exists}")

            # Show loaded config values
            print(f"\n{Fore.YELLOW}Loaded Configuration Values:{Style.RESET_ALL}")
            if not config:
                print("  No configuration file found.")
            else:
                for key, value in config.items():
                    if key == "api_keys":
                        print(f"  {key}:")
                        for provider, api_key in value.items():
                            masked_key = f"{api_key[:4]}...{api_key[-4:]}" if api_key and len(api_key) > 8 else "Not set"
                            print(f"    {provider}: {masked_key}")
                    else:
                        print(f"  {key}: {value}")

            # Show effective settings (CLI args override config)
            print(f"\n{Fore.YELLOW}Effective Settings:{Style.RESET_ALL}")
            print(f"  Provider: {args.provider}")
            print(f"  Model: {args.model}")
            print(f"  Use Cache: {not args.refresh_cache}")
            print(f"  Timeout: {args.timeout} seconds")
            print(f"  Max Retries: {args.retry}")
            print(f"  Default Prompt Text: {args.prompt_text or 'None'}")
            print(f"  Default Tags: {args.tags or 'None'}")

            print(f"\n{Fore.CYAN}==========================={Style.RESET_ALL}\n")

        exit(0)

    # Get provider from args or config
    provider = args.provider

    # ✅ Validate CLI inputs before doing anything
    if USE_RICH and hasattr(args, 'source') and hasattr(args, 'prompt_text') and hasattr(args, 'tags'):
        if not validate_inputs(source_path=args.source, prompt_text=args.prompt_text, tags=args.tags):
            sys.exit(1)
    # Fallback to original validation if validate_inputs is not available
    elif not validate_cli_args(args):
        print_error("Validation failed. Please fix the issues and try again.")
        sys.exit(1)
    else:
        print_success("Validation passed. Proceeding with code injection.")

    # Pass config values to inject_code
    with spinner_wrap(f"Injecting code into {args.source}"):
        inject_code(
            source_path=args.source,
            prompt_text=args.prompt_text,
            provider=provider,
            preview=args.preview,
            refresh=args.refresh_cache,
            output_path=args.output,
            prompt_path=args.prompt,
            recursive=args.recursive,
            extensions=args.ext
        )
