#!/usr/bin/env python3
"""
Quick fix for WebSocket reconnection settings in built frontend.
"""

import re
import os

def fix_websocket_settings():
    """Fix WebSocket settings in the built JavaScript file."""
    
    js_file = "frontend/dist/assets/index-515198a6.js"
    
    if not os.path.exists(js_file):
        print(f"❌ File not found: {js_file}")
        return False
    
    print(f"🔧 Fixing WebSocket settings in {js_file}")
    
    try:
        # Read the file
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Make WebSocket more aggressive with reconnection
        original_content = content
        
        # Fix reconnection settings
        content = re.sub(r'maxRetries:\s*\d+', 'maxRetries: 999', content)
        content = re.sub(r'maxReconnectDelay:\s*\d+', 'maxReconnectDelay: 2000', content)
        content = re.sub(r'timeoutMs:\s*\d+', 'timeoutMs: 5000', content)
        content = re.sub(r'autoConnect:\s*false', 'autoConnect: true', content)
        
        # Also fix any hardcoded retry limits
        content = re.sub(r'retryCount\s*>=\s*\d+', 'retryCount >= 999', content)
        content = re.sub(r'attempt\s*\d+/\d+', 'attempt ${retryCount}/999', content)
        
        # Write back if changed
        if content != original_content:
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ WebSocket settings updated successfully!")
            print("   - maxRetries: 999 (was limited)")
            print("   - maxReconnectDelay: 2000ms (was higher)")
            print("   - timeoutMs: 5000ms (was higher)")
            print("   - autoConnect: true (was false)")
            return True
        else:
            print("⚠️ No changes needed - settings already optimal")
            return True
            
    except Exception as e:
        print(f"❌ Error fixing WebSocket settings: {e}")
        return False

if __name__ == "__main__":
    print("🔧 WebSocket Connection Fix Tool")
    print("=" * 40)
    
    success = fix_websocket_settings()
    
    if success:
        print("\n🎉 WebSocket fix completed!")
        print("💡 Restart the frontend server to apply changes")
        print("📝 The AI optimization button should stay active now")
    else:
        print("\n❌ WebSocket fix failed!")
        print("🔧 Manual intervention may be required")
