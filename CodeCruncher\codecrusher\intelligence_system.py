#!/usr/bin/env python3
"""
CodeCrusher Intelligence System
Handles rating, learning, and prompt shaping based on user feedback
"""

import os
import json
import sqlite3
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from rich.console import Console

console = Console()

class IntelligenceSystem:
    """Main intelligence system for learning from user feedback and improving AI responses."""
    
    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.base_dir = Path.home() / ".codecrusher"
        self.logs_dir = self.base_dir / "e2e-logs"
        self.db_path = self.base_dir / "intelligence.db"
        self.shaping_config_path = self.base_dir / "shaping_config.json"
        
        # Ensure directories exist
        self.base_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Load or create shaping configuration
        self._load_shaping_config()
    
    def _init_database(self):
        """Initialize the intelligence database."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS feedback (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    source_path TEXT NOT NULL,
                    rating INTEGER NOT NULL,
                    comment TEXT,
                    prompt_used TEXT,
                    model_used TEXT,
                    shaping_params TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS learning_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    entries_analyzed INTEGER,
                    improvements_made INTEGER,
                    parameters_updated TEXT,
                    before_config TEXT,
                    after_config TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
    
    def _load_shaping_config(self):
        """Load or create default shaping configuration."""
        default_config = {
            "prompt_style": "detailed",
            "fallback_sensitivity": 0.7,
            "model_preference": ["gpt-4", "claude-3", "llama-70b"],
            "tone": "professional",
            "verbosity": "medium",
            "error_handling": "comprehensive",
            "code_style": "clean",
            "last_updated": datetime.now().isoformat()
        }
        
        if self.shaping_config_path.exists():
            try:
                with open(self.shaping_config_path, 'r') as f:
                    self.shaping_config = json.load(f)
            except Exception as e:
                if self.verbose:
                    console.print(f"[yellow]Warning: Could not load shaping config: {e}[/yellow]")
                self.shaping_config = default_config
        else:
            self.shaping_config = default_config
            self._save_shaping_config()
    
    def _save_shaping_config(self):
        """Save shaping configuration to file."""
        try:
            with open(self.shaping_config_path, 'w') as f:
                json.dump(self.shaping_config, f, indent=2)
        except Exception as e:
            if self.verbose:
                console.print(f"[yellow]Warning: Could not save shaping config: {e}[/yellow]")
    
    def rate_injection(
        self, 
        source_path: str, 
        rating: int, 
        comment: Optional[str] = None,
        recursive: bool = False
    ) -> Dict[str, Any]:
        """Rate injection results and store feedback."""
        try:
            timestamp = datetime.now().isoformat()
            files_rated = 0
            
            # Find files to rate
            files_to_rate = self._find_files_to_rate(source_path, recursive)
            
            with sqlite3.connect(self.db_path) as conn:
                for file_path in files_to_rate:
                    conn.execute("""
                        INSERT INTO feedback 
                        (timestamp, source_path, rating, comment, prompt_used, model_used, shaping_params)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        timestamp,
                        str(file_path),
                        rating,
                        comment,
                        "test_prompt",  # Would be actual prompt in real implementation
                        "auto",
                        json.dumps(self.shaping_config)
                    ))
                    files_rated += 1
                
                conn.commit()
            
            # Calculate average rating
            average_rating = self._get_average_rating()
            total_entries = self._get_total_entries()
            
            # Log the rating
            self._log_rating(source_path, rating, comment, files_rated)
            
            return {
                'success': True,
                'files_rated': files_rated,
                'average_rating': average_rating,
                'total_entries': total_entries
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'files_rated': 0
            }
    
    def learn_from_feedback(
        self, 
        apply_changes: bool = False, 
        dry_run: bool = False
    ) -> Dict[str, Any]:
        """Learn from feedback and update shaping parameters."""
        try:
            # Analyze feedback
            feedback_analysis = self._analyze_feedback()
            
            if not feedback_analysis['has_data']:
                return {
                    'success': False,
                    'error': 'No feedback data available for learning',
                    'entries_analyzed': 0,
                    'improvements_made': 0
                }
            
            # Generate improvements based on analysis
            improvements = self._generate_improvements(feedback_analysis)
            
            # Store current config for comparison
            before_config = self.shaping_config.copy()
            
            if apply_changes and not dry_run:
                # Apply improvements
                self._apply_improvements(improvements)
                self._save_shaping_config()
            
            # Log learning session
            self._log_learning_session(
                feedback_analysis['total_entries'],
                len(improvements),
                improvements,
                before_config,
                self.shaping_config if apply_changes else before_config
            )
            
            return {
                'success': True,
                'entries_analyzed': feedback_analysis['total_entries'],
                'improvements_made': len(improvements),
                'parameters_updated': len(improvements),
                'improvements': [imp['description'] for imp in improvements]
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'entries_analyzed': 0,
                'improvements_made': 0
            }
    
    def _find_files_to_rate(self, source_path: str, recursive: bool) -> List[Path]:
        """Find files to rate based on source path and recursive flag."""
        path = Path(source_path)
        files = []
        
        if path.is_file():
            files.append(path)
        elif path.is_dir():
            if recursive:
                files.extend(path.rglob("*.py"))
                files.extend(path.rglob("*.js"))
                files.extend(path.rglob("*.ts"))
            else:
                files.extend(path.glob("*.py"))
                files.extend(path.glob("*.js"))
                files.extend(path.glob("*.ts"))
        
        return files
    
    def _get_average_rating(self) -> float:
        """Get average rating from all feedback."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT AVG(rating) FROM feedback")
            result = cursor.fetchone()
            return result[0] if result[0] is not None else 0.0
    
    def _get_total_entries(self) -> int:
        """Get total number of feedback entries."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT COUNT(*) FROM feedback")
            result = cursor.fetchone()
            return result[0] if result else 0
    
    def _analyze_feedback(self) -> Dict[str, Any]:
        """Analyze feedback to identify patterns and issues."""
        with sqlite3.connect(self.db_path) as conn:
            # Get all feedback
            cursor = conn.execute("""
                SELECT rating, comment, timestamp, shaping_params 
                FROM feedback 
                ORDER BY created_at DESC
            """)
            feedback_data = cursor.fetchall()
        
        if not feedback_data:
            return {'has_data': False, 'total_entries': 0}
        
        # Analyze ratings
        ratings = [row[0] for row in feedback_data]
        avg_rating = sum(ratings) / len(ratings)
        low_ratings = [r for r in ratings if r <= 2]
        
        # Analyze comments for patterns
        comments = [row[1] for row in feedback_data if row[1]]
        
        return {
            'has_data': True,
            'total_entries': len(feedback_data),
            'average_rating': avg_rating,
            'low_rating_count': len(low_ratings),
            'low_rating_percentage': len(low_ratings) / len(ratings) * 100,
            'comments': comments,
            'needs_improvement': avg_rating < 3.0 or len(low_ratings) > len(ratings) * 0.3
        }
    
    def _generate_improvements(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate improvements based on feedback analysis."""
        improvements = []
        
        if analysis['average_rating'] < 3.0:
            improvements.append({
                'parameter': 'prompt_style',
                'old_value': self.shaping_config['prompt_style'],
                'new_value': 'comprehensive',
                'description': 'Switched to comprehensive prompt style due to low ratings'
            })
            
            improvements.append({
                'parameter': 'verbosity',
                'old_value': self.shaping_config['verbosity'],
                'new_value': 'high',
                'description': 'Increased verbosity to provide more detailed responses'
            })
        
        if analysis['low_rating_percentage'] > 40:
            improvements.append({
                'parameter': 'fallback_sensitivity',
                'old_value': self.shaping_config['fallback_sensitivity'],
                'new_value': 0.5,
                'description': 'Lowered fallback sensitivity to trigger fallback strategies sooner'
            })
            
            improvements.append({
                'parameter': 'error_handling',
                'old_value': self.shaping_config['error_handling'],
                'new_value': 'extensive',
                'description': 'Enhanced error handling due to poor feedback'
            })
        
        # Analyze comments for specific improvements
        for comment in analysis.get('comments', []):
            if comment and 'optimization' in comment.lower():
                improvements.append({
                    'parameter': 'code_style',
                    'old_value': self.shaping_config['code_style'],
                    'new_value': 'optimized',
                    'description': 'Switched to optimized code style based on user feedback'
                })
        
        return improvements
    
    def _apply_improvements(self, improvements: List[Dict[str, Any]]):
        """Apply improvements to shaping configuration."""
        for improvement in improvements:
            self.shaping_config[improvement['parameter']] = improvement['new_value']
        
        self.shaping_config['last_updated'] = datetime.now().isoformat()
    
    def _log_rating(self, source_path: str, rating: int, comment: Optional[str], files_rated: int):
        """Log rating to file."""
        log_file = self.logs_dir / f"rating_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'source_path': source_path,
            'rating': rating,
            'comment': comment,
            'files_rated': files_rated,
            'average_rating': self._get_average_rating()
        }
        
        with open(log_file, 'w') as f:
            json.dump(log_entry, f, indent=2)
    
    def _log_learning_session(
        self, 
        entries_analyzed: int, 
        improvements_made: int, 
        improvements: List[Dict[str, Any]],
        before_config: Dict[str, Any],
        after_config: Dict[str, Any]
    ):
        """Log learning session to database and file."""
        timestamp = datetime.now().isoformat()
        
        # Log to database
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO learning_sessions 
                (timestamp, entries_analyzed, improvements_made, parameters_updated, before_config, after_config)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                timestamp,
                entries_analyzed,
                improvements_made,
                json.dumps([imp['parameter'] for imp in improvements]),
                json.dumps(before_config),
                json.dumps(after_config)
            ))
            conn.commit()
        
        # Log to file
        log_file = self.logs_dir / f"learning_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        log_entry = {
            'timestamp': timestamp,
            'entries_analyzed': entries_analyzed,
            'improvements_made': improvements_made,
            'improvements': improvements,
            'before_config': before_config,
            'after_config': after_config
        }
        
        with open(log_file, 'w') as f:
            json.dump(log_entry, f, indent=2)
