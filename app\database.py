"""
Database module for CodeCrusher
Handles user management and authentication data storage
"""

import sqlite3
import os
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
from pathlib import Path

logger = logging.getLogger(__name__)

# Database configuration
DB_DIR = Path.home() / ".codecrusher" / "db"
DB_PATH = DB_DIR / "codecrusher.db"

class DatabaseManager:
    """Manages SQLite database operations for CodeCrusher."""

    def __init__(self, db_path: str = None):
        """Initialize database manager."""
        self.db_path = db_path or str(DB_PATH)
        self.ensure_db_directory()
        self.init_database()

    def ensure_db_directory(self):
        """Ensure database directory exists."""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Database directory ensured: {db_dir}")

    @contextmanager
    def get_connection(self):
        """Get database connection with context manager."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # Enable dict-like access
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database error: {e}")
            raise
        finally:
            if conn:
                conn.close()

    def init_database(self):
        """Initialize database with required tables."""
        with self.get_connection() as conn:
            # Users table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT UNIQUE NOT NULL,
                    hashed_password TEXT NOT NULL,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            """)

            # User sessions table (for token management)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    token_hash TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            # User preferences table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    preference_key TEXT NOT NULL,
                    preference_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(user_id, preference_key)
                )
            """)

            # Teams table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS teams (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_by INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            """)

            # Team memberships table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS team_memberships (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    team_id INTEGER NOT NULL,
                    role TEXT NOT NULL DEFAULT 'member',
                    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (team_id) REFERENCES teams (id),
                    UNIQUE(user_id, team_id)
                )
            """)

            # Team invitations table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS team_invitations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    team_id INTEGER NOT NULL,
                    invited_email TEXT NOT NULL,
                    invited_by INTEGER NOT NULL,
                    role TEXT NOT NULL DEFAULT 'member',
                    invitation_token TEXT UNIQUE NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    accepted_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (team_id) REFERENCES teams (id),
                    FOREIGN KEY (invited_by) REFERENCES users (id)
                )
            """)

            # Injection logs table (team-aware)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS injection_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    team_id INTEGER,
                    file_path TEXT NOT NULL,
                    prompt TEXT NOT NULL,
                    model TEXT NOT NULL,
                    mode TEXT NOT NULL DEFAULT 'preview',
                    tags TEXT,
                    intent TEXT,
                    success BOOLEAN NOT NULL,
                    output TEXT,
                    error_message TEXT,
                    execution_time REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    rating INTEGER,
                    feedback TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (team_id) REFERENCES teams (id)
                )
            """)

            # Prompt memory table (team-aware)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS prompt_memory (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    team_id INTEGER,
                    prompt_hash TEXT NOT NULL,
                    original_prompt TEXT NOT NULL,
                    shaped_prompt TEXT NOT NULL,
                    model TEXT NOT NULL,
                    intent TEXT,
                    success_count INTEGER DEFAULT 0,
                    failure_count INTEGER DEFAULT 0,
                    avg_rating REAL,
                    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (team_id) REFERENCES teams (id),
                    UNIQUE(prompt_hash, team_id)
                )
            """)

            # Team settings table (shared configurations)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS team_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    team_id INTEGER NOT NULL,
                    setting_key TEXT NOT NULL,
                    setting_value TEXT,
                    created_by INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (team_id) REFERENCES teams (id),
                    FOREIGN KEY (created_by) REFERENCES users (id),
                    UNIQUE(team_id, setting_key)
                )
            """)

            conn.commit()
            logger.info("Database initialized successfully")

    # User management methods
    def create_user(self, email: str, hashed_password: str, role: str = "user") -> Optional[int]:
        """Create a new user."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "INSERT INTO users (email, hashed_password, role) VALUES (?, ?, ?)",
                    (email, hashed_password, role)
                )
                conn.commit()
                user_id = cursor.lastrowid
                logger.info(f"User created successfully: {email} (ID: {user_id})")
                return user_id
        except sqlite3.IntegrityError:
            logger.warning(f"User creation failed - email already exists: {email}")
            return None
        except Exception as e:
            logger.error(f"User creation error: {e}")
            return None

    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM users WHERE email = ? AND is_active = 1",
                    (email,)
                )
                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None
        except Exception as e:
            logger.error(f"Get user error: {e}")
            return None

    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM users WHERE id = ? AND is_active = 1",
                    (user_id,)
                )
                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None
        except Exception as e:
            logger.error(f"Get user by ID error: {e}")
            return None

    def update_last_login(self, user_id: int) -> bool:
        """Update user's last login timestamp."""
        try:
            with self.get_connection() as conn:
                conn.execute(
                    "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                    (user_id,)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Update last login error: {e}")
            return False

    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user account."""
        try:
            with self.get_connection() as conn:
                conn.execute(
                    "UPDATE users SET is_active = 0 WHERE id = ?",
                    (user_id,)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Deactivate user error: {e}")
            return False

    def get_all_users(self, include_inactive: bool = False) -> List[Dict[str, Any]]:
        """Get all users."""
        try:
            with self.get_connection() as conn:
                if include_inactive:
                    cursor = conn.execute("SELECT * FROM users ORDER BY created_at DESC")
                else:
                    cursor = conn.execute("SELECT * FROM users WHERE is_active = 1 ORDER BY created_at DESC")

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Get all users error: {e}")
            return []

    # Session management methods
    def create_session(self, user_id: int, token_hash: str, expires_at: datetime) -> Optional[int]:
        """Create a user session."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "INSERT INTO user_sessions (user_id, token_hash, expires_at) VALUES (?, ?, ?)",
                    (user_id, token_hash, expires_at)
                )
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            logger.error(f"Create session error: {e}")
            return None

    def invalidate_session(self, token_hash: str) -> bool:
        """Invalidate a user session."""
        try:
            with self.get_connection() as conn:
                conn.execute(
                    "UPDATE user_sessions SET is_active = 0 WHERE token_hash = ?",
                    (token_hash,)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Invalidate session error: {e}")
            return False

    def cleanup_expired_sessions(self) -> int:
        """Clean up expired sessions."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP"
                )
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"Cleanup sessions error: {e}")
            return 0

    # User preferences methods
    def set_user_preference(self, user_id: int, key: str, value: str) -> bool:
        """Set user preference."""
        try:
            with self.get_connection() as conn:
                conn.execute(
                    """INSERT OR REPLACE INTO user_preferences
                       (user_id, preference_key, preference_value, updated_at)
                       VALUES (?, ?, ?, CURRENT_TIMESTAMP)""",
                    (user_id, key, value)
                )
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Set user preference error: {e}")
            return False

    def get_user_preference(self, user_id: int, key: str) -> Optional[str]:
        """Get user preference."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT preference_value FROM user_preferences WHERE user_id = ? AND preference_key = ?",
                    (user_id, key)
                )
                row = cursor.fetchone()
                return row[0] if row else None
        except Exception as e:
            logger.error(f"Get user preference error: {e}")
            return None

    def get_user_preferences(self, user_id: int) -> Dict[str, str]:
        """Get all user preferences."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT preference_key, preference_value FROM user_preferences WHERE user_id = ?",
                    (user_id,)
                )
                return {row[0]: row[1] for row in cursor.fetchall()}
        except Exception as e:
            logger.error(f"Get user preferences error: {e}")
            return {}

    # Team management methods
    def create_team(self, name: str, description: str, created_by: int) -> Optional[int]:
        """Create a new team."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "INSERT INTO teams (name, description, created_by) VALUES (?, ?, ?)",
                    (name, description, created_by)
                )
                team_id = cursor.lastrowid

                # Add creator as owner
                conn.execute(
                    "INSERT INTO team_memberships (user_id, team_id, role) VALUES (?, ?, 'owner')",
                    (created_by, team_id)
                )

                conn.commit()
                logger.info(f"Team created successfully: {name} (ID: {team_id})")
                return team_id
        except Exception as e:
            logger.error(f"Team creation error: {e}")
            return None

    def get_user_teams(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all teams for a user."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT t.*, tm.role, tm.joined_at
                    FROM teams t
                    JOIN team_memberships tm ON t.id = tm.team_id
                    WHERE tm.user_id = ? AND tm.is_active = 1 AND t.is_active = 1
                    ORDER BY tm.joined_at DESC
                """, (user_id,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Get user teams error: {e}")
            return []

    def get_team_by_id(self, team_id: int) -> Optional[Dict[str, Any]]:
        """Get team by ID."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM teams WHERE id = ? AND is_active = 1",
                    (team_id,)
                )
                row = cursor.fetchone()
                if row:
                    return dict(row)
                return None
        except Exception as e:
            logger.error(f"Get team by ID error: {e}")
            return None

    def get_team_members(self, team_id: int) -> List[Dict[str, Any]]:
        """Get all members of a team."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT u.id, u.email, u.role as user_role, u.created_at as user_created_at,
                           tm.role as team_role, tm.joined_at
                    FROM users u
                    JOIN team_memberships tm ON u.id = tm.user_id
                    WHERE tm.team_id = ? AND tm.is_active = 1 AND u.is_active = 1
                    ORDER BY tm.joined_at ASC
                """, (team_id,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Get team members error: {e}")
            return []

    def check_team_membership(self, user_id: int, team_id: int) -> Optional[str]:
        """Check if user is member of team and return role."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT role FROM team_memberships WHERE user_id = ? AND team_id = ? AND is_active = 1",
                    (user_id, team_id)
                )
                row = cursor.fetchone()
                return row[0] if row else None
        except Exception as e:
            logger.error(f"Check team membership error: {e}")
            return None

    def create_team_invitation(self, team_id: int, invited_email: str, invited_by: int,
                              role: str = "member", expires_at: datetime = None) -> Optional[str]:
        """Create a team invitation."""
        import uuid
        from datetime import timedelta

        if expires_at is None:
            expires_at = datetime.now() + timedelta(days=7)  # 7 days expiry

        invitation_token = str(uuid.uuid4())

        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO team_invitations
                    (team_id, invited_email, invited_by, role, invitation_token, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (team_id, invited_email, invited_by, role, invitation_token, expires_at))

                conn.commit()
                logger.info(f"Team invitation created: {invited_email} to team {team_id}")
                return invitation_token
        except Exception as e:
            logger.error(f"Create team invitation error: {e}")
            return None

    def accept_team_invitation(self, invitation_token: str, user_id: int) -> bool:
        """Accept a team invitation."""
        try:
            with self.get_connection() as conn:
                # Get invitation details
                cursor = conn.execute("""
                    SELECT team_id, role, invited_email, expires_at
                    FROM team_invitations
                    WHERE invitation_token = ? AND is_active = 1
                """, (invitation_token,))

                invitation = cursor.fetchone()
                if not invitation:
                    return False

                # Check if invitation is expired
                expires_at = datetime.fromisoformat(invitation[3])
                if datetime.now() > expires_at:
                    return False

                team_id, role, invited_email, _ = invitation

                # Add user to team
                conn.execute("""
                    INSERT OR REPLACE INTO team_memberships
                    (user_id, team_id, role, is_active)
                    VALUES (?, ?, ?, 1)
                """, (user_id, team_id, role))

                # Mark invitation as accepted
                conn.execute("""
                    UPDATE team_invitations
                    SET accepted_at = CURRENT_TIMESTAMP, is_active = 0
                    WHERE invitation_token = ?
                """, (invitation_token,))

                conn.commit()
                logger.info(f"Team invitation accepted: user {user_id} joined team {team_id}")
                return True
        except Exception as e:
            logger.error(f"Accept team invitation error: {e}")
            return False

    # Injection logs methods (team-aware)
    def log_injection(self, user_id: int, team_id: Optional[int], file_path: str,
                     prompt: str, model: str, mode: str, tags: List[str],
                     intent: str, success: bool, output: str = None,
                     error_message: str = None, execution_time: float = None) -> Optional[int]:
        """Log an injection with team context."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO injection_logs
                    (user_id, team_id, file_path, prompt, model, mode, tags, intent,
                     success, output, error_message, execution_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (user_id, team_id, file_path, prompt, model, mode,
                      ','.join(tags) if tags else None, intent, success,
                      output, error_message, execution_time))

                conn.commit()
                log_id = cursor.lastrowid
                logger.info(f"Injection logged: {file_path} (ID: {log_id}, Team: {team_id})")
                return log_id
        except Exception as e:
            logger.error(f"Log injection error: {e}")
            return None

    def get_injection_logs(self, user_id: int = None, team_id: int = None,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """Get injection logs with team filtering."""
        try:
            with self.get_connection() as conn:
                if team_id:
                    # Get team logs (all members)
                    cursor = conn.execute("""
                        SELECT il.*, u.email as user_email
                        FROM injection_logs il
                        JOIN users u ON il.user_id = u.id
                        WHERE il.team_id = ?
                        ORDER BY il.created_at DESC
                        LIMIT ?
                    """, (team_id, limit))
                elif user_id:
                    # Get user logs (personal only)
                    cursor = conn.execute("""
                        SELECT il.*, u.email as user_email
                        FROM injection_logs il
                        JOIN users u ON il.user_id = u.id
                        WHERE il.user_id = ? AND il.team_id IS NULL
                        ORDER BY il.created_at DESC
                        LIMIT ?
                    """, (user_id, limit))
                else:
                    # Get all logs
                    cursor = conn.execute("""
                        SELECT il.*, u.email as user_email
                        FROM injection_logs il
                        JOIN users u ON il.user_id = u.id
                        ORDER BY il.created_at DESC
                        LIMIT ?
                    """, (limit,))

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Get injection logs error: {e}")
            return []

    def rate_injection(self, log_id: int, rating: int, feedback: str = None) -> bool:
        """Rate an injection log."""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    UPDATE injection_logs
                    SET rating = ?, feedback = ?
                    WHERE id = ?
                """, (rating, feedback, log_id))

                conn.commit()
                logger.info(f"Injection rated: {log_id} -> {rating}/5")
                return True
        except Exception as e:
            logger.error(f"Rate injection error: {e}")
            return False

    # Prompt memory methods (team-aware)
    def save_prompt_memory(self, user_id: int, team_id: Optional[int],
                          prompt_hash: str, original_prompt: str,
                          shaped_prompt: str, model: str, intent: str) -> bool:
        """Save prompt shaping memory with team context."""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO prompt_memory
                    (user_id, team_id, prompt_hash, original_prompt, shaped_prompt,
                     model, intent, success_count, last_used)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP)
                """, (user_id, team_id, prompt_hash, original_prompt,
                      shaped_prompt, model, intent))

                conn.commit()
                logger.info(f"Prompt memory saved: {prompt_hash} (Team: {team_id})")
                return True
        except Exception as e:
            logger.error(f"Save prompt memory error: {e}")
            return False

    def get_prompt_memory(self, team_id: int = None, user_id: int = None) -> List[Dict[str, Any]]:
        """Get prompt memory with team context."""
        try:
            with self.get_connection() as conn:
                if team_id:
                    # Get team prompt memory
                    cursor = conn.execute("""
                        SELECT pm.*, u.email as user_email
                        FROM prompt_memory pm
                        JOIN users u ON pm.user_id = u.id
                        WHERE pm.team_id = ?
                        ORDER BY pm.last_used DESC
                    """, (team_id,))
                elif user_id:
                    # Get user prompt memory (personal only)
                    cursor = conn.execute("""
                        SELECT pm.*, u.email as user_email
                        FROM prompt_memory pm
                        JOIN users u ON pm.user_id = u.id
                        WHERE pm.user_id = ? AND pm.team_id IS NULL
                        ORDER BY pm.last_used DESC
                    """, (user_id,))
                else:
                    # Get all prompt memory
                    cursor = conn.execute("""
                        SELECT pm.*, u.email as user_email
                        FROM prompt_memory pm
                        JOIN users u ON pm.user_id = u.id
                        ORDER BY pm.last_used DESC
                    """)

                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"Get prompt memory error: {e}")
            return []

    def update_prompt_success(self, prompt_hash: str, team_id: int, success: bool, rating: int = None) -> bool:
        """Update prompt memory success/failure counts."""
        try:
            with self.get_connection() as conn:
                if success:
                    conn.execute("""
                        UPDATE prompt_memory
                        SET success_count = success_count + 1,
                            avg_rating = CASE
                                WHEN ? IS NOT NULL THEN
                                    (COALESCE(avg_rating, 0) * success_count + ?) / (success_count + 1)
                                ELSE avg_rating
                            END,
                            last_used = CURRENT_TIMESTAMP
                        WHERE prompt_hash = ? AND team_id = ?
                    """, (rating, rating, prompt_hash, team_id))
                else:
                    conn.execute("""
                        UPDATE prompt_memory
                        SET failure_count = failure_count + 1,
                            last_used = CURRENT_TIMESTAMP
                        WHERE prompt_hash = ? AND team_id = ?
                    """, (prompt_hash, team_id))

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Update prompt success error: {e}")
            return False

    # Team settings methods
    def set_team_setting(self, team_id: int, key: str, value: str, user_id: int) -> bool:
        """Set team setting."""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO team_settings
                    (team_id, setting_key, setting_value, created_by, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (team_id, key, value, user_id))

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Set team setting error: {e}")
            return False

    def get_team_setting(self, team_id: int, key: str) -> Optional[str]:
        """Get team setting."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT setting_value FROM team_settings
                    WHERE team_id = ? AND setting_key = ?
                """, (team_id, key))

                row = cursor.fetchone()
                return row[0] if row else None
        except Exception as e:
            logger.error(f"Get team setting error: {e}")
            return None

    def get_team_settings(self, team_id: int) -> Dict[str, str]:
        """Get all team settings."""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT setting_key, setting_value FROM team_settings
                    WHERE team_id = ?
                """, (team_id,))

                return {row[0]: row[1] for row in cursor.fetchall()}
        except Exception as e:
            logger.error(f"Get team settings error: {e}")
            return {}

# Global database instance
db_manager = DatabaseManager()

# Convenience functions
def get_db() -> DatabaseManager:
    """Get database manager instance."""
    return db_manager
