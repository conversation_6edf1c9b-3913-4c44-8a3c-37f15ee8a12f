"""
Team Activity Event System for CodeCrusher
Handles real-time team collaboration events and WebSocket broadcasting
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Types of team activity events."""
    INJECTION_CREATED = "injection_created"
    INJECTION_RATED = "injection_rated"
    PROMPT_SHAPED = "prompt_shaped"
    TEAM_SETTING_CHANGED = "team_setting_changed"
    USER_JOINED_TEAM = "user_joined_team"
    USER_LEFT_TEAM = "user_left_team"

@dataclass
class TeamEvent:
    """Team activity event data structure."""
    event_id: str
    team_id: int
    event_type: EventType
    user_id: int
    user_email: str
    timestamp: str
    payload: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary for JSON serialization."""
        return {
            "event_id": self.event_id,
            "team_id": self.team_id,
            "event_type": self.event_type.value,
            "user_id": self.user_id,
            "user_email": self.user_email,
            "timestamp": self.timestamp,
            "payload": self.payload
        }
    
    def to_json(self) -> str:
        """Convert event to JSON string."""
        return json.dumps(self.to_dict())

class TeamEventManager:
    """Manages team activity events and WebSocket broadcasting."""
    
    def __init__(self):
        # Store active WebSocket connections by team_id
        self.team_connections: Dict[int, List] = {}
        # Store recent events for new connections
        self.recent_events: Dict[int, List[TeamEvent]] = {}
        self.max_recent_events = 50
    
    def add_team_connection(self, team_id: int, websocket, user_id: int):
        """Add a WebSocket connection to a team."""
        if team_id not in self.team_connections:
            self.team_connections[team_id] = []
        
        # Store connection with user info
        connection_info = {
            "websocket": websocket,
            "user_id": user_id,
            "connected_at": datetime.now().isoformat()
        }
        
        self.team_connections[team_id].append(connection_info)
        logger.info(f"User {user_id} connected to team {team_id} WebSocket")
        
        # Send recent events to new connection
        self._send_recent_events(team_id, websocket)
    
    def remove_team_connection(self, team_id: int, websocket):
        """Remove a WebSocket connection from a team."""
        if team_id in self.team_connections:
            self.team_connections[team_id] = [
                conn for conn in self.team_connections[team_id] 
                if conn["websocket"] != websocket
            ]
            
            # Clean up empty team connections
            if not self.team_connections[team_id]:
                del self.team_connections[team_id]
            
            logger.info(f"WebSocket disconnected from team {team_id}")
    
    def _send_recent_events(self, team_id: int, websocket):
        """Send recent events to a newly connected WebSocket."""
        if team_id in self.recent_events:
            recent_events = self.recent_events[team_id][-10:]  # Last 10 events
            for event in recent_events:
                try:
                    websocket.send_text(json.dumps({
                        "type": "recent_event",
                        "event": event.to_dict()
                    }))
                except Exception as e:
                    logger.error(f"Failed to send recent event: {e}")
    
    async def broadcast_event(self, event: TeamEvent):
        """Broadcast an event to all team members."""
        team_id = event.team_id
        
        # Store event in recent events
        if team_id not in self.recent_events:
            self.recent_events[team_id] = []
        
        self.recent_events[team_id].append(event)
        
        # Keep only recent events
        if len(self.recent_events[team_id]) > self.max_recent_events:
            self.recent_events[team_id] = self.recent_events[team_id][-self.max_recent_events:]
        
        # Broadcast to all connected team members
        if team_id in self.team_connections:
            message = json.dumps({
                "type": "team_event",
                "event": event.to_dict()
            })
            
            # Send to all connections, remove failed ones
            active_connections = []
            for connection_info in self.team_connections[team_id]:
                try:
                    await connection_info["websocket"].send_text(message)
                    active_connections.append(connection_info)
                except Exception as e:
                    logger.warning(f"Failed to send event to WebSocket: {e}")
            
            # Update active connections
            self.team_connections[team_id] = active_connections
        
        logger.info(f"Broadcasted {event.event_type.value} event to team {team_id}")
    
    def create_injection_event(self, team_id: int, user_id: int, user_email: str, 
                              injection_data: Dict[str, Any]) -> TeamEvent:
        """Create an injection created event."""
        import uuid
        
        return TeamEvent(
            event_id=str(uuid.uuid4()),
            team_id=team_id,
            event_type=EventType.INJECTION_CREATED,
            user_id=user_id,
            user_email=user_email,
            timestamp=datetime.now().isoformat(),
            payload={
                "injection_id": injection_data.get("id"),
                "file_path": injection_data.get("file_path"),
                "model": injection_data.get("model"),
                "mode": injection_data.get("mode"),
                "tags": injection_data.get("tags"),
                "intent": injection_data.get("intent"),
                "success": injection_data.get("success")
            }
        )
    
    def create_rating_event(self, team_id: int, user_id: int, user_email: str,
                           injection_id: int, rating: int, feedback: str = None) -> TeamEvent:
        """Create an injection rated event."""
        import uuid
        
        return TeamEvent(
            event_id=str(uuid.uuid4()),
            team_id=team_id,
            event_type=EventType.INJECTION_RATED,
            user_id=user_id,
            user_email=user_email,
            timestamp=datetime.now().isoformat(),
            payload={
                "injection_id": injection_id,
                "rating": rating,
                "feedback": feedback
            }
        )
    
    def create_prompt_shaped_event(self, team_id: int, user_id: int, user_email: str,
                                  prompt_data: Dict[str, Any]) -> TeamEvent:
        """Create a prompt shaped event."""
        import uuid
        
        return TeamEvent(
            event_id=str(uuid.uuid4()),
            team_id=team_id,
            event_type=EventType.PROMPT_SHAPED,
            user_id=user_id,
            user_email=user_email,
            timestamp=datetime.now().isoformat(),
            payload={
                "prompt_hash": prompt_data.get("prompt_hash"),
                "original_prompt": prompt_data.get("original_prompt")[:100] + "..." if len(prompt_data.get("original_prompt", "")) > 100 else prompt_data.get("original_prompt"),
                "model": prompt_data.get("model"),
                "intent": prompt_data.get("intent"),
                "improvement": "Auto-shaped for better results"
            }
        )
    
    def create_setting_changed_event(self, team_id: int, user_id: int, user_email: str,
                                   setting_key: str, setting_value: str) -> TeamEvent:
        """Create a team setting changed event."""
        import uuid
        
        return TeamEvent(
            event_id=str(uuid.uuid4()),
            team_id=team_id,
            event_type=EventType.TEAM_SETTING_CHANGED,
            user_id=user_id,
            user_email=user_email,
            timestamp=datetime.now().isoformat(),
            payload={
                "setting_key": setting_key,
                "setting_value": setting_value
            }
        )
    
    def create_user_joined_event(self, team_id: int, user_id: int, user_email: str,
                               joined_user_id: int, joined_user_email: str) -> TeamEvent:
        """Create a user joined team event."""
        import uuid
        
        return TeamEvent(
            event_id=str(uuid.uuid4()),
            team_id=team_id,
            event_type=EventType.USER_JOINED_TEAM,
            user_id=user_id,
            user_email=user_email,
            timestamp=datetime.now().isoformat(),
            payload={
                "joined_user_id": joined_user_id,
                "joined_user_email": joined_user_email
            }
        )
    
    def get_team_activity_summary(self, team_id: int, limit: int = 20) -> List[Dict[str, Any]]:
        """Get recent team activity summary."""
        if team_id not in self.recent_events:
            return []
        
        recent_events = self.recent_events[team_id][-limit:]
        return [event.to_dict() for event in recent_events]
    
    def get_active_team_members(self, team_id: int) -> List[Dict[str, Any]]:
        """Get currently active team members (connected via WebSocket)."""
        if team_id not in self.team_connections:
            return []
        
        active_members = []
        for connection_info in self.team_connections[team_id]:
            active_members.append({
                "user_id": connection_info["user_id"],
                "connected_at": connection_info["connected_at"]
            })
        
        return active_members

# Global team event manager instance
team_event_manager = TeamEventManager()
