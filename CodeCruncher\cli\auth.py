"""
Authentication commands for CodeCrusher CLI
Provides login, logout, and status commands
"""

import typer
import logging
from rich.panel import Panel
from rich.table import Table
from rich.console import Console

from .common import setup_logging
from .auth_client import get_auth_client, login, logout, status, is_authenticated

# Create the auth app
auth_app = typer.Typer(
    help="🔐 Authentication commands for CodeCrusher",
    short_help="Authentication"
)

console = Console()

@auth_app.command("login")
def login_command(
    email: str = typer.Option(
        None, "--email", "-e",
        help="📧 Email address for login"
    ),
    password: str = typer.Option(
        None, "--password", "-p",
        help="🔑 Password (will prompt securely if not provided)"
    ),
    api_base: str = typer.Option(
        "http://localhost:8001", "--api-base", "-a",
        help="🌐 API base URL"
    ),
    verbose: bool = typer.Option(
        False, "--verbose", "-v",
        help="🔍 Enable verbose logging"
    )
):
    """
    🔐 Login to CodeCrusher with email and password.
    
    This command authenticates you with the CodeCrusher backend and stores
    a JWT token locally for subsequent API calls.
    """
    # Set up logging
    setup_logging(verbose)
    
    # Display welcome message
    console.print(Panel(
        "[bold]CodeCrusher Authentication[/bold]\n"
        "Login to access personalized features and secure API endpoints.",
        title="[bold cyan]🔐 Login[/bold cyan]",
        border_style="cyan"
    ))
    
    try:
        # Get auth client with custom API base
        client = get_auth_client()
        client.api_base = api_base
        
        # Display connection info
        console.print(f"[cyan]🌐 Connecting to:[/cyan] [yellow]{api_base}[/yellow]")
        
        # Attempt login
        success = client.login(email, password)
        
        if success:
            # Display success information
            user_info = client.user_info
            
            success_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
            success_table.add_row("[cyan]✅ Status:[/cyan]", "[bold green]Login Successful[/bold green]")
            success_table.add_row("[cyan]👤 User:[/cyan]", f"[yellow]{user_info.get('email', 'Unknown')}[/yellow]")
            success_table.add_row("[cyan]🎭 Role:[/cyan]", f"[yellow]{user_info.get('role', 'user')}[/yellow]")
            success_table.add_row("[cyan]🔗 API:[/cyan]", f"[yellow]{api_base}[/yellow]")
            
            console.print(Panel(
                success_table,
                title="[bold green]Authentication Successful[/bold green]",
                border_style="green"
            ))
            
            console.print("[green]💡 You can now use authenticated CodeCrusher commands![/green]")
            
        else:
            console.print(Panel(
                "[bold red]❌ Login failed[/bold red]\n"
                "Please check your credentials and try again.",
                title="[bold red]Authentication Failed[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)
    
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️ Login cancelled by user[/yellow]")
        raise typer.Exit(code=1)
    
    except Exception as e:
        console.print(f"[bold red]❌ Login error:[/bold red] {e}")
        logging.error(f"Login command error: {e}")
        raise typer.Exit(code=1)

@auth_app.command("logout")
def logout_command(
    verbose: bool = typer.Option(
        False, "--verbose", "-v",
        help="🔍 Enable verbose logging"
    )
):
    """
    🚪 Logout from CodeCrusher and clear stored authentication.
    
    This command clears your locally stored JWT token and logs you out
    from the CodeCrusher backend.
    """
    # Set up logging
    setup_logging(verbose)
    
    # Display logout message
    console.print(Panel(
        "[bold]CodeCrusher Logout[/bold]\n"
        "Clearing authentication and logging out from backend.",
        title="[bold yellow]🚪 Logout[/bold yellow]",
        border_style="yellow"
    ))
    
    try:
        success = logout()
        
        if success:
            console.print(Panel(
                "[bold green]✅ Logout successful[/bold green]\n"
                "Authentication token has been cleared.",
                title="[bold green]Logged Out[/bold green]",
                border_style="green"
            ))
        else:
            console.print(Panel(
                "[bold red]❌ Logout failed[/bold red]\n"
                "There may have been an issue clearing your authentication.",
                title="[bold red]Logout Error[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)
    
    except Exception as e:
        console.print(f"[bold red]❌ Logout error:[/bold red] {e}")
        logging.error(f"Logout command error: {e}")
        raise typer.Exit(code=1)

@auth_app.command("status")
def status_command(
    verbose: bool = typer.Option(
        False, "--verbose", "-v",
        help="🔍 Enable verbose logging"
    )
):
    """
    📊 Display current authentication status.
    
    Shows information about your current login status, user details,
    and token expiration.
    """
    # Set up logging
    setup_logging(verbose)
    
    # Display status header
    console.print(Panel(
        "[bold]CodeCrusher Authentication Status[/bold]\n"
        "Current login status and user information.",
        title="[bold blue]📊 Status[/bold blue]",
        border_style="blue"
    ))
    
    try:
        client = get_auth_client()
        
        if client.is_authenticated():
            user_info = client.user_info or {}
            
            # Create status table
            status_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
            status_table.add_row("[cyan]🔐 Status:[/cyan]", "[bold green]Authenticated[/bold green]")
            status_table.add_row("[cyan]👤 Email:[/cyan]", f"[yellow]{user_info.get('email', 'Unknown')}[/yellow]")
            status_table.add_row("[cyan]🎭 Role:[/cyan]", f"[yellow]{user_info.get('role', 'user')}[/yellow]")
            status_table.add_row("[cyan]📅 Created:[/cyan]", f"[yellow]{user_info.get('created_at', 'Unknown')}[/yellow]")
            status_table.add_row("[cyan]🔗 API:[/cyan]", f"[yellow]{client.api_base}[/yellow]")
            
            # Check token expiration
            try:
                import json
                from datetime import datetime
                from pathlib import Path
                
                token_file = Path.home() / ".codecrusher_token"
                if token_file.exists():
                    with open(token_file, 'r') as f:
                        token_data = json.load(f)
                    
                    expires_at = datetime.fromisoformat(token_data.get('expires_at', ''))
                    time_left = expires_at - datetime.now()
                    
                    if time_left.total_seconds() > 0:
                        hours, remainder = divmod(int(time_left.total_seconds()), 3600)
                        minutes, seconds = divmod(remainder, 60)
                        status_table.add_row("[cyan]⏰ Expires:[/cyan]", f"[yellow]{hours:02d}:{minutes:02d}:{seconds:02d}[/yellow]")
                    else:
                        status_table.add_row("[cyan]⏰ Status:[/cyan]", "[bold red]Token Expired[/bold red]")
            except Exception:
                status_table.add_row("[cyan]⏰ Expires:[/cyan]", "[yellow]Unknown[/yellow]")
            
            console.print(Panel(
                status_table,
                title="[bold green]Authentication Status[/bold green]",
                border_style="green"
            ))
            
            console.print("[green]💡 You can use authenticated CodeCrusher commands![/green]")
            
        else:
            # Not authenticated
            status_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
            status_table.add_row("[cyan]🔐 Status:[/cyan]", "[bold red]Not Authenticated[/bold red]")
            status_table.add_row("[cyan]🔗 API:[/cyan]", f"[yellow]{client.api_base}[/yellow]")
            
            console.print(Panel(
                status_table,
                title="[bold red]Not Logged In[/bold red]",
                border_style="red"
            ))
            
            console.print("[yellow]💡 Use 'codecrusher auth login' to authenticate[/yellow]")
    
    except Exception as e:
        console.print(f"[bold red]❌ Status error:[/bold red] {e}")
        logging.error(f"Status command error: {e}")
        raise typer.Exit(code=1)

@auth_app.command("whoami")
def whoami_command():
    """
    👤 Display current user information (alias for status).
    """
    status_command()

@auth_app.command("test")
def test_command(
    verbose: bool = typer.Option(
        False, "--verbose", "-v",
        help="🔍 Enable verbose logging"
    )
):
    """
    🧪 Test authentication by making a simple API call.
    
    This command tests your authentication by making a request to
    the /auth/me endpoint to verify your token is valid.
    """
    # Set up logging
    setup_logging(verbose)
    
    console.print(Panel(
        "[bold]CodeCrusher Authentication Test[/bold]\n"
        "Testing authentication with a simple API call.",
        title="[bold blue]🧪 Test[/bold blue]",
        border_style="blue"
    ))
    
    try:
        client = get_auth_client()
        
        if not client.is_authenticated():
            console.print(Panel(
                "[bold red]❌ Not authenticated[/bold red]\n"
                "Please login first using 'codecrusher auth login'",
                title="[bold red]Authentication Required[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)
        
        # Test API call
        console.print("[cyan]🔍 Testing API connection...[/cyan]")
        
        response = client.make_authenticated_request("GET", "/auth/me")
        
        if response.status_code == 200:
            user_data = response.json()
            
            test_table = Table(show_header=False, box=None, padding=(0, 1, 0, 1))
            test_table.add_row("[cyan]✅ Status:[/cyan]", "[bold green]Authentication Valid[/bold green]")
            test_table.add_row("[cyan]📡 Response:[/cyan]", f"[yellow]{response.status_code} OK[/yellow]")
            test_table.add_row("[cyan]👤 User ID:[/cyan]", f"[yellow]{user_data.get('id', 'Unknown')}[/yellow]")
            test_table.add_row("[cyan]📧 Email:[/cyan]", f"[yellow]{user_data.get('email', 'Unknown')}[/yellow]")
            
            console.print(Panel(
                test_table,
                title="[bold green]Authentication Test Successful[/bold green]",
                border_style="green"
            ))
            
        else:
            console.print(Panel(
                f"[bold red]❌ API test failed[/bold red]\n"
                f"Status: {response.status_code}\n"
                f"Response: {response.text}",
                title="[bold red]Test Failed[/bold red]",
                border_style="red"
            ))
            raise typer.Exit(code=1)
    
    except Exception as e:
        console.print(f"[bold red]❌ Test error:[/bold red] {e}")
        logging.error(f"Auth test error: {e}")
        raise typer.Exit(code=1)
