# Test script for the inject run command

# Create a test file
$testFileContent = @"
# Test file for CodeCrusher inject command

def hello_world():
    print('Hello, World!')

def factorial(n):
    if n == 0:
        return 1
    else:
        return n * factorial(n - 1)
"@

$testFileContent | Out-File -FilePath test_file.py -Encoding utf8

Write-Host "=== Testing inject run command with verbose flag ===" -ForegroundColor Cyan
python simple_inject.py --input test_file.py --prompt-text "Add type hints" --verbose

Write-Host ""
Write-Host "=== Testing inject run command with output file ===" -ForegroundColor Cyan
python simple_inject.py --input test_file.py --prompt-text "Add docstrings" --output test_output.py

Write-Host ""
Write-Host "=== Testing inject run command with non-existent file ===" -ForegroundColor Cyan
python simple_inject.py --input non_existent_file.py --prompt-text "Refactor this"

Write-Host ""
Write-Host "=== Testing inject run command with provider and model ===" -ForegroundColor Cyan
python simple_inject.py --input test_file.py --prompt-text "Optimize this code" --provider groq --model llama3 --cache

# Clean up
# Remove-Item test_file.py, test_output.py
