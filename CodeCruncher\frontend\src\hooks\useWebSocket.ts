import { useState, useEffect, useRef, useCallback } from 'react';

export type ConnectionStatus = 'idle' | 'connecting' | 'connected' | 'reconnecting' | 'error' | 'closed';

export interface WebSocketMessage {
  type?: string;
  message?: string;
  value?: number;
  progress?: number;
  data?: any;
}

export interface UseWebSocketOptions {
  url: string;
  maxRetries?: number;
  maxReconnectDelay?: number;
  onMessage?: (message: WebSocketMessage) => void;
  onStatusChange?: (status: ConnectionStatus) => void;
  autoConnect?: boolean;
  timeoutMs?: number;
  autoRestartOnTimeout?: boolean;
}

export interface UseWebSocketReturn {
  connectionStatus: ConnectionStatus;
  lastMessage: WebSocketMessage | null;
  sendMessage: (message: string | object) => boolean;
  connect: () => void;
  disconnect: () => void;
  retry: () => void;
  retryCount: number;
  isConnected: boolean;
  isFrozen: boolean;
  lastMessageTime: number;
  restartStream: () => void;
}

export function useWebSocket(options: UseWebSocketOptions): UseWebSocketReturn {
  const {
    url,
    maxRetries = 10,
    maxReconnectDelay = 30000,
    onMessage,
    onStatusChange,
    autoConnect = true,
    timeoutMs = 15000, // 15 seconds default timeout
    autoRestartOnTimeout = true
  } = options;

  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('idle');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isFrozen, setIsFrozen] = useState(false);
  const [lastMessageTime, setLastMessageTime] = useState(Date.now());

  const socketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManuallyClosedRef = useRef(false);
  const statusUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const watchdogTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateStatus = useCallback((status: ConnectionStatus) => {
    // Clear any pending status update
    if (statusUpdateTimeoutRef.current) {
      clearTimeout(statusUpdateTimeoutRef.current);
    }

    // Debounce rapid status changes
    statusUpdateTimeoutRef.current = setTimeout(() => {
      setConnectionStatus(status);
      onStatusChange?.(status);
    }, 100);
  }, [onStatusChange]);

  const calculateReconnectDelay = useCallback((retries: number): number => {
    // Exponential backoff: 1s → 2s → 4s → 8s → 16s → 30s (max)
    const baseDelay = 1000;
    const exponentialDelay = baseDelay * Math.pow(2, retries);
    return Math.min(exponentialDelay, maxReconnectDelay);
  }, [maxReconnectDelay]);

  // Watchdog timer to detect frozen streams
  const startWatchdog = useCallback(() => {
    if (watchdogTimeoutRef.current) {
      clearTimeout(watchdogTimeoutRef.current);
    }

    watchdogTimeoutRef.current = setTimeout(() => {
      console.warn(`WebSocket: Stream frozen - no data received for ${timeoutMs}ms`);
      setIsFrozen(true);

      if (autoRestartOnTimeout) {
        console.log('WebSocket: Auto-restarting frozen stream...');
        setTimeout(() => {
          restartStream();
        }, 2000); // Wait 2 seconds before auto-restart
      }
    }, timeoutMs);
  }, [timeoutMs, autoRestartOnTimeout]);

  const resetWatchdog = useCallback(() => {
    setLastMessageTime(Date.now());
    setIsFrozen(false);

    if (connectionStatus === 'connected') {
      startWatchdog();
    }
  }, [connectionStatus, startWatchdog]);

  const handleMessage = useCallback((event: MessageEvent) => {
    // Reset watchdog timer on any message
    resetWatchdog();

    try {
      // Try to parse as JSON first
      const data = JSON.parse(event.data);
      const message: WebSocketMessage = {
        type: data.type,
        message: data.message,
        value: data.value || data.progress,
        data: data
      };

      setLastMessage(message);
      onMessage?.(message);
    } catch {
      // Handle plain text messages
      const message: WebSocketMessage = {
        type: 'text',
        message: event.data,
        data: event.data
      };

      // Extract progress from plain text
      const progressMatch = event.data.match(/Progress: (\d+)%/);
      if (progressMatch) {
        message.value = parseInt(progressMatch[1]);
        message.type = 'progress';
      }

      setLastMessage(message);
      onMessage?.(message);
    }
  }, [onMessage, resetWatchdog]);

  const connectWebSocket = useCallback(() => {
    // Don't reconnect if we've exceeded max retries
    if (retryCount >= maxRetries) {
      console.error(`WebSocket: Max retries (${maxRetries}) exceeded`);
      updateStatus('error');
      return;
    }

    // Don't create new connection if one already exists and is connecting/open
    if (socketRef.current?.readyState === WebSocket.CONNECTING ||
        socketRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    // Clear any existing reconnection timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    console.log(`WebSocket: Connecting to ${url} (attempt ${retryCount + 1}/${maxRetries})`);
    updateStatus('connecting');

    try {
      const socket = new WebSocket(url);
      socketRef.current = socket;

      socket.onopen = () => {
        console.log('WebSocket: Connected successfully');
        updateStatus('connected');
        setRetryCount(0); // Reset retry count on successful connection
        isManuallyClosedRef.current = false;

        // Start watchdog timer for frozen stream detection
        startWatchdog();
      };

      socket.onmessage = handleMessage;

      socket.onclose = (event) => {
        console.log(`WebSocket: Connection closed (code: ${event.code}, reason: ${event.reason})`);

        // Don't reconnect if manually closed
        if (isManuallyClosedRef.current) {
          updateStatus('closed');
          return;
        }

        // Don't reconnect if we've exceeded max retries
        if (retryCount >= maxRetries) {
          updateStatus('error');
          return;
        }

        // Start reconnection process
        updateStatus('reconnecting');
        const newRetryCount = retryCount + 1;
        setRetryCount(newRetryCount);

        const delay = calculateReconnectDelay(newRetryCount - 1);
        console.log(`WebSocket: Reconnecting in ${delay}ms (attempt ${newRetryCount}/${maxRetries})`);

        reconnectTimeoutRef.current = setTimeout(() => {
          connectWebSocket();
        }, delay);
      };

      socket.onerror = (error) => {
        console.error('WebSocket: Connection error', error);
        updateStatus('error');
      };

    } catch (error) {
      console.error('WebSocket: Failed to create connection', error);
      updateStatus('error');
    }
  }, [url, retryCount, maxRetries, updateStatus, handleMessage, calculateReconnectDelay]);

  const sendMessage = useCallback((message: string | object): boolean => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      try {
        const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
        socketRef.current.send(messageStr);
        return true;
      } catch (error) {
        console.error('WebSocket: Failed to send message', error);
        return false;
      }
    }
    console.warn('WebSocket: Cannot send message - not connected');
    return false;
  }, []);

  const disconnect = useCallback(() => {
    console.log('WebSocket: Manually disconnecting');
    isManuallyClosedRef.current = true;

    // Clear any pending reconnection
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Close the socket
    if (socketRef.current) {
      socketRef.current.close(1000, 'Manual disconnect');
      socketRef.current = null;
    }

    setRetryCount(0);
    updateStatus('closed');
  }, [updateStatus]);

  const retry = useCallback(() => {
    console.log('WebSocket: Manual retry requested');
    setRetryCount(0); // Reset retry count for manual retry
    isManuallyClosedRef.current = false;

    // Clear any pending reconnection
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Close existing connection if any
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }

    connectWebSocket();
  }, [connectWebSocket]);

  const connect = useCallback(() => {
    if (connectionStatus === 'connected' || connectionStatus === 'connecting') {
      return;
    }

    isManuallyClosedRef.current = false;
    setRetryCount(0);
    connectWebSocket();
  }, [connectionStatus, connectWebSocket]);

  const restartStream = useCallback(() => {
    console.log('WebSocket: Restarting stream due to timeout');
    setIsFrozen(false);
    setRetryCount(0);

    // Clear watchdog timer
    if (watchdogTimeoutRef.current) {
      clearTimeout(watchdogTimeoutRef.current);
      watchdogTimeoutRef.current = null;
    }

    // Close existing connection if any
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }

    // Start fresh connection
    isManuallyClosedRef.current = false;
    connectWebSocket();
  }, [connectWebSocket]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (watchdogTimeoutRef.current) {
        clearTimeout(watchdogTimeoutRef.current);
      }
      if (statusUpdateTimeoutRef.current) {
        clearTimeout(statusUpdateTimeoutRef.current);
      }
      if (socketRef.current) {
        isManuallyClosedRef.current = true;
        socketRef.current.close();
      }
    };
  }, [autoConnect, connect]);

  const isConnected = connectionStatus === 'connected';

  return {
    connectionStatus,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    retry,
    retryCount,
    isConnected,
    isFrozen,
    lastMessageTime,
    restartStream
  };
}
