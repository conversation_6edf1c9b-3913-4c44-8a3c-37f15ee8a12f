"""
Auto-Refine Engine
Automatically improves prompts based on feedback and performance
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from .prompt_logger import PromptLogger, PromptLogEntry
from .feedback_engine import FeedbackEngine

logger = logging.getLogger(__name__)

@dataclass
class PromptTemplate:
    """Template for prompt refinement"""
    name: str
    base_template: str
    constraints: List[str]
    tone_modifiers: List[str]
    success_rate: float = 0.0
    usage_count: int = 0

class AutoRefineEngine:
    """Engine for automatically refining prompts based on feedback"""
    
    def __init__(self, prompt_logger: PromptLogger, feedback_engine: FeedbackEngine, 
                 config_path: str = "data/auto_refine_config.json"):
        self.prompt_logger = prompt_logger
        self.feedback_engine = feedback_engine
        self.config_path = Path(config_path)
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self.templates = self._load_templates()
        self.model_hierarchy = [
            "llama3-8b",
            "mistral", 
            "gemma",
            "mixtral",
            "llama3-70b",
            "gpt-4-turbo"
        ]
    
    def _load_templates(self) -> Dict[str, PromptTemplate]:
        """Load prompt templates from config"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    data = json.load(f)
                    templates = {}
                    for key, template_data in data.get('templates', {}).items():
                        templates[key] = PromptTemplate(**template_data)
                    return templates
            except Exception as e:
                logger.warning(f"Failed to load auto-refine templates: {e}")
        
        # Default templates
        return {
            "bugfix": PromptTemplate(
                name="bugfix",
                base_template="Fix the following issue in {file}: {prompt}",
                constraints=[
                    "Focus on the specific bug mentioned",
                    "Preserve existing functionality", 
                    "Add error handling where appropriate",
                    "Include comments explaining the fix"
                ],
                tone_modifiers=[
                    "Be precise and minimal",
                    "Explain the root cause briefly",
                    "Test edge cases"
                ]
            ),
            "optimize": PromptTemplate(
                name="optimize",
                base_template="Optimize the following code in {file}: {prompt}",
                constraints=[
                    "Maintain existing functionality",
                    "Consider time and space complexity",
                    "Preserve readability",
                    "Document performance improvements"
                ],
                tone_modifiers=[
                    "Focus on algorithmic improvements",
                    "Explain performance gains",
                    "Consider memory usage"
                ]
            ),
            "refactor": PromptTemplate(
                name="refactor",
                base_template="Refactor the following code in {file}: {prompt}",
                constraints=[
                    "Improve code structure and readability",
                    "Maintain all existing functionality",
                    "Follow best practices",
                    "Add appropriate documentation"
                ],
                tone_modifiers=[
                    "Focus on clean code principles",
                    "Improve maintainability",
                    "Use descriptive names"
                ]
            ),
            "feature": PromptTemplate(
                name="feature",
                base_template="Add the following feature to {file}: {prompt}",
                constraints=[
                    "Integrate seamlessly with existing code",
                    "Follow established patterns",
                    "Add comprehensive error handling",
                    "Include usage examples"
                ],
                tone_modifiers=[
                    "Be thorough and complete",
                    "Consider edge cases",
                    "Document the new functionality"
                ]
            )
        }
    
    def _save_templates(self):
        """Save templates to config"""
        try:
            data = {
                'templates': {
                    key: {
                        'name': template.name,
                        'base_template': template.base_template,
                        'constraints': template.constraints,
                        'tone_modifiers': template.tone_modifiers,
                        'success_rate': template.success_rate,
                        'usage_count': template.usage_count
                    }
                    for key, template in self.templates.items()
                }
            }
            
            with open(self.config_path, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save auto-refine templates: {e}")
    
    def refine_prompt(self, original_prompt: str, injection_type: str, 
                     file_path: str, model: str, tags: List[str] = None) -> Tuple[str, str]:
        """Refine a prompt based on learned patterns and feedback"""
        
        # Get improvement suggestions from feedback engine
        suggestions = self.feedback_engine.get_improvement_suggestions(injection_type, tags)
        
        # Get appropriate template
        template = self.templates.get(injection_type, self.templates.get('bugfix'))
        
        # Build refined prompt
        refined_prompt = self._build_refined_prompt(
            original_prompt, template, suggestions, file_path
        )
        
        # Check if model escalation is needed
        recommended_model = self._get_recommended_model(injection_type, model)
        
        # Update template usage
        template.usage_count += 1
        self._save_templates()
        
        logger.info(f"Refined prompt for {injection_type} in {file_path}")
        return refined_prompt, recommended_model
    
    def _build_refined_prompt(self, original_prompt: str, template: PromptTemplate, 
                            suggestions: List[str], file_path: str) -> str:
        """Build a refined prompt using template and suggestions"""
        
        # Start with base template
        refined = template.base_template.format(
            file=file_path,
            prompt=original_prompt
        )
        
        # Add constraints
        if template.constraints:
            refined += "\n\nConstraints:"
            for constraint in template.constraints:
                refined += f"\n- {constraint}"
        
        # Add learned suggestions
        if suggestions:
            refined += "\n\nBased on previous feedback:"
            for suggestion in suggestions[:3]:  # Limit to top 3
                refined += f"\n- {suggestion}"
        
        # Add tone modifiers
        if template.tone_modifiers:
            refined += "\n\nStyle guidelines:"
            for modifier in template.tone_modifiers:
                refined += f"\n- {modifier}"
        
        return refined
    
    def _get_recommended_model(self, injection_type: str, current_model: str) -> str:
        """Get recommended model based on performance and escalation rules"""
        
        # Check if escalation is needed
        if self.feedback_engine.should_escalate_model(injection_type):
            current_index = self.model_hierarchy.index(current_model) if current_model in self.model_hierarchy else 0
            
            # Escalate to next model in hierarchy
            if current_index < len(self.model_hierarchy) - 1:
                recommended = self.model_hierarchy[current_index + 1]
                logger.info(f"Escalating model from {current_model} to {recommended} for {injection_type}")
                return recommended
        
        return current_model
    
    def update_template_performance(self, injection_type: str, success: bool, rating: Optional[int] = None):
        """Update template performance based on execution results"""
        if injection_type in self.templates:
            template = self.templates[injection_type]
            
            # Update success rate using exponential moving average
            alpha = 0.1  # Learning rate
            new_success = 1.0 if success and (rating is None or rating >= 3) else 0.0
            
            if template.usage_count == 1:
                template.success_rate = new_success
            else:
                template.success_rate = (1 - alpha) * template.success_rate + alpha * new_success
            
            self._save_templates()
            logger.debug(f"Updated {injection_type} template success rate: {template.success_rate:.2f}")
    
    def get_best_template(self, injection_type: str) -> PromptTemplate:
        """Get the best performing template for an injection type"""
        if injection_type in self.templates:
            return self.templates[injection_type]
        
        # Fallback to highest performing template
        best_template = max(self.templates.values(), key=lambda t: t.success_rate)
        return best_template
    
    def analyze_prompt_patterns(self) -> Dict[str, any]:
        """Analyze patterns in successful vs failed prompts"""
        recent_entries = self.prompt_logger.get_recent_entries(limit=100)
        
        successful = [e for e in recent_entries if e.rating and e.rating >= 4]
        failed = [e for e in recent_entries if e.rating and e.rating <= 2]
        
        analysis = {
            'total_analyzed': len(recent_entries),
            'successful_count': len(successful),
            'failed_count': len(failed),
            'success_rate': len(successful) / len(recent_entries) if recent_entries else 0,
            'template_performance': {}
        }
        
        # Analyze template performance
        for template_name, template in self.templates.items():
            analysis['template_performance'][template_name] = {
                'success_rate': template.success_rate,
                'usage_count': template.usage_count
            }
        
        # Common patterns in successful prompts
        if successful:
            successful_prompts = [e.prompt for e in successful]
            analysis['successful_patterns'] = self._extract_common_patterns(successful_prompts)
        
        # Common patterns in failed prompts  
        if failed:
            failed_prompts = [e.prompt for e in failed]
            analysis['failed_patterns'] = self._extract_common_patterns(failed_prompts)
        
        return analysis
    
    def _extract_common_patterns(self, prompts: List[str]) -> List[str]:
        """Extract common patterns from a list of prompts"""
        # Simple pattern extraction - could be enhanced with NLP
        common_words = {}
        
        for prompt in prompts:
            words = prompt.lower().split()
            for word in words:
                if len(word) > 3:  # Skip short words
                    common_words[word] = common_words.get(word, 0) + 1
        
        # Return most common words
        sorted_words = sorted(common_words.items(), key=lambda x: x[1], reverse=True)
        return [word for word, count in sorted_words[:10] if count > 1]
    
    def get_refinement_stats(self) -> Dict[str, any]:
        """Get statistics about prompt refinement"""
        return {
            'templates': {
                name: {
                    'success_rate': template.success_rate,
                    'usage_count': template.usage_count
                }
                for name, template in self.templates.items()
            },
            'model_hierarchy': self.model_hierarchy,
            'feedback_summary': self.feedback_engine.get_feedback_summary()
        }
