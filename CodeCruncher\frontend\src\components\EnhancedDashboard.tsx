import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { EnterpriseFooter } from './EnterpriseFooter';

const EnhancedDashboard = () => {
  const [prompt, setPrompt] = useState('Add comprehensive error handling and logging');
  const [model, setModel] = useState('auto');
  const [fallback, setFallback] = useState(true);
  const [logs, setLogs] = useState('');
  const [progress, setProgress] = useState(0);
  const [isRunning, setIsRunning] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [filePath, setFilePath] = useState('./src');

  const logRef = useRef<HTMLPreElement>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const models = [
    { value: 'auto', label: '🤖 Auto Selection' },
    { value: 'mistral', label: '🧠 Mistral' },
    { value: 'mixtral', label: '🔥 Mixtral' },
    { value: 'gemma', label: '💎 Gemma' },
    { value: 'llama3', label: '🦙 LLaMA 3' }
  ];

  // Connect to WebSocket
  const connectWebSocket = () => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    wsRef.current = new WebSocket('ws://localhost:8001/ws/logs');

    wsRef.current.onopen = () => {
      setIsConnected(true);
      console.log('🔗 WebSocket connected');
    };

    wsRef.current.onmessage = (event) => {
      const message = event.data;
      setLogs(prev => prev + message + '\n');

      // Extract progress from messages
      if (message.includes('Progress:')) {
        const match = message.match(/Progress: (\d+)%/);
        if (match) {
          setProgress(Number(match[1]));
        }
      }

      // Check for completion
      if (message.includes('completed successfully') || message.includes('injection failed')) {
        setIsRunning(false);
      }
    };

    wsRef.current.onclose = () => {
      setIsConnected(false);
      console.log('🔌 WebSocket disconnected');

      // Attempt to reconnect after 3 seconds
      setTimeout(() => {
        if (!isConnected) {
          connectWebSocket();
        }
      }, 3000);
    };

    wsRef.current.onerror = (error) => {
      console.error('🚨 WebSocket error:', error);
      setIsConnected(false);
    };
  };

  // Initialize WebSocket connection
  useEffect(() => {
    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Auto-scroll logs
  useEffect(() => {
    if (logRef.current) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [logs]);

  const runInjection = async () => {
    if (!isConnected) {
      alert('WebSocket not connected. Please wait for connection.');
      return;
    }

    setIsRunning(true);
    setLogs('');
    setProgress(0);

    try {
      const response = await fetch('http://localhost:8001/inject', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: prompt,
          file_path: filePath,
          model,
          apply: true,
          fallback_enabled: fallback,
          tags: ['enhanced-dashboard']
        })
      });

      const result = await response.json();

      if (!result.success) {
        console.error('Injection failed:', result.error);
      }

    } catch (error) {
      console.error('Request failed:', error);
      setIsRunning(false);
      setLogs(prev => prev + `❌ Request failed: ${error}\n`);
    }
  };

  const clearLogs = () => {
    setLogs('');
    setProgress(0);
  };

  const stopInjection = () => {
    setIsRunning(false);
    // In a real implementation, this would send a stop signal
  };

  return (
    <div className="min-h-screen flex flex-col">
      <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 p-6 max-w-7xl mx-auto">
        {/* Control Panel */}
        <Card className="h-fit">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold">🚀 AI Code Injector</h2>
              <Badge variant={isConnected ? "default" : "destructive"}>
                {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
              </Badge>
            </div>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">File Path</label>
              <Input
                placeholder="./src"
                value={filePath}
                onChange={(e) => setFilePath(e.target.value)}
                className="mb-4"
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Injection Prompt</label>
              <Textarea
                placeholder="Enter your prompt here..."
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                rows={4}
                className="resize-none"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">AI Model</label>
                <Select value={model} onValueChange={setModel}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select model" />
                  </SelectTrigger>
                  <SelectContent>
                    {models.map((m) => (
                      <SelectItem key={m.value} value={m.value}>
                        {m.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={fallback}
                    onCheckedChange={setFallback}
                    id="fallback-switch"
                  />
                  <label htmlFor="fallback-switch" className="text-sm font-medium">
                    Enable Fallback
                  </label>
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={runInjection}
                disabled={isRunning || !isConnected || !prompt.trim()}
                className="flex-1"
              >
                {isRunning ? '⏳ Running...' : '🚀 Run Injection'}
              </Button>

              {isRunning && (
                <Button
                  onClick={stopInjection}
                  variant="destructive"
                  className="px-6"
                >
                  🛑 Stop
                </Button>
              )}

              <Button
                onClick={clearLogs}
                variant="outline"
                className="px-6"
              >
                🗑️ Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Logs & Progress Panel */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold">📊 Logs & Progress</h2>
              <span className="text-sm text-gray-600">
                {logs.split('\n').length - 1} messages
              </span>
            </div>

            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Progress</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-3" />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Live Output</label>
              <pre
                ref={logRef}
                className="bg-black text-green-400 p-4 rounded-lg h-96 overflow-y-auto text-sm font-mono whitespace-pre-wrap border"
              >
                {logs || 'Waiting for logs...'}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>

      {/* Enterprise Footer */}
      <EnterpriseFooter />
    </div>
  );
};

export default EnhancedDashboard;
