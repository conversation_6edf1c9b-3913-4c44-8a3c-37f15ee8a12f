<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2563eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="20" cy="20" r="18" fill="url(#logoGradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Code brackets -->
  <path d="M12 14L8 20L12 26" stroke="#ffffff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  <path d="M28 14L32 20L28 26" stroke="#ffffff" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
  
  <!-- AI brain/neural network symbol -->
  <circle cx="20" cy="18" r="1.5" fill="#ffffff"/>
  <circle cx="16" cy="22" r="1" fill="#ffffff"/>
  <circle cx="24" cy="22" r="1" fill="#ffffff"/>
  <circle cx="20" cy="25" r="1" fill="#ffffff"/>
  
  <!-- Connecting lines -->
  <line x1="20" y1="18" x2="16" y2="22" stroke="#ffffff" stroke-width="1" opacity="0.8"/>
  <line x1="20" y1="18" x2="24" y2="22" stroke="#ffffff" stroke-width="1" opacity="0.8"/>
  <line x1="16" y1="22" x2="20" y2="25" stroke="#ffffff" stroke-width="1" opacity="0.8"/>
  <line x1="24" y1="22" x2="20" y2="25" stroke="#ffffff" stroke-width="1" opacity="0.8"/>
</svg>
