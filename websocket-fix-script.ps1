#!/usr/bin/env powershell
<#
.SYNOPSIS
    CodeCrusher WebSocket Fix Script
.DESCRIPTION
    Comprehensive script to diagnose and fix WebSocket connection issues
#>

Write-Host "🚀 CodeCrusher WebSocket Fix Script" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Step 1: Check Backend Status
Write-Host "`n1️⃣ Checking Backend Status..." -ForegroundColor Yellow

try {
    $healthResponse = Invoke-WebRequest -Uri "http://127.0.0.1:8001/health" -UseBasicParsing -TimeoutSec 5
    if ($healthResponse.StatusCode -eq 200) {
        $healthData = $healthResponse.Content | ConvertFrom-Json
        Write-Host "✅ Backend is healthy on port 8001" -ForegroundColor Green
        Write-Host "   Connected clients: $($healthData.connected_clients)" -ForegroundColor Gray
    }
} catch {
    Write-Host "❌ Backend health check failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 Make sure backend is running: python app/backend_main.py" -ForegroundColor Yellow
    exit 1
}

# Step 2: Check Frontend Status
Write-Host "`n2️⃣ Checking Frontend Status..." -ForegroundColor Yellow

$frontendPort = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
if ($frontendPort) {
    Write-Host "✅ Frontend is running on port 3000" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend is not running on port 3000" -ForegroundColor Red
    Write-Host "💡 Start frontend: cd frontend && npm run dev" -ForegroundColor Yellow
}

# Step 3: Test WebSocket Endpoints
Write-Host "`n3️⃣ Testing WebSocket Endpoints..." -ForegroundColor Yellow

# Test direct backend connection
Write-Host "   Testing direct backend WebSocket..." -ForegroundColor Gray
Write-Host "   📝 WebSocket test available at: http://127.0.0.1:3000/websocket_test.html" -ForegroundColor Gray

# Step 4: Check for Configuration Issues
Write-Host "`n4️⃣ Checking Configuration..." -ForegroundColor Yellow

# Check Vite config
if (Test-Path "frontend/vite.config.ts") {
    $viteConfig = Get-Content "frontend/vite.config.ts" -Raw
    if ($viteConfig -match "target.*127\.0\.0\.1:8001") {
        Write-Host "✅ Vite proxy correctly configured for port 8001" -ForegroundColor Green
    } else {
        Write-Host "❌ Vite proxy configuration issue detected" -ForegroundColor Red
    }

    if ($viteConfig -match "ws:\s*true") {
        Write-Host "✅ WebSocket proxy enabled in Vite" -ForegroundColor Green
    } else {
        Write-Host "❌ WebSocket proxy not enabled in Vite" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Vite config not found" -ForegroundColor Red
}

# Step 5: Provide Fix Recommendations
Write-Host "`n5️⃣ Fix Recommendations..." -ForegroundColor Yellow

Write-Host "📋 To fix WebSocket issues:" -ForegroundColor Cyan
Write-Host "   1. Ensure backend runs on port 8001: python app/backend_main.py" -ForegroundColor White
Write-Host "   2. Ensure frontend runs on port 3000: cd frontend && npm run dev" -ForegroundColor White
Write-Host "   3. Use Vite proxy URLs: ws://127.0.0.1:3000/ws/logs" -ForegroundColor White
Write-Host "   4. Test at: http://127.0.0.1:3000/websocket_test.html" -ForegroundColor White

# Step 6: Open Test Pages
Write-Host "`n6️⃣ Opening Test Pages..." -ForegroundColor Yellow

if ($frontendPort) {
    Write-Host "🌐 Opening WebSocket test page..." -ForegroundColor Gray
    Start-Process "http://127.0.0.1:3000/websocket_test.html"

    Write-Host "🌐 Opening main dashboard..." -ForegroundColor Gray
    Start-Process "http://127.0.0.1:3000"
} else {
    Write-Host "⚠️ Frontend not running - cannot open test pages" -ForegroundColor Yellow
}

Write-Host "`n🎉 WebSocket Fix Script Complete!" -ForegroundColor Green
Write-Host "Check the opened browser tabs for WebSocket test results." -ForegroundColor Gray
