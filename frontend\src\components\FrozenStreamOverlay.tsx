import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  RefreshCw, 
  AlertTriangle, 
  Zap,
  Timer,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FrozenStreamOverlayProps {
  isFrozen: boolean;
  lastMessageTime: number;
  timeoutMs: number;
  onRestart: () => void;
  onDismiss?: () => void;
  autoRestartEnabled?: boolean;
  className?: string;
}

export function FrozenStreamOverlay({ 
  isFrozen, 
  lastMessageTime, 
  timeoutMs,
  onRestart, 
  onDismiss,
  autoRestartEnabled = true,
  className 
}: FrozenStreamOverlayProps) {
  if (!isFrozen) {
    return null;
  }

  const timeSinceLastMessage = Date.now() - lastMessageTime;
  const timeoutSeconds = Math.floor(timeoutMs / 1000);
  const elapsedSeconds = Math.floor(timeSinceLastMessage / 1000);

  return (
    <>
      {/* Top Banner Warning */}
      <div className={cn(
        "fixed top-0 left-0 right-0 z-40 transition-all duration-300",
        className
      )}>
        <div className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-center py-3 px-4 shadow-lg">
          <div className="flex items-center justify-center gap-2 max-w-4xl mx-auto">
            <Clock className="w-4 h-4 animate-pulse" />
            <span className="font-medium">
              ⏳ AI Stream Frozen - No data for {elapsedSeconds}s (timeout: {timeoutSeconds}s)
            </span>
            <div className="flex items-center gap-2 ml-4">
              <Button
                onClick={onRestart}
                variant="outline"
                size="sm"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <RefreshCw className="w-3 h-3 mr-1" />
                Restart Now
              </Button>
              {onDismiss && (
                <Button
                  onClick={onDismiss}
                  variant="outline"
                  size="sm"
                  className="bg-white/10 border-white/20 text-white hover:bg-white/20"
                >
                  Dismiss
                </Button>
              )}
            </div>
          </div>
          <div className="text-xs opacity-90 mt-1">
            {autoRestartEnabled ? 'Auto-restart in progress...' : 'Manual restart required'}
          </div>
        </div>
      </div>

      {/* Detailed Modal for Critical Timeout */}
      {elapsedSeconds > timeoutSeconds + 10 && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-yellow-100 rounded-full">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">AI Stream Timeout</h3>
                <p className="text-sm text-gray-600">The AI backend has stopped responding</p>
              </div>
            </div>

            <Alert variant="destructive" className="mb-4">
              <Timer className="h-4 w-4" />
              <AlertTitle>Stream Frozen</AlertTitle>
              <AlertDescription>
                No data received for {elapsedSeconds} seconds. The AI processing may have stalled 
                or encountered an error.
              </AlertDescription>
            </Alert>

            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                <strong>Possible causes:</strong>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>AI model processing timeout</li>
                  <li>Backend server overload</li>
                  <li>Network connectivity issues</li>
                  <li>Large file processing delay</li>
                </ul>
              </div>

              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 text-blue-800 text-sm font-medium mb-1">
                  <Activity className="w-4 h-4" />
                  Stream Status
                </div>
                <div className="text-xs text-blue-700 space-y-1">
                  <div className="flex justify-between">
                    <span>Last message:</span>
                    <span>{new Date(lastMessageTime).toLocaleTimeString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Elapsed time:</span>
                    <span>{elapsedSeconds}s</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Timeout threshold:</span>
                    <span>{timeoutSeconds}s</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={onRestart} className="flex-1">
                  <Zap className="w-4 h-4 mr-2" />
                  Restart Stream
                </Button>
                {onDismiss && (
                  <Button 
                    onClick={onDismiss} 
                    variant="outline"
                    className="flex-1"
                  >
                    Continue Waiting
                  </Button>
                )}
              </div>

              {autoRestartEnabled && (
                <div className="text-xs text-gray-500 text-center">
                  Auto-restart is enabled and will attempt to reconnect automatically
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

interface StreamTimeoutBadgeProps {
  isFrozen: boolean;
  lastMessageTime: number;
  timeoutMs: number;
  className?: string;
}

export function StreamTimeoutBadge({ 
  isFrozen, 
  lastMessageTime, 
  timeoutMs,
  className 
}: StreamTimeoutBadgeProps) {
  if (!isFrozen) {
    return null;
  }

  const timeSinceLastMessage = Date.now() - lastMessageTime;
  const elapsedSeconds = Math.floor(timeSinceLastMessage / 1000);
  const timeoutSeconds = Math.floor(timeoutMs / 1000);

  return (
    <Badge 
      variant="destructive"
      className={cn(
        "flex items-center gap-1.5 px-2 py-1 animate-pulse",
        className
      )}
    >
      <Clock className="w-3 h-3" />
      <span className="text-xs font-medium">
        Frozen {elapsedSeconds}s/{timeoutSeconds}s
      </span>
    </Badge>
  );
}
