import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Brain, CheckCircle, XCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface NavigationWrapperProps {
  children: React.ReactNode;
  healthStatus?: any;
  wsConnected?: boolean;
  getStatusIcon?: () => React.ReactElement;
  getStatusText?: () => string;
}

export function NavigationWrapper({
  children,
  healthStatus,
  wsConnected,
  getStatusIcon,
  getStatusText
}: NavigationWrapperProps) {
  const navigate = useNavigate();
  const location = useLocation();

  // Get current route to determine view mode
  const getCurrentViewMode = () => {
    const path = location.pathname;
    switch (path) {
      case '/': return 'main';
      case '/dashboard': return 'simple';
      case '/streamlined': return 'streamlined';
      case '/clean': return 'clean';
      case '/enhanced': return 'enhanced';
      case '/backend': return 'backend';
      case '/ui': return 'ui';
      case '/status': return 'status';
      case '/stable': return 'stable';
      case '/enterprise': return 'enterprise';
      case '/demo': return 'demo';
      case '/styletest': return 'styletest';
      default: return 'enterprise';
    }
  };

  const setViewMode = (mode: string) => {
    const routeMap: { [key: string]: string } = {
      'main': '/',
      'simple': '/dashboard',
      'streamlined': '/streamlined',
      'clean': '/clean',
      'enhanced': '/enhanced',
      'backend': '/backend',
      'ui': '/ui',
      'status': '/status',
      'stable': '/stable',
      'enterprise': '/enterprise',
      'demo': '/demo',
      'styletest': '/styletest'
    };

    const route = routeMap[mode] || '/enterprise';
    navigate(route);
  };

  const viewMode = getCurrentViewMode();

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-100">
      {/* Navigation Bar */}
      <div className="bg-white/95 backdrop-blur-xl border-b border-gray-200/30 shadow-lg">
        <div className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3">
          {/* Top Row: Logo + Primary Navigation */}
          <div className="flex items-center justify-between mb-2">
            {/* Logo Section - Compact Left */}
            <div className="flex items-center min-w-0">
              <h1 className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-1.5 text-gray-800">
                <Brain className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-blue-600 flex-shrink-0" />
                <span className="hidden sm:inline truncate">CodeCrusher Dashboard</span>
                <span className="sm:hidden truncate">CodeCrusher</span>
              </h1>
            </div>

            {/* Primary Navigation Row - Full Text */}
            <div className="flex-1 flex justify-center mx-4">
              <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-4xl">
                <Button
                  variant={viewMode === 'main' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('main')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Main Dashboard
                </Button>
                <Button
                  variant={viewMode === 'simple' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('simple')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Full Dashboard
                </Button>
                <Button
                  variant={viewMode === 'streamlined' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('streamlined')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Streamlined
                </Button>
                <Button
                  variant={viewMode === 'clean' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('clean')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Clean
                </Button>
                <Button
                  variant={viewMode === 'enhanced' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('enhanced')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Enhanced
                </Button>
                <Button
                  variant={viewMode === 'backend' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('backend')}
                  className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                >
                  Backend
                </Button>
              </div>
            </div>

            {/* Right Side Balance */}
            <div className="w-16 sm:w-20 lg:w-24 flex-shrink-0"></div>
          </div>

          {/* Bottom Row: Secondary Navigation */}
          <div className="flex justify-center">
            <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-3xl">
              <Button
                variant={viewMode === 'ui' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('ui')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
              >
                UI
              </Button>
              <Button
                variant={viewMode === 'status' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('status')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
              >
                Status
              </Button>
              <Button
                variant={viewMode === 'stable' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('stable')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
              >
                Stable
              </Button>
              <Button
                variant={viewMode === 'enterprise' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('enterprise')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 hover:from-blue-700 hover:to-indigo-700"
              >
                Enterprise
              </Button>
              <Button
                variant={viewMode === 'demo' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('demo')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
              >
                Log Panel Demo
              </Button>
              <Button
                variant={viewMode === 'styletest' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('styletest')}
                className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-yellow-500 text-white border-0 hover:bg-yellow-600"
              >
                Style Test
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Health Status */}
      {getStatusIcon && getStatusText && (
        <div className="bg-white/90 backdrop-blur-sm border-b border-gray-100 py-2">
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm font-medium">{getStatusText()}</span>
            </div>
            <div className="flex items-center space-x-2">
              {wsConnected ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm font-medium">
                WebSocket {wsConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Health Alert */}
      {healthStatus && !healthStatus.codecrusher_available && (
        <div className="px-4 py-2">
          <Alert>
            <AlertDescription>
              CodeCrusher CLI not found. Please ensure it's installed and available in your PATH or virtual environment.
              {healthStatus.codecrusher_path && ` Detected path: ${healthStatus.codecrusher_path}`}
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Component Content */}
      <div className="flex-1">
        {children}
      </div>
    </div>
  );
}
