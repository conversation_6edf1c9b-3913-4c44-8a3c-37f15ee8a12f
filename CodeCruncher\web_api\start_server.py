#!/usr/bin/env python3
"""
CodeCrusher Web API Server Startup Script

This script provides a convenient way to start the CodeCrusher Web API server
with various configuration options and environment setup.
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def check_codecrusher_availability():
    """Check if CodeCrusher CLI is available in the system PATH."""
    try:
        result = subprocess.run(
            ["codecrusher", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        ("fastapi", "fastapi"),
        ("uvicorn", "uvicorn"),
        ("pydantic", "pydantic"),
        ("python-multipart", "multipart"),
        ("aiofiles", "aiofiles"),
        ("aiohttp", "aiohttp"),
        ("rich", "rich")
    ]

    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    return missing_packages

def install_dependencies():
    """Install missing dependencies."""
    print("Installing required dependencies...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def start_server(host="0.0.0.0", port=8000, reload=False, workers=1):
    """Start the FastAPI server using uvicorn."""
    cmd = [
        "uvicorn", "main:app",
        "--host", host,
        "--port", str(port)
    ]

    if reload:
        cmd.append("--reload")

    if workers > 1:
        cmd.extend(["--workers", str(workers)])

    print(f"🚀 Starting CodeCrusher Web API server...")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Reload: {reload}")
    print(f"   Workers: {workers}")
    print(f"   URL: http://{host}:{port}")
    print(f"   Docs: http://{host}:{port}/docs")
    print()

    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        return False

    return True

def main():
    """Main entry point for the startup script."""
    parser = argparse.ArgumentParser(
        description="CodeCrusher Web API Server Startup Script",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python start_server.py                    # Start with default settings
  python start_server.py --port 9000       # Start on port 9000
  python start_server.py --reload          # Start with auto-reload for development
  python start_server.py --workers 4       # Start with 4 worker processes
  python start_server.py --check-only      # Only check dependencies and CodeCrusher
        """
    )

    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind the server to (default: 127.0.0.1)"
    )

    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)"
    )

    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )

    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="Number of worker processes (default: 1)"
    )

    parser.add_argument(
        "--install-deps",
        action="store_true",
        help="Install missing dependencies before starting"
    )

    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Only check dependencies and CodeCrusher availability"
    )

    args = parser.parse_args()

    print("CodeCrusher Web API Server Startup")
    print("=" * 40)

    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ Error: main.py not found in current directory")
        print("   Please run this script from the web_api directory")
        return 1

    # Check dependencies
    print("🔍 Checking dependencies...")
    missing_deps = check_dependencies()

    if missing_deps:
        print(f"❌ Missing dependencies: {', '.join(missing_deps)}")

        if args.install_deps:
            if not install_dependencies():
                return 1
        else:
            print("   Run with --install-deps to install them automatically")
            print("   Or run: pip install -r requirements.txt")
            return 1
    else:
        print("✅ All dependencies are installed")

    # Check CodeCrusher availability
    print("🔍 Checking CodeCrusher CLI availability...")
    if check_codecrusher_availability():
        print("✅ CodeCrusher CLI is available")
    else:
        print("⚠️  CodeCrusher CLI not found in PATH")
        print("   The API will still start but injection functionality may not work")
        print("   Make sure CodeCrusher is installed and available in your PATH")

    if args.check_only:
        print("\n✅ Dependency check complete")
        return 0

    # Start the server
    print("\n" + "=" * 40)
    success = start_server(
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers
    )

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
