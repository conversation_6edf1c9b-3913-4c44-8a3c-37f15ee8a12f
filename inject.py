import typer
import logging
import async<PERSON>
from typing import Optional
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
import time

from codecrusher_engine import run_codecrusher_injection

# Initialize rich console
console = Console()

inject_app = typer.Typer(help="Inject AI-generated code into source files")

@inject_app.command("run")
def inject_run(
    input: str = typer.Option(..., "--input", "-i", help="Path to input file"),
    prompt_text: str = typer.Option(..., "--prompt-text", "-t", help="Prompt for code injection"),
    provider: str = typer.Option("auto", "--provider", help="AI provider to use"),
    model: str = typer.Option("auto", "--model", help="Model to use"),
    cache: bool = typer.Option(False, "--cache", help="Enable caching"),
    verbose: bool = typer.Option(False, "--verbose", help="Enable verbose logging"),
    output: Optional[str] = typer.Option(None, "--output", "-o", help="Output file path (default: print to console)"),
    save: bool = typer.Option(False, "--save", help="Save changes to the input file"),
):
    """
    Run code injection on the input file using AI engine.

    This command reads the input file, sends the code and prompt to the AI engine,
    and either prints the injected code or saves it to a file.
    """
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG if verbose else logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
    )
    logger = logging.getLogger(__name__)

    # Display configuration
    console.print(Panel(
        f"[bold]CodeCrusher Injection[/bold]\n\n"
        f"[cyan]Input file:[/cyan] {input}\n"
        f"[cyan]Prompt:[/cyan] {prompt_text}\n"
        f"[cyan]Provider:[/cyan] {provider}\n"
        f"[cyan]Model:[/cyan] {model}\n"
        f"[cyan]Cache:[/cyan] {'Enabled' if cache else 'Disabled'}\n"
        f"[cyan]Output:[/cyan] {output if output else 'Console'}\n"
        f"[cyan]Save to input file:[/cyan] {'Yes' if save else 'No'}",
        title="Configuration",
        border_style="blue"
    ))

    logger.info(f"Starting injection for file: {input}")
    logger.info(f"Prompt: {prompt_text}")
    logger.info(f"Provider: {provider}, Model: {model}, Cache: {cache}")

    # Read input file
    try:
        with open(input, "r", encoding="utf-8") as f:
            code = f.read()
        if verbose:
            logger.debug(f"Original code:\n{code}")
    except FileNotFoundError:
        error_msg = f"File not found: {input}"
        logger.error(error_msg)
        console.print(f"[bold red]❌ Error:[/bold red] {error_msg}")
        raise typer.Exit(code=1)
    except Exception as e:
        error_msg = f"Error reading file: {str(e)}"
        logger.error(error_msg)
        console.print(f"[bold red]❌ Error:[/bold red] {error_msg}")
        raise typer.Exit(code=1)

    # Call AI engine
    try:
        # Show progress spinner while waiting for AI
        with Progress(
            SpinnerColumn(),
            TextColumn("[bold green]Processing...[/bold green]"),
            console=console,
            transient=True,
        ) as progress:
            task = progress.add_task("Generating code...", total=None)

            # Run the AI engine
            logger.info("Sending code and prompt to AI engine...")
            injected_code = asyncio.run(run_codecrusher_injection(
                code=code,
                prompt=prompt_text,
                provider=provider,
                model=model,
                cache=cache,
                verbose=verbose
            ))

            # Mark task as complete
            progress.update(task, completed=True)

        # Log the result
        if verbose:
            logger.debug(f"Injected code:\n{injected_code}")

        # Handle output
        if output:
            # Save to output file
            try:
                with open(output, "w", encoding="utf-8") as f:
                    f.write(injected_code)
                console.print(f"[bold green][SUCCESS] Injected code saved to:[/bold green] {output}")
            except Exception as e:
                error_msg = f"Error writing to output file: {str(e)}"
                logger.error(error_msg)
                console.print(f"[bold red]❌ Error:[/bold red] {error_msg}")
                raise typer.Exit(code=1)
        elif save:
            # Save to input file
            try:
                with open(input, "w", encoding="utf-8") as f:
                    f.write(injected_code)
                console.print(f"[bold green][SUCCESS] Injected code saved to:[/bold green] {input}")
            except Exception as e:
                error_msg = f"Error writing to input file: {str(e)}"
                logger.error(error_msg)
                console.print(f"[bold red]❌ Error:[/bold red] {error_msg}")
                raise typer.Exit(code=1)
        else:
            # Print to console
            console.print("\n[bold]Injected Code:[/bold]")
            console.print(injected_code)

        logger.info("Injection completed successfully.")
        console.print("[bold green][SUCCESS] Injection completed successfully![/bold green]")

    except Exception as e:
        error_msg = f"Error during injection: {str(e)}"
        logger.error(error_msg)
        console.print(f"[bold red]❌ Error:[/bold red] {error_msg}")
        raise typer.Exit(code=1)
