/* CodeCrusher Public Documentation Styles */

/* CSS Variables for Theme Support */
:root {
  /* Light Theme Colors */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --success-color: #22c55e;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-code: #1e293b;

  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --text-inverse: #ffffff;

  /* Border Colors */
  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;

  /* Shadow */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Spacing */
  --container-max-width: 1200px;
  --section-padding: 5rem 0;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-code: #0f172a;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;

  --border-color: #334155;
  --border-hover: #475569;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Container */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: all 0.3s ease;
}

[data-theme="dark"] .navbar {
  background: rgba(15, 23, 42, 0.95);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--text-primary);
  text-decoration: none;
}

.logo {
  height: 2rem;
  width: auto;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link.github-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.nav-link.github-link:hover {
  background: var(--primary-color);
  color: var(--text-inverse);
  border-color: var(--primary-color);
}

.theme-toggle {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.theme-toggle:hover {
  background: var(--bg-secondary);
  color: var(--primary-color);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  gap: 0.25rem;
}

.mobile-menu-toggle span {
  width: 1.5rem;
  height: 2px;
  background: var(--text-primary);
  transition: all 0.3s ease;
}

/* Hero Section */
.hero {
  padding: 8rem 0 6rem;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-badge {
  display: inline-block;
  margin-bottom: 1.5rem;
}

.badge-text {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: var(--text-inverse);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-block;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin-bottom: 3rem;
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 4rem;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: 1rem;
}

.btn-primary {
  background: var(--primary-color);
  color: var(--text-inverse);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-secondary);
  border-color: var(--border-hover);
}

/* Terminal Demo */
.hero-demo {
  max-width: 600px;
  margin: 0 auto;
}

.terminal {
  background: var(--bg-code);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.terminal-header {
  background: #374151;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.terminal-buttons {
  display: flex;
  gap: 0.5rem;
}

.terminal-buttons span {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.btn-close { background: #ef4444; }
.btn-minimize { background: #f59e0b; }
.btn-maximize { background: #22c55e; }

.terminal-title {
  color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
}

.terminal-body {
  padding: 1.5rem;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.6;
}

.terminal-line {
  margin-bottom: 0.5rem;
}

.prompt {
  color: #22c55e;
  margin-right: 0.5rem;
}

.command {
  color: #e5e7eb;
}

.output {
  margin-left: 1rem;
}

.success { color: #22c55e; }
.info { color: #3b82f6; }
.warning { color: #f59e0b; }

/* Sections */
.section {
  padding: var(--section-padding);
}

.section-alt {
  background: var(--bg-secondary);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: var(--text-inverse);
  font-size: 1.5rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.feature-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Installation Methods */
.installation-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.method-card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.method-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.method-title i {
  color: var(--primary-color);
}

.method-description {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

/* Code Blocks */
.code-block {
  background: var(--bg-code);
  border-radius: var(--border-radius);
  overflow: hidden;
  margin: 1rem 0;
}

.code-header {
  background: #374151;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-lang {
  color: #d1d5db;
  font-size: 0.875rem;
  font-weight: 500;
}

.copy-btn {
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.3s ease;
}

.copy-btn:hover {
  color: #e5e7eb;
}

.code-block pre {
  padding: 1.5rem;
  margin: 0;
  overflow-x: auto;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  line-height: 1.6;
}

.code-block code {
  color: #e5e7eb;
}

.code-inline {
  background: var(--bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

/* Setup Steps */
.steps-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--text-primary);
}

.steps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.step {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.step-number {
  width: 2rem;
  height: 2rem;
  background: var(--primary-color);
  color: var(--text-inverse);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.step-content h4 {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.step-content p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* Usage Examples */
.usage-examples {
  display: grid;
  gap: 2rem;
}

.example-card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.example-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

/* Footer */
.footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 3rem 0 1.5rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.footer-logo {
  height: 2rem;
  width: auto;
}

.footer-brand-text {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--text-primary);
}

.footer-description {
  color: var(--text-secondary);
  line-height: 1.6;
}

.footer-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-copyright {
  color: var(--text-muted);
  font-size: 0.875rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.social-link {
  color: var(--text-secondary);
  font-size: 1.25rem;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: var(--primary-color);
}

/* Features Detailed */
.features-detailed {
  display: grid;
  gap: 3rem;
}

.feature-detailed {
  background: var(--bg-primary);
  padding: 2.5rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.feature-list {
  display: grid;
  gap: 1rem;
  margin-top: 1.5rem;
}

.feature-item {
  display: grid;
  grid-template-columns: auto 1fr auto;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.tier {
  background: var(--primary-color);
  color: var(--text-inverse);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 600;
}

.model {
  font-weight: 600;
  color: var(--text-primary);
}

.use-case {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.language-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.language-example {
  text-align: center;
}

.language-example h4 {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

/* Architecture Diagram */
.architecture-diagram {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.arch-layer {
  text-align: center;
  width: 100%;
  max-width: 600px;
}

.layer-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.layer-components {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.component {
  background: var(--primary-color);
  color: var(--text-inverse);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 500;
  font-size: 0.875rem;
}

.arch-arrow {
  font-size: 1.5rem;
  color: var(--primary-color);
  font-weight: bold;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.arch-feature {
  text-align: center;
  padding: 1.5rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.arch-feature h4 {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.arch-feature p {
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Contributing */
.contributing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.contrib-card {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  text-align: center;
}

.contrib-icon {
  width: 3rem;
  height: 3rem;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: var(--text-inverse);
  font-size: 1.25rem;
}

.contrib-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.contrib-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.guidelines-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--text-primary);
}

.guidelines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.guideline {
  padding: 1.5rem;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.guideline h4 {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.guideline p {
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .hero {
    padding: 6rem 0 4rem;
  }

  .hero-stats {
    gap: 2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .installation-methods {
    grid-template-columns: 1fr;
  }

  .steps-grid {
    grid-template-columns: 1fr;
  }

  .feature-item {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 0.5rem;
  }

  .language-examples {
    grid-template-columns: 1fr;
  }

  .layer-components {
    flex-direction: column;
    align-items: center;
  }

  .architecture-features {
    grid-template-columns: 1fr;
  }

  .contributing-grid {
    grid-template-columns: 1fr;
  }

  .guidelines-grid {
    grid-template-columns: 1fr;
  }

  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .feature-detailed {
    padding: 1.5rem;
  }

  .architecture-diagram {
    padding: 1rem;
  }
}
