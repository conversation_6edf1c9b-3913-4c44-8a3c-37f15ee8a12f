"""
Feedback Models for CodeCrusher Analytics

This module provides data models for tracking feedback and model performance
analytics across different AI models and injection scenarios.

Features:
- Enhanced feedback schema with performance metrics
- Model performance tracking (latency, token count, retry success)
- User rating analytics and satisfaction scoring
- Database integration ready for SQLAlchemy
- Analytics aggregation support
"""

from datetime import datetime
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
from enum import Enum

class RatingType(str, Enum):
    """User rating types for feedback."""
    UP = "up"
    DOWN = "down"
    NEUTRAL = "neutral"
    MEH = "meh"  # Alternative neutral rating

class ModelType(str, Enum):
    """Supported AI model types."""
    MIXTRAL = "mixtral"
    LLAMA3_8B = "llama3-8b"
    LLAMA3_70B = "llama3-70b"
    GPT4_TURBO = "gpt-4-turbo"
    GPT4 = "gpt-4"
    CLAUDE = "claude"
    CLAUDE_SONNET = "claude-sonnet"
    GEMINI = "gemini"
    LOCAL_MODEL = "local"

class IntentType(str, Enum):
    """Code injection intent types."""
    REFACTOR = "refactor"
    OPTIMIZE = "optimize"
    DOCUMENT = "document"
    TEST = "test"
    DEBUG = "debug"
    FEATURE = "feature"
    TRANSFORM = "transform"
    LOCALIZE = "localize"
    EXPLAIN = "explain"
    AUTO = "auto"

@dataclass
class FeedbackMetrics:
    """Performance metrics for feedback analysis."""
    latency_ms: int = 0
    token_count: Optional[int] = None
    retry_success: bool = False
    diff_precision_score: Optional[float] = None
    prompt_quality_score: Optional[float] = None
    confidence_score: Optional[float] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "latency_ms": self.latency_ms,
            "token_count": self.token_count,
            "retry_success": self.retry_success,
            "diff_precision_score": self.diff_precision_score,
            "prompt_quality_score": self.prompt_quality_score,
            "confidence_score": self.confidence_score
        }

@dataclass
class Feedback:
    """
    Enhanced feedback model with performance analytics.

    This model tracks comprehensive feedback data including user ratings,
    model performance metrics, and analytics for continuous improvement.
    """

    # Core feedback fields
    feedback_id: str = ""
    rating: RatingType = RatingType.NEUTRAL
    comment: Optional[str] = None

    # Injection context
    prompt: Optional[str] = None
    model: ModelType = ModelType.MIXTRAL
    intent: Optional[IntentType] = None
    file_path: Optional[str] = None
    injection_id: Optional[str] = None
    session_id: Optional[str] = None

    # Performance metrics (10.8.A requirements)
    latency_ms: int = 0
    token_count: Optional[int] = None
    retry_success: bool = False

    # Code analysis
    diff: Optional[str] = None
    before_code: Optional[str] = None
    after_code: Optional[str] = None
    diff_precision_score: Optional[float] = None

    # Quality metrics
    prompt_quality_score: Optional[float] = None
    confidence_score: Optional[float] = None

    # Auto-fix tracking
    auto_fix_id: Optional[str] = None
    auto_fix_applied: bool = False
    auto_fix_improved_rating: Optional[RatingType] = None

    # Metadata
    timestamp: datetime = field(default_factory=datetime.now)
    unix_timestamp: int = field(default_factory=lambda: int(datetime.now().timestamp()))
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None

    # Additional analytics data
    additional_data: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        """Post-initialization processing."""
        if isinstance(self.rating, str):
            self.rating = RatingType(self.rating)
        if isinstance(self.model, str):
            self.model = ModelType(self.model)
        if isinstance(self.intent, str) and self.intent:
            self.intent = IntentType(self.intent)

    def get_rating_score(self) -> float:
        """
        Convert rating to numerical score for analytics.

        Returns:
            Float score: up=1.0, neutral/meh=0.5, down=0.0
        """
        rating_scores = {
            RatingType.UP: 1.0,
            RatingType.NEUTRAL: 0.5,
            RatingType.MEH: 0.5,
            RatingType.DOWN: 0.0
        }
        return rating_scores.get(self.rating, 0.0)

    def is_positive_feedback(self) -> bool:
        """Check if feedback is positive."""
        return self.rating == RatingType.UP

    def is_negative_feedback(self) -> bool:
        """Check if feedback is negative."""
        return self.rating == RatingType.DOWN

    def get_performance_metrics(self) -> FeedbackMetrics:
        """Get performance metrics as structured object."""
        return FeedbackMetrics(
            latency_ms=self.latency_ms,
            token_count=self.token_count,
            retry_success=self.retry_success,
            diff_precision_score=self.diff_precision_score,
            prompt_quality_score=self.prompt_quality_score,
            confidence_score=self.confidence_score
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert feedback to dictionary for JSON serialization."""
        return {
            "feedback_id": self.feedback_id,
            "rating": self.rating.value if isinstance(self.rating, RatingType) else self.rating,
            "comment": self.comment,
            "prompt": self.prompt,
            "model": self.model.value if isinstance(self.model, ModelType) else self.model,
            "intent": self.intent.value if isinstance(self.intent, IntentType) else self.intent,
            "file_path": self.file_path,
            "injection_id": self.injection_id,
            "session_id": self.session_id,
            "latency_ms": self.latency_ms,
            "token_count": self.token_count,
            "retry_success": self.retry_success,
            "diff": self.diff,
            "before_code": self.before_code,
            "after_code": self.after_code,
            "diff_precision_score": self.diff_precision_score,
            "prompt_quality_score": self.prompt_quality_score,
            "confidence_score": self.confidence_score,
            "auto_fix_id": self.auto_fix_id,
            "auto_fix_applied": self.auto_fix_applied,
            "auto_fix_improved_rating": self.auto_fix_improved_rating.value if self.auto_fix_improved_rating else None,
            "timestamp": self.timestamp.isoformat(),
            "unix_timestamp": self.unix_timestamp,
            "user_agent": self.user_agent,
            "ip_address": self.ip_address,
            "additional_data": self.additional_data
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Feedback':
        """Create Feedback instance from dictionary."""
        # Handle timestamp conversion
        if isinstance(data.get('timestamp'), str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])

        # Handle enum conversions
        if 'rating' in data and isinstance(data['rating'], str):
            data['rating'] = RatingType(data['rating'])
        if 'model' in data and isinstance(data['model'], str):
            data['model'] = ModelType(data['model'])
        if 'intent' in data and isinstance(data['intent'], str) and data['intent']:
            data['intent'] = IntentType(data['intent'])
        if 'auto_fix_improved_rating' in data and isinstance(data['auto_fix_improved_rating'], str):
            data['auto_fix_improved_rating'] = RatingType(data['auto_fix_improved_rating'])

        return cls(**data)

@dataclass
class ModelPerformanceStats:
    """
    Aggregated performance statistics for a specific model.

    This class provides analytics and insights for model performance
    across different metrics and time periods.
    """

    model: ModelType
    total_feedback: int = 0

    # Rating analytics
    avg_rating: float = 0.0
    positive_feedback_count: int = 0
    negative_feedback_count: int = 0
    neutral_feedback_count: int = 0
    success_rate: float = 0.0

    # Performance metrics
    avg_latency_ms: float = 0.0
    min_latency_ms: int = 0
    max_latency_ms: int = 0
    avg_token_count: float = 0.0

    # Retry and auto-fix analytics
    retry_success_count: int = 0
    retry_success_rate: float = 0.0
    retry_saved_percentage: float = 0.0  # Percentage of bad prompts saved by retry
    auto_fix_applied_count: int = 0
    auto_fix_success_rate: float = 0.0

    # Quality metrics
    avg_prompt_quality_score: float = 0.0
    avg_diff_precision_score: float = 0.0
    avg_confidence_score: float = 0.0

    # Intent distribution
    intent_distribution: Dict[str, int] = field(default_factory=dict)

    # Time period
    period_start: Optional[datetime] = None
    period_end: Optional[datetime] = None

    def get_satisfaction_rate(self) -> float:
        """Calculate user satisfaction rate (positive feedback / total rated)."""
        total_rated = self.positive_feedback_count + self.negative_feedback_count
        if total_rated == 0:
            return 0.0
        return (self.positive_feedback_count / total_rated) * 100

    def get_performance_grade(self) -> str:
        """Get performance grade based on overall metrics."""
        satisfaction = self.get_satisfaction_rate()

        if satisfaction >= 90:
            return "A+"
        elif satisfaction >= 80:
            return "A"
        elif satisfaction >= 70:
            return "B+"
        elif satisfaction >= 60:
            return "B"
        elif satisfaction >= 50:
            return "C"
        else:
            return "D"

    def to_dict(self) -> Dict[str, Any]:
        """Convert stats to dictionary for JSON serialization."""
        return {
            "model": self.model.value if isinstance(self.model, ModelType) else self.model,
            "total_feedback": self.total_feedback,
            "avg_rating": round(self.avg_rating, 3),
            "positive_feedback_count": self.positive_feedback_count,
            "negative_feedback_count": self.negative_feedback_count,
            "neutral_feedback_count": self.neutral_feedback_count,
            "success_rate": round(self.success_rate, 3),
            "satisfaction_rate": round(self.get_satisfaction_rate(), 1),
            "performance_grade": self.get_performance_grade(),
            "avg_latency_ms": round(self.avg_latency_ms, 1),
            "min_latency_ms": self.min_latency_ms,
            "max_latency_ms": self.max_latency_ms,
            "avg_token_count": round(self.avg_token_count, 1),
            "retry_success_count": self.retry_success_count,
            "retry_success_rate": round(self.retry_success_rate, 3),
            "retry_saved_percentage": round(self.retry_saved_percentage, 1),
            "auto_fix_applied_count": self.auto_fix_applied_count,
            "auto_fix_success_rate": round(self.auto_fix_success_rate, 3),
            "avg_prompt_quality_score": round(self.avg_prompt_quality_score, 3),
            "avg_diff_precision_score": round(self.avg_diff_precision_score, 3),
            "avg_confidence_score": round(self.avg_confidence_score, 3),
            "intent_distribution": self.intent_distribution,
            "period_start": self.period_start.isoformat() if self.period_start else None,
            "period_end": self.period_end.isoformat() if self.period_end else None
        }

# Utility functions for analytics
def calculate_model_stats(feedback_list: list[Feedback]) -> Dict[str, ModelPerformanceStats]:
    """
    Calculate performance statistics for all models from feedback data.

    Args:
        feedback_list: List of Feedback objects

    Returns:
        Dictionary mapping model names to their performance stats
    """

    model_stats = {}

    # Group feedback by model
    model_feedback = {}
    for feedback in feedback_list:
        model = feedback.model
        if model not in model_feedback:
            model_feedback[model] = []
        model_feedback[model].append(feedback)

    # Calculate stats for each model
    for model, feedbacks in model_feedback.items():
        stats = ModelPerformanceStats(model=model)
        stats.total_feedback = len(feedbacks)

        if feedbacks:
            # Rating analytics
            ratings = [f.get_rating_score() for f in feedbacks]
            stats.avg_rating = sum(ratings) / len(ratings)

            stats.positive_feedback_count = sum(1 for f in feedbacks if f.is_positive_feedback())
            stats.negative_feedback_count = sum(1 for f in feedbacks if f.is_negative_feedback())
            stats.neutral_feedback_count = stats.total_feedback - stats.positive_feedback_count - stats.negative_feedback_count

            stats.success_rate = stats.positive_feedback_count / stats.total_feedback

            # Performance metrics
            latencies = [f.latency_ms for f in feedbacks if f.latency_ms > 0]
            if latencies:
                stats.avg_latency_ms = sum(latencies) / len(latencies)
                stats.min_latency_ms = min(latencies)
                stats.max_latency_ms = max(latencies)

            token_counts = [f.token_count for f in feedbacks if f.token_count is not None]
            if token_counts:
                stats.avg_token_count = sum(token_counts) / len(token_counts)

            # Retry analytics
            retry_successes = [f for f in feedbacks if f.retry_success]
            stats.retry_success_count = len(retry_successes)
            stats.retry_success_rate = len(retry_successes) / len(feedbacks)

            # Auto-fix analytics
            auto_fix_applied = [f for f in feedbacks if f.auto_fix_applied]
            stats.auto_fix_applied_count = len(auto_fix_applied)

            auto_fix_successes = [f for f in auto_fix_applied if f.auto_fix_improved_rating == RatingType.UP]
            if auto_fix_applied:
                stats.auto_fix_success_rate = len(auto_fix_successes) / len(auto_fix_applied)

            # Calculate retry saved percentage (bad prompts that were saved by retry)
            bad_feedback = [f for f in feedbacks if f.rating in [RatingType.DOWN, RatingType.NEUTRAL]]
            retry_saved = [f for f in bad_feedback if f.retry_success]
            stats.retry_saved_percentage = (len(retry_saved) / len(bad_feedback) * 100) if bad_feedback else 0.0

            # Quality metrics
            quality_scores = [f.prompt_quality_score for f in feedbacks if f.prompt_quality_score is not None]
            if quality_scores:
                stats.avg_prompt_quality_score = sum(quality_scores) / len(quality_scores)

            precision_scores = [f.diff_precision_score for f in feedbacks if f.diff_precision_score is not None]
            if precision_scores:
                stats.avg_diff_precision_score = sum(precision_scores) / len(precision_scores)

            confidence_scores = [f.confidence_score for f in feedbacks if f.confidence_score is not None]
            if confidence_scores:
                stats.avg_confidence_score = sum(confidence_scores) / len(confidence_scores)

            # Intent distribution
            for feedback in feedbacks:
                if feedback.intent:
                    intent_str = feedback.intent.value if isinstance(feedback.intent, IntentType) else str(feedback.intent)
                    stats.intent_distribution[intent_str] = stats.intent_distribution.get(intent_str, 0) + 1

        model_stats[model.value if isinstance(model, ModelType) else str(model)] = stats

    return model_stats
