#!/usr/bin/env python3
"""
Test Runner for CodeCrusher

This script runs all unit tests for the CodeCrusher system,
including intent detection, prompt shaping, and core functionality.
"""

import unittest
import sys
import os
from pathlib import Path

def run_all_tests():
    """Run all unit tests in the tests directory."""
    
    print("🧪 CODECRUSHER UNIT TEST RUNNER")
    print("=" * 50)
    
    # Add the current directory to Python path
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = current_dir / 'tests'
    
    if not start_dir.exists():
        print(f"❌ Tests directory not found: {start_dir}")
        return False
    
    # Load all tests from the tests directory
    suite = loader.discover(str(start_dir), pattern='test_*.py')
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        descriptions=True,
        failfast=False
    )
    
    print(f"🔍 Discovering tests in: {start_dir}")
    print(f"📊 Running test suite...")
    print()
    
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    
    success_rate = ((total_tests - failures - errors) / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📈 Total tests run: {total_tests}")
    print(f"✅ Successful: {total_tests - failures - errors}")
    print(f"❌ Failures: {failures}")
    print(f"💥 Errors: {errors}")
    print(f"⏭️ Skipped: {skipped}")
    print(f"📊 Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  • {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"  • {test}: {traceback.split('Exception:')[-1].strip()}")
    
    print("\n" + "=" * 50)
    
    # Return True if all tests passed
    return failures == 0 and errors == 0

def run_intent_tests_only():
    """Run only the intent detection tests."""
    
    print("🎯 INTENT DETECTION TESTS ONLY")
    print("=" * 40)
    
    # Add the current directory to Python path
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    try:
        # Import and run intent tests specifically
        from tests.test_intent import (
            TestIntentClassification,
            TestDetailedIntentClassification,
            TestIntentSuggestions,
            TestIntentUtilities,
            TestEdgeCases
        )
        
        # Create test suite
        suite = unittest.TestSuite()
        
        # Add all intent test classes
        test_classes = [
            TestIntentClassification,
            TestDetailedIntentClassification,
            TestIntentSuggestions,
            TestIntentUtilities,
            TestEdgeCases
        ]
        
        for test_class in test_classes:
            tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
            suite.addTests(tests)
        
        # Run tests
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        # Print summary
        total_tests = result.testsRun
        failures = len(result.failures)
        errors = len(result.errors)
        success_rate = ((total_tests - failures - errors) / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n📊 Intent Tests Summary:")
        print(f"✅ {total_tests - failures - errors}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        return failures == 0 and errors == 0
        
    except ImportError as e:
        print(f"❌ Could not import intent tests: {e}")
        return False

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Run CodeCrusher unit tests")
    parser.add_argument(
        "--intent-only", 
        action="store_true", 
        help="Run only intent detection tests"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    
    args = parser.parse_args()
    
    if args.intent_only:
        success = run_intent_tests_only()
    else:
        success = run_all_tests()
    
    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
