import React, { useState, useEffect, createContext, useContext } from 'react';
import { X, CheckCircle, AlertCircle, Info, Star, Code, Activity } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Toast {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning' | 'team-activity';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  teamEvent?: any;
}

interface ToastContextType {
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  addTeamActivityToast: (event: any) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const addToast = (toast: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast = { ...toast, id };
    
    setToasts(prev => [...prev, newToast]);

    // Auto-remove toast after duration
    const duration = toast.duration || 5000;
    setTimeout(() => {
      removeToast(id);
    }, duration);
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const addTeamActivityToast = (event: any) => {
    const userName = event.user_email.split('@')[0];
    
    let title = '';
    let message = '';
    let icon = '';
    
    switch (event.event_type) {
      case 'injection_created':
        title = '💉 New Injection';
        message = `${userName} injected ${event.payload.file_path}`;
        break;
      case 'injection_rated':
        title = '⭐ Injection Rated';
        message = `${userName} rated injection ${event.payload.rating}/5`;
        if (event.payload.feedback) {
          message += `: "${event.payload.feedback}"`;
        }
        break;
      case 'prompt_shaped':
        title = '🧠 Prompt Shaped';
        message = `${userName} improved prompt for ${event.payload.intent}`;
        break;
      case 'team_setting_changed':
        title = '⚙️ Setting Changed';
        message = `${userName} updated ${event.payload.setting_key}`;
        break;
      case 'user_joined_team':
        title = '👋 New Team Member';
        message = `${event.payload.joined_user_email} joined the team`;
        break;
      default:
        title = '📢 Team Activity';
        message = `${userName} performed an action`;
    }

    addToast({
      type: 'team-activity',
      title,
      message,
      duration: 4000,
      teamEvent: event
    });
  };

  return (
    <ToastContext.Provider value={{ addToast, removeToast, addTeamActivityToast }}>
      {children}
      <ToastContainer toasts={toasts} onRemove={removeToast} />
    </ToastContext.Provider>
  );
}

function ToastContainer({ toasts, onRemove }: { toasts: Toast[]; onRemove: (id: string) => void }) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map((toast) => (
        <ToastItem key={toast.id} toast={toast} onRemove={onRemove} />
      ))}
    </div>
  );
}

function ToastItem({ toast, onRemove }: { toast: Toast; onRemove: (id: string) => void }) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    setTimeout(() => setIsVisible(true), 10);
  }, []);

  const handleRemove = () => {
    setIsLeaving(true);
    setTimeout(() => onRemove(toast.id), 300);
  };

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      case 'team-activity':
        return getTeamActivityIcon(toast.teamEvent?.event_type);
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTeamActivityIcon = (eventType: string) => {
    switch (eventType) {
      case 'injection_created':
        return <Code className="h-5 w-5 text-blue-500" />;
      case 'injection_rated':
        return <Star className="h-5 w-5 text-yellow-500" />;
      case 'prompt_shaped':
        return <Activity className="h-5 w-5 text-purple-500" />;
      default:
        return <Activity className="h-5 w-5 text-gray-500" />;
    }
  };

  const getBackgroundColor = () => {
    switch (toast.type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      case 'team-activity':
        return 'bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200';
      default:
        return 'bg-white border-gray-200';
    }
  };

  return (
    <div
      className={cn(
        'relative overflow-hidden rounded-lg border shadow-lg transition-all duration-300 ease-in-out',
        getBackgroundColor(),
        isVisible && !isLeaving
          ? 'transform translate-x-0 opacity-100'
          : 'transform translate-x-full opacity-0',
        isLeaving && 'transform translate-x-full opacity-0'
      )}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          
          <div className="ml-3 flex-1">
            <p className="text-sm font-medium text-gray-900">
              {toast.title}
            </p>
            <p className="mt-1 text-sm text-gray-600">
              {toast.message}
            </p>
            
            {toast.action && (
              <div className="mt-3">
                <button
                  onClick={toast.action.onClick}
                  className="text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors"
                >
                  {toast.action.label}
                </button>
              </div>
            )}
          </div>
          
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={handleRemove}
              className="inline-flex text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
      
      {/* Progress bar for auto-dismiss */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200">
        <div
          className="h-full bg-blue-500 transition-all ease-linear"
          style={{
            animation: `shrink ${toast.duration || 5000}ms linear forwards`
          }}
        />
      </div>
      
      <style jsx>{`
        @keyframes shrink {
          from {
            width: 100%;
          }
          to {
            width: 0%;
          }
        }
      `}</style>
    </div>
  );
}

// Utility functions for common toast types
export const toast = {
  success: (title: string, message: string, options?: Partial<Toast>) => ({
    type: 'success' as const,
    title,
    message,
    ...options
  }),
  
  error: (title: string, message: string, options?: Partial<Toast>) => ({
    type: 'error' as const,
    title,
    message,
    ...options
  }),
  
  info: (title: string, message: string, options?: Partial<Toast>) => ({
    type: 'info' as const,
    title,
    message,
    ...options
  }),
  
  warning: (title: string, message: string, options?: Partial<Toast>) => ({
    type: 'warning' as const,
    title,
    message,
    ...options
  }),
  
  teamActivity: (event: any) => ({
    type: 'team-activity' as const,
    title: 'Team Activity',
    message: 'New team activity',
    teamEvent: event
  })
};
