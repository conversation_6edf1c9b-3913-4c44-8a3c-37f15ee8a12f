# CodeCrusher Assets

This directory contains visual assets for the CodeCrusher documentation site.

## Files

### Logo Files
- `logo.svg` - Vector logo for high-resolution displays
- `logo.png` - <PERSON><PERSON> logo (40x40px) - **To be added**
- `favicon.png` - Favicon (32x32px) - **To be added**

### Creating PNG Assets

To create the PNG versions from the SVG:

1. **Using Inkscape (Free)**:
   ```bash
   # Install Inkscape
   # Convert SVG to PNG
   inkscape --export-png=logo.png --export-width=40 --export-height=40 logo.svg
   inkscape --export-png=favicon.png --export-width=32 --export-height=32 logo.svg
   ```

2. **Using ImageMagick**:
   ```bash
   # Install ImageMagick
   # Convert SVG to PNG
   convert -background transparent -size 40x40 logo.svg logo.png
   convert -background transparent -size 32x32 logo.svg favicon.png
   ```

3. **Using Online Converter**:
   - Upload `logo.svg` to https://convertio.co/svg-png/
   - Set dimensions: 40x40 for logo, 32x32 for favicon
   - Download the converted files

4. **Using Design Tools**:
   - Open `logo.svg` in Figma, Adobe Illustrator, or Sketch
   - Export as PNG with appropriate dimensions

## Logo Design

The CodeCrusher logo features:
- **Gradient background**: Blue to green representing AI intelligence
- **Code brackets**: `< >` symbols representing code
- **Neural network**: Connected dots representing AI learning
- **Clean design**: Professional and modern appearance

## Usage Guidelines

- Use the SVG version when possible for crisp display at any size
- Use PNG versions for favicon and when SVG is not supported
- Maintain aspect ratio when resizing
- Ensure sufficient contrast against background colors
- The logo works well on both light and dark backgrounds

## Color Palette

- **Primary Blue**: #2563eb
- **Accent Green**: #10b981
- **White**: #ffffff (for symbols and text)

## Alternative Text

When using the logo in HTML, use appropriate alt text:
```html
<img src="assets/logo.png" alt="CodeCrusher Logo" />
```
