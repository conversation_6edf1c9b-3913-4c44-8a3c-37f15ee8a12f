# CodeCrusher Test Cases

This directory contains various test files for validating CodeCrusher CLI functionality.

## Test Files

### 1. `valid_python.py`
- **Purpose**: Valid Python file with proper AI_INJECT tags
- **Features**: 
  - Multiple AI_INJECT tags for different functionalities
  - Clean, well-structured code
  - Proper Python syntax and conventions
- **Expected Behavior**: Should process successfully

### 2. `large_file.py`
- **Purpose**: Large Python file to test performance
- **Features**:
  - Multiple classes and functions
  - Complex data structures
  - Extensive documentation
  - Multiple AI_INJECT tags
- **Expected Behavior**: Should handle large files efficiently

### 3. `syntax_error.py`
- **Purpose**: Python file with intentional syntax errors
- **Features**:
  - Missing parentheses, quotes, colons
  - Indentation errors
  - Undefined variables
- **Expected Behavior**: Should handle gracefully with error reporting

### 4. `unsupported.txt`
- **Purpose**: Plain text file (unsupported format)
- **Features**:
  - Contains AI_INJECT tags
  - Plain text content
- **Expected Behavior**: Should skip or handle gracefully

### 5. `javascript_example.js`
- **Purpose**: JavaScript file for multi-language testing
- **Features**:
  - ES6 class syntax
  - AI_INJECT tags
  - Module exports
- **Expected Behavior**: Should process if JavaScript support is enabled

### 6. `non_utf8.py`
- **Purpose**: Python file with non-UTF8 encoding
- **Features**:
  - Latin-1 encoding
  - Special characters (ñ, ç, ß, etc.)
  - Encoding declaration
- **Expected Behavior**: Should handle encoding properly

## Usage Examples

### Basic Test
```bash
codecrusher inject ./test-cases/valid_python.py --prompt "Add error handling" --preview
```

### Recursive Test
```bash
codecrusher inject ./test-cases --recursive --preview --ext py,js
```

### Apply Changes Test
```bash
codecrusher inject ./test-cases/valid_python.py --prompt "Add logging" --apply
```

### Force Test
```bash
codecrusher inject ./test-cases --recursive --prompt "Optimize code" --apply --force
```

## Expected Outcomes

1. **Valid Files**: Should process successfully with appropriate modifications
2. **Syntax Errors**: Should report errors but not crash
3. **Unsupported Files**: Should skip with informative messages
4. **Large Files**: Should process efficiently without memory issues
5. **Encoding Issues**: Should handle gracefully with proper encoding detection

## Testing Checklist

- [ ] CLI entry point works: `codecrusher inject`
- [ ] Recursive processing: `--recursive` flag
- [ ] Preview mode: `--preview` flag
- [ ] Apply mode: `--apply` flag
- [ ] Extension filtering: `--ext` flag
- [ ] Error handling for invalid files
- [ ] Performance with large files
- [ ] Multi-language support
- [ ] Encoding handling
- [ ] Help and usage information
