#!/usr/bin/env python3
"""
Simple CodeCrusher Web Server
A minimal FastAPI server to demonstrate CodeCrusher's web interface
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any
import subprocess
import os
import json
from datetime import datetime

# Initialize FastAPI app
app = FastAPI(
    title="CodeCrusher Web API",
    description="AI-powered code injection via web interface",
    version="1.0.0"
)

# CORS support
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response models
class InjectionRequest(BaseModel):
    source: str
    prompt_text: str
    apply: bool = False
    preview: bool = True
    model: Optional[str] = "llama3-70b-8192"
    auto_model_routing: bool = False
    recursive: bool = False
    ext: str = "py"
    tag: str = "web"
    refresh_cache: bool = False
    force: bool = False

class InjectionResponse(BaseModel):
    success: bool
    message: str
    output: Optional[str] = None
    error: Optional[str] = None
    execution_time: Optional[float] = None
    timestamp: str

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve a simple web interface."""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>CodeCrusher Web Interface</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #34495e; }
            input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
            textarea { height: 100px; resize: vertical; }
            button { background: #3498db; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin-right: 10px; }
            button:hover { background: #2980b9; }
            .preview-btn { background: #f39c12; }
            .preview-btn:hover { background: #e67e22; }
            .output { margin-top: 20px; padding: 15px; background: #ecf0f1; border-radius: 5px; white-space: pre-wrap; font-family: monospace; max-height: 400px; overflow-y: auto; }
            .success { border-left: 4px solid #27ae60; }
            .error { border-left: 4px solid #e74c3c; }
            .checkbox-group { display: flex; align-items: center; gap: 10px; }
            .checkbox-group input { width: auto; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧠 CodeCrusher Web Interface</h1>
            <p style="text-align: center; color: #7f8c8d; margin-bottom: 30px;">
                Enterprise-grade AI-powered code injection tool
            </p>
            
            <form id="injectionForm">
                <div class="form-group">
                    <label for="source">Source Path:</label>
                    <input type="text" id="source" name="source" value="example.py" placeholder="Path to file or directory">
                </div>
                
                <div class="form-group">
                    <label for="prompt_text">AI Prompt:</label>
                    <textarea id="prompt_text" name="prompt_text" placeholder="Describe what you want the AI to implement...">Add comprehensive error handling and logging</textarea>
                </div>
                
                <div class="form-group">
                    <label for="model">AI Model:</label>
                    <select id="model" name="model">
                        <option value="llama3-70b-8192">LLaMA 3 70B (Recommended)</option>
                        <option value="llama3-8b-8192">LLaMA 3 8B (Fast)</option>
                        <option value="auto">Auto Selection</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="recursive" name="recursive">
                        <label for="recursive">Recursive (scan subdirectories)</label>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="refresh_cache" name="refresh_cache">
                        <label for="refresh_cache">Refresh Cache (force new generation)</label>
                    </div>
                </div>
                
                <div style="text-align: center;">
                    <button type="button" class="preview-btn" onclick="runInjection(false)">Preview Changes</button>
                    <button type="button" onclick="runInjection(true)">Apply Changes</button>
                </div>
            </form>
            
            <div id="output" class="output" style="display: none;"></div>
        </div>

        <script>
            async function runInjection(apply) {
                const form = document.getElementById('injectionForm');
                const output = document.getElementById('output');
                
                const data = {
                    source: form.source.value,
                    prompt_text: form.prompt_text.value,
                    model: form.model.value,
                    apply: apply,
                    preview: !apply,
                    recursive: form.recursive.checked,
                    refresh_cache: form.refresh_cache.checked,
                    auto_model_routing: form.model.value === 'auto'
                };
                
                output.style.display = 'block';
                output.innerHTML = 'Processing... 🤖';
                output.className = 'output';
                
                try {
                    const response = await fetch('/inject', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        output.className = 'output success';
                        output.innerHTML = `✅ ${result.message}\\n\\n${result.output || ''}`;
                    } else {
                        output.className = 'output error';
                        output.innerHTML = `❌ ${result.message}\\n\\n${result.error || ''}`;
                    }
                } catch (error) {
                    output.className = 'output error';
                    output.innerHTML = `❌ Network Error: ${error.message}`;
                }
            }
        </script>
    </body>
    </html>
    """
    return html_content

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Check if codecrusher is available
        result = subprocess.run(["codecrusher", "--help"], 
                              capture_output=True, text=True, timeout=5)
        codecrusher_available = result.returncode == 0
    except:
        codecrusher_available = False
    
    return {
        "status": "healthy" if codecrusher_available else "degraded",
        "codecrusher_available": codecrusher_available,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/inject", response_model=InjectionResponse)
async def inject_code(req: InjectionRequest):
    """Main code injection endpoint."""
    start_time = datetime.now()
    
    try:
        # Build codecrusher command
        cmd = ["codecrusher", "--source", req.source, "--prompt-text", req.prompt_text]
        
        if req.apply:
            cmd.append("--apply")
        else:
            cmd.append("--preview")
            
        if req.model and req.model != "auto":
            cmd.extend(["--model", req.model])
        
        if req.auto_model_routing or req.model == "auto":
            cmd.append("--auto-model-routing")
            
        if req.recursive:
            cmd.append("--recursive")
            
        if req.refresh_cache:
            cmd.append("--refresh-cache")
            
        if req.force:
            cmd.append("--force")
            
        cmd.extend(["--ext", req.ext, "--tag", req.tag])
        
        # Execute codecrusher
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        success = result.returncode == 0
        output_text = result.stdout if result.stdout else ""
        error_text = result.stderr if result.stderr else ""
        
        return InjectionResponse(
            success=success,
            message="Code injection completed successfully" if success else "Code injection failed",
            output=output_text,
            error=error_text if not success else None,
            execution_time=execution_time,
            timestamp=datetime.now().isoformat()
        )
        
    except subprocess.TimeoutExpired:
        return InjectionResponse(
            success=False,
            message="Code injection timed out",
            error="Operation timed out after 120 seconds",
            execution_time=120.0,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        return InjectionResponse(
            success=False,
            message=f"Internal error: {str(e)}",
            error=str(e),
            execution_time=execution_time,
            timestamp=datetime.now().isoformat()
        )

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting CodeCrusher Web Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API docs available at: http://localhost:8000/docs")
    uvicorn.run(app, host="0.0.0.0", port=8000)
