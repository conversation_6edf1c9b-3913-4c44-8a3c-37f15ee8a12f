"""
Dashboard command for CodeCrusher.

This module provides the dashboard command for CodeCrusher, which launches
a local web server to visualize telemetry data.
"""

import typer
import json
import os
import sys
import threading
import webbrowser
from pathlib import Path
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict, Counter
import time
from rich.console import Console
from rich.panel import Panel

# Import telemetry logger
from codecrusher.telemetry_logger import get_telemetry_entries, TELEMETRY_FILE, TELEMETRY_DIR

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

# Define cache file path
CACHE_FILE = os.path.join(TELEMETRY_DIR, ".dashboard_cache.json")
CACHE_EXPIRY = 60 * 5  # 5 minutes in seconds

# Try to import Flask and Plotly
try:
    from flask import Flask, render_template, jsonify, request
    import plotly
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import pandas as pd
    DEPENDENCIES_INSTALLED = True
except ImportError:
    DEPENDENCIES_INSTALLED = False

def check_dependencies():
    """
    Check if the required dependencies are installed.
    """
    if not DEPENDENCIES_INSTALLED:
        console.print("[bold red]❌ Error:[/bold red] Required dependencies not installed.")
        console.print("[yellow]Please install the required dependencies:[/yellow]")
        console.print("pip install flask plotly pandas")
        return False
    return True

def load_cached_data():
    """
    Load cached data if available and not expired.

    Returns:
        Tuple[bool, Dict]: (is_valid, data)
    """
    if not os.path.exists(CACHE_FILE):
        return False, {}

    try:
        with open(CACHE_FILE, "r", encoding="utf-8") as f:
            cache = json.load(f)

        # Check if cache is expired
        cache_time = cache.get("timestamp", 0)
        if time.time() - cache_time > CACHE_EXPIRY:
            return False, {}

        return True, cache.get("data", {})
    except Exception as e:
        console.print(f"[yellow]Failed to load cache: {e}[/yellow]")
        return False, {}

def save_cached_data(data):
    """
    Save data to cache.

    Args:
        data: Data to cache
    """
    try:
        cache = {
            "timestamp": time.time(),
            "data": data
        }

        with open(CACHE_FILE, "w", encoding="utf-8") as f:
            json.dump(cache, f)
    except Exception as e:
        console.print(f"[yellow]Failed to save cache: {e}[/yellow]")

def process_telemetry_data(limit=10000, use_cache=True):
    """
    Process telemetry data for visualization.

    Args:
        limit: Maximum number of entries to process
        use_cache: Whether to use cached data

    Returns:
        Dict: Processed data
    """
    # Check if we can use cached data
    if use_cache:
        is_valid, cached_data = load_cached_data()
        if is_valid:
            return cached_data

    # Get telemetry entries
    entries = get_telemetry_entries(limit=limit)

    # Process data
    data = {
        "entries": entries,
        "injections_per_day": defaultdict(int),
        "latency_by_model": defaultdict(list),
        "fallback_by_model": defaultdict(lambda: {"total": 0, "fallback": 0}),
        "token_usage": [],
        "cache_hits": defaultdict(lambda: {"cached": 0, "live": 0}),
        "errors": defaultdict(int),
        "tag_frequency": Counter(),
        "recent_fallbacks": [],
        "tag_distribution": Counter(),  # For tag breakdown chart
        "model_uptime_24h": defaultdict(lambda: {"success": 0, "total": 0}),  # For model uptime bar
        "raw_entries": entries,  # For CSV export
    }

    for entry in entries:
        # Extract date from timestamp
        timestamp = entry.get("timestamp", "")
        try:
            dt = datetime.fromisoformat(timestamp)
            date_str = dt.strftime("%Y-%m-%d")
        except (ValueError, TypeError):
            date_str = "unknown"

        # Count injections per day
        if entry.get("operation_type") == "injection":
            data["injections_per_day"][date_str] += 1

        # Collect latency by model
        model = entry.get("model", "unknown")
        latency = entry.get("latency_ms")
        if latency is not None:
            data["latency_by_model"][model].append(latency)

        # Track fallback rate by model
        data["fallback_by_model"][model]["total"] += 1
        if entry.get("fallback", False):
            data["fallback_by_model"][model]["fallback"] += 1

            # Add to recent fallbacks
            if timestamp:
                data["recent_fallbacks"].append({
                    "timestamp": timestamp,
                    "model": model,
                    "provider": entry.get("provider", "unknown"),
                    "error": entry.get("error", "unknown")
                })

        # Collect token usage
        tokens_in = entry.get("tokens_in")
        tokens_out = entry.get("tokens_out")
        if tokens_in is not None or tokens_out is not None:
            data["token_usage"].append({
                "timestamp": timestamp,
                "tokens_in": tokens_in or 0,
                "tokens_out": tokens_out or 0
            })

        # Track cache hits by provider
        provider = entry.get("provider", "unknown")
        if entry.get("cached", False):
            data["cache_hits"][provider]["cached"] += 1
        else:
            data["cache_hits"][provider]["live"] += 1

        # Count errors
        error = entry.get("error")
        if error:
            error_type = "unknown"
            if "timeout" in str(error).lower():
                error_type = "timeout"
            elif "invalid" in str(error).lower():
                error_type = "invalid_response"
            elif "api" in str(error).lower():
                error_type = "api_error"
            data["errors"][error_type] += 1

        # Count tag frequency
        for tag in entry.get("tags", []):
            data["tag_frequency"][tag] += 1

    # Sort recent fallbacks by timestamp (newest first)
    data["recent_fallbacks"].sort(key=lambda x: x.get("timestamp", ""), reverse=True)

    # Limit to top 5 recent fallbacks
    data["recent_fallbacks"] = data["recent_fallbacks"][:5]

    # Get top 5 tags by frequency
    data["top_tags"] = data["tag_frequency"].most_common(5)

    # Get top 10 tags for tag distribution chart
    data["tag_distribution"] = data["tag_frequency"].most_common(10)

    # Calculate model uptime for the last 24 hours
    now = datetime.now()
    one_day_ago = now - timedelta(days=1)

    # Reset model uptime data
    model_uptime_24h = defaultdict(lambda: {"success": 0, "total": 0})

    for entry in entries:
        # Extract timestamp
        timestamp = entry.get("timestamp", "")
        try:
            dt = datetime.fromisoformat(timestamp)
            # Check if entry is from the last 24 hours
            if dt >= one_day_ago:
                model = entry.get("model", "unknown")
                model_uptime_24h[model]["total"] += 1

                # Count as success if no error and not a fallback
                if not entry.get("error") and not entry.get("fallback", False):
                    model_uptime_24h[model]["success"] += 1
        except (ValueError, TypeError):
            pass

    # Calculate uptime percentages
    for model, counts in model_uptime_24h.items():
        if counts["total"] > 0:
            counts["uptime"] = (counts["success"] / counts["total"]) * 100
        else:
            counts["uptime"] = 0

    data["model_uptime_24h"] = dict(model_uptime_24h)

    # Cache the processed data
    save_cached_data(data)

    return data

def create_flask_app():
    """
    Create the Flask app for the dashboard.

    Returns:
        Flask: Flask app
    """
    # Create Flask app
    flask_app = Flask(__name__, template_folder=os.path.join(os.path.dirname(__file__), "templates"))

    # Create templates directory if it doesn't exist
    templates_dir = os.path.join(os.path.dirname(__file__), "templates")
    os.makedirs(templates_dir, exist_ok=True)

    # Create dashboard.html template
    dashboard_html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>CodeCrusher Dashboard</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
        <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
        <style>
            body {
                padding-top: 20px;
                padding-bottom: 20px;
                background-color: #f8f9fa;
            }
            .chart-container {
                background-color: white;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                padding: 15px;
                margin-bottom: 20px;
            }
            .navbar {
                margin-bottom: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .card {
                margin-bottom: 20px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .refresh-btn {
                margin-left: 10px;
            }
            #last-updated {
                font-size: 0.8em;
                color: #6c757d;
                margin-top: 5px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                <div class="container-fluid">
                    <a class="navbar-brand" href="#">CodeCrusher Dashboard</a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav">
                            <li class="nav-item">
                                <a class="nav-link active" href="#">Dashboard</a>
                            </li>
                        </ul>
                        <div class="ms-auto">
                            <a href="/export-csv" class="btn btn-success me-2">⬇ Export All Telemetry to CSV</a>
                            <button id="refresh-btn" class="btn btn-light refresh-btn">Refresh Data</button>
                        </div>
                    </div>
                </div>
            </nav>

            <div class="row">
                <div class="col-md-12">
                    <div id="last-updated"></div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Injections per Day</h4>
                        <div id="injections-chart"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Average Latency by Model</h4>
                        <div id="latency-chart"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Fallback Rate by Model</h4>
                        <div id="fallback-chart"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Token Usage</h4>
                        <div id="token-chart"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Cached vs Live Hits by Provider</h4>
                        <div id="cache-chart"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Error Frequency</h4>
                        <div id="error-chart"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">Top 5 Tags</h5>
                        </div>
                        <div class="card-body">
                            <div id="tags-chart"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="card-title mb-0">Recent Fallback Events</h5>
                        </div>
                        <div class="card-body">
                            <div id="recent-fallbacks"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Tag Distribution</h4>
                        <div class="mb-2">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="tagDisplayType" id="tagDisplayAbsolute" value="absolute" checked>
                                <label class="form-check-label" for="tagDisplayAbsolute">Absolute</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="tagDisplayType" id="tagDisplayPercentage" value="percentage">
                                <label class="form-check-label" for="tagDisplayPercentage">Percentage</label>
                            </div>
                        </div>
                        <div id="tag-distribution-chart"></div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h4>Model Uptime (Last 24h)</h4>
                        <div id="model-uptime-chart"></div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Function to update the dashboard
            function updateDashboard() {
                fetch('/data')
                    .then(response => response.json())
                    .then(data => {
                        // Update last updated time
                        document.getElementById('last-updated').textContent = 'Last updated: ' + new Date().toLocaleString();

                        // Render charts
                        renderInjectionsChart(data.injections_per_day);
                        renderLatencyChart(data.latency_by_model);
                        renderFallbackChart(data.fallback_by_model);
                        renderTokenChart(data.token_usage);
                        renderCacheChart(data.cache_hits);
                        renderErrorChart(data.errors);
                        renderTagsChart(data.top_tags);
                        renderRecentFallbacks(data.recent_fallbacks);
                        renderTagDistributionChart(data.tag_distribution);
                        renderModelUptimeChart(data.model_uptime_24h);
                    })
                    .catch(error => console.error('Error fetching data:', error));
            }

            // Render injections per day chart
            function renderInjectionsChart(data) {
                const dates = Object.keys(data).sort();
                const counts = dates.map(date => data[date]);

                const trace = {
                    x: dates,
                    y: counts,
                    type: 'bar',
                    marker: {
                        color: 'rgba(0, 123, 255, 0.7)'
                    }
                };

                const layout = {
                    margin: { t: 10, b: 40, l: 40, r: 10 },
                    yaxis: { title: 'Count' }
                };

                Plotly.newPlot('injections-chart', [trace], layout);
            }

            // Render average latency by model chart
            function renderLatencyChart(data) {
                const models = Object.keys(data);
                const avgLatencies = models.map(model => {
                    const latencies = data[model];
                    return latencies.length > 0 ? latencies.reduce((a, b) => a + b, 0) / latencies.length : 0;
                });

                const trace = {
                    x: models,
                    y: avgLatencies,
                    type: 'bar',
                    marker: {
                        color: 'rgba(40, 167, 69, 0.7)'
                    }
                };

                const layout = {
                    margin: { t: 10, b: 40, l: 40, r: 10 },
                    yaxis: { title: 'Avg Latency (ms)' }
                };

                Plotly.newPlot('latency-chart', [trace], layout);
            }

            // Render fallback rate by model chart
            function renderFallbackChart(data) {
                const models = Object.keys(data);
                const fallbackRates = models.map(model => {
                    const modelData = data[model];
                    return modelData.total > 0 ? (modelData.fallback / modelData.total) * 100 : 0;
                });

                const trace = {
                    x: models,
                    y: fallbackRates,
                    type: 'bar',
                    marker: {
                        color: 'rgba(255, 193, 7, 0.7)'
                    }
                };

                const layout = {
                    margin: { t: 10, b: 40, l: 40, r: 10 },
                    yaxis: { title: 'Fallback Rate (%)' }
                };

                Plotly.newPlot('fallback-chart', [trace], layout);
            }

            // Render token usage chart
            function renderTokenChart(data) {
                if (data.length === 0) {
                    document.getElementById('token-chart').innerHTML = '<p class="text-muted">No token usage data available</p>';
                    return;
                }

                // Sort by timestamp
                data.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

                const timestamps = data.map(item => item.timestamp);
                const tokensIn = data.map(item => item.tokens_in);
                const tokensOut = data.map(item => item.tokens_out);

                const traceIn = {
                    x: timestamps,
                    y: tokensIn,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Tokens In',
                    line: { color: 'rgba(0, 123, 255, 0.7)' }
                };

                const traceOut = {
                    x: timestamps,
                    y: tokensOut,
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Tokens Out',
                    line: { color: 'rgba(220, 53, 69, 0.7)' }
                };

                const layout = {
                    margin: { t: 10, b: 40, l: 40, r: 10 },
                    yaxis: { title: 'Token Count' },
                    legend: { orientation: 'h', y: -0.2 }
                };

                Plotly.newPlot('token-chart', [traceIn, traceOut], layout);
            }

            // Render cached vs live hits chart
            function renderCacheChart(data) {
                const providers = Object.keys(data);
                const cachedHits = providers.map(provider => data[provider].cached);
                const liveHits = providers.map(provider => data[provider].live);

                const traceCached = {
                    x: providers,
                    y: cachedHits,
                    name: 'Cached',
                    type: 'bar',
                    marker: { color: 'rgba(40, 167, 69, 0.7)' }
                };

                const traceLive = {
                    x: providers,
                    y: liveHits,
                    name: 'Live',
                    type: 'bar',
                    marker: { color: 'rgba(0, 123, 255, 0.7)' }
                };

                const layout = {
                    barmode: 'group',
                    margin: { t: 10, b: 40, l: 40, r: 10 },
                    yaxis: { title: 'Count' },
                    legend: { orientation: 'h', y: -0.2 }
                };

                Plotly.newPlot('cache-chart', [traceCached, traceLive], layout);
            }

            // Render error frequency chart
            function renderErrorChart(data) {
                const errorTypes = Object.keys(data);
                const errorCounts = errorTypes.map(type => data[type]);

                if (errorCounts.every(count => count === 0)) {
                    document.getElementById('error-chart').innerHTML = '<p class="text-muted">No errors recorded</p>';
                    return;
                }

                const trace = {
                    labels: errorTypes,
                    values: errorCounts,
                    type: 'pie',
                    marker: {
                        colors: ['rgba(220, 53, 69, 0.7)', 'rgba(255, 193, 7, 0.7)', 'rgba(108, 117, 125, 0.7)']
                    }
                };

                const layout = {
                    margin: { t: 10, b: 10, l: 10, r: 10 },
                    showlegend: true
                };

                Plotly.newPlot('error-chart', [trace], layout);
            }

            // Render top tags chart
            function renderTagsChart(data) {
                if (data.length === 0) {
                    document.getElementById('tags-chart').innerHTML = '<p class="text-muted">No tags data available</p>';
                    return;
                }

                const tags = data.map(item => item[0]);
                const counts = data.map(item => item[1]);

                const trace = {
                    y: tags,
                    x: counts,
                    type: 'bar',
                    orientation: 'h',
                    marker: {
                        color: 'rgba(0, 123, 255, 0.7)'
                    }
                };

                const layout = {
                    margin: { t: 10, b: 30, l: 100, r: 10 },
                    xaxis: { title: 'Count' }
                };

                Plotly.newPlot('tags-chart', [trace], layout);
            }

            // Render recent fallbacks
            function renderRecentFallbacks(data) {
                const container = document.getElementById('recent-fallbacks');

                if (data.length === 0) {
                    container.innerHTML = '<p class="text-muted">No recent fallbacks</p>';
                    return;
                }

                let html = '<ul class="list-group">';

                data.forEach(item => {
                    const timestamp = new Date(item.timestamp).toLocaleString();
                    html += `
                        <li class="list-group-item">
                            <div><strong>Time:</strong> ${timestamp}</div>
                            <div><strong>Model:</strong> ${item.model}</div>
                            <div><strong>Provider:</strong> ${item.provider}</div>
                            <div><strong>Error:</strong> ${item.error || 'Unknown'}</div>
                        </li>
                    `;
                });

                html += '</ul>';
                container.innerHTML = html;
            }

            // Initial update
            updateDashboard();

            // Set up auto-refresh
            let refreshInterval = setInterval(updateDashboard, 10000); // 10 seconds

            // Render tag distribution chart
            function renderTagDistributionChart(data) {
                if (!data || data.length === 0) {
                    document.getElementById('tag-distribution-chart').innerHTML = '<p class="text-muted">No tag data available</p>';
                    return;
                }

                // Get display type (absolute or percentage)
                const displayType = document.querySelector('input[name="tagDisplayType"]:checked').value;

                // Extract tags and counts
                const tags = data.map(item => item[0]);
                let counts = data.map(item => item[1]);

                // Calculate total for percentage
                const total = counts.reduce((a, b) => a + b, 0);

                // Convert to percentages if needed
                if (displayType === 'percentage') {
                    counts = counts.map(count => (count / total) * 100);
                }

                // Define colors for tags
                const colors = [
                    'rgba(0, 123, 255, 0.7)',   // Blue
                    'rgba(40, 167, 69, 0.7)',   // Green
                    'rgba(255, 193, 7, 0.7)',   // Yellow
                    'rgba(220, 53, 69, 0.7)',   // Red
                    'rgba(111, 66, 193, 0.7)',  // Purple
                    'rgba(23, 162, 184, 0.7)',  // Cyan
                    'rgba(255, 127, 80, 0.7)',  // Coral
                    'rgba(128, 128, 128, 0.7)', // Gray
                    'rgba(0, 128, 128, 0.7)',   // Teal
                    'rgba(255, 105, 180, 0.7)'  // Pink
                ];

                // Create traces with colors
                const trace = {
                    x: counts,
                    y: tags,
                    type: 'bar',
                    orientation: 'h',
                    marker: {
                        color: colors.slice(0, tags.length)
                    },
                    text: displayType === 'percentage'
                        ? counts.map(count => count.toFixed(1) + '%')
                        : counts,
                    textposition: 'auto'
                };

                const layout = {
                    margin: { t: 10, b: 30, l: 150, r: 10 },
                    xaxis: {
                        title: displayType === 'percentage' ? 'Percentage (%)' : 'Count',
                        range: [0, displayType === 'percentage' ? 100 : Math.max(...counts) * 1.1]
                    },
                    height: 400
                };

                Plotly.newPlot('tag-distribution-chart', [trace], layout);
            }

            // Render model uptime chart
            function renderModelUptimeChart(data) {
                if (!data || Object.keys(data).length === 0) {
                    document.getElementById('model-uptime-chart').innerHTML = '<p class="text-muted">No model uptime data available</p>';
                    return;
                }

                // Extract models and uptime percentages
                const models = Object.keys(data);
                const uptimes = models.map(model => data[model].uptime || 0);

                // Define color function based on uptime
                const getColor = (uptime) => {
                    if (uptime >= 90) return 'rgba(40, 167, 69, 0.7)';  // Green
                    if (uptime >= 70) return 'rgba(255, 193, 7, 0.7)';  // Yellow
                    return 'rgba(220, 53, 69, 0.7)';                    // Red
                };

                // Create bar colors based on uptime
                const colors = uptimes.map(getColor);

                const trace = {
                    x: uptimes,
                    y: models,
                    type: 'bar',
                    orientation: 'h',
                    marker: { color: colors },
                    text: uptimes.map(uptime => uptime.toFixed(1) + '%'),
                    textposition: 'auto'
                };

                const layout = {
                    margin: { t: 10, b: 30, l: 100, r: 10 },
                    xaxis: {
                        title: 'Uptime (%)',
                        range: [0, 100]
                    },
                    height: 400
                };

                Plotly.newPlot('model-uptime-chart', [trace], layout);
            }

            // Add event listeners for tag display type
            document.querySelectorAll('input[name="tagDisplayType"]').forEach(radio => {
                radio.addEventListener('change', () => {
                    // Re-fetch data and update charts
                    fetch('/data')
                        .then(response => response.json())
                        .then(data => {
                            renderTagDistributionChart(data.tag_distribution);
                        });
                });
            });

            // Manual refresh button
            document.getElementById('refresh-btn').addEventListener('click', updateDashboard);
        </script>
    </body>
    </html>
    """

    with open(os.path.join(templates_dir, "dashboard.html"), "w", encoding="utf-8") as f:
        f.write(dashboard_html)

    # Define routes
    @flask_app.route('/')
    def index():
        return render_template('dashboard.html')

    @flask_app.route('/data')
    def data():
        # Get filter parameters
        use_cache = request.args.get('use_cache', 'true').lower() == 'true'

        # Process telemetry data
        data = process_telemetry_data(use_cache=use_cache)

        return jsonify(data)

    @flask_app.route('/export-csv')
    def export_csv():
        """
        Export telemetry data as CSV.
        """
        import csv
        import io
        from flask import Response

        # Process telemetry data
        data = process_telemetry_data(use_cache=True)

        # Create CSV in memory
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow([
            'id', 'timestamp', 'model', 'provider', 'latency_ms',
            'tokens_in', 'tokens_out', 'cached', 'fallback', 'error', 'tags'
        ])

        # Write data
        for entry in data.get("raw_entries", []):
            writer.writerow([
                entry.get('id', ''),
                entry.get('timestamp', ''),
                entry.get('model', ''),
                entry.get('provider', ''),
                entry.get('latency_ms', ''),
                entry.get('tokens_in', ''),
                entry.get('tokens_out', ''),
                entry.get('cached', False),
                entry.get('fallback', False),
                entry.get('error', ''),
                ', '.join(entry.get('tags', []))
            ])

        # Create response
        response = Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={
                'Content-Disposition': 'attachment; filename=codecrusher_telemetry.csv'
            }
        )

        return response

    return flask_app

@app.command("run")
def run_dashboard(
    port: int = typer.Option(8008, "--port", "-p", help="Port to run the dashboard on"),
    no_browser: bool = typer.Option(False, "--no-browser", help="Don't open the browser automatically"),
    no_cache: bool = typer.Option(False, "--no-cache", help="Don't use cached data"),
):
    """
    Launch a local dashboard for visualizing CodeCrusher metrics.

    This command launches a local web server to visualize telemetry data
    from the telemetry file. The dashboard includes time-series plots for
    various metrics such as injections per day, average latency, fallback rate,
    token usage, cached vs live hits, and error frequency.

    Examples:
        codecrusher dashboard run
        codecrusher dashboard run --port 8080
        codecrusher dashboard run --no-browser
        codecrusher dashboard run --no-cache
    """
    # Check if dependencies are installed
    if not check_dependencies():
        raise typer.Exit(code=1)

    # Check if telemetry file exists
    if not os.path.exists(TELEMETRY_FILE):
        console.print("[yellow]Telemetry file does not exist. Creating an empty file.[/yellow]")
        # Ensure the telemetry directory exists
        os.makedirs(os.path.dirname(TELEMETRY_FILE), exist_ok=True)
        # Create an empty telemetry file
        with open(TELEMETRY_FILE, "w", encoding="utf-8") as f:
            pass

    # Create Flask app
    flask_app = create_flask_app()

    # Display dashboard information
    console.print(Panel(
        f"[bold]CodeCrusher Dashboard[/bold]\n\n"
        f"[cyan]URL:[/cyan] http://localhost:{port}\n"
        f"[cyan]Telemetry File:[/cyan] {TELEMETRY_FILE}\n"
        f"[cyan]Cache:[/cyan] {'Disabled' if no_cache else 'Enabled'}\n"
        f"[cyan]Auto-open Browser:[/cyan] {'No' if no_browser else 'Yes'}",
        title="Dashboard",
        border_style="blue"
    ))

    # Open browser
    if not no_browser:
        threading.Timer(1.0, lambda: webbrowser.open(f"http://localhost:{port}")).start()

    # Run Flask app
    try:
        flask_app.run(host="0.0.0.0", port=port, debug=False)
    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] Failed to start dashboard: {str(e)}")
        raise typer.Exit(code=1)

    return True
