"""
Manual Tuning Configuration
Supports local config for fallback sensitivity, prompt style, and max retries
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class TuningConfig:
    """Configuration class for manual tuning parameters"""
    
    # Model and fallback settings
    fallback_sensitivity: float = 0.7  # 0.0-1.0, higher = more sensitive to failures
    max_retries: int = 3
    model_escalation_enabled: bool = True
    preferred_models: List[str] = None  # Ordered list of preferred models
    
    # Prompt style settings
    prompt_style: str = "balanced"  # "minimal", "verbose", "balanced"
    include_examples: bool = True
    include_context: bool = True
    enhancement_level: str = "moderate"  # "low", "moderate", "high"
    
    # Code generation preferences
    prefer_comments: bool = True
    prefer_error_handling: bool = True
    prefer_type_hints: bool = True
    code_style: str = "clean"  # "minimal", "clean", "verbose"
    
    # Performance and caching
    cache_sensitivity: float = 0.8  # How aggressively to use cache
    parallel_processing: bool = True
    timeout_seconds: int = 30
    
    # Feedback and learning
    auto_feedback_analysis: bool = True
    learning_rate: float = 0.1  # How quickly to adapt to feedback
    confidence_threshold: float = 0.5  # Minimum confidence for applying enhancements
    
    def __post_init__(self):
        if self.preferred_models is None:
            self.preferred_models = ["mixtral", "llama3-70b", "gpt-4-turbo"]


class TuningConfigManager:
    """Manager for loading and saving tuning configuration"""
    
    def __init__(self, config_path: str = "tuning.json"):
        self.config_path = Path(config_path)
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        self._config = None
    
    def load_config(self) -> TuningConfig:
        """Load tuning configuration from file"""
        if self._config is not None:
            return self._config
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    data = json.load(f)
                    self._config = TuningConfig(**data)
                    logger.info(f"Loaded tuning config from {self.config_path}")
                    return self._config
            except Exception as e:
                logger.warning(f"Failed to load tuning config: {e}, using defaults")
        
        # Create default config
        self._config = TuningConfig()
        self.save_config()
        return self._config
    
    def save_config(self, config: TuningConfig = None):
        """Save tuning configuration to file"""
        if config is not None:
            self._config = config
        
        if self._config is None:
            logger.error("No config to save")
            return
        
        try:
            with open(self.config_path, 'w') as f:
                json.dump(asdict(self._config), f, indent=2)
            logger.info(f"Saved tuning config to {self.config_path}")
        except Exception as e:
            logger.error(f"Failed to save tuning config: {e}")
    
    def update_config(self, **kwargs):
        """Update specific configuration values"""
        config = self.load_config()
        
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
                logger.info(f"Updated config: {key} = {value}")
            else:
                logger.warning(f"Unknown config parameter: {key}")
        
        self.save_config(config)
    
    def reset_to_defaults(self):
        """Reset configuration to defaults"""
        self._config = TuningConfig()
        self.save_config()
        logger.info("Reset tuning config to defaults")
    
    def get_model_selection_config(self) -> Dict[str, Any]:
        """Get model selection related configuration"""
        config = self.load_config()
        return {
            "fallback_sensitivity": config.fallback_sensitivity,
            "max_retries": config.max_retries,
            "model_escalation_enabled": config.model_escalation_enabled,
            "preferred_models": config.preferred_models,
            "timeout_seconds": config.timeout_seconds
        }
    
    def get_prompt_enhancement_config(self) -> Dict[str, Any]:
        """Get prompt enhancement related configuration"""
        config = self.load_config()
        return {
            "prompt_style": config.prompt_style,
            "include_examples": config.include_examples,
            "include_context": config.include_context,
            "enhancement_level": config.enhancement_level,
            "confidence_threshold": config.confidence_threshold,
            "learning_rate": config.learning_rate
        }
    
    def get_code_generation_config(self) -> Dict[str, Any]:
        """Get code generation related configuration"""
        config = self.load_config()
        return {
            "prefer_comments": config.prefer_comments,
            "prefer_error_handling": config.prefer_error_handling,
            "prefer_type_hints": config.prefer_type_hints,
            "code_style": config.code_style
        }
    
    def apply_to_prompt(self, base_prompt: str) -> str:
        """Apply tuning configuration to enhance a prompt"""
        config = self.load_config()
        enhanced_prompt = base_prompt
        
        # Apply prompt style
        if config.prompt_style == "verbose":
            enhanced_prompt += "\n\nPlease provide a detailed implementation with comprehensive comments and documentation."
        elif config.prompt_style == "minimal":
            enhanced_prompt += "\n\nProvide a concise, minimal implementation."
        else:  # balanced
            enhanced_prompt += "\n\nProvide a well-structured implementation with appropriate comments."
        
        # Apply code preferences
        preferences = []
        if config.prefer_comments:
            preferences.append("include helpful comments")
        if config.prefer_error_handling:
            preferences.append("add proper error handling")
        if config.prefer_type_hints:
            preferences.append("use type hints where appropriate")
        
        if preferences:
            enhanced_prompt += f"\n\nEnsure the code: {', '.join(preferences)}."
        
        # Apply code style
        if config.code_style == "clean":
            enhanced_prompt += "\n\nFollow clean code principles and best practices."
        elif config.code_style == "verbose":
            enhanced_prompt += "\n\nPrioritize readability and detailed documentation."
        elif config.code_style == "minimal":
            enhanced_prompt += "\n\nKeep the code concise and efficient."
        
        return enhanced_prompt
    
    def should_escalate_model(self, failure_count: int, current_model: str) -> bool:
        """Determine if model should be escalated based on configuration"""
        config = self.load_config()
        
        if not config.model_escalation_enabled:
            return False
        
        # Calculate escalation threshold based on sensitivity
        escalation_threshold = max(1, int(config.max_retries * config.fallback_sensitivity))
        
        return failure_count >= escalation_threshold
    
    def get_next_model(self, current_model: str, failure_count: int) -> Optional[str]:
        """Get the next model to try based on configuration"""
        config = self.load_config()
        
        if not config.model_escalation_enabled:
            return None
        
        try:
            current_index = config.preferred_models.index(current_model)
            if current_index < len(config.preferred_models) - 1:
                return config.preferred_models[current_index + 1]
        except ValueError:
            # Current model not in preferred list, start from beginning
            if config.preferred_models:
                return config.preferred_models[0]
        
        return None
    
    def should_use_cache(self, cache_score: float) -> bool:
        """Determine if cache should be used based on configuration"""
        config = self.load_config()
        return cache_score >= config.cache_sensitivity
    
    def get_enhancement_strength(self) -> float:
        """Get enhancement strength based on configuration"""
        config = self.load_config()
        
        strength_map = {
            "low": 0.3,
            "moderate": 0.6,
            "high": 0.9
        }
        
        return strength_map.get(config.enhancement_level, 0.6)
    
    def export_config(self, export_path: str):
        """Export configuration to a different file"""
        config = self.load_config()
        export_path = Path(export_path)
        
        try:
            with open(export_path, 'w') as f:
                json.dump(asdict(config), f, indent=2)
            logger.info(f"Exported config to {export_path}")
        except Exception as e:
            logger.error(f"Failed to export config: {e}")
    
    def import_config(self, import_path: str):
        """Import configuration from a file"""
        import_path = Path(import_path)
        
        if not import_path.exists():
            logger.error(f"Import file not found: {import_path}")
            return
        
        try:
            with open(import_path, 'r') as f:
                data = json.load(f)
                self._config = TuningConfig(**data)
                self.save_config()
            logger.info(f"Imported config from {import_path}")
        except Exception as e:
            logger.error(f"Failed to import config: {e}")
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return any issues"""
        config = self.load_config()
        issues = []
        
        # Validate ranges
        if not 0.0 <= config.fallback_sensitivity <= 1.0:
            issues.append("fallback_sensitivity must be between 0.0 and 1.0")
        
        if config.max_retries < 1:
            issues.append("max_retries must be at least 1")
        
        if not 0.0 <= config.cache_sensitivity <= 1.0:
            issues.append("cache_sensitivity must be between 0.0 and 1.0")
        
        if not 0.0 <= config.learning_rate <= 1.0:
            issues.append("learning_rate must be between 0.0 and 1.0")
        
        if not 0.0 <= config.confidence_threshold <= 1.0:
            issues.append("confidence_threshold must be between 0.0 and 1.0")
        
        if config.timeout_seconds < 1:
            issues.append("timeout_seconds must be at least 1")
        
        # Validate choices
        valid_styles = ["minimal", "verbose", "balanced"]
        if config.prompt_style not in valid_styles:
            issues.append(f"prompt_style must be one of: {valid_styles}")
        
        valid_enhancement_levels = ["low", "moderate", "high"]
        if config.enhancement_level not in valid_enhancement_levels:
            issues.append(f"enhancement_level must be one of: {valid_enhancement_levels}")
        
        valid_code_styles = ["minimal", "clean", "verbose"]
        if config.code_style not in valid_code_styles:
            issues.append(f"code_style must be one of: {valid_code_styles}")
        
        return issues


# Global instance for easy access
_global_config_manager = None

def get_config_manager(config_path: str = "tuning.json") -> TuningConfigManager:
    """Get global configuration manager instance"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = TuningConfigManager(config_path)
    return _global_config_manager

def load_config() -> TuningConfig:
    """Convenience function to load configuration"""
    return get_config_manager().load_config()

def apply_tuning_to_prompt(prompt: str) -> str:
    """Convenience function to apply tuning to a prompt"""
    return get_config_manager().apply_to_prompt(prompt)


if __name__ == "__main__":
    # Test the tuning config
    manager = TuningConfigManager("test_tuning.json")
    
    # Test loading and saving
    config = manager.load_config()
    print(f"Loaded config: {config.prompt_style}")
    
    # Test updating
    manager.update_config(prompt_style="verbose", max_retries=5)
    
    # Test validation
    issues = manager.validate_config()
    print(f"Validation issues: {issues}")
    
    # Test prompt enhancement
    original_prompt = "Fix this function"
    enhanced_prompt = manager.apply_to_prompt(original_prompt)
    print(f"Enhanced prompt: {enhanced_prompt}")
    
    print("✅ Tuning config test completed!")
