# CodeCrusher for VS Code

VS Code integration for CodeCrusher - Enterprise-grade AI-powered code injection tool.

## Features

### 🚀 CLI Hook Integration with Live Logs WebSocket Panel (NEW!)
- **Right-click Context Menu**: "Inject with CodeCrusher" on any code file
- **Command Palette**: "Inject with CodeCrusher" command
- **Smart Mode Selection**: QuickPick dialog to choose between Preview and Apply modes
  - **Preview Mode (Safe)**: Shows side-by-side diff with highlighted changes
  - **Apply Mode (Direct)**: Immediately applies changes to the current file
- **AI Model Selection**: Choose from 6 AI models with visual icons and descriptions
  - **auto** ⚡ - Automatically choose the best model (recommended)
  - **mistral** 🤖 - Fast and efficient for general tasks
  - **gemma** ⭐ - Balanced performance from Google
  - **mixtral** 🚀 - High quality output for complex tasks
  - **llama3-8b** 🔥 - Fast and lightweight responses
  - **llama3-70b** 🛡️ - Most powerful for complex generation
- **Fallback Settings**: Enable/disable backup model usage
  - **Enable fallback** ✅ - Use backup models if primary fails (recommended)
  - **Disable fallback** ❌ - Only use selected model (faster but less reliable)
- **Live Logs WebSocket Panel**: Real-time streaming logs from Code<PERSON>rusher
  - **WebSocket Connection**: Connects to `ws://localhost:11434/logs` (configurable port)
  - **Real-time Feedback**: Model status, fallback usage, execution time, errors
  - **Syntax Highlighting**: Highlights model names, fallback triggers, file paths, timing
  - **Auto-scroll**: Automatically scrolls to latest logs with toggle option
  - **Clear Logs**: Button to clear log history
  - **Connection Status**: Visual indicators for WebSocket connection state
  - **Auto-show**: Optional auto-open logs panel when injection starts
- **Live Settings Sync**: Two-way synchronization with web dashboard
  - **Auto-Pull**: Pulls remote settings on activation and every 2 minutes
  - **Auto-Push**: Pushes settings when user makes selections in VS Code
  - **Conflict Resolution**: Favors VS Code for local changes, dashboard for remote changes
  - **Timestamp Tracking**: Uses timestamps to determine most recent changes
  - **Configurable**: Enable/disable sync, custom dashboard URL, sync interval
- **Workspace-Scoped Settings**: Secure per-project settings isolation
  - **Per-Workspace Storage**: Settings stored per workspace root folder using hashed workspace ID
  - **Multi-User Safe**: Prevents settings leakage across unrelated projects
  - **Workspace State**: Uses VS Code workspaceState for persistent storage
  - **Fallback to Global**: Automatically falls back to global settings if no workspace settings found
  - **Copy to Global**: Command to copy workspace settings to global configuration
- **Default Highlighting**: Shows configured defaults with ⭐ indicators
- **Side-by-Side Diff Viewer**: Uses VS Code's built-in diff functionality
  - **Before/After Comparison**: Clear visual comparison of original vs modified code
  - **Syntax Highlighting**: Full syntax highlighting in both panels
  - **Color-Coded Changes**: Green additions, red deletions, standard VS Code diff colors
  - **Apply from Diff**: Option to apply changes directly from the diff view
- **Enhanced CLI Execution**: Runs `codecrusher inject --model <model> --fallback <true/false> --preview/apply`
- **Success Notifications**: Shows confirmation when changes are applied with optional change log
- **Error Handling**: Graceful error handling for CLI failures, timeouts, and missing installations
- **File Type Support**: Works with Python, JavaScript, TypeScript, Java, C#, Go, Rust, HTML, CSS files

### Injection Tagging
- Auto-suggest and insert injection tags based on context and file type
- Hover over tags to see telemetry data
- Insert tags with a simple command

### Telemetry Peek
- View telemetry data for injections directly in VS Code
- Hover over injection tags to see details
- Access full telemetry reports with a single click

### Command Palette Integration
- `Inject with CodeCrusher` - **NEW!** Direct CLI hook with preview/apply modes
- `CodeCrusher: Show Live Logs` - **NEW!** Open real-time WebSocket logs panel
- `CodeCrusher: Copy Workspace Settings to Global` - **NEW!** Copy workspace settings to global configuration
- `CodeCrusher: Run Injection` - Run an injection on the current file
- `CodeCrusher: View Telemetry` - View telemetry data
- `CodeCrusher: Replay Injection` - Replay a previous injection
- `CodeCrusher: Insert Tag` - Insert an injection tag
- `CodeCrusher: Show Status` - Show CodeCrusher status

### Status Bar Widget
- See current injection stats, cache hit rate, and model uptime
- Click to view detailed status

## Requirements

- CodeCrusher CLI must be installed and configured
- Python 3.8 or higher

## Extension Settings

This extension contributes the following settings:

* `codecrusher.pythonPath`: Path to Python executable for running CodeCrusher CLI
* `codecrusher.cliPath`: Path to CodeCrusher CLI executable (use 'codecrusher' if installed globally)
* `codecrusher.defaultProvider`: Default AI provider to use
* `codecrusher.defaultModel`: Default AI model ("auto", "mistral", "gemma", "mixtral", "llama3-8b", "llama3-70b")
* `codecrusher.enableFallback`: Enable fallback to other models if primary model fails (default: true)
* `codecrusher.logServerPort`: **NEW!** Port for CodeCrusher WebSocket log server (default: 11434)
* `codecrusher.autoShowLogs`: Automatically show live logs panel when injection starts (default: false)
* `codecrusher.dashboardUrl`: **NEW!** URL of the CodeCrusher web dashboard for settings sync (default: "http://localhost:8000")
* `codecrusher.syncInterval`: **NEW!** Interval in seconds to sync settings with dashboard (default: 120, 0 to disable)
* `codecrusher.enableSettingsSync`: **NEW!** Enable automatic synchronization of settings with web dashboard (default: true)
* `codecrusher.useCache`: Whether to use cache by default
* `codecrusher.showStatusBar`: Show CodeCrusher status in the status bar
* `codecrusher.telemetryRefreshInterval`: Interval in seconds to refresh telemetry data in status bar
* `codecrusher.defaultMode`: Default injection mode ("preview" or "apply")

## Keyboard Shortcuts

- `Ctrl+Alt+I` - Run CodeCrusher Injection (requires task configuration)
- `Ctrl+Alt+S` - Run CodeCrusher Scan (requires task configuration)
- `Ctrl+Alt+O` - Run CodeCrusher Optimize (requires task configuration)

## 🛠️ Development Setup

### Prerequisites
- Node.js 16+ and npm
- VS Code 1.74.0 or higher
- TypeScript 4.9+
- CodeCrusher CLI installed and accessible

### Build Instructions

1. **Clone and Install Dependencies**
   ```bash
   cd vscode-extension
   npm install
   ```

2. **Compile TypeScript**
   ```bash
   npm run compile
   ```

3. **Watch Mode (for development)**
   ```bash
   npm run watch
   ```

4. **Package Extension**
   ```bash
   npm run package
   ```

### 🧪 Testing the Extension

1. **Launch Extension Development Host**
   ```bash
   # Open VS Code in the extension directory
   code .

   # Press F5 to launch Extension Development Host
   # OR use Command Palette: "Debug: Start Debugging"
   ```

2. **Test CLI Hook Functionality**
   - Open any code file (`.py`, `.js`, `.ts`, `.java`, `.cs`, `.go`, `.rs`, `.html`, `.css`)
   - Right-click → "Inject with CodeCrusher"
   - Enter a prompt (e.g., "add modern navigation bar", "optimize this function")
   - **Test Mode Selection**: Choose between Preview/Apply modes
   - **Test Model Selection**: Choose from 6 AI models
     - Verify default model is highlighted with ⭐ and "(default)"
     - Test different models (auto, mistral, gemma, mixtral, llama3-8b, llama3-70b)
     - Check that model icons and descriptions are clear
   - **Test Fallback Selection**: Choose Enable/Disable fallback
     - Verify default fallback setting is highlighted
     - Test both enabled and disabled options
   - **Test Preview Mode**: Select "Preview changes (safe mode)"
     - Verify side-by-side diff viewer opens
     - Check syntax highlighting in both panels
     - Verify color-coded additions (green) and deletions (red)
     - Test "Apply Changes" button in the diff notification
     - Check that original file is unchanged until applied
   - **Test Apply Mode**: Select "Apply changes (overwrite file)"
     - Verify success notification appears
     - Check that original file is modified immediately
     - Optionally view the change log
   - **Verify CLI Command**: Check that generated command includes `--model <model> --fallback <true/false>`

3. **Test Live Logs Panel**
   - Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
   - Type "CodeCrusher: Show Live Logs"
   - Verify WebSocket panel opens with connection status
   - Test WebSocket connection to `ws://localhost:11434/logs`
   - Verify auto-scroll, clear logs, and reconnect functionality
   - Test auto-show logs setting during injection

4. **Test Command Palette**
   - Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
   - Type "Inject with CodeCrusher"
   - Execute command and verify functionality

4. **Test Error Handling**
   - Try with CodeCrusher CLI not installed
   - Try with invalid file paths
   - Verify graceful error messages

### 📁 Project Structure
```
vscode-extension/
├── src/
│   ├── extension.ts          # Main extension entry point
│   ├── panel.ts             # WebView panel implementation
│   └── panels/              # Additional panel components
├── media/                   # WebView assets (CSS, JS, icons)
│   ├── codecrusher-icon.svg
│   ├── panel.css
│   └── panel.js
├── out/                     # Compiled JavaScript output
├── package.json             # Extension manifest
├── tsconfig.json           # TypeScript configuration
└── README.md               # This file
```

## Getting Started

### For End Users
1. Install the extension from VS Code Marketplace
2. Configure the extension settings to point to your CodeCrusher CLI installation
3. Right-click any code file → "Inject with CodeCrusher"
4. Use the command palette to run CodeCrusher commands
5. Hover over injection tags to see telemetry data
6. Use the status bar widget to monitor CodeCrusher status

### For Developers
1. Follow the Development Setup instructions above
2. Use F5 to launch the Extension Development Host
3. Test the CLI hook functionality with real code files
4. Modify `src/extension.ts` to customize behavior
5. Use `npm run watch` for live recompilation during development

## Known Issues

- The extension currently requires the CodeCrusher CLI to be installed and configured
- Some features may require additional configuration

## Release Notes

### 0.8.0 - Secure Workspace-Scoped Settings Sync

**🔐 Per-Workspace Settings Isolation:**
- **Workspace-Scoped Storage**: Settings stored per workspace root folder using hashed workspace ID
- **Multi-User Safe**: Prevents settings leakage across unrelated projects and team environments
- **Secure Hashing**: Uses SHA-256 hash of workspace path for privacy and consistency
- **Workspace State**: Leverages VS Code workspaceState for persistent per-project storage
- **Automatic Detection**: Detects workspace folder changes and switches settings context

**⚙️ Enhanced Settings Management:**
- **Fallback to Global**: Automatically falls back to global settings if no workspace settings found
- **Copy to Global**: New command to copy workspace settings to global configuration
- **Workspace-Scoped Sync**: API endpoints include workspace_id for backend isolation
- **Configuration Target**: Uses Workspace configuration target for proper scoping
- **Settings Key Format**: Uses `cc-settings:<workspace_id>` pattern for storage

**🛠️ Backend API Updates:**
- **Workspace Endpoints**: GET/POST `/api/settings/vscode/{workspace_id}` for per-workspace settings
- **Backward Compatibility**: Maintains global endpoints for legacy support
- **Multi-Workspace Storage**: Backend stores settings per workspace with proper isolation
- **Workspace History**: Enhanced history endpoint showing all workspace settings
- **Reset Per Workspace**: DELETE endpoint for resetting individual workspace settings

**🎯 Smart Workspace Logic:**
- **Workspace Detection**: Uses `vscode.workspace.workspaceFolders[0].uri.fsPath` as unique key
- **Hash Generation**: Creates 16-character hash for workspace identification
- **No Workspace Fallback**: Gracefully handles cases with no workspace folders
- **Multi-Folder Support**: Uses first workspace folder as primary workspace
- **Settings Inheritance**: Workspace settings override global, with proper fallback chain

**🔧 Technical Implementation:**
- **Workspace State API**: Uses `extensionContext.workspaceState` for persistence
- **Configuration Scoping**: Proper use of `ConfigurationTarget.Workspace`
- **Timestamp Tracking**: Per-workspace timestamp tracking for conflict resolution
- **Memory Management**: Proper cleanup and disposal of workspace-specific resources
- **Error Handling**: Graceful handling of workspace detection failures

### 0.7.0 - Live Settings Sync with Web Dashboard

**🔄 Two-Way Settings Synchronization:**
- **Auto-Pull Settings**: Pulls remote settings on activation and every 2 minutes (configurable)
- **Auto-Push Settings**: Pushes settings when user makes selections in VS Code
- **Conflict Resolution**: Timestamp-based conflict resolution favoring most recent changes
- **Real-time Sync**: Immediate sync when user selects different model/mode/fallback
- **Background Sync**: Periodic background synchronization with configurable interval

**⚙️ Configuration & Integration:**
- **codecrusher.dashboardUrl**: Configure dashboard URL (default: "http://localhost:8000")
- **codecrusher.syncInterval**: Set sync interval in seconds (default: 120, 0 to disable)
- **codecrusher.enableSettingsSync**: Enable/disable automatic sync (default: true)
- **REST API Integration**: GET/POST to `/api/settings/vscode` endpoints
- **Persistent Storage**: Uses VS Code globalState for timestamp tracking

**🎯 Smart Sync Logic:**
- **Local Changes**: VS Code settings take precedence when user makes selections
- **Remote Changes**: Dashboard settings take precedence when changed remotely
- **Timestamp Tracking**: Uses lastSyncTimestamp to determine most recent changes
- **Graceful Fallback**: Silently handles dashboard unavailability
- **Debounced Updates**: Prevents excessive API calls with smart debouncing

**🛠️ Backend API Example:**
- **FastAPI Example**: Complete backend implementation in `backend-example/`
- **REST Endpoints**: GET/POST `/api/settings/vscode` with proper validation
- **Data Model**: VSCodeSettings interface with model, fallback, mode, timestamp
- **CORS Support**: Configured for VS Code extension integration
- **File Persistence**: Optional file-based settings storage

**🔧 Technical Implementation:**
- **SettingsSync Class**: Comprehensive sync management with proper lifecycle
- **Configuration Watching**: Monitors VS Code configuration changes
- **HTTP Client**: Uses axios for reliable API communication
- **Error Handling**: Graceful handling of network failures and timeouts
- **Memory Management**: Proper disposal and cleanup of sync intervals

### 0.6.0 - Live Logs WebSocket Panel

**📡 Real-time WebSocket Logging:**
- **Live Logs Panel**: New WebView panel titled "CodeCrusher Live Logs"
- **WebSocket Connection**: Connects to `ws://localhost:11434/logs` (configurable port)
- **Real-time Streaming**: Live model status, fallback usage, execution time, errors
- **Connection Management**: Auto-connect, reconnect button, connection status indicators
- **Professional UI**: VS Code-themed interface with status bar and controls

**🎨 Enhanced Log Visualization:**
- **Syntax Highlighting**: Highlights model names, fallback triggers, file paths, timing
- **Log Categories**: Color-coded log levels (info, success, warning, error, model)
- **Auto-scroll**: Automatically scrolls to latest logs with toggle option
- **Clear Logs**: Button to clear log history
- **Log Counter**: Shows total number of logs received
- **Empty State**: Friendly waiting message when no logs are present

**⚙️ Configuration & Integration:**
- **codecrusher.logServerPort**: Configure WebSocket server port (default: 11434)
- **codecrusher.autoShowLogs**: Auto-open logs panel when injection starts (default: false)
- **Command Integration**: "CodeCrusher: Show Live Logs" in Command Palette
- **Injection Integration**: Automatic logging of injection start, model selection, and settings

**🔧 Technical Implementation:**
- **WebView Panel**: Persistent panel with script support and context retention
- **Fallback HTML**: Built-in fallback if external HTML file fails to load
- **Message Passing**: Bi-directional communication between extension and WebView
- **Error Handling**: Graceful WebSocket connection failures and reconnection
- **Memory Management**: Proper panel disposal and cleanup

### 0.5.0 - Model + Fallback Selector UI

**🤖 AI Model Selection:**
- **6 AI Models Available**: auto, mistral, gemma, mixtral, llama3-8b, llama3-70b
- **Visual Model Picker**: QuickPick with icons, descriptions, and performance details
- **Smart Recommendations**: Clear guidance on which model to use for different tasks
- **Default Highlighting**: Shows configured default model with ⭐ indicator and "(default)" label
- **Enhanced CLI Commands**: Automatically appends `--model <selected_model>` to CLI calls

**⚙️ Fallback Settings:**
- **Fallback Toggle**: Enable/disable backup model usage with clear explanations
- **Smart Defaults**: Fallback enabled by default for reliability
- **Visual Indicators**: Default fallback setting highlighted with ⭐ indicator
- **CLI Integration**: Automatically appends `--fallback <true/false>` to CLI calls

**🎯 Enhanced User Experience:**
- **Three-Step Selection**: Mode → Model → Fallback for complete control
- **Graceful Cancellation**: Users can cancel at any step without errors
- **Configuration Integration**: Respects user's default model and fallback preferences
- **Clear Descriptions**: Each model includes performance characteristics and use cases
- **Example CLI Command**: `codecrusher inject --model mixtral --fallback true --preview <file>`

**⚙️ Configuration Settings:**
- **codecrusher.defaultModel**: Set preferred AI model (default: "auto")
- **codecrusher.enableFallback**: Set default fallback behavior (default: true)
- **Enum Descriptions**: Clear explanations for each model option in VS Code settings

### 0.4.0 - Diff Viewer with Highlights

**🔍 New Diff Viewer Features:**
- **Side-by-Side Diff**: Uses VS Code's built-in `vscode.diff` command for professional diff viewing
- **Before/After Comparison**: Clear visual comparison of original vs CodeCrusher-modified code
- **Syntax Highlighting**: Full syntax highlighting in both diff panels
- **Color-Coded Changes**: Standard VS Code diff colors (green additions, red deletions)
- **Apply from Diff**: "Apply Changes" button directly in diff notification
- **Smart Content Extraction**: Intelligent parsing of CLI output to extract clean modified content
- **Fallback Handling**: Graceful fallback to raw output view if content extraction fails

**🔧 Technical Improvements:**
- **Virtual Document Providers**: Separate content providers for original and modified content
- **Memory Management**: Proper cleanup of virtual documents after use
- **Content Pattern Matching**: Multiple regex patterns to extract modified content from CLI output
- **Error Recovery**: Fallback to traditional preview if diff viewer fails
- **URI Management**: Unique timestamped URIs to prevent conflicts

**🎯 Enhanced User Experience:**
- **Preview Mode**: Now shows side-by-side diff instead of simple text preview
- **Apply Options**: Multiple ways to apply changes (from mode selection or diff notification)
- **Raw Output Access**: "View Raw Output" option for debugging CLI responses
- **Better Notifications**: Context-aware notifications with relevant action buttons

### 0.3.0 - Preview/Apply Toggle Integration

**🎯 New Features:**
- **Smart Mode Selection**: QuickPick dialog when running "Inject with CodeCrusher"
  - **Preview Mode**: Safe preview in virtual document without modifying original file
  - **Apply Mode**: Direct application of changes to the current file
- **Enhanced User Experience**: Clear visual indicators and descriptions for each mode
- **Success Notifications**: Confirmation messages when changes are applied
- **Change Log Option**: Optional preview of applied changes for reference
- **Document Refresh**: Automatic reload of modified files to show changes
- **Configuration Setting**: `codecrusher.defaultMode` for setting preferred mode

**🔧 Technical Improvements:**
- Updated CLI command generation for `--preview` and `--apply` flags
- Enhanced error handling with mode-specific error messages
- Improved virtual document provider with custom titles and status indicators
- Added document reload functionality for applied changes
- Better progress notifications with mode-appropriate messaging

### 0.2.0 - CLI Hook Integration

**🚀 New Features:**
- **CLI Hook Command**: Added `codecrusher.inject` command with direct CLI integration
- **Right-click Context Menu**: "Inject with CodeCrusher" on supported file types
- **Live Preview**: Virtual document tab showing CLI output titled "CodeCrusher Preview"
- **Enhanced Error Handling**: Graceful handling of CLI failures, timeouts, and missing installations
- **File Type Support**: Extended support for Python, JavaScript, TypeScript, Java, C#, Go, Rust, HTML, CSS
- **Progress Notifications**: Real-time progress updates during CLI execution
- **Configuration**: Added `codecrusher.cliPath` setting for CLI executable path

**🔧 Technical Improvements:**
- Uses `child_process.exec` for CLI execution
- 30-second timeout with 1MB buffer for CLI commands
- Virtual document content provider for preview functionality
- Improved command registration and context menu integration

**📝 Documentation:**
- Comprehensive build and test instructions
- Development setup guide
- Project structure documentation
- Testing procedures for CLI hook functionality

### 0.1.0

Initial release of CodeCrusher for VS Code
