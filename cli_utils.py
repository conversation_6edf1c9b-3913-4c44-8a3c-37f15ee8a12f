"""
CLI utilities for CodeCrusher
"""

from rich import print as rprint
from rich.console import Console
from halo import Halo
import os
import sys
from datetime import datetime

console = Console()

def validate_inputs(source_path: str, prompt_text: str, tags: list):
    """
    Validate input parameters
    
    Args:
        source_path (str): Path to the source file
        prompt_text (str): Prompt text to send to AI provider
        tags (list): List of tags to use for injection
        
    Returns:
        bool: True if all validations pass, False otherwise
    """
    errors = []
    if not os.path.exists(source_path):
        errors.append(f"[red]❌ Source file not found:[/red] {source_path}")
    if not prompt_text:
        errors.append("[red]❌ Prompt text is empty[/red]")
    if not tags:
        errors.append("[red]❌ No injection tags provided[/red]")
    
    if errors:
        for err in errors:
            rprint(err)
        rprint("[bold red]Aborting due to validation errors.[/bold red]")
        return False
    
    return True

def print_success(message: str):
    """
    Print a success message
    
    Args:
        message (str): The message to print
    """
    rprint(f"[green]✅ {message}[/green]")

def print_warning(message: str):
    """
    Print a warning message
    
    Args:
        message (str): The message to print
    """
    rprint(f"[yellow]⚠️ {message}[/yellow]")

def print_error(message: str):
    """
    Print an error message
    
    Args:
        message (str): The message to print
    """
    rprint(f"[red]❌ {message}[/red]")

def print_info(message: str):
    """
    Print an info message
    
    Args:
        message (str): The message to print
    """
    rprint(f"[cyan]ℹ️ {message}[/cyan]")

def spinner_wrap(task_description: str):
    """
    Create a spinner with the given task description
    
    Args:
        task_description (str): The task description to display
        
    Returns:
        Halo: A Halo spinner instance
    """
    return Halo(text=task_description, spinner='dots', color='cyan')

def log_with_timestamp(message: str, level="info"):
    """
    Log a message with a timestamp
    
    Args:
        message (str): The message to log
        level (str, optional): The log level. Defaults to "info".
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    if level == "success":
        rprint(f"[dim][{timestamp}][/dim] [green]✅ {message}[/green]")
    elif level == "warning":
        rprint(f"[dim][{timestamp}][/dim] [yellow]⚠️ {message}[/yellow]")
    elif level == "error":
        rprint(f"[dim][{timestamp}][/dim] [red]❌ {message}[/red]")
    else:  # info
        rprint(f"[dim][{timestamp}][/dim] [cyan]ℹ️ {message}[/cyan]")

def confirm_action(prompt: str) -> bool:
    """
    Ask for user confirmation
    
    Args:
        prompt (str): The prompt to display
        
    Returns:
        bool: True if the user confirms, False otherwise
    """
    response = console.input(f"[yellow]{prompt} (y/n):[/yellow] ").lower()
    return response == 'y'
