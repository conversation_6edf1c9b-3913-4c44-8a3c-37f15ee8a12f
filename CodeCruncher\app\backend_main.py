from fastapi import <PERSON><PERSON><PERSON>, WebSocket, Request, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import asyncio
import subprocess
import json
import os
import sys
import logging
from pathlib import Path
from datetime import datetime

# Import route modules
try:
    from routes.dashboard_routes import router as dashboard_router
    from routes.api_feedback import router as feedback_router
except ImportError as e:
    logging.warning(f"Failed to import route modules: {e}")
    dashboard_router = None
    feedback_router = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("codecrusher")

app = FastAPI(
    title="CodeCrusher Backend API",
    description="WebSocket + Injection API with Intelligence Hub integration",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Lock down in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Register route modules
if dashboard_router:
    app.include_router(dashboard_router, tags=["Intelligence Hub"])
    logger.info("✅ Dashboard routes registered")
else:
    logger.warning("⚠️ Dashboard routes not available")

if feedback_router:
    app.include_router(feedback_router, tags=["Feedback"])
    logger.info("✅ Feedback routes registered")
else:
    logger.warning("⚠️ Feedback routes not available")

# WebSocket client management
clients = set()

@app.websocket("/ws/logs")
async def websocket_logs(websocket: WebSocket):
    """Real-time logging stream from CodeCrusher engine."""
    client_host = websocket.client.host if websocket.client else "unknown"
    client_port = websocket.client.port if websocket.client else "unknown"
    client_info = f"{client_host}:{client_port}"

    await websocket.accept()
    clients.add(websocket)

    try:
        # Send welcome message with connection confirmation
        welcome_msg = f"🔗 Connected to CodeCrusher logs from {client_info}"
        await websocket.send_text(welcome_msg)
        logger.info(f"WebSocket client connected from {client_info}. Total clients: {len(clients)}")

        # Keep connection alive
        while True:
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        logger.info(f"WebSocket client {client_info} disconnected")
    except Exception as e:
        logger.error(f"WebSocket error from {client_info}: {e}")
    finally:
        clients.discard(websocket)
        logger.info(f"Client {client_info} removed. Total clients: {len(clients)}")

async def broadcast_to_clients(message: str):
    """Broadcast message to all connected WebSocket clients."""
    if not clients:
        return

    # Add timestamp to message
    timestamp = datetime.now().strftime("%H:%M:%S")
    formatted_message = f"[{timestamp}] {message}"

    disconnected = set()
    for client in clients:
        try:
            await client.send_text(formatted_message)
        except Exception as e:
            logger.warning(f"Failed to send to client: {e}")
            disconnected.add(client)

    # Remove disconnected clients
    clients.difference_update(disconnected)

class InjectionRequest(BaseModel):
    source: str
    prompt_text: str
    model: str = "auto"
    tag: str = "web-ui"
    auto_model: bool = True
    apply: bool = False
    use_fallback: bool = True
    recursive: bool = True
    ext: str = "py"

def ensure_virtualenv():
    """Ensure we're running in the correct codecrushervenv."""
    venv_name = "codecrushervenv"

    # Check if we're already in the virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        current_venv = os.path.basename(sys.prefix)
        if current_venv == venv_name:
            logger.info(f"✅ Running in correct virtual environment: {current_venv}")
            return True
        else:
            logger.warning(f"⚠️ Running in different virtual environment: {current_venv}")

    # Try to find the virtual environment
    possible_paths = [
        f"./{venv_name}",
        f"../{venv_name}",
        f".venv",
        f"venv"
    ]

    for venv_path in possible_paths:
        if os.path.exists(venv_path):
            logger.info(f"📁 Found virtual environment at: {venv_path}")
            return venv_path

    logger.warning("⚠️ Virtual environment not found, using system Python")
    return None

def build_codecrusher_command(req: InjectionRequest, venv_path=None):
    """Build CodeCrusher command with proper virtualenv setup."""
    cmd = ["codecrusher"]

    # Add basic parameters
    cmd.extend(["--source", req.source])
    cmd.extend(["--prompt-text", req.prompt_text])
    cmd.extend(["--tag", req.tag])
    cmd.extend(["--ext", req.ext])

    # Add recursive flag
    if req.recursive:
        cmd.append("--recursive")

    # Model selection
    if req.auto_model:
        cmd.append("--auto-model-routing")
    else:
        cmd.extend(["--model", req.model])

    # Apply changes or preview
    if req.apply:
        cmd.append("--apply")
    else:
        cmd.append("--preview")  # Default to preview mode

    # Fallback handling
    if not req.use_fallback:
        cmd.append("--no-fallback")

    return cmd

@app.post("/api/inject")
async def trigger_injection(req: InjectionRequest):
    """Trigger injection with params (prompt, model, tag, etc.)."""

    try:
        # Ensure virtual environment
        venv_path = ensure_virtualenv()

        # Broadcast start message
        await broadcast_to_clients("🚀 Starting CodeCrusher injection...")
        await broadcast_to_clients(f"📋 Source: {req.source}")
        await broadcast_to_clients(f"🧠 Model: {req.model} (auto: {req.auto_model})")
        await broadcast_to_clients(f"🏷️ Tag: {req.tag}")
        await broadcast_to_clients(f"⚙️ Apply: {req.apply}, Fallback: {req.use_fallback}")

        # Check if CodeCrusher CLI is available
        try:
            # Try to find codecrusher command
            proc_test = await asyncio.create_subprocess_exec(
                "codecrusher", "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await proc_test.wait()
            codecrusher_available = proc_test.returncode == 0
        except FileNotFoundError:
            codecrusher_available = False

        if not codecrusher_available:
            # Mock implementation for testing
            await broadcast_to_clients("⚠️ CodeCrusher CLI not found - using mock implementation for testing")
            await broadcast_to_clients("🔧 Mock processing your request...")

            # Simulate processing time
            await asyncio.sleep(2)

            # Mock successful response
            await broadcast_to_clients("✅ Mock injection completed successfully!")
            await broadcast_to_clients("📊 Progress: 100%")
            await broadcast_to_clients(f"📝 Processed source: {req.source[:100]}...")
            await broadcast_to_clients(f"💡 Applied prompt: {req.prompt_text}")

            return JSONResponse({
                "status": "completed",
                "success": True,
                "return_code": 0,
                "message": "Mock injection completed (CodeCrusher CLI not installed)"
            })

        # Build command for real CodeCrusher
        cmd = build_codecrusher_command(req, venv_path)
        await broadcast_to_clients(f"📋 Command: {' '.join(cmd)}")

        # Set up environment
        env = os.environ.copy()
        if venv_path:
            # Add virtual environment to PATH
            if os.name == 'nt':  # Windows
                venv_scripts = os.path.join(venv_path, "Scripts")
            else:  # Unix/Linux/macOS
                venv_scripts = os.path.join(venv_path, "bin")

            env["PATH"] = f"{venv_scripts}{os.pathsep}{env.get('PATH', '')}"
            await broadcast_to_clients(f"🐍 Using virtual environment: {venv_path}")

        # Start process
        await broadcast_to_clients("⚡ Executing CodeCrusher...")

        proc = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT,
            cwd="./",
            env=env
        )

        # Stream output in real-time
        line_count = 0
        while True:
            line = await proc.stdout.readline()
            if not line:
                break

            text = line.decode().strip()
            if text:
                line_count += 1
                await broadcast_to_clients(text)

                # Send progress updates
                if line_count % 5 == 0:
                    progress = min(line_count * 2, 95)  # Cap at 95% until completion
                    await broadcast_to_clients(f"📊 Progress: {progress}%")

        # Wait for process completion
        return_code = await proc.wait()

        if return_code == 0:
            await broadcast_to_clients("✅ CodeCrusher injection completed successfully!")
            await broadcast_to_clients("📊 Progress: 100%")
            return JSONResponse({
                "status": "completed",
                "success": True,
                "return_code": return_code
            })
        else:
            await broadcast_to_clients(f"❌ CodeCrusher injection failed with return code: {return_code}")
            return JSONResponse({
                "status": "completed",
                "success": False,
                "return_code": return_code,
                "error": f"Process failed with return code {return_code}"
            })

    except FileNotFoundError:
        error_msg = "❌ CodeCrusher CLI not found. Please ensure it's installed in the virtual environment."
        await broadcast_to_clients(error_msg)
        return JSONResponse({
            "status": "error",
            "success": False,
            "error": "CodeCrusher CLI not found"
        })

    except Exception as e:
        error_msg = f"💥 Error during injection: {str(e)}"
        await broadcast_to_clients(error_msg)
        logger.error(f"Injection error: {e}")
        return JSONResponse({
            "status": "error",
            "success": False,
            "error": str(e)
        })

@app.get("/")
async def root():
    """API root with endpoint information."""
    venv_status = ensure_virtualenv()
    return {
        "message": "CodeCrusher Backend API",
        "version": "1.0.0",
        "virtualenv_status": "active" if venv_status else "not_found",
        "endpoints": {
            "websocket_logs": "/ws/logs",
            "websocket_intelligence": "/ws/intelligence",
            "inject": "/api/inject",
            "health": "/health",
            "status": "/api/status",
            "intel_feedback": "/api/intel/feedback",
            "intel_summary": "/api/intel/summary",
            "intel_models": "/api/intel/models",
            "intel_tones": "/api/intel/tones",
            "intel_status": "/api/intel/status",
            "intel_recent": "/api/intel/recent"
        },
        "connected_clients": len(clients)
    }

@app.get("/health")
async def health():
    """Health check endpoint."""
    venv_status = ensure_virtualenv()
    return {
        "status": "healthy",
        "virtualenv": "active" if venv_status else "not_found",
        "connected_clients": len(clients),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/status")
async def get_status():
    """Get current system status."""
    venv_status = ensure_virtualenv()
    return {
        "status": "running",
        "virtualenv": venv_status if venv_status else "not_found",
        "connected_clients": len(clients),
        "websocket_endpoint": "/ws/logs",
        "injection_endpoint": "/api/inject"
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting CodeCrusher Backend API...")
    print("📡 WebSocket endpoint: ws://localhost:8001/ws/logs")
    print("🧠 Intelligence WebSocket: ws://localhost:8001/ws/intelligence")
    print("🔗 Injection API: http://localhost:8001/api/inject")
    print("📊 Intelligence API: http://localhost:8001/api/intel/summary")
    print("📋 Documentation: http://localhost:8001/docs")
    print("=" * 50)
    uvicorn.run(app, host="127.0.0.1", port=8001)
