#!/usr/bin/env python3
"""
CodeCrusher Setup Configuration
Installs CodeCrusher CLI with entry point for global usage
"""

from setuptools import setup, find_packages

# Read README for long description
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return "CodeCrusher - AI-powered code injection and optimization tool"

# Read requirements
def read_requirements():
    try:
        with open("requirements.txt", "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    except FileNotFoundError:
        return [
            "typer[all]>=0.9.0",
            "rich>=13.0.0",
            "requests>=2.28.0",
            "fastapi>=0.100.0",
            "uvicorn>=0.20.0",
            "pydantic>=2.0.0",
            "python-multipart>=0.0.6",
            "python-jose[cryptography]>=3.3.0",
            "passlib[bcrypt]>=1.7.4",
            "sqlalchemy>=2.0.0",
            "aiosqlite>=0.19.0"
        ]

setup(
    name="codecrusher",
    version="1.0.0",
    description="Enterprise-grade AI-powered code injector with self-improving intelligence",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="CodeCrusher Team",
    author_email="<EMAIL>",
    url="https://github.com/Codegx-Technology/CodeCruncher",
    license="MIT",

    # Package discovery
    packages=find_packages(exclude=["tests*", "frontend*", "app*", "e2e-*"]),

    # Include additional files
    include_package_data=True,
    package_data={
        "": ["*.md", "*.txt", "*.yml", "*.yaml", "*.json"],
        "codecrusher": ["templates/*", "config/*"],
    },

    # Dependencies
    install_requires=[
        "click>=8.0.0",
        "rich>=13.0.0",
        "requests>=2.28.0",
        "groq>=0.4.0",
        "pydantic>=2.0.0",
        "sqlalchemy>=2.0.0",
        "aiosqlite>=0.19.0",
        "python-dotenv>=0.20.0",
        "pyyaml>=6.0.0",
        "jinja2>=3.1.0",
    ],

    # Python version requirement
    python_requires=">=3.8",

    # CLI Entry Points - Updated for new structure
    entry_points={
        "console_scripts": [
            "codecrusher=codecrusher_cli:main",  # Main CLI entry point
        ],
    },

    # Optional dependencies
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
            "mypy>=1.0.0",
            "pre-commit>=2.20.0",
        ],
        "web": [
            "fastapi>=0.100.0",
            "uvicorn>=0.20.0",
            "flask>=2.2.0",
            "flask-cors>=4.0.0",
            "flask-socketio>=5.3.0",
        ],
        "ai": [
            "openai>=1.0.0",
            "anthropic>=0.3.0",
            "mistralai>=0.1.0",
        ],
        "full": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "fastapi>=0.100.0",
            "openai>=1.0.0",
            "anthropic>=0.3.0",
        ]
    },

    # Classifiers for PyPI
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Developers",
        "Intended Audience :: Information Technology",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Code Generators",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Software Development :: Quality Assurance",
        "Topic :: Utilities",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],

    # Keywords for discovery
    keywords=[
        "ai", "code-injection", "automation", "cli", "development-tools",
        "code-generation", "team-collaboration", "productivity", "machine-learning",
        "self-improving", "intelligence", "feedback-loop", "quality-assurance"
    ],

    # Project URLs
    project_urls={
        "Homepage": "https://github.com/Codegx-Technology/CodeCruncher",
        "Bug Reports": "https://github.com/Codegx-Technology/CodeCruncher/issues",
        "Source": "https://github.com/Codegx-Technology/CodeCruncher",
        "Documentation": "https://github.com/Codegx-Technology/CodeCruncher/blob/main/README.md",
        "Comparison Results": "https://github.com/Codegx-Technology/CodeCruncher/tree/main/e2e-comparison",
        "Changelog": "https://github.com/Codegx-Technology/CodeCruncher/releases",
    },
)
