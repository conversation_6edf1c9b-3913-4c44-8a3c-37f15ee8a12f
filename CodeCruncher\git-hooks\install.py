#!/usr/bin/env python3
"""
Install CodeCrusher Git hooks.

This script installs the CodeCrusher Git hooks into the current Git repository.
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def get_git_hooks_dir():
    """Get the Git hooks directory for the current repository."""
    result = subprocess.run(
        ["git", "rev-parse", "--git-dir"],
        capture_output=True,
        text=True
    )
    
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        print("Are you in a Git repository?")
        return None
    
    git_dir = result.stdout.strip()
    hooks_dir = os.path.join(git_dir, "hooks")
    
    return hooks_dir

def install_hook(hooks_dir, hook_name):
    """Install a Git hook."""
    # Source hook path
    source_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), hook_name)
    
    # Destination hook path
    dest_path = os.path.join(hooks_dir, hook_name)
    
    # Check if hook already exists
    if os.path.exists(dest_path):
        backup_path = f"{dest_path}.bak"
        print(f"Hook {hook_name} already exists. Creating backup at {backup_path}")
        shutil.copy2(dest_path, backup_path)
    
    # Copy hook
    shutil.copy2(source_path, dest_path)
    
    # Make hook executable
    os.chmod(dest_path, 0o755)
    
    print(f"Installed {hook_name} hook.")

def main():
    print("Installing CodeCrusher Git hooks...")
    
    # Get Git hooks directory
    hooks_dir = get_git_hooks_dir()
    
    if not hooks_dir:
        return 1
    
    # Create hooks directory if it doesn't exist
    os.makedirs(hooks_dir, exist_ok=True)
    
    # Install hooks
    install_hook(hooks_dir, "pre-commit")
    install_hook(hooks_dir, "post-commit")
    
    print("CodeCrusher Git hooks installed successfully.")
    print("To uninstall, run: python git-hooks/uninstall.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
