"""
Common pytest fixtures for CodeCrusher tests
"""

import pytest
import tempfile
import shutil
import os
from pathlib import Path

@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

@pytest.fixture
def example_file(temp_dir):
    """Create an example file with injection tags"""
    file_path = os.path.join(temp_dir, "example.py")
    with open(file_path, "w") as f:
        f.write("""
# Example file for testing

# AI_INJECT: factorial_function
# This tag will be replaced with a factorial function

# AI_INJECT: fibonacci_function
# This tag will be replaced with a fibonacci function
""")
    yield file_path
    
@pytest.fixture
def example_prompt():
    """Return an example prompt for testing"""
    return "Write a function to calculate factorial"

@pytest.fixture
def example_tag():
    """Return an example tag for testing"""
    return "factorial_function"
