"""
Scan command for CodeCrusher.

This module provides the scan command for CodeCrusher, which scans telemetry data
for anomalies such as high error rates, model instability, token spikes, and
abnormal fallback patterns.
"""

import typer
import os
import json
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich import box

# Import anomaly engine
from codecrusher.anomaly_engine import detect_anomalies, get_suggested_action
from codecrusher.telemetry_logger import get_telemetry_entries, TELEMETRY_FILE

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

def parse_time_period(period: str) -> Optional[timedelta]:
    """
    Parse a time period string into a timedelta.

    Args:
        period: Time period string (e.g., "24h", "7d", "30m")

    Returns:
        timedelta object or None if invalid
    """
    if not period:
        return None

    try:
        # Extract number and unit
        if period.endswith("h"):
            hours = int(period[:-1])
            return timedelta(hours=hours)
        elif period.endswith("d"):
            days = int(period[:-1])
            return timedelta(days=days)
        elif period.endswith("m"):
            minutes = int(period[:-1])
            return timedelta(minutes=minutes)
        else:
            # Default to hours if no unit specified
            hours = int(period)
            return timedelta(hours=hours)
    except ValueError:
        console.print(f"[red]Invalid time period: {period}[/red]")
        return None

@app.command("run")
def run_scan(
    since: Optional[str] = typer.Option("24h", "--since", "-s", help="Show anomalies since this time period (e.g., 24h, 7d, 30m)"),
    model: Optional[str] = typer.Option(None, "--model", "-m", help="Filter by model"),
    tag: Optional[str] = typer.Option(None, "--tag", "-t", help="Filter by tag"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Show detailed information including suggested actions"),
    limit: int = typer.Option(10, "--limit", "-l", help="Limit the number of anomalies to display"),
    json: bool = typer.Option(False, "--json", help="Output clean, structured JSON for programmatic consumption"),
):
    """
    Scan telemetry data for anomalies.

    This command analyzes telemetry data and identifies potential issues such as
    high error rates, model instability, token spikes, and abnormal fallback patterns.

    Examples:
        codecrusher scan
        codecrusher scan --since 6h
        codecrusher scan --model mistral
        codecrusher scan --tag bugfix
        codecrusher scan --verbose
        codecrusher scan --json

    The --json flag outputs clean, structured JSON suitable for programmatic consumption.
    """
    # Parse time period
    since_delta = parse_time_period(since)
    if not since_delta:
        return False

    # Format time period for display
    time_period = f"Last {since}" if since else "Last 24h"

    # Get all telemetry entries (high limit to get everything)
    entries = get_telemetry_entries(limit=10000)

    # Check if telemetry file exists
    if not os.path.exists(TELEMETRY_FILE):
        console.print("[yellow]No telemetry data found. Run some commands first.[/yellow]")
        return False

    # Check if we have data
    if not entries:
        console.print("[yellow]No telemetry data found for the specified filters[/yellow]")
        return False

    # Parse tag filter
    tag_filter = [tag] if tag else None

    # Detect anomalies
    start_time = datetime.now()
    anomalies = detect_anomalies(
        entries=entries,
        since=since_delta,
        model_filter=model,
        tag_filter=tag_filter
    )
    end_time = datetime.now()
    scan_duration = (end_time - start_time).total_seconds()

    # Prepare JSON data
    anomalies_data = []
    for anomaly in anomalies:
        anomaly_dict = {
            "type": anomaly.anomaly_type,
            "description": anomaly.description,
            "timestamp": anomaly.timestamp.isoformat(),
            "model": anomaly.model or "",
            "value": anomaly.value,
            "threshold": anomaly.threshold,
            "tags": anomaly.tags
        }
        if verbose:
            anomaly_dict["suggested_action"] = get_suggested_action(anomaly)
        anomalies_data.append(anomaly_dict)

    json_data = {
        "time_period": time_period,
        "model_filter": model or "All models",
        "tag_filter": tag or "All tags",
        "telemetry_file": str(TELEMETRY_FILE),
        "entries_analyzed": len(entries),
        "scan_duration_seconds": scan_duration,
        "anomalies_count": len(anomalies),
        "anomalies": anomalies_data[:limit]
    }

    # Output JSON if requested
    if json:
        print(json.dumps(json_data))
        return True

    # Display scan configuration
    console.print(Panel(
        f"[bold]CodeCrusher Anomaly Scan[/bold]\n\n"
        f"[cyan]Time Period:[/cyan] {time_period}\n"
        f"[cyan]Model Filter:[/cyan] {model or 'All models'}\n"
        f"[cyan]Tag Filter:[/cyan] {tag or 'All tags'}\n"
        f"[cyan]Telemetry File:[/cyan] {TELEMETRY_FILE}\n"
        f"[cyan]Entries Analyzed:[/cyan] {len(entries)}\n"
        f"[cyan]Scan Duration:[/cyan] {scan_duration:.3f} seconds",
        title="Scan Configuration",
        border_style="blue"
    ))

    # Display results
    if not anomalies:
        console.print(Panel(
            "[green]No anomalies detected in the specified time period.[/green]",
            title="Scan Results",
            border_style="green"
        ))
        return True

    # Limit the number of anomalies to display
    anomalies = anomalies[:limit]

    # Create a table for anomalies
    table = Table(
        title=f"Detected Anomalies ({len(anomalies)})",
        box=box.ROUNDED,
        show_header=True,
        header_style="bold cyan"
    )

    # Add columns
    table.add_column("Type", style="bold")
    table.add_column("Description")
    table.add_column("Timestamp")
    table.add_column("Model", style="cyan")
    if verbose:
        table.add_column("Suggested Action", style="yellow")

    # Add rows
    for anomaly in anomalies:
        # Format timestamp
        timestamp = anomaly.timestamp.strftime("%Y-%m-%d %H:%M")

        # Add row
        if verbose:
            table.add_row(
                f"{anomaly.icon} {anomaly.anomaly_type.replace('_', ' ').title()}",
                anomaly.description,
                timestamp,
                anomaly.model or "-",
                get_suggested_action(anomaly)
            )
        else:
            table.add_row(
                f"{anomaly.icon} {anomaly.anomaly_type.replace('_', ' ').title()}",
                anomaly.description,
                timestamp,
                anomaly.model or "-"
            )

    # Display the table
    console.print(table)

    return True
