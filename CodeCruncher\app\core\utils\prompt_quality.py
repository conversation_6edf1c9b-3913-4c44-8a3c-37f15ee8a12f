"""
Prompt Quality Assessment Module for CodeCrusher

This module provides intelligent prompt quality scoring and filtering
to ensure prompts meet minimum clarity standards for effective AI generation.
"""

import re
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass


@dataclass
class PromptQualityResult:
    """Result of prompt quality assessment."""
    score: int
    is_acceptable: bool
    issues: List[str]
    suggestions: List[str]
    category: str
    confidence: float


class PromptQualityScorer:
    """Advanced prompt quality scorer with multiple assessment criteria."""
    
    # Quality scoring criteria
    VAGUE_TERMS = {
        "fix this", "do it", "make better", "improve it", "update", "change",
        "modify", "enhance", "upgrade", "better", "good", "nice", "cool"
    }
    
    POSITIVE_KEYWORDS = {
        "code", "function", "test", "add", "create", "implement", "build",
        "develop", "design", "refactor", "optimize", "debug", "document",
        "class", "method", "variable", "algorithm", "database", "api",
        "interface", "component", "module", "library", "framework"
    }
    
    SPECIFIC_ACTIONS = {
        "add", "remove", "create", "delete", "implement", "build", "design",
        "refactor", "optimize", "debug", "test", "document", "validate",
        "sanitize", "encrypt", "authenticate", "authorize", "cache", "log"
    }
    
    TECHNICAL_TERMS = {
        "html", "css", "javascript", "python", "java", "react", "vue", "angular",
        "node", "express", "django", "flask", "spring", "bootstrap", "tailwind",
        "sql", "mongodb", "redis", "docker", "kubernetes", "aws", "azure"
    }
    
    def __init__(self):
        """Initialize the prompt quality scorer."""
        pass
    
    def score_prompt_quality(self, prompt: str) -> PromptQualityResult:
        """
        Comprehensive prompt quality scoring.
        
        Args:
            prompt: The user prompt to assess
            
        Returns:
            PromptQualityResult with detailed assessment
        """
        prompt_clean = prompt.strip()
        prompt_lower = prompt_clean.lower()
        words = prompt_lower.split()
        
        # Base score
        score = 50
        issues = []
        suggestions = []
        
        # Length assessment
        length_score, length_issues, length_suggestions = self._assess_length(prompt_clean, words)
        score += length_score
        issues.extend(length_issues)
        suggestions.extend(length_suggestions)
        
        # Specificity assessment
        specificity_score, spec_issues, spec_suggestions = self._assess_specificity(prompt_lower, words)
        score += specificity_score
        issues.extend(spec_issues)
        suggestions.extend(spec_suggestions)
        
        # Technical content assessment
        technical_score, tech_issues, tech_suggestions = self._assess_technical_content(prompt_lower, words)
        score += technical_score
        issues.extend(tech_issues)
        suggestions.extend(tech_suggestions)
        
        # Action clarity assessment
        action_score, action_issues, action_suggestions = self._assess_action_clarity(prompt_lower, words)
        score += action_score
        issues.extend(action_issues)
        suggestions.extend(action_suggestions)
        
        # Normalize score
        score = max(0, min(score, 100))
        
        # Determine category and confidence
        category = self._categorize_prompt(score, prompt_lower)
        confidence = self._calculate_confidence(score, len(issues))
        
        # Determine if acceptable
        is_acceptable = score >= 50 and len([i for i in issues if "critical" in i.lower()]) == 0
        
        return PromptQualityResult(
            score=score,
            is_acceptable=is_acceptable,
            issues=issues,
            suggestions=suggestions,
            category=category,
            confidence=confidence
        )
    
    def _assess_length(self, prompt: str, words: List[str]) -> Tuple[int, List[str], List[str]]:
        """Assess prompt length quality."""
        score = 0
        issues = []
        suggestions = []
        
        char_count = len(prompt)
        word_count = len(words)
        
        if char_count < 5:
            score -= 30
            issues.append("CRITICAL: Prompt too short (< 5 characters)")
            suggestions.append("Add more detail about what you want to accomplish")
        elif char_count < 10:
            score -= 20
            issues.append("Prompt very short (< 10 characters)")
            suggestions.append("Provide more context about your request")
        elif char_count < 15:
            score -= 10
            issues.append("Prompt quite short (< 15 characters)")
            suggestions.append("Consider adding more specific details")
        
        if word_count < 2:
            score -= 25
            issues.append("CRITICAL: Too few words (< 2)")
            suggestions.append("Use at least 2-3 words to describe your request")
        elif word_count < 3:
            score -= 15
            issues.append("Very few words (< 3)")
            suggestions.append("Add more descriptive words")
        
        # Bonus for good length
        if 20 <= char_count <= 100:
            score += 10
        elif 15 <= char_count <= 150:
            score += 5
        
        return score, issues, suggestions
    
    def _assess_specificity(self, prompt_lower: str, words: List[str]) -> Tuple[int, List[str], List[str]]:
        """Assess prompt specificity and clarity."""
        score = 0
        issues = []
        suggestions = []
        
        # Check for vague terms
        vague_count = sum(1 for term in self.VAGUE_TERMS if term in prompt_lower)
        if vague_count > 0:
            score -= min(vague_count * 8, 25)
            issues.append(f"Contains vague terms ({vague_count} found)")
            suggestions.append("Replace vague terms with specific actions (e.g., 'add navigation', 'fix login bug')")
        
        # Check for positive keywords
        positive_count = sum(1 for keyword in self.POSITIVE_KEYWORDS if keyword in prompt_lower)
        if positive_count > 0:
            score += min(positive_count * 5, 20)
        else:
            issues.append("No specific technical terms found")
            suggestions.append("Include relevant technical terms (e.g., 'function', 'component', 'API')")
        
        # Check for specific actions
        action_count = sum(1 for action in self.SPECIFIC_ACTIONS if action in prompt_lower)
        if action_count > 0:
            score += min(action_count * 3, 15)
        else:
            issues.append("No clear action specified")
            suggestions.append("Start with a clear action verb (e.g., 'add', 'create', 'fix', 'optimize')")
        
        return score, issues, suggestions
    
    def _assess_technical_content(self, prompt_lower: str, words: List[str]) -> Tuple[int, List[str], List[str]]:
        """Assess technical content and context."""
        score = 0
        issues = []
        suggestions = []
        
        # Check for technical terms
        tech_count = sum(1 for term in self.TECHNICAL_TERMS if term in prompt_lower)
        if tech_count > 0:
            score += min(tech_count * 4, 15)
        
        # Check for code-related context
        code_indicators = ["function", "class", "method", "variable", "component", "module"]
        code_count = sum(1 for indicator in code_indicators if indicator in prompt_lower)
        if code_count > 0:
            score += min(code_count * 3, 10)
        
        # Check for file/project context
        context_indicators = ["file", "project", "page", "website", "app", "application"]
        context_count = sum(1 for indicator in context_indicators if indicator in prompt_lower)
        if context_count > 0:
            score += min(context_count * 2, 8)
        
        return score, issues, suggestions
    
    def _assess_action_clarity(self, prompt_lower: str, words: List[str]) -> Tuple[int, List[str], List[str]]:
        """Assess clarity of requested action."""
        score = 0
        issues = []
        suggestions = []
        
        # Check for question words (often indicate unclear requests)
        question_words = ["what", "how", "why", "when", "where", "which"]
        question_count = sum(1 for word in question_words if word in words)
        if question_count > 0:
            score -= min(question_count * 5, 15)
            issues.append("Contains question words - may be unclear request")
            suggestions.append("Convert questions to clear action statements")
        
        # Check for imperative mood (good for clear instructions)
        imperative_starters = ["add", "create", "make", "build", "implement", "fix", "remove", "delete"]
        if any(prompt_lower.startswith(starter) for starter in imperative_starters):
            score += 10
        
        # Check for conditional language (may indicate uncertainty)
        conditional_words = ["maybe", "perhaps", "possibly", "might", "could", "should"]
        conditional_count = sum(1 for word in conditional_words if word in words)
        if conditional_count > 0:
            score -= min(conditional_count * 3, 10)
            issues.append("Contains uncertain language")
            suggestions.append("Use definitive language for clearer instructions")
        
        return score, issues, suggestions
    
    def _categorize_prompt(self, score: int, prompt_lower: str) -> str:
        """Categorize prompt based on score and content."""
        if score >= 80:
            return "excellent"
        elif score >= 65:
            return "good"
        elif score >= 50:
            return "acceptable"
        elif score >= 35:
            return "weak"
        else:
            return "poor"
    
    def _calculate_confidence(self, score: int, issue_count: int) -> float:
        """Calculate confidence in the quality assessment."""
        base_confidence = min(score / 100.0, 1.0)
        issue_penalty = min(issue_count * 0.05, 0.3)
        return max(base_confidence - issue_penalty, 0.1)


# Global scorer instance
_scorer = PromptQualityScorer()


def score_prompt_quality(prompt: str) -> int:
    """
    Score prompt quality from 0 to 100 based on clarity, length, and keywords.
    
    Args:
        prompt: The user prompt to assess
        
    Returns:
        Quality score (0-100)
        
    Examples:
        >>> score_prompt_quality("Fix")
        25
        >>> score_prompt_quality("Add error handling to the login function")
        78
    """
    result = _scorer.score_prompt_quality(prompt)
    return result.score


def is_prompt_too_weak(prompt: str, threshold: int = 50) -> bool:
    """
    Check if prompt is too weak based on quality threshold.
    
    Args:
        prompt: The user prompt to assess
        threshold: Minimum acceptable quality score (default: 50)
        
    Returns:
        True if prompt is too weak, False otherwise
    """
    score = score_prompt_quality(prompt)
    return score < threshold


def assess_prompt_quality(prompt: str) -> PromptQualityResult:
    """
    Get detailed prompt quality assessment.
    
    Args:
        prompt: The user prompt to assess
        
    Returns:
        Detailed PromptQualityResult with score, issues, and suggestions
    """
    return _scorer.score_prompt_quality(prompt)


def get_quality_suggestions(prompt: str) -> List[str]:
    """
    Get suggestions for improving prompt quality.
    
    Args:
        prompt: The user prompt to assess
        
    Returns:
        List of improvement suggestions
    """
    result = _scorer.score_prompt_quality(prompt)
    return result.suggestions


def format_quality_report(prompt: str) -> str:
    """
    Generate a formatted quality report for a prompt.
    
    Args:
        prompt: The user prompt to assess
        
    Returns:
        Formatted quality report string
    """
    result = _scorer.score_prompt_quality(prompt)
    
    report = f"""
📊 PROMPT QUALITY REPORT
========================
Prompt: "{prompt}"
Score: {result.score}/100 ({result.category.upper()})
Acceptable: {'✅ Yes' if result.is_acceptable else '❌ No'}
Confidence: {result.confidence:.1%}

"""
    
    if result.issues:
        report += "⚠️ ISSUES FOUND:\n"
        for issue in result.issues:
            report += f"  • {issue}\n"
        report += "\n"
    
    if result.suggestions:
        report += "💡 SUGGESTIONS:\n"
        for suggestion in result.suggestions:
            report += f"  • {suggestion}\n"
        report += "\n"
    
    return report.strip()
