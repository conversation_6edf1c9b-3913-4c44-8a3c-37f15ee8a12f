<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeCrusher Phase 13 - Shared Intelligence Layer Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .icon {
            font-size: 1.5rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #718096;
            font-weight: 500;
        }
        
        .metric-value {
            font-weight: bold;
            color: #2d3748;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active {
            background-color: #48bb78;
        }
        
        .status-warning {
            background-color: #ed8936;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4299e1, #3182ce);
            transition: width 0.3s ease;
        }
        
        .model-performance {
            margin-top: 15px;
        }
        
        .model-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }
        
        .model-name {
            font-weight: 600;
            color: #4a5568;
        }
        
        .model-rating {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .stars {
            color: #f6ad55;
        }
        
        .recent-activity {
            list-style: none;
        }
        
        .activity-item {
            padding: 10px 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-file {
            font-weight: 600;
            color: #4a5568;
        }
        
        .activity-model {
            color: #718096;
            font-size: 0.9rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            color: white;
            text-align: center;
        }
        
        .feature-item h4 {
            margin-bottom: 8px;
            color: #f7fafc;
        }
        
        .feature-item p {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .commit-status {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            margin-top: 30px;
        }
        
        .commit-status h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .commit-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .commit-item {
            background: #f7fafc;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #4299e1;
        }
        
        .commit-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .commit-desc {
            color: #718096;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 CodeCrusher Phase 13</h1>
            <p>Shared Intelligence Layer - Live Demo Dashboard</p>
        </div>
        
        <div class="dashboard">
            <!-- Intelligence Summary Card -->
            <div class="card">
                <h3><span class="icon">📊</span>Intelligence Summary</h3>
                <div class="metric">
                    <span class="metric-label">Total Entries</span>
                    <span class="metric-value">9</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Models Tracked</span>
                    <span class="metric-value">4</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Files Processed</span>
                    <span class="metric-value">6</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Average Rating</span>
                    <span class="metric-value">4.0/5.0 ⭐</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Best Model</span>
                    <span class="metric-value">GPT-4</span>
                </div>
            </div>
            
            <!-- Model Performance Card -->
            <div class="card">
                <h3><span class="icon">🤖</span>Model Performance</h3>
                <div class="model-performance">
                    <div class="model-item">
                        <span class="model-name">GPT-4</span>
                        <div class="model-rating">
                            <span class="stars">⭐⭐⭐⭐⭐</span>
                            <span>5.0</span>
                        </div>
                    </div>
                    <div class="model-item">
                        <span class="model-name">TestAPI</span>
                        <div class="model-rating">
                            <span class="stars">⭐⭐⭐⭐⭐</span>
                            <span>5.0</span>
                        </div>
                    </div>
                    <div class="model-item">
                        <span class="model-name">TestModel</span>
                        <div class="model-rating">
                            <span class="stars">⭐⭐⭐⭐</span>
                            <span>4.0</span>
                        </div>
                    </div>
                    <div class="model-item">
                        <span class="model-name">Mixtral</span>
                        <div class="model-rating">
                            <span class="stars">⭐⭐⭐</span>
                            <span>3.4</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tone Effectiveness Card -->
            <div class="card">
                <h3><span class="icon">🎨</span>Tone Effectiveness</h3>
                <div class="metric">
                    <span class="metric-label">Assertive</span>
                    <span class="metric-value">5.0 ⭐</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Neutral</span>
                    <span class="metric-value">4.5 ⭐</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Formal</span>
                    <span class="metric-value">4.5 ⭐</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Friendly</span>
                    <span class="metric-value">3.2 ⭐</span>
                </div>
            </div>
            
            <!-- System Status Card -->
            <div class="card">
                <h3><span class="icon">🏠</span>System Status</h3>
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator status-active"></span>
                        Intel Directory
                    </span>
                    <span class="metric-value">Active</span>
                </div>
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator status-active"></span>
                        Config File
                    </span>
                    <span class="metric-value">53 bytes</span>
                </div>
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator status-active"></span>
                        Feedback Log
                    </span>
                    <span class="metric-value">3,819 bytes</span>
                </div>
                <div class="metric">
                    <span class="metric-label">
                        <span class="status-indicator status-active"></span>
                        Logs Database
                    </span>
                    <span class="metric-value">36,864 bytes</span>
                </div>
            </div>
            
            <!-- Recent Activity Card -->
            <div class="card">
                <h3><span class="icon">📈</span>Recent Activity</h3>
                <ul class="recent-activity">
                    <li class="activity-item">
                        <div>
                            <div class="activity-file">example.py</div>
                            <div class="activity-model">Mixtral</div>
                        </div>
                        <span class="metric-value">⭐ 4</span>
                    </li>
                    <li class="activity-item">
                        <div>
                            <div class="activity-file">auth_service.py</div>
                            <div class="activity-model">GPT-4</div>
                        </div>
                        <span class="metric-value">⭐ 5</span>
                    </li>
                    <li class="activity-item">
                        <div>
                            <div class="activity-file">test_intel_api.py</div>
                            <div class="activity-model">TestAPI</div>
                        </div>
                        <span class="metric-value">⭐ 5</span>
                    </li>
                </ul>
            </div>
            
            <!-- Phase 13 Features Card -->
            <div class="card">
                <h3><span class="icon">🚀</span>Phase 13 Features</h3>
                <div class="features-grid">
                    <div class="feature-item">
                        <h4>✅ Centralized Storage</h4>
                        <p>Unified intelligence directory</p>
                    </div>
                    <div class="feature-item">
                        <h4>✅ Structured Schema</h4>
                        <p>Consistent feedback format</p>
                    </div>
                    <div class="feature-item">
                        <h4>✅ Intel API</h4>
                        <p>Lightweight interface</p>
                    </div>
                    <div class="feature-item">
                        <h4>✅ Analytics</h4>
                        <p>Real-time insights</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Commit Status -->
        <div class="commit-status">
            <h3>🎯 GitHub Commit Status - All Changes Pushed!</h3>
            <div class="commit-list">
                <div class="commit-item">
                    <div class="commit-title">Phase 13 Step 1</div>
                    <div class="commit-desc">Shared Intelligence Layer (Centralized Storage)</div>
                </div>
                <div class="commit-item">
                    <div class="commit-title">Phase 13 Step 2</div>
                    <div class="commit-desc">Feedback Registry Format (Structured Schema)</div>
                </div>
                <div class="commit-item">
                    <div class="commit-title">Phase 13 Step 3</div>
                    <div class="commit-desc">Intel API (Lightweight Shared Intelligence Interface)</div>
                </div>
            </div>
            <p style="margin-top: 20px; color: #4a5568; font-weight: 600;">
                🎉 Ready for Phase 13 Step 4: CLI + Extension Integration!
            </p>
        </div>
    </div>
    
    <script>
        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate progress bars
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
            
            // Add hover effects to cards
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
