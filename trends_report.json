{"2025-05-23": {"total_injections": 113, "fallbacks": 25, "fallback_rate": 22.123893805309734, "errors": 26, "error_types": {"Model response error": 8, "API timeout error": 7, "Timeout error": 8, "Timeout after 30 seconds": 1, "timeout after 30s": 1, "Invalid response format": 1}, "models": {"llama3": 27, "mistral": 40, "llama3-70b": 15, "gpt-4": 9, "claude-3": 20, "llama3-70b-8192": 1, "llama3-8b-8192": 1}, "tokens_in": 35152, "tokens_out": 85273, "tags": {"urgent-fix": 60, "teamC": 20, "teamB": 24, "teamA": 20, "@error:timeout": 1, "@cached:true": 1, "@model:llama3": 1, "@fallback:true": 1, "bugfix": 3, "optimization": 1}}, "2025-05-22": {"total_injections": 9, "fallbacks": 1, "fallback_rate": 11.11111111111111, "errors": 5, "error_types": {"Token limit exceeded": 4, "Rate limit exceeded": 1}, "models": {"llama3": 2, "mistral": 2, "gpt-4": 4, "claude-3": 1}, "tokens_in": 3076, "tokens_out": 9062, "tags": {"optimization": 1, "bugfix": 3, "documentation": 1, "refactor": 2, "feature": 1}}, "2025-05-21": {"total_injections": 11, "fallbacks": 2, "fallback_rate": 18.181818181818183, "errors": 0, "error_types": {}, "models": {"claude-3": 2, "mistral": 5, "llama3": 3, "gpt-4": 1}, "tokens_in": 3374, "tokens_out": 11864, "tags": {"documentation": 2, "refactor": 3, "feature": 1, "bugfix": 2, "optimization": 1}}, "2025-05-20": {"total_injections": 10, "fallbacks": 0, "fallback_rate": 0.0, "errors": 1, "error_types": {"API timeout error": 1}, "models": {"mistral": 3, "gpt-4": 4, "llama3": 2, "claude-3": 1}, "tokens_in": 3548, "tokens_out": 11366, "tags": {"bugfix": 1, "feature": 2, "optimization": 4, "refactor": 5, "documentation": 1}}, "2025-05-19": {"total_injections": 11, "fallbacks": 3, "fallback_rate": 27.27272727272727, "errors": 1, "error_types": {"API timeout error": 1}, "models": {"claude-3": 4, "mistral": 1, "gpt-4": 5, "llama3": 1}, "tokens_in": 3934, "tokens_out": 8610, "tags": {"optimization": 2, "refactor": 4, "feature": 4, "bugfix": 1, "documentation": 1}}, "2025-05-18": {"total_injections": 8, "fallbacks": 1, "fallback_rate": 12.5, "errors": 1, "error_types": {"API timeout error": 1}, "models": {"llama3": 1, "claude-3": 3, "mistral": 3, "gpt-4": 1}, "tokens_in": 2207, "tokens_out": 10079, "tags": {"documentation": 2, "optimization": 3, "bugfix": 1, "refactor": 1, "feature": 1}}, "2025-05-17": {"total_injections": 11, "fallbacks": 2, "fallback_rate": 18.181818181818183, "errors": 3, "error_types": {"Rate limit exceeded": 1, "Token limit exceeded": 1, "Invalid response format": 1}, "models": {"llama3": 2, "claude-3": 2, "gpt-4": 4, "mistral": 3}, "tokens_in": 4095, "tokens_out": 13729, "tags": {"refactor": 3, "feature": 3, "documentation": 4, "bugfix": 2, "optimization": 2}}, "2025-05-16": {"total_injections": 10, "fallbacks": 0, "fallback_rate": 0.0, "errors": 2, "error_types": {"Invalid response format": 1, "Rate limit exceeded": 1}, "models": {"mistral": 5, "llama3": 2, "gpt-4": 2, "claude-3": 1}, "tokens_in": 3269, "tokens_out": 8446, "tags": {"feature": 3, "bugfix": 2, "refactor": 2, "optimization": 3, "documentation": 1}}, "2025-05-15": {"total_injections": 8, "fallbacks": 1, "fallback_rate": 12.5, "errors": 2, "error_types": {"API timeout error": 1, "Token limit exceeded": 1}, "models": {"claude-3": 3, "gpt-4": 3, "llama3": 1, "mistral": 1}, "tokens_in": 2047, "tokens_out": 10794, "tags": {"documentation": 3, "bugfix": 4, "feature": 4, "optimization": 2}}, "2025-05-14": {"total_injections": 10, "fallbacks": 3, "fallback_rate": 30.0, "errors": 3, "error_types": {"Rate limit exceeded": 1, "API timeout error": 1, "Token limit exceeded": 1}, "models": {"gpt-4": 2, "claude-3": 3, "llama3": 4, "mistral": 1}, "tokens_in": 3315, "tokens_out": 12697, "tags": {"documentation": 1, "optimization": 4, "refactor": 2, "feature": 1, "bugfix": 2}}, "2025-05-13": {"total_injections": 9, "fallbacks": 3, "fallback_rate": 33.33333333333333, "errors": 2, "error_types": {"Invalid response format": 1, "Token limit exceeded": 1}, "models": {"gpt-4": 2, "claude-3": 2, "llama3": 3, "mistral": 2}, "tokens_in": 2646, "tokens_out": 11813, "tags": {"documentation": 2, "optimization": 1, "feature": 2, "bugfix": 2}}, "2025-05-12": {"total_injections": 9, "fallbacks": 0, "fallback_rate": 0.0, "errors": 5, "error_types": {"Rate limit exceeded": 1, "Token limit exceeded": 2, "Invalid response format": 1, "API timeout error": 1}, "models": {"gpt-4": 3, "llama3": 2, "claude-3": 2, "mistral": 2}, "tokens_in": 2397, "tokens_out": 7875, "tags": {"optimization": 5, "bugfix": 4, "feature": 1, "documentation": 3, "refactor": 1}}, "2025-05-11": {"total_injections": 9, "fallbacks": 3, "fallback_rate": 33.33333333333333, "errors": 1, "error_types": {"Token limit exceeded": 1}, "models": {"gpt-4": 3, "claude-3": 1, "mistral": 2, "llama3": 3}, "tokens_in": 2671, "tokens_out": 12588, "tags": {"feature": 3, "documentation": 2, "optimization": 2, "refactor": 1}}, "2025-05-10": {"total_injections": 11, "fallbacks": 1, "fallback_rate": 9.090909090909092, "errors": 5, "error_types": {"Token limit exceeded": 1, "API timeout error": 1, "Invalid response format": 1, "Rate limit exceeded": 2}, "models": {"gpt-4": 6, "mistral": 2, "llama3": 1, "claude-3": 2}, "tokens_in": 3532, "tokens_out": 11532, "tags": {"optimization": 3, "documentation": 3, "refactor": 1, "feature": 2}}, "2025-05-09": {"total_injections": 11, "fallbacks": 1, "fallback_rate": 9.090909090909092, "errors": 3, "error_types": {"Token limit exceeded": 2, "Invalid response format": 1}, "models": {"gpt-4": 5, "claude-3": 1, "mistral": 3, "llama3": 2}, "tokens_in": 3510, "tokens_out": 13235, "tags": {"feature": 3, "optimization": 3, "bugfix": 2, "documentation": 6}}, "2025-05-08": {"total_injections": 9, "fallbacks": 0, "fallback_rate": 0.0, "errors": 2, "error_types": {"Invalid response format": 2}, "models": {"gpt-4": 2, "claude-3": 3, "mistral": 3, "llama3": 1}, "tokens_in": 2204, "tokens_out": 9995, "tags": {"documentation": 2, "optimization": 2, "refactor": 2, "bugfix": 2, "feature": 1}}, "2025-05-07": {"total_injections": 11, "fallbacks": 3, "fallback_rate": 27.27272727272727, "errors": 1, "error_types": {"Token limit exceeded": 1}, "models": {"llama3": 2, "gpt-4": 3, "mistral": 2, "claude-3": 4}, "tokens_in": 3160, "tokens_out": 9534, "tags": {"documentation": 3, "bugfix": 3, "optimization": 2, "refactor": 1, "feature": 3}}, "2025-05-06": {"total_injections": 9, "fallbacks": 1, "fallback_rate": 11.11111111111111, "errors": 3, "error_types": {"API timeout error": 1, "Rate limit exceeded": 1, "Token limit exceeded": 1}, "models": {"gpt-4": 7, "mistral": 2}, "tokens_in": 2635, "tokens_out": 8466, "tags": {"bugfix": 3, "refactor": 1, "feature": 1, "optimization": 1, "documentation": 1}}, "2025-05-05": {"total_injections": 9, "fallbacks": 0, "fallback_rate": 0.0, "errors": 1, "error_types": {"Rate limit exceeded": 1}, "models": {"llama3": 6, "mistral": 2, "gpt-4": 1}, "tokens_in": 2750, "tokens_out": 9557, "tags": {"refactor": 1, "feature": 1, "documentation": 2}}, "2025-05-04": {"total_injections": 10, "fallbacks": 1, "fallback_rate": 10.0, "errors": 1, "error_types": {"Rate limit exceeded": 1}, "models": {"claude-3": 4, "gpt-4": 1, "mistral": 4, "llama3": 1}, "tokens_in": 2906, "tokens_out": 12982, "tags": {"refactor": 1, "optimization": 1, "documentation": 1}}, "2025-05-03": {"total_injections": 11, "fallbacks": 0, "fallback_rate": 0.0, "errors": 1, "error_types": {"API timeout error": 1}, "models": {"claude-3": 5, "gpt-4": 3, "mistral": 1, "llama3": 2}, "tokens_in": 3200, "tokens_out": 10013, "tags": {"feature": 1, "optimization": 3, "documentation": 2, "bugfix": 3, "refactor": 1}}, "2025-05-02": {"total_injections": 8, "fallbacks": 2, "fallback_rate": 25.0, "errors": 1, "error_types": {"Invalid response format": 1}, "models": {"llama3": 2, "mistral": 3, "gpt-4": 2, "claude-3": 1}, "tokens_in": 2091, "tokens_out": 10519, "tags": {"bugfix": 3, "documentation": 2, "optimization": 2, "feature": 2}}, "2025-05-01": {"total_injections": 9, "fallbacks": 3, "fallback_rate": 33.33333333333333, "errors": 1, "error_types": {"Invalid response format": 1}, "models": {"mistral": 2, "claude-3": 5, "llama3": 2}, "tokens_in": 2605, "tokens_out": 10730, "tags": {"bugfix": 1, "feature": 3, "documentation": 1, "optimization": 3, "refactor": 1}}, "2025-04-30": {"total_injections": 9, "fallbacks": 2, "fallback_rate": 22.22222222222222, "errors": 1, "error_types": {"API timeout error": 1}, "models": {"mistral": 2, "claude-3": 2, "llama3": 2, "gpt-4": 3}, "tokens_in": 3013, "tokens_out": 11506, "tags": {"refactor": 2, "bugfix": 5, "feature": 1, "optimization": 2, "documentation": 1}}, "2025-04-29": {"total_injections": 11, "fallbacks": 2, "fallback_rate": 18.181818181818183, "errors": 1, "error_types": {"API timeout error": 1}, "models": {"gpt-4": 3, "mistral": 3, "claude-3": 3, "llama3": 2}, "tokens_in": 3623, "tokens_out": 12224, "tags": {"bugfix": 5, "refactor": 3, "optimization": 2}}, "2025-04-28": {"total_injections": 11, "fallbacks": 1, "fallback_rate": 9.090909090909092, "errors": 1, "error_types": {"Token limit exceeded": 1}, "models": {"claude-3": 5, "gpt-4": 3, "mistral": 1, "llama3": 2}, "tokens_in": 2972, "tokens_out": 9769, "tags": {"optimization": 3, "feature": 2, "bugfix": 3, "documentation": 2}}, "2025-04-27": {"total_injections": 11, "fallbacks": 0, "fallback_rate": 0.0, "errors": 4, "error_types": {"Rate limit exceeded": 1, "Token limit exceeded": 2, "Invalid response format": 1}, "models": {"llama3": 5, "gpt-4": 3, "mistral": 3}, "tokens_in": 3685, "tokens_out": 13756, "tags": {"documentation": 1, "feature": 2, "refactor": 4, "optimization": 2}}, "2025-04-26": {"total_injections": 11, "fallbacks": 0, "fallback_rate": 0.0, "errors": 5, "error_types": {"Rate limit exceeded": 2, "Invalid response format": 1, "Token limit exceeded": 1, "API timeout error": 1}, "models": {"mistral": 1, "llama3": 6, "claude-3": 3, "gpt-4": 1}, "tokens_in": 3266, "tokens_out": 13773, "tags": {"optimization": 3, "refactor": 4, "feature": 3, "documentation": 1, "bugfix": 2}}, "2025-04-25": {"total_injections": 8, "fallbacks": 0, "fallback_rate": 0.0, "errors": 4, "error_types": {"API timeout error": 1, "Invalid response format": 1, "Token limit exceeded": 2}, "models": {"gpt-4": 5, "claude-3": 2, "mistral": 1}, "tokens_in": 2246, "tokens_out": 7472, "tags": {"feature": 3, "documentation": 2, "optimization": 1}}, "2025-04-24": {"total_injections": 8, "fallbacks": 1, "fallback_rate": 12.5, "errors": 3, "error_types": {"Token limit exceeded": 1, "Rate limit exceeded": 1, "Invalid response format": 1}, "models": {"claude-3": 3, "mistral": 2, "llama3": 2, "gpt-4": 1}, "tokens_in": 2619, "tokens_out": 10951, "tags": {"optimization": 3, "documentation": 2, "feature": 4, "bugfix": 1, "refactor": 3}}}