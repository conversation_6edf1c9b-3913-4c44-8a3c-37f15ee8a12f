# Architecture Guide

This guide provides a comprehensive overview of CodeCrusher's system architecture, design patterns, and core components.

## 🏗️ System Overview

CodeCrusher is built as a modular, extensible system with clear separation of concerns and a focus on intelligence learning and adaptation.

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    CodeCrusher System                       │
├─────────────────────────────────────────────────────────────┤
│  CLI Interface (codecrusher_cli.py)                        │
├─────────────────────────────────────────────────────────────┤
│  Core Engine                                                │
│  ├── AI Injector        ├── Intelligence System            │
│  ├── Model Router       ├── Quality Scorer                 │
│  └── File Processor     └── Feedback Collector             │
├─────────────────────────────────────────────────────────────┤
│  AI Providers                                               │
│  ├── Groq API          ├── OpenAI API                      │
│  ├── Mistral API       └── Custom Providers                │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ├── SQLite Database   ├── File Cache                      │
│  └── Configuration     └── Logs                            │
└─────────────────────────────────────────────────────────────┘
```

---

## 🧩 Core Components

### 1. **CLI Interface** (`codecrusher_cli.py`)

The main entry point that provides a user-friendly command-line interface.

#### Responsibilities:
- Command parsing and validation
- User interaction and feedback
- Progress reporting and logging
- Error handling and recovery

#### Key Features:
- **Click-based** command structure
- **Rich console** output with colors and formatting
- **Interactive prompts** for user input
- **Progress bars** for long-running operations

```python
# CLI Architecture Pattern
@click.group()
@click.option('--verbose', is_flag=True)
@click.pass_context
def cli(ctx, verbose):
    """CodeCrusher CLI main entry point."""
    ctx.ensure_object(dict)
    ctx.obj['verbose'] = verbose

@cli.command()
@click.argument('path')
@click.option('--prompt', required=True)
@click.pass_context
def inject(ctx, path, prompt):
    """Inject AI-generated code."""
    injector = AIInjector(verbose=ctx.obj['verbose'])
    result = injector.inject(path, prompt)
    display_result(result)
```

### 2. **AI Injector** (`codecrusher/ai_injector.py`)

Core component responsible for code injection logic.

#### Responsibilities:
- File parsing and tag detection
- AI model communication
- Code generation and insertion
- Backup and rollback management

#### Architecture Pattern:
```python
class AIInjector:
    def __init__(self, model_router: ModelRouter, quality_scorer: QualityScorer):
        self.model_router = model_router
        self.quality_scorer = quality_scorer
        self.file_processor = FileProcessor()
    
    def inject(self, file_path: Path, prompt: str) -> InjectionResult:
        # 1. Parse file and find injection tags
        tags = self.file_processor.find_injection_tags(file_path)
        
        # 2. Generate code using AI models
        generated_code = self.model_router.generate_code(prompt, context)
        
        # 3. Score quality and apply if acceptable
        quality_score = self.quality_scorer.score(generated_code)
        
        # 4. Insert code and create backup
        if quality_score >= threshold:
            return self.file_processor.insert_code(file_path, generated_code)
```

### 3. **Model Router** (`codecrusher/model_router.py`)

Manages multiple AI models with intelligent fallback and escalation.

#### Responsibilities:
- Model selection and routing
- Fallback and escalation logic
- API rate limiting and error handling
- Performance monitoring

#### Fallback Strategy:
```python
class ModelRouter:
    def __init__(self):
        self.models = [
            GroqModel("llama3-8b"),      # Tier 1: Fast
            GroqModel("mixtral-8x7b"),   # Tier 2: Balanced
            GroqModel("llama3-70b"),     # Tier 3: Quality
            OpenAIModel("gpt-4-turbo")   # Tier 4: Premium
        ]
    
    def generate_code(self, prompt: str, context: dict) -> str:
        for model in self.models:
            try:
                result = model.generate(prompt, context)
                if self.quality_scorer.score(result) >= self.threshold:
                    return result
            except APIError:
                continue  # Try next model
        
        raise AllModelsFailedError("No models available")
```

### 4. **Intelligence System** (`codecrusher/intelligence_system.py`)

The self-improving intelligence that learns from user feedback.

#### Responsibilities:
- Feedback collection and analysis
- Parameter learning and adaptation
- Quality improvement tracking
- Learning analytics and reporting

#### Learning Loop:
```python
class IntelligenceSystem:
    def __init__(self):
        self.feedback_store = FeedbackStore()
        self.parameter_learner = ParameterLearner()
        self.quality_tracker = QualityTracker()
    
    def collect_feedback(self, injection_id: str, rating: int, comment: str):
        """Collect user feedback for learning."""
        feedback = Feedback(injection_id, rating, comment, timestamp=now())
        self.feedback_store.save(feedback)
    
    def trigger_learning(self) -> LearningResult:
        """Analyze feedback and update parameters."""
        feedback_data = self.feedback_store.get_recent()
        parameter_updates = self.parameter_learner.analyze(feedback_data)
        
        # Apply learned parameters
        for param, value in parameter_updates.items():
            self.update_parameter(param, value)
        
        return LearningResult(parameter_updates, improvement_metrics)
```

---

## 🔄 Data Flow

### Injection Workflow

```
User Input → CLI → AI Injector → Model Router → AI Provider
    ↓           ↓        ↓            ↓            ↓
File System ← Quality ← Generated ← API Response ← AI Model
    ↓         Scorer     Code         
Intelligence ← Feedback ← User Rating
System
```

#### Detailed Flow:

1. **User Input**: User provides file path and prompt via CLI
2. **File Processing**: System parses file and identifies injection tags
3. **Context Building**: Extracts relevant context from surrounding code
4. **Model Selection**: Router selects appropriate AI model based on complexity
5. **Code Generation**: AI model generates code based on prompt and context
6. **Quality Assessment**: Generated code is scored for quality and relevance
7. **Fallback Logic**: If quality is insufficient, escalate to better model
8. **Code Insertion**: High-quality code is inserted into the target file
9. **Feedback Collection**: User rates the result for future learning
10. **Intelligence Learning**: System analyzes feedback and updates parameters

### Learning Workflow

```
User Feedback → Feedback Store → Parameter Learner → Configuration Update
     ↓              ↓                 ↓                    ↓
Quality Metrics → Analytics → Learning Algorithm → Improved Performance
```

---

## 🗄️ Data Architecture

### Database Schema

#### **Injection Logs**
```sql
CREATE TABLE injection_logs (
    id TEXT PRIMARY KEY,
    file_path TEXT NOT NULL,
    prompt TEXT NOT NULL,
    model_used TEXT NOT NULL,
    generated_code TEXT,
    quality_score INTEGER,
    timestamp DATETIME,
    user_rating INTEGER,
    user_comment TEXT
);
```

#### **Learning Parameters**
```sql
CREATE TABLE learning_parameters (
    parameter_name TEXT PRIMARY KEY,
    current_value TEXT NOT NULL,
    previous_value TEXT,
    update_timestamp DATETIME,
    update_reason TEXT
);
```

#### **Feedback Entries**
```sql
CREATE TABLE feedback_entries (
    id TEXT PRIMARY KEY,
    injection_id TEXT REFERENCES injection_logs(id),
    rating INTEGER NOT NULL,
    comment TEXT,
    timestamp DATETIME,
    processed BOOLEAN DEFAULT FALSE
);
```

### File System Structure

```
~/.codecrusher/
├── config/
│   ├── settings.yaml       # User configuration
│   ├── models.yaml         # Model preferences
│   └── learning.yaml       # Learning parameters
├── cache/
│   ├── responses/          # Cached AI responses
│   └── quality_scores/     # Cached quality assessments
├── logs/
│   ├── injection.log       # Injection operation logs
│   ├── learning.log        # Learning system logs
│   └── error.log           # Error and debug logs
└── intelligence.db         # SQLite database
```

---

## 🔌 Extension Points

### Model Providers

#### **Adding New AI Providers**
```python
class CustomAIProvider(BaseAIProvider):
    def __init__(self, api_key: str, base_url: str):
        super().__init__()
        self.api_key = api_key
        self.base_url = base_url
    
    def generate_code(self, prompt: str, context: dict) -> str:
        """Generate code using custom AI provider."""
        response = self.make_api_request(prompt, context)
        return self.parse_response(response)
    
    def is_available(self) -> bool:
        """Check if provider is available."""
        return self.test_connection()

# Register provider
model_router.register_provider("custom", CustomAIProvider)
```

### Quality Scorers

#### **Custom Quality Assessment**
```python
class CustomQualityScorer(BaseQualityScorer):
    def score(self, code: str, context: dict) -> int:
        """Custom quality scoring logic."""
        score = 50  # Base score
        
        # Add scoring criteria
        if self.has_error_handling(code):
            score += 15
        if self.has_documentation(code):
            score += 10
        if self.follows_style_guide(code):
            score += 10
        
        return min(score, 100)

# Register scorer
quality_system.register_scorer("custom", CustomQualityScorer)
```

### Learning Algorithms

#### **Custom Learning Strategies**
```python
class CustomLearningAlgorithm(BaseLearningAlgorithm):
    def analyze_feedback(self, feedback_data: List[Feedback]) -> Dict[str, Any]:
        """Custom learning algorithm implementation."""
        parameter_updates = {}
        
        # Analyze patterns in feedback
        avg_rating = sum(f.rating for f in feedback_data) / len(feedback_data)
        
        if avg_rating < 3.0:
            parameter_updates['verbosity'] = 'high'
            parameter_updates['error_handling'] = 'extensive'
        
        return parameter_updates

# Register algorithm
intelligence_system.register_algorithm("custom", CustomLearningAlgorithm)
```

---

## 🔒 Security Architecture

### API Key Management

```python
class SecureKeyManager:
    def __init__(self):
        self.key_store = EncryptedKeyStore()
    
    def store_key(self, provider: str, key: str):
        """Securely store API key."""
        encrypted_key = self.encrypt(key)
        self.key_store.save(provider, encrypted_key)
    
    def get_key(self, provider: str) -> str:
        """Retrieve and decrypt API key."""
        encrypted_key = self.key_store.load(provider)
        return self.decrypt(encrypted_key)
```

### Input Validation

```python
class InputValidator:
    def validate_file_path(self, path: str) -> Path:
        """Validate and sanitize file path."""
        path_obj = Path(path).resolve()
        
        # Security checks
        if not path_obj.exists():
            raise FileNotFoundError(f"File not found: {path}")
        
        if not self.is_safe_path(path_obj):
            raise SecurityError(f"Unsafe path: {path}")
        
        return path_obj
    
    def validate_prompt(self, prompt: str) -> str:
        """Validate and sanitize user prompt."""
        if len(prompt) > MAX_PROMPT_LENGTH:
            raise ValueError("Prompt too long")
        
        return self.sanitize_prompt(prompt)
```

---

## 📊 Performance Architecture

### Caching Strategy

```python
class IntelligentCache:
    def __init__(self):
        self.response_cache = LRUCache(maxsize=1000)
        self.quality_cache = LRUCache(maxsize=500)
    
    def get_cached_response(self, prompt_hash: str) -> Optional[str]:
        """Get cached AI response if available."""
        cached_entry = self.response_cache.get(prompt_hash)
        
        if cached_entry and not self.is_expired(cached_entry):
            return cached_entry.response
        
        return None
    
    def cache_response(self, prompt_hash: str, response: str, quality_score: int):
        """Cache response if quality is sufficient."""
        if quality_score >= CACHE_QUALITY_THRESHOLD:
            entry = CacheEntry(response, quality_score, timestamp=now())
            self.response_cache[prompt_hash] = entry
```

### Async Processing

```python
class AsyncInjectionProcessor:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.queue = asyncio.Queue()
    
    async def process_injection(self, injection_request: InjectionRequest):
        """Process injection asynchronously."""
        future = self.executor.submit(self.sync_inject, injection_request)
        result = await asyncio.wrap_future(future)
        return result
    
    def sync_inject(self, request: InjectionRequest) -> InjectionResult:
        """Synchronous injection processing."""
        return self.ai_injector.inject(request.file_path, request.prompt)
```

---

## 🔗 Integration Architecture

### CLI Integration

The CLI serves as the primary interface, orchestrating all system components:

```python
class CLIOrchestrator:
    def __init__(self):
        self.ai_injector = AIInjector()
        self.intelligence_system = IntelligenceSystem()
        self.model_router = ModelRouter()
        self.quality_scorer = QualityScorer()
    
    def execute_injection(self, args: CLIArgs) -> CLIResult:
        """Orchestrate complete injection workflow."""
        # 1. Validate input
        validated_args = self.validate_args(args)
        
        # 2. Execute injection
        injection_result = self.ai_injector.inject(
            validated_args.file_path, 
            validated_args.prompt
        )
        
        # 3. Collect feedback if requested
        if validated_args.collect_feedback:
            feedback = self.collect_user_feedback(injection_result)
            self.intelligence_system.process_feedback(feedback)
        
        return CLIResult(injection_result, feedback)
```

---

## 🔗 Future Architecture

### Planned Enhancements

#### **Microservices Architecture**
- **API Gateway**: Central routing and authentication
- **Injection Service**: Dedicated code injection processing
- **Intelligence Service**: Learning and analytics
- **Model Service**: AI provider management

#### **Event-Driven Architecture**
- **Event Bus**: Asynchronous communication between components
- **Event Sourcing**: Complete audit trail of all operations
- **CQRS**: Separate read and write models for optimization

#### **Distributed Intelligence**
- **Federated Learning**: Cross-team intelligence sharing
- **Distributed Cache**: Shared response caching
- **Load Balancing**: Distribute processing across instances

---

**[← Contributing Guide](contributing.md)** | **[Next: Changelog →](changelog.md)**
