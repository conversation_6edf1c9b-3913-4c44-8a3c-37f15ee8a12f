#!/usr/bin/env python3
"""
Comprehensive WebSocket diagnostics for CodeCrusher dashboard.
"""

import asyncio
import websockets
import json
import time
from datetime import datetime
import requests

async def test_websocket_connection():
    """Test WebSocket connection with detailed diagnostics."""
    
    url = "ws://127.0.0.1:8001/ws/logs"
    
    print("🔍 WebSocket Connection Diagnostics")
    print("=" * 60)
    print(f"📡 Testing: {url}")
    print(f"🕒 Started: {datetime.now().strftime('%H:%M:%S')}")
    print("=" * 60)
    
    # Test 1: Basic connection
    print("\n🧪 Test 1: Basic Connection")
    try:
        async with websockets.connect(url) as websocket:
            print("✅ Connection established successfully")
            
            # Test message reception
            try:
                message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"📨 First message: {message}")
            except asyncio.TimeoutError:
                print("⏰ No initial message received (this is normal)")
            
            # Test connection stability
            print("\n🔄 Testing connection stability (10 seconds)...")
            start_time = time.time()
            message_count = 0
            
            while time.time() - start_time < 10:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    message_count += 1
                    print(f"📨 Message {message_count}: {message[:50]}...")
                except asyncio.TimeoutError:
                    print(".", end="", flush=True)
                    continue
            
            print(f"\n✅ Connection stable for 10 seconds, received {message_count} messages")
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False
    
    # Test 2: Multiple rapid connections
    print("\n🧪 Test 2: Multiple Rapid Connections")
    for i in range(3):
        try:
            async with websockets.connect(url) as websocket:
                message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                print(f"✅ Connection {i+1}/3: Success")
        except Exception as e:
            print(f"❌ Connection {i+1}/3: Failed - {e}")
    
    # Test 3: Connection with custom headers
    print("\n🧪 Test 3: Connection with Headers")
    try:
        headers = {
            "User-Agent": "CodeCrusher-Dashboard/1.0",
            "Origin": "http://127.0.0.1:5173"
        }
        async with websockets.connect(url, extra_headers=headers) as websocket:
            message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
            print("✅ Connection with headers: Success")
    except Exception as e:
        print(f"❌ Connection with headers: Failed - {e}")
    
    return True

def test_backend_health():
    """Test backend health and WebSocket endpoint availability."""
    
    print("\n🏥 Backend Health Check")
    print("=" * 40)
    
    # Test health endpoint
    try:
        response = requests.get("http://127.0.0.1:8001/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend health: OK")
            print(f"📊 Status: {data.get('status', 'unknown')}")
            print(f"🔌 Connected clients: {data.get('connected_clients', 0)}")
        else:
            print(f"⚠️ Backend health: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend health check failed: {e}")
    
    # Test root endpoint
    try:
        response = requests.get("http://127.0.0.1:8001/", timeout=5)
        if response.status_code == 200:
            print("✅ Root endpoint: OK")
        else:
            print(f"⚠️ Root endpoint: {response.status_code}")
    except Exception as e:
        print(f"❌ Root endpoint failed: {e}")

def test_frontend_websocket_config():
    """Check if frontend WebSocket configuration is correct."""
    
    print("\n🌐 Frontend WebSocket Configuration")
    print("=" * 45)
    
    js_file = "frontend/dist/assets/index-515198a6.js"
    
    try:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for localhost references
        localhost_count = content.count('localhost:8001')
        ipv4_count = content.count('127.0.0.1:8001')
        
        print(f"🔍 Checking {js_file}")
        print(f"📊 localhost:8001 references: {localhost_count}")
        print(f"📊 127.0.0.1:8001 references: {ipv4_count}")
        
        if localhost_count > 0:
            print("⚠️ Found localhost references - may cause IPv6/IPv4 issues")
        else:
            print("✅ No localhost references found")
        
        if ipv4_count > 0:
            print("✅ IPv4 references found - good for stability")
        else:
            print("⚠️ No IPv4 references found")
            
        # Check for WebSocket connection settings
        if 'maxRetries' in content:
            print("✅ WebSocket retry settings found")
        else:
            print("⚠️ WebSocket retry settings not found")
            
    except FileNotFoundError:
        print(f"❌ Frontend build file not found: {js_file}")
    except Exception as e:
        print(f"❌ Error checking frontend config: {e}")

async def main():
    """Run all WebSocket diagnostics."""
    
    print("🚀 CodeCrusher WebSocket Diagnostics Suite")
    print("=" * 60)
    
    # Test backend health first
    test_backend_health()
    
    # Test frontend configuration
    test_frontend_websocket_config()
    
    # Test WebSocket connections
    success = await test_websocket_connection()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 WebSocket diagnostics completed successfully!")
        print("💡 WebSocket connections appear to be working correctly")
    else:
        print("⚠️ WebSocket diagnostics found issues")
        print("🔧 Check the error messages above for troubleshooting")
    
    print("\n📋 Summary:")
    print("- Backend should be running on http://127.0.0.1:8001")
    print("- Frontend should be running on http://127.0.0.1:5173")
    print("- WebSocket should connect to ws://127.0.0.1:8001/ws/logs")
    print("- All references should use 127.0.0.1 (IPv4) not localhost")

if __name__ == "__main__":
    asyncio.run(main())
