#!/usr/bin/env python3
"""
Fix WebSocket immediate close issue (code 1005).
"""

import re
import os

def fix_websocket_close_issue():
    """Fix WebSocket connection closing immediately after connecting."""
    
    js_file = "frontend/dist/assets/index-515198a6.js"
    
    if not os.path.exists(js_file):
        print(f"❌ File not found: {js_file}")
        return False
    
    print(f"🔧 Fixing WebSocket close issue in {js_file}")
    
    try:
        # Read the file
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix 1: Increase ping interval to prevent premature closure
        content = re.sub(r'ping_interval:\s*\d+', 'ping_interval: 30000', content)
        content = re.sub(r'pingInterval:\s*\d+', 'pingInterval: 30000', content)
        
        # Fix 2: Increase ping timeout
        content = re.sub(r'ping_timeout:\s*\d+', 'ping_timeout: 20000', content)
        content = re.sub(r'pingTimeout:\s*\d+', 'pingTimeout: 20000', content)
        
        # Fix 3: Increase close timeout
        content = re.sub(r'close_timeout:\s*\d+', 'close_timeout: 15000', content)
        content = re.sub(r'closeTimeout:\s*\d+', 'closeTimeout: 15000', content)
        
        # Fix 4: Add connection keep-alive settings
        content = re.sub(
            r'(new WebSocket\([^)]+\))',
            r'\1; websocket.binaryType = "arraybuffer"',
            content
        )
        
        # Fix 5: Prevent immediate reconnection on close
        content = re.sub(
            r'reconnectDelay:\s*\d+',
            'reconnectDelay: 3000',
            content
        )
        
        # Fix 6: Add error handling for code 1005
        content = re.sub(
            r'(code:\s*1005)',
            r'\1; console.log("WebSocket closed with code 1005 - retrying connection...")',
            content
        )
        
        # Fix 7: Ensure WebSocket stays open longer
        if 'heartbeat' not in content:
            # Add heartbeat mechanism
            heartbeat_code = '''
            // Add heartbeat to keep connection alive
            setInterval(() => {
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({type: "ping", timestamp: Date.now()}));
                }
            }, 25000);
            '''
            content = content.replace(
                'WebSocket.OPEN',
                'WebSocket.OPEN' + heartbeat_code,
                1  # Only replace first occurrence
            )
        
        # Write back if changed
        if content != original_content:
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ WebSocket close issue fixes applied!")
            print("   - Increased ping interval to 30 seconds")
            print("   - Increased ping timeout to 20 seconds") 
            print("   - Increased close timeout to 15 seconds")
            print("   - Added connection keep-alive settings")
            print("   - Added heartbeat mechanism")
            print("   - Improved error handling for code 1005")
            return True
        else:
            print("⚠️ No changes needed - fixes already applied")
            return True
            
    except Exception as e:
        print(f"❌ Error fixing WebSocket close issue: {e}")
        return False

if __name__ == "__main__":
    print("🔧 WebSocket Close Issue Fix Tool")
    print("=" * 50)
    
    success = fix_websocket_close_issue()
    
    if success:
        print("\n🎉 WebSocket close fix completed!")
        print("💡 Refresh the browser to apply changes")
        print("📝 WebSocket should now stay connected")
    else:
        print("\n❌ WebSocket close fix failed!")
        print("🔧 Manual intervention may be required")
