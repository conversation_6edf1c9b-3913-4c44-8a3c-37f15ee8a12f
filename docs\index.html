<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CodeCrusher - Self-Improving AI Code Injection</title>
    <meta name="description" content="Enterprise-grade AI-powered code injector with self-improving intelligence. The only AI code tool that learns from your feedback and gets better over time.">
    <meta name="keywords" content="AI, code injection, machine learning, development tools, automation, self-improving">

    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="CodeCrusher - Self-Improving AI Code Injection">
    <meta property="og:description" content="Enterprise-grade AI-powered code injector with self-improving intelligence">
    <meta property="og:url" content="https://codegx-technology.github.io/codecruncher/">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/favicon.png">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/syntax-highlight.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <img src="assets/logo.png" alt="CodeCrusher Logo" class="logo" onerror="this.style.display='none'">
                <span class="brand-text">CodeCrusher</span>
            </div>
            <div class="nav-menu">
                <a href="#overview" class="nav-link">Overview</a>
                <a href="#installation" class="nav-link">Installation</a>
                <a href="#usage" class="nav-link">Usage</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#architecture" class="nav-link">Architecture</a>
                <a href="#contributing" class="nav-link">Contributing</a>
                <a href="https://github.com/Codegx-Technology/CodeCruncher" class="nav-link github-link" target="_blank">
                    <i class="fab fa-github"></i> GitHub
                </a>
            </div>
            <button class="theme-toggle" id="themeToggle" aria-label="Toggle theme">
                <i class="fas fa-moon"></i>
            </button>
            <button class="mobile-menu-toggle" id="mobileMenuToggle" aria-label="Toggle menu">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span class="badge-text">🏆 75% Win Rate vs Augment</span>
                </div>
                <h1 class="hero-title">
                    Self-Improving AI Code Injection
                </h1>
                <p class="hero-subtitle">
                    Enterprise-grade AI-powered code injector with intelligence that learns from your feedback and gets better over time.
                </p>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">92.5/100</span>
                        <span class="stat-label">Quality Score</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">75%</span>
                        <span class="stat-label">Win Rate</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">4</span>
                        <span class="stat-label">AI Models</span>
                    </div>
                </div>
                <div class="hero-actions">
                    <a href="#installation" class="btn btn-primary">
                        <i class="fas fa-download"></i>
                        Get Started
                    </a>
                    <a href="https://github.com/Codegx-Technology/CodeCruncher" class="btn btn-secondary" target="_blank">
                        <i class="fab fa-github"></i>
                        View on GitHub
                    </a>
                </div>
                <div class="hero-demo">
                    <div class="terminal">
                        <div class="terminal-header">
                            <div class="terminal-buttons">
                                <span class="btn-close"></span>
                                <span class="btn-minimize"></span>
                                <span class="btn-maximize"></span>
                            </div>
                            <span class="terminal-title">CodeCrusher Demo</span>
                        </div>
                        <div class="terminal-body">
                            <div class="terminal-line">
                                <span class="prompt">$</span>
                                <span class="command">codecrusher inject ./src --prompt "Add error handling" --preview</span>
                            </div>
                            <div class="terminal-line output">
                                <span class="success">✅ Generated comprehensive error handling with logging</span>
                            </div>
                            <div class="terminal-line">
                                <span class="prompt">$</span>
                                <span class="command">codecrusher rate ./src --rating 5 --comment "Perfect!"</span>
                            </div>
                            <div class="terminal-line output">
                                <span class="info">🧠 Intelligence system learning from feedback...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Overview Section -->
    <section id="overview" class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">What is CodeCrusher?</h2>
                <p class="section-subtitle">
                    The only AI code tool that learns from your feedback and continuously improves its output quality.
                </p>
            </div>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="feature-title">Self-Improving Intelligence</h3>
                    <p class="feature-description">
                        AI that learns from your feedback and automatically adjusts parameters for better code quality over time.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3 class="feature-title">Multi-Model Fallback</h3>
                    <p class="feature-description">
                        Intelligent routing across 4 AI models with automatic escalation for complex tasks and quality assurance.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-terminal"></i>
                    </div>
                    <h3 class="feature-title">Professional CLI</h3>
                    <p class="feature-description">
                        Rich console interface with interactive preview, progress tracking, and beautiful output formatting.
                    </p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Quality Analytics</h3>
                    <p class="feature-description">
                        Comprehensive quality scoring and improvement tracking with detailed analytics and learning insights.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Installation Section -->
    <section id="installation" class="section section-alt">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Installation</h2>
                <p class="section-subtitle">Get CodeCrusher running on your system in minutes</p>
            </div>
            <div class="installation-methods">
                <div class="method-card">
                    <h3 class="method-title">
                        <i class="fas fa-cube"></i>
                        Install from PyPI
                    </h3>
                    <p class="method-description">Recommended for most users</p>
                    <div class="code-block">
                        <div class="code-header">
                            <span class="code-lang">bash</span>
                            <button class="copy-btn" data-copy="pip install codecrusher">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre><code># Install the latest stable version
pip install codecrusher

# Verify installation
codecrusher --version</code></pre>
                    </div>
                </div>
                <div class="method-card">
                    <h3 class="method-title">
                        <i class="fas fa-code-branch"></i>
                        Install from Source
                    </h3>
                    <p class="method-description">For developers and contributors</p>
                    <div class="code-block">
                        <div class="code-header">
                            <span class="code-lang">bash</span>
                            <button class="copy-btn" data-copy="git clone https://github.com/Codegx-Technology/CodeCruncher.git
cd CodeCruncher
pip install -e .">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre><code># Clone repository
git clone https://github.com/Codegx-Technology/CodeCruncher.git
cd CodeCruncher

# Install in development mode
pip install -e .</code></pre>
                    </div>
                </div>
            </div>
            <div class="setup-steps">
                <h3 class="steps-title">Setup API Keys</h3>
                <div class="steps-grid">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Get Groq API Key</h4>
                            <p>Sign up at <a href="https://console.groq.com" target="_blank">console.groq.com</a> and generate an API key</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Set Environment Variable</h4>
                            <div class="code-inline">
                                <code>export GROQ_API_KEY="gsk_your_key_here"</code>
                            </div>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Verify Setup</h4>
                            <div class="code-inline">
                                <code>codecrusher status</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Usage Section -->
    <section id="usage" class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Usage Examples</h2>
                <p class="section-subtitle">Learn the core workflows and commands</p>
            </div>
            <div class="usage-examples">
                <div class="example-card">
                    <h3 class="example-title">Basic Code Injection</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span class="code-lang">bash</span>
                            <button class="copy-btn" data-copy="codecrusher inject ./src/main.py --prompt 'Add error handling' --preview">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre><code># Preview changes before applying
codecrusher inject ./src/main.py --prompt "Add error handling" --preview

# Apply changes directly
codecrusher inject ./src/main.py --prompt "Add error handling" --apply</code></pre>
                    </div>
                </div>
                <div class="example-card">
                    <h3 class="example-title">Intelligence Learning Workflow</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span class="code-lang">bash</span>
                            <button class="copy-btn" data-copy="codecrusher rate ./src --rating 4 --comment 'Good but needs more logging'">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre><code># Rate injection quality for learning
codecrusher rate ./src --rating 4 --comment "Good but needs more logging"

# Trigger intelligence learning
codecrusher learn --apply

# Check learning progress
codecrusher learn --analytics</code></pre>
                    </div>
                </div>
                <div class="example-card">
                    <h3 class="example-title">Project-Wide Processing</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span class="code-lang">bash</span>
                            <button class="copy-btn" data-copy="codecrusher inject ./src --recursive --prompt 'Add comprehensive error handling'">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre><code># Process entire directory recursively
codecrusher inject ./src --recursive --prompt "Add comprehensive error handling"

# Use specific AI model
codecrusher inject ./src --model llama3-70b --prompt "Optimize performance"</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="section section-alt">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Advanced Features</h2>
                <p class="section-subtitle">Discover what makes CodeCrusher unique</p>
            </div>
            <div class="features-detailed">
                <div class="feature-detailed">
                    <div class="feature-content">
                        <h3 class="feature-title">
                            <i class="fas fa-route"></i>
                            Multi-Model AI Routing
                        </h3>
                        <p class="feature-description">
                            Intelligent routing across 4 AI models with automatic escalation for complex tasks.
                        </p>
                        <div class="feature-list">
                            <div class="feature-item">
                                <span class="tier">Tier 1</span>
                                <span class="model">LLaMA 3 8B</span>
                                <span class="use-case">Fast, simple tasks</span>
                            </div>
                            <div class="feature-item">
                                <span class="tier">Tier 2</span>
                                <span class="model">Mixtral 8x7B</span>
                                <span class="use-case">Complex reasoning</span>
                            </div>
                            <div class="feature-item">
                                <span class="tier">Tier 3</span>
                                <span class="model">LLaMA 3 70B</span>
                                <span class="use-case">Deep analysis</span>
                            </div>
                            <div class="feature-item">
                                <span class="tier">Tier 4</span>
                                <span class="model">GPT-4 Turbo</span>
                                <span class="use-case">Premium quality</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="feature-detailed">
                    <div class="feature-content">
                        <h3 class="feature-title">
                            <i class="fas fa-sliders-h"></i>
                            Intelligent Tuning System
                        </h3>
                        <p class="feature-description">
                            Configure and optimize CodeCrusher's behavior for your specific needs.
                        </p>
                        <div class="code-block">
                            <div class="code-header">
                                <span class="code-lang">bash</span>
                                <button class="copy-btn" data-copy="codecrusher tune --set prompt_style=comprehensive">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <pre><code># Configure prompt style
codecrusher tune --set prompt_style=comprehensive

# Set verbosity level
codecrusher tune --set verbosity=high

# Adjust fallback sensitivity
codecrusher tune --set fallback_sensitivity=0.5

# Apply preset configuration
codecrusher tune --preset enterprise</code></pre>
                        </div>
                    </div>
                </div>
                <div class="feature-detailed">
                    <div class="feature-content">
                        <h3 class="feature-title">
                            <i class="fas fa-code"></i>
                            Language Support
                        </h3>
                        <p class="feature-description">
                            Support for multiple programming languages with intelligent tag detection.
                        </p>
                        <div class="language-examples">
                            <div class="language-example">
                                <h4>Python</h4>
                                <div class="code-inline">
                                    <code># AI_INJECT:function_name</code>
                                </div>
                            </div>
                            <div class="language-example">
                                <h4>JavaScript</h4>
                                <div class="code-inline">
                                    <code>// AI_INJECT:function_name</code>
                                </div>
                            </div>
                            <div class="language-example">
                                <h4>Java/C++</h4>
                                <div class="code-inline">
                                    <code>// AI_INJECT:function_name</code>
                                </div>
                            </div>
                            <div class="language-example">
                                <h4>HTML</h4>
                                <div class="code-inline">
                                    <code>&lt;!-- AI_INJECT:component_name --&gt;</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Architecture Section -->
    <section id="architecture" class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">System Architecture</h2>
                <p class="section-subtitle">Understanding CodeCrusher's intelligent design</p>
            </div>
            <div class="architecture-diagram">
                <div class="arch-layer">
                    <h3 class="layer-title">CLI Interface</h3>
                    <div class="layer-components">
                        <div class="component">codecrusher_cli.py</div>
                    </div>
                </div>
                <div class="arch-arrow">↓</div>
                <div class="arch-layer">
                    <h3 class="layer-title">Core Engine</h3>
                    <div class="layer-components">
                        <div class="component">AI Injector</div>
                        <div class="component">Intelligence System</div>
                        <div class="component">Model Router</div>
                        <div class="component">Quality Scorer</div>
                    </div>
                </div>
                <div class="arch-arrow">↓</div>
                <div class="arch-layer">
                    <h3 class="layer-title">AI Providers</h3>
                    <div class="layer-components">
                        <div class="component">Groq API</div>
                        <div class="component">OpenAI API</div>
                        <div class="component">Mistral API</div>
                    </div>
                </div>
                <div class="arch-arrow">↓</div>
                <div class="arch-layer">
                    <h3 class="layer-title">Data Layer</h3>
                    <div class="layer-components">
                        <div class="component">SQLite Database</div>
                        <div class="component">File Cache</div>
                        <div class="component">Configuration</div>
                    </div>
                </div>
            </div>
            <div class="architecture-features">
                <div class="arch-feature">
                    <h4>Self-Improving Intelligence</h4>
                    <p>Learns from user feedback and automatically adjusts parameters for better code quality.</p>
                </div>
                <div class="arch-feature">
                    <h4>Intelligent Fallback</h4>
                    <p>Automatic escalation to better models when quality thresholds aren't met.</p>
                </div>
                <div class="arch-feature">
                    <h4>Quality Assurance</h4>
                    <p>Comprehensive scoring system with 100-point quality assessment.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contributing Section -->
    <section id="contributing" class="section section-alt">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Contributing</h2>
                <p class="section-subtitle">Help make CodeCrusher even better</p>
            </div>
            <div class="contributing-grid">
                <div class="contrib-card">
                    <div class="contrib-icon">
                        <i class="fas fa-code-branch"></i>
                    </div>
                    <h3 class="contrib-title">Development Setup</h3>
                    <div class="code-block">
                        <div class="code-header">
                            <span class="code-lang">bash</span>
                            <button class="copy-btn" data-copy="git clone https://github.com/Codegx-Technology/CodeCruncher.git">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <pre><code># Clone repository
git clone https://github.com/Codegx-Technology/CodeCruncher.git
cd CodeCruncher

# Install development dependencies
pip install -r requirements-dev.txt

# Install in editable mode
pip install -e .</code></pre>
                    </div>
                </div>
                <div class="contrib-card">
                    <div class="contrib-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                    <h3 class="contrib-title">Bug Reports</h3>
                    <p class="contrib-description">
                        Found a bug? Help us fix it by creating a detailed bug report with reproduction steps.
                    </p>
                    <a href="https://github.com/Codegx-Technology/CodeCruncher/issues/new" class="btn btn-secondary" target="_blank">
                        Report Bug
                    </a>
                </div>
                <div class="contrib-card">
                    <div class="contrib-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h3 class="contrib-title">Feature Requests</h3>
                    <p class="contrib-description">
                        Have an idea for improvement? Share your feature request and help shape the future of CodeCrusher.
                    </p>
                    <a href="https://github.com/Codegx-Technology/CodeCruncher/issues/new" class="btn btn-secondary" target="_blank">
                        Request Feature
                    </a>
                </div>
            </div>
            <div class="contrib-guidelines">
                <h3 class="guidelines-title">Contribution Guidelines</h3>
                <div class="guidelines-grid">
                    <div class="guideline">
                        <h4>Code Style</h4>
                        <p>Follow PEP 8 with Black formatting, include type hints and comprehensive docstrings.</p>
                    </div>
                    <div class="guideline">
                        <h4>Testing</h4>
                        <p>Maintain 80%+ test coverage with unit, integration, and E2E tests for new features.</p>
                    </div>
                    <div class="guideline">
                        <h4>Documentation</h4>
                        <p>Update documentation for new features and include clear examples and usage instructions.</p>
                    </div>
                    <div class="guideline">
                        <h4>Pull Requests</h4>
                        <p>Create focused PRs with clear descriptions, link related issues, and ensure CI passes.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <img src="assets/logo.png" alt="CodeCrusher Logo" class="footer-logo" onerror="this.style.display='none'">
                        <span class="footer-brand-text">CodeCrusher</span>
                    </div>
                    <p class="footer-description">
                        Self-improving AI code injection that gets better with every use.
                    </p>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Documentation</h4>
                    <ul class="footer-links">
                        <li><a href="#installation">Installation</a></li>
                        <li><a href="#usage">Usage Guide</a></li>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#architecture">Architecture</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Community</h4>
                    <ul class="footer-links">
                        <li><a href="https://github.com/Codegx-Technology/CodeCruncher" target="_blank">GitHub</a></li>
                        <li><a href="https://github.com/Codegx-Technology/CodeCruncher/issues" target="_blank">Issues</a></li>
                        <li><a href="#contributing">Contributing</a></li>
                        <li><a href="https://github.com/Codegx-Technology/CodeCruncher/releases" target="_blank">Releases</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">Resources</h4>
                    <ul class="footer-links">
                        <li><a href="https://pypi.org/project/codecrusher/" target="_blank">PyPI Package</a></li>
                        <li><a href="#" target="_blank">API Reference</a></li>
                        <li><a href="#" target="_blank">Examples</a></li>
                        <li><a href="#" target="_blank">Blog</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p class="footer-copyright">
                    © 2025 CodeCrusher Team. Licensed under MIT License.
                </p>
                <div class="footer-social">
                    <a href="https://github.com/Codegx-Technology/CodeCruncher" target="_blank" class="social-link">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="js/main.js"></script>
</body>
</html>
