# Setting Up API Keys for CodeCrusher

This guide explains how to set up API keys for CodeCrusher to ensure proper functioning of the AI model routing and fallback mechanisms.

## Problem: Model Race Failure

If you encounter the following error:

```
❌ Problem Summary
Model Race Failed
All models in the auto-routing race either:
- Didn't respond,
- Timed out,
- Or gave poor-quality output.

Fallback to Mistral Triggered, but...
❌ MISTRAL_API_KEY not found in environment variables
```

This means that the API keys are not being properly loaded by the application.

## Solution

### 1. Set the MISTRAL_API_KEY

Your `.env` file or environment needs to include the Mistral API key. Create or edit a `.env` file in the root directory of the project:

```
MISTRAL_API_KEY=sk-your-real-key-here
```

If you're using PowerShell, you can set it like this:

```powershell
$env:MISTRAL_API_KEY="sk-your-real-key-here"
```

### 2. Set the GROQ_API_KEY

Similarly, you need to set the GROQ API key:

```
GROQ_API_KEY=gsk-your-real-key-here
```

In PowerShell:

```powershell
$env:GROQ_API_KEY="gsk-your-real-key-here"
```

### 3. Verify Your API Keys

You can verify that your API keys are correctly set up by running the included test script:

```bash
python test_env_loading.py
```

This script will check if the environment variables are loaded correctly and if the API keys are valid.

### 4. Run CodeCrusher

Once your API keys are set up, you can run CodeCrusher with auto-model routing:

```bash
codecrusher --source example.py --prompt-text "#REFACTOR" --ai --auto-model-routing --refresh-cache --preview
```

### 5. Force Mistral (for testing)

If you're testing and just want to skip model racing, you can force the use of Mistral:

```bash
codecrusher --source example.py --prompt-text "#REFACTOR" --ai --model mistral-fallback --refresh-cache --preview
```

## Troubleshooting

### Check Your .env File

Make sure your `.env` file is in the correct location (root directory of the project) and has the correct format:

```
MISTRAL_API_KEY=sk-your-real-key-here
GROQ_API_KEY=gsk-your-real-key-here
```

### Check Your Example File

Make sure your example.py file has a tag like:

```python
# AI_INJECT: hello_world
def hello_world():
    pass
```

### Check API Key Validity

You can check if your API keys are valid by running:

```bash
python test_env_loading.py
```

If the keys are invalid, you'll need to obtain new ones from the respective providers.

## Getting API Keys

- **Mistral AI**: Sign up at [https://console.mistral.ai/](https://console.mistral.ai/) to get your API key.
- **Groq**: Sign up at [https://console.groq.com/](https://console.groq.com/) to get your API key.

Both services offer free tiers that you can use for testing.
