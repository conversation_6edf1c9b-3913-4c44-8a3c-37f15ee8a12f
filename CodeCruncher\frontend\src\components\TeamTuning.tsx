import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Settings,
  Sliders,
  Brain,
  Palette,
  RefreshCw,
  Lock,
  Unlock,
  Eye,
  Save,
  RotateCcw,
  TrendingUp,
  Zap,
  Shield,
  AlertCircle,
  CheckCircle,
  GripVertical
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthenticatedAPI } from '@/hooks/useAuthenticatedFetch';
import { useToast } from '@/components/ToastNotifications';
import { useTeamPermissions, getRoleInfo, getRestrictedMessage } from '@/hooks/useTeamPermissions';

interface TeamTuningConfig {
  fallback_sensitivity: number;
  model_order: string[];
  tone_style: string;
  escalation_strategy: string;
  auto_apply_threshold: number;
  retry_attempts: number;
  admin_only: boolean;
  last_updated_by: string;
  last_updated_at: string;
}

interface TeamTuningProps {
  teamId: number;
  userRole: string;
}

const AVAILABLE_MODELS = [
  { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', tier: 'premium' },
  { id: 'mixtral-8x7b', name: 'Mixtral 8x7B', tier: 'advanced' },
  { id: 'llama3-70b', name: 'LLaMA 3 70B', tier: 'advanced' },
  { id: 'llama3-8b', name: 'LLaMA 3 8B', tier: 'standard' },
  { id: 'gemma-7b', name: 'Gemma 7B', tier: 'standard' }
];

const TONE_STYLES = [
  { id: 'professional', name: 'Professional', description: 'Formal, clear, and concise' },
  { id: 'friendly', name: 'Friendly', description: 'Approachable and conversational' },
  { id: 'technical', name: 'Technical', description: 'Detailed and precise' },
  { id: 'creative', name: 'Creative', description: 'Innovative and expressive' },
  { id: 'aggressive', name: 'Aggressive', description: 'Bold and assertive optimizations' }
];

const ESCALATION_STRATEGIES = [
  { id: 'conservative', name: 'Conservative', description: 'Careful escalation, prefer quality' },
  { id: 'balanced', name: 'Balanced', description: 'Standard escalation strategy' },
  { id: 'aggressive', name: 'Aggressive', description: 'Quick escalation, prefer speed' }
];

export function TeamTuning({ teamId, userRole }: TeamTuningProps) {
  const { user } = useAuth();
  const api = useAuthenticatedAPI();
  const { addToast } = useToast();

  const [config, setConfig] = useState<TeamTuningConfig>({
    fallback_sensitivity: 0.7,
    model_order: ['mixtral-8x7b', 'llama3-70b', 'llama3-8b'],
    tone_style: 'professional',
    escalation_strategy: 'balanced',
    auto_apply_threshold: 0.85,
    retry_attempts: 3,
    admin_only: false,
    last_updated_by: '',
    last_updated_at: ''
  });

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [originalConfig, setOriginalConfig] = useState<TeamTuningConfig | null>(null);

  // Use permissions hook
  const permissions = useTeamPermissions({
    userRole,
    teamAdminOnly: config.admin_only
  });

  const roleInfo = getRoleInfo(userRole);
  const canEdit = permissions.canEditTuning;

  useEffect(() => {
    loadTuningConfig();
  }, [teamId]);

  const loadTuningConfig = async () => {
    try {
      setLoading(true);
      const data = await api.get(`/teams/${teamId}/tuning`);
      setConfig(data);
      setOriginalConfig(data);
      setHasChanges(false);
    } catch (err) {
      setError('Failed to load tuning configuration');
      console.error('Load tuning config error:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = (updates: Partial<TeamTuningConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    setHasChanges(JSON.stringify(newConfig) !== JSON.stringify(originalConfig));
  };

  const handleSave = async () => {
    if (!canEdit) return;

    try {
      setSaving(true);
      await api.post(`/teams/${teamId}/tuning`, config);

      setOriginalConfig(config);
      setHasChanges(false);

      addToast({
        type: 'success',
        title: 'Tuning Saved',
        message: 'Team prompt tuning preferences updated successfully'
      });
    } catch (err) {
      setError('Failed to save tuning configuration');
      console.error('Save tuning config error:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (originalConfig) {
      setConfig(originalConfig);
      setHasChanges(false);
    }
  };

  const moveModel = (fromIndex: number, toIndex: number) => {
    if (!canEdit) return;

    const newOrder = [...config.model_order];
    const [moved] = newOrder.splice(fromIndex, 1);
    newOrder.splice(toIndex, 0, moved);
    updateConfig({ model_order: newOrder });
  };

  const getModelTier = (modelId: string) => {
    return AVAILABLE_MODELS.find(m => m.id === modelId)?.tier || 'standard';
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'premium': return 'bg-purple-100 text-purple-800 border-purple-300';
      case 'advanced': return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'standard': return 'bg-gray-100 text-gray-800 border-gray-300';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading tuning preferences...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Settings className="h-6 w-6" />
            Prompt Tuning
          </h2>
          <p className="text-gray-600">Configure team-wide AI behavior and preferences</p>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline" className={`flex items-center gap-1 ${roleInfo.color}`}>
            <span>{roleInfo.icon}</span>
            {roleInfo.label}
          </Badge>

          {config.admin_only && !permissions.canEditTuning && (
            <Badge variant="outline" className="flex items-center gap-1 text-red-600 bg-red-50 border-red-200">
              <Lock className="h-3 w-3" />
              Admin Only
            </Badge>
          )}

          {permissions.isReadOnly && (
            <Badge variant="outline" className="flex items-center gap-1 text-gray-600 bg-gray-50 border-gray-200">
              <Eye className="h-3 w-3" />
              Read Only
            </Badge>
          )}

          {hasChanges && (
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleReset} size="sm">
                <RotateCcw className="h-4 w-4 mr-1" />
                Reset
              </Button>
              <Button onClick={handleSave} disabled={saving || !canEdit} size="sm">
                {saving ? (
                  <RefreshCw className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-1" />
                )}
                Save Changes
              </Button>
            </div>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {!canEdit && (
        <Alert>
          <Lock className="h-4 w-4" />
          <AlertDescription>
            {getRestrictedMessage('editTuning', userRole)}
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="behavior" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="behavior" className="flex items-center gap-2">
            <Sliders className="h-4 w-4" />
            Behavior
          </TabsTrigger>
          <TabsTrigger value="models" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Models
          </TabsTrigger>
          <TabsTrigger value="style" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Style
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Advanced
          </TabsTrigger>
        </TabsList>

        {/* Behavior Tab */}
        <TabsContent value="behavior" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Fallback Sensitivity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Sensitivity Level: {(config.fallback_sensitivity * 100).toFixed(0)}%</Label>
                <div className="mt-2">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={config.fallback_sensitivity}
                    onChange={(e) => updateConfig({ fallback_sensitivity: parseFloat(e.target.value) })}
                    disabled={!canEdit}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Strict</span>
                    <span>Balanced</span>
                    <span>Flexible</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Higher sensitivity means more aggressive fallback to alternative models when quality is low.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Auto-Apply Threshold
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Quality Threshold: {(config.auto_apply_threshold * 100).toFixed(0)}%</Label>
                <div className="mt-2">
                  <input
                    type="range"
                    min="0.5"
                    max="1"
                    step="0.05"
                    value={config.auto_apply_threshold}
                    onChange={(e) => updateConfig({ auto_apply_threshold: parseFloat(e.target.value) })}
                    disabled={!canEdit}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Conservative</span>
                    <span>Standard</span>
                    <span>Aggressive</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Minimum quality score required for automatic application of changes.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Models Tab */}
        <TabsContent value="models" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                Model Preference Order
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Drag to reorder. CodeCrusher will try models in this order when escalating.
              </p>

              <div className="space-y-2">
                {config.model_order.map((modelId, index) => {
                  const model = AVAILABLE_MODELS.find(m => m.id === modelId);
                  if (!model) return null;

                  return (
                    <div
                      key={modelId}
                      className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-500">#{index + 1}</span>
                        {canEdit && (
                          <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                        )}
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{model.name}</span>
                          <Badge className={getTierColor(model.tier)}>
                            {model.tier}
                          </Badge>
                        </div>
                      </div>

                      {canEdit && index > 0 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => moveModel(index, index - 1)}
                        >
                          ↑
                        </Button>
                      )}

                      {canEdit && index < config.model_order.length - 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => moveModel(index, index + 1)}
                        >
                          ↓
                        </Button>
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Style Tab */}
        <TabsContent value="style" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Tone & Style Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Communication Style</Label>
                <div className="grid grid-cols-1 gap-3 mt-2">
                  {TONE_STYLES.map((style) => (
                    <label
                      key={style.id}
                      className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                        config.tone_style === style.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      } ${!canEdit ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <input
                        type="radio"
                        name="tone_style"
                        value={style.id}
                        checked={config.tone_style === style.id}
                        onChange={(e) => updateConfig({ tone_style: e.target.value })}
                        disabled={!canEdit}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium">{style.name}</div>
                        <div className="text-sm text-gray-600">{style.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Tab */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Escalation Strategy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Strategy</Label>
                <div className="grid grid-cols-1 gap-3 mt-2">
                  {ESCALATION_STRATEGIES.map((strategy) => (
                    <label
                      key={strategy.id}
                      className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                        config.escalation_strategy === strategy.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      } ${!canEdit ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <input
                        type="radio"
                        name="escalation_strategy"
                        value={strategy.id}
                        checked={config.escalation_strategy === strategy.id}
                        onChange={(e) => updateConfig({ escalation_strategy: e.target.value })}
                        disabled={!canEdit}
                        className="mr-3"
                      />
                      <div>
                        <div className="font-medium">{strategy.name}</div>
                        <div className="text-sm text-gray-600">{strategy.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <Label>Retry Attempts: {config.retry_attempts}</Label>
                <div className="mt-2">
                  <input
                    type="range"
                    min="1"
                    max="5"
                    step="1"
                    value={config.retry_attempts}
                    onChange={(e) => updateConfig({ retry_attempts: parseInt(e.target.value) })}
                    disabled={!canEdit}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>1</span>
                    <span>3</span>
                    <span>5</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  Number of retry attempts before escalating to next model.
                </p>
              </div>

              {isAdmin && (
                <div className="pt-4 border-t">
                  <label className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      checked={config.admin_only}
                      onChange={(e) => updateConfig({ admin_only: e.target.checked })}
                      className="rounded"
                    />
                    <div>
                      <div className="font-medium">Admin Only Settings</div>
                      <div className="text-sm text-gray-600">
                        Only team owners can modify tuning preferences
                      </div>
                    </div>
                  </label>
                </div>
              )}
            </CardContent>
          </Card>

          {config.last_updated_by && (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Last updated by {config.last_updated_by} on{' '}
                  {new Date(config.last_updated_at).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
