import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, AlertTriangle } from 'lucide-react';

export default function StyleTest() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-center text-blue-600 mb-8">
          Style Test Dashboard
        </h1>

        {/* Basic Tailwind Classes */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl text-gray-800">Basic Tailwind Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-500 text-white p-4 rounded-lg">
              Blue background with white text
            </div>
            <div className="bg-green-500 text-white p-4 rounded-lg">
              Green background with white text
            </div>
            <div className="bg-red-500 text-white p-4 rounded-lg">
              Red background with white text
            </div>
          </CardContent>
        </Card>

        {/* UI Components Test */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl text-gray-800">UI Components Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-4 flex-wrap">
              <Button variant="default">Default Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="destructive">Destructive Button</Button>
              <Button variant="secondary">Secondary Button</Button>
            </div>

            <div className="flex gap-2 flex-wrap">
              <Badge variant="default">Default Badge</Badge>
              <Badge variant="secondary">Secondary Badge</Badge>
              <Badge variant="destructive">Destructive Badge</Badge>
              <Badge variant="outline">Outline Badge</Badge>
            </div>

            <Progress value={75} className="w-full" />

            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                This is a success alert with an icon.
              </AlertDescription>
            </Alert>

            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This is a destructive alert with an icon.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* CSS Variables Test */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl text-gray-800">CSS Variables Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-primary text-primary-foreground p-4 rounded-lg">
              Primary background with primary foreground text
            </div>
            <div className="bg-secondary text-secondary-foreground p-4 rounded-lg">
              Secondary background with secondary foreground text
            </div>
            <div className="bg-muted text-muted-foreground p-4 rounded-lg">
              Muted background with muted foreground text
            </div>
            <div className="bg-accent text-accent-foreground p-4 rounded-lg">
              Accent background with accent foreground text
            </div>
          </CardContent>
        </Card>

        {/* Gradient Test */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl text-gray-800">Gradient Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
              Blue to Purple Gradient
            </div>
            <div className="bg-gradient-to-r from-green-400 to-blue-500 text-white p-4 rounded-lg">
              Green to Blue Gradient
            </div>
            <div className="bg-gradient-to-br from-pink-500 via-red-500 to-yellow-500 text-white p-4 rounded-lg">
              Multi-color Gradient
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
