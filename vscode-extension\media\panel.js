// Get VS Code API
const vscode = acquireVsCodeApi();

// DOM elements
const loadingElement = document.getElementById('loading');
const serverErrorElement = document.getElementById('server-error');
const contentElement = document.getElementById('content');
const refreshButton = document.getElementById('refresh-btn');
const scanButton = document.getElementById('scan-btn');
const trendsButton = document.getElementById('trends-btn');
const refreshServerButton = document.getElementById('refresh-button');
const modelsContainer = document.getElementById('models-container');
const cacheStatsElement = document.getElementById('cache-stats');
const anomaliesContainer = document.getElementById('anomalies-container');
const trendsContainer = document.getElementById('trends-container');

// Event listeners
if (refreshButton) {
    refreshButton.addEventListener('click', () => {
        vscode.postMessage({ command: 'refresh' });
        showLoading();
    });
}

if (scanButton) {
    scanButton.addEventListener('click', () => {
        vscode.postMessage({ command: 'rescan' });
        showLoading();
    });
}

if (trendsButton) {
    trendsButton.addEventListener('click', () => {
        vscode.postMessage({ command: 'openTrends' });
    });
}

if (refreshServerButton) {
    refreshServerButton.addEventListener('click', () => {
        vscode.postMessage({ command: 'refresh' });
        showLoading();
    });
}

// Show loading state
function showLoading() {
    if (loadingElement) loadingElement.classList.remove('hidden');
    if (contentElement) contentElement.classList.add('hidden');
    if (serverErrorElement) serverErrorElement.classList.add('hidden');
}

// Show server error
function showServerError() {
    if (loadingElement) loadingElement.classList.add('hidden');
    if (contentElement) contentElement.classList.add('hidden');
    if (serverErrorElement) serverErrorElement.classList.remove('hidden');
}

// Show content
function showContent() {
    if (loadingElement) loadingElement.classList.add('hidden');
    if (contentElement) contentElement.classList.remove('hidden');
    if (serverErrorElement) serverErrorElement.classList.add('hidden');
}

// Update models display
function updateModels(models) {
    if (!modelsContainer) return;
    
    modelsContainer.innerHTML = '';
    
    for (const [model, stats] of Object.entries(models)) {
        const modelElement = document.createElement('div');
        modelElement.className = 'model-item';
        
        // Determine status color based on success rate
        let statusColor = 'green';
        if (stats.success_rate < 90) {
            statusColor = 'red';
        } else if (stats.success_rate < 95) {
            statusColor = 'orange';
        }
        
        modelElement.innerHTML = `
            <div class="model-name">${model}</div>
            <div class="model-stats">
                <div class="model-count">${stats.count} calls</div>
                <div class="model-status" style="color: ${statusColor};">${stats.success_rate}%</div>
            </div>
        `;
        
        modelsContainer.appendChild(modelElement);
    }
}

// Update cache stats
function updateCacheStats(cacheStats) {
    if (!cacheStatsElement) return;
    
    cacheStatsElement.innerHTML = `
        <div class="stat-item">
            <div class="stat-label">Hit Rate</div>
            <div class="stat-value">${cacheStats.hit_rate}%</div>
        </div>
        <div class="stat-item">
            <div class="stat-label">Hits</div>
            <div class="stat-value">${cacheStats.hits}</div>
        </div>
        <div class="stat-item">
            <div class="stat-label">Misses</div>
            <div class="stat-value">${cacheStats.misses}</div>
        </div>
    `;
}

// Update anomalies
function updateAnomalies(anomalies) {
    if (!anomaliesContainer) return;
    
    if (anomalies.length === 0) {
        anomaliesContainer.innerHTML = '<div class="no-anomalies">No anomalies detected</div>';
        return;
    }
    
    anomaliesContainer.innerHTML = '';
    
    // Show only the first 3 anomalies
    const displayAnomalies = anomalies.slice(0, 3);
    
    for (const anomaly of displayAnomalies) {
        const anomalyElement = document.createElement('div');
        anomalyElement.className = 'anomaly-item';
        anomalyElement.innerHTML = anomaly;
        anomaliesContainer.appendChild(anomalyElement);
    }
    
    // Show count if there are more
    if (anomalies.length > 3) {
        const moreElement = document.createElement('div');
        moreElement.className = 'more-anomalies';
        moreElement.textContent = `+ ${anomalies.length - 3} more anomalies`;
        anomaliesContainer.appendChild(moreElement);
    }
}

// Update trends
function updateTrends(trends) {
    if (!trendsContainer) return;
    
    // Get the last 7 days of data
    const metrics = trends.metrics;
    const days = Object.keys(metrics).sort().slice(-7);
    
    if (days.length === 0) {
        trendsContainer.innerHTML = '<div class="no-trends">No trend data available</div>';
        return;
    }
    
    // Calculate totals
    let totalInjections = 0;
    let totalFallbacks = 0;
    const modelCounts = {};
    
    for (const day of days) {
        const dayMetrics = metrics[day];
        totalInjections += dayMetrics.total_injections;
        totalFallbacks += dayMetrics.fallbacks;
        
        // Count models
        for (const [model, count] of Object.entries(dayMetrics.models || {})) {
            modelCounts[model] = (modelCounts[model] || 0) + count;
        }
    }
    
    // Sort models by usage
    const sortedModels = Object.entries(modelCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3);
    
    // Create trends summary
    trendsContainer.innerHTML = `
        <div class="trend-item">
            <div class="trend-label">Injections (7d)</div>
            <div class="trend-value">${totalInjections}</div>
        </div>
        <div class="trend-item">
            <div class="trend-label">Fallback Rate</div>
            <div class="trend-value">${totalInjections > 0 ? ((totalFallbacks / totalInjections) * 100).toFixed(1) : 0}%</div>
        </div>
        <div class="trend-item">
            <div class="trend-label">Top Models</div>
            <div class="trend-models">
                ${sortedModels.map(([model, count]) => `
                    <div class="trend-model">
                        <span class="model-name">${model}</span>
                        <span class="model-count">${count}</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Handle messages from extension
window.addEventListener('message', event => {
    const message = event.data;
    
    switch (message.type) {
        case 'refresh':
            const { status, scan, trends } = message.data;
            updateModels(status.models_used);
            updateCacheStats(status.cache_stats);
            updateAnomalies(status.anomalies);
            updateTrends(trends);
            showContent();
            break;
            
        case 'serverStatus':
            if (!message.running) {
                showServerError();
            }
            break;
            
        case 'scanning':
            if (message.scanning) {
                showLoading();
            }
            break;
            
        case 'scanComplete':
            updateAnomalies(message.data.anomalies);
            showContent();
            break;
            
        case 'error':
            // Show error message
            vscode.postMessage({ 
                command: 'showError', 
                message: message.message 
            });
            showServerError();
            break;
    }
});
