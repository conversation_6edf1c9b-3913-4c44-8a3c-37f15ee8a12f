#!/bin/bash
# CodeCrusher Release Script
# Alternative shell-based publishing automation

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  [$(date +'%H:%M:%S')] $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ [$(date +'%H:%M:%S')] $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  [$(date +'%H:%M:%S')] $1${NC}"
}

log_error() {
    echo -e "${RED}❌ [$(date +'%H:%M:%S')] $1${NC}"
}

# Help function
show_help() {
    cat << EOF
${BOLD}CodeCrusher Release Script${NC}

Usage: $0 [OPTIONS] VERSION_TYPE

VERSION_TYPE:
    patch       Bump patch version (x.y.Z)
    minor       Bump minor version (x.Y.z)
    major       Bump major version (X.y.z)

OPTIONS:
    --test      Publish to TestPyPI instead of PyPI
    --tag       Create and push git tag
    --dry-run   Show what would happen without making changes
    --help      Show this help message

Examples:
    $0 patch                    # Bump patch and publish to PyPI
    $0 minor --test             # Bump minor and publish to TestPyPI
    $0 major --tag              # Bump major, publish, and create git tag
    $0 patch --dry-run          # Show what would happen

Requirements:
    - Python 3.8+
    - twine (pip install twine)
    - setuptools and wheel (pip install setuptools wheel)
    - Git (for tagging)

EOF
}

# Check requirements
check_requirements() {
    log_info "Checking requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check if we're in the right directory
    if [[ ! -f "setup.py" ]]; then
        log_error "setup.py not found. Run this script from the project root."
        exit 1
    fi
    
    # Check twine
    if ! python3 -c "import twine" &> /dev/null; then
        log_warning "twine not found. Install with: pip install twine"
        if [[ "$DRY_RUN" != "true" ]]; then
            exit 1
        fi
    fi
    
    log_success "Requirements check passed"
}

# Parse arguments
parse_args() {
    VERSION_TYPE=""
    TEST_PYPI=false
    CREATE_TAG=false
    DRY_RUN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            patch|minor|major)
                if [[ -n "$VERSION_TYPE" ]]; then
                    log_error "Multiple version types specified"
                    exit 1
                fi
                VERSION_TYPE="$1"
                shift
                ;;
            --test)
                TEST_PYPI=true
                shift
                ;;
            --tag)
                CREATE_TAG=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    if [[ -z "$VERSION_TYPE" ]]; then
        log_error "Version type is required"
        show_help
        exit 1
    fi
}

# Main execution
main() {
    echo -e "${BOLD}🚀 CodeCrusher Release Script${NC}"
    echo
    
    parse_args "$@"
    check_requirements
    
    # Delegate to Python script
    PYTHON_ARGS="--$VERSION_TYPE"
    
    if [[ "$TEST_PYPI" == "true" ]]; then
        PYTHON_ARGS="$PYTHON_ARGS --test"
    fi
    
    if [[ "$CREATE_TAG" == "true" ]]; then
        PYTHON_ARGS="$PYTHON_ARGS --tag"
    fi
    
    if [[ "$DRY_RUN" == "true" ]]; then
        PYTHON_ARGS="$PYTHON_ARGS --dry-run"
    fi
    
    log_info "Executing: python3 publish.py $PYTHON_ARGS"
    python3 publish.py $PYTHON_ARGS
}

# Run main function with all arguments
main "$@"
