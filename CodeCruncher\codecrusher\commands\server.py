"""
Serve command for CodeCrusher.

This module provides the serve command for CodeCrusher, which starts a local
HTTP server to expose telemetry, injection metadata, and anomaly detection data
via a REST API for IDE plugins and CI/CD tools.
"""

import typer
import json
import os
import sys
import threading
import webbrowser
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict, Counter
import time
from rich.console import Console
from rich.panel import Panel
from fastapi import FastAPI, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Import telemetry logger
from codecrusher.telemetry_logger import get_telemetry_entries, TELEMETRY_FILE, TELEMETRY_DIR
from codecrusher.anomaly_engine import detect_anomalies
from codecrusher.commands.trends import parse_time_period as parse_trends_time_period
from codecrusher.commands.trends import filter_entries_by_date_range, filter_entries_by_model, filter_entries_by_tag
from codecrusher.commands.trends import group_entries_by_day, calculate_daily_metrics

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()

# Create FastAPI app
api = FastAPI(
    title="CodeCrusher API",
    description="REST API for CodeCrusher telemetry and injection info",
    version="0.1.0"
)

# Add CORS middleware
api.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Cache for telemetry data
_telemetry_cache = {}
_cache_timestamp = 0
_cache_lock = threading.Lock()

def get_cached_telemetry(force_refresh: bool = False) -> List[Dict[str, Any]]:
    """
    Get cached telemetry data.

    Args:
        force_refresh: Whether to force a refresh of the cache

    Returns:
        List[Dict[str, Any]]: Cached telemetry data
    """
    global _telemetry_cache, _cache_timestamp

    with _cache_lock:
        # Check if cache is valid
        if force_refresh or not _telemetry_cache or time.time() - _cache_timestamp > 60:
            # Refresh cache
            _telemetry_cache = get_telemetry_entries(limit=10000)
            _cache_timestamp = time.time()

        return _telemetry_cache

@api.get("/api/telemetry")
async def get_telemetry(
    limit: int = Query(100, description="Maximum number of entries to return"),
    since: Optional[str] = Query(None, description="Only return entries since this timestamp"),
    model: Optional[str] = Query(None, description="Filter by model"),
    tag: Optional[str] = Query(None, description="Filter by tag"),
    operation_type: Optional[str] = Query(None, description="Filter by operation type"),
    cached: Optional[bool] = Query(None, description="Filter by cached status"),
    fallback: Optional[bool] = Query(None, description="Filter by fallback status"),
    error: Optional[bool] = Query(None, description="Filter by error status"),
    refresh: bool = Query(False, description="Force refresh of cache")
):
    """Get telemetry data."""
    entries = get_cached_telemetry(force_refresh=refresh)

    # Apply filters
    if since:
        try:
            since_dt = datetime.fromisoformat(since)
            entries = [e for e in entries if datetime.fromisoformat(e.get("timestamp", "1970-01-01T00:00:00")) >= since_dt]
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid timestamp format")

    if model:
        entries = [e for e in entries if model.lower() in e.get("model", "").lower()]

    if tag:
        entries = [e for e in entries if tag in e.get("tags", [])]

    if operation_type:
        entries = [e for e in entries if operation_type == e.get("operation_type")]

    if cached is not None:
        entries = [e for e in entries if e.get("cached", False) == cached]

    if fallback is not None:
        entries = [e for e in entries if e.get("fallback", False) == fallback]

    if error is not None:
        entries = [e for e in entries if (e.get("error") is not None) == error]

    # Limit entries
    entries = entries[:limit]

    return {"entries": entries, "count": len(entries)}

@api.get("/api/status")
async def get_status(
    since: str = Query("24h", description="Time period to analyze (e.g., 24h, 7d)"),
    model: Optional[str] = Query(None, description="Filter by model"),
    refresh: bool = Query(False, description="Force refresh of cache")
):
    """Get CodeCrusher status."""
    entries = get_cached_telemetry(force_refresh=refresh)

    # Parse time period
    try:
        if since.endswith('h'):
            hours = int(since[:-1])
            since_dt = datetime.now() - timedelta(hours=hours)
        elif since.endswith('d'):
            days = int(since[:-1])
            since_dt = datetime.now() - timedelta(days=days)
        else:
            since_dt = datetime.now() - timedelta(hours=24)
    except ValueError:
        since_dt = datetime.now() - timedelta(hours=24)

    # Filter entries by time
    filtered_entries = [
        e for e in entries
        if datetime.fromisoformat(e.get("timestamp", "1970-01-01T00:00:00")) >= since_dt
    ]

    # Filter by model if specified
    if model:
        filtered_entries = [e for e in filtered_entries if model.lower() in e.get("model", "").lower()]

    # Calculate statistics
    models_used = {}
    for entry in filtered_entries:
        model_name = entry.get("model", "unknown")
        if model_name not in models_used:
            models_used[model_name] = {"count": 0, "errors": 0, "fallbacks": 0}

        models_used[model_name]["count"] += 1

        if entry.get("error"):
            models_used[model_name]["errors"] += 1

        if entry.get("fallback", False):
            models_used[model_name]["fallbacks"] += 1

    # Calculate cache statistics
    cache_hits = sum(1 for e in filtered_entries if e.get("cached", False))
    cache_misses = len(filtered_entries) - cache_hits
    cache_hit_rate = (cache_hits / len(filtered_entries) * 100) if filtered_entries else 0

    # Calculate fallback statistics
    fallback_count = sum(1 for e in filtered_entries if e.get("fallback", False))
    fallback_rate = (fallback_count / len(filtered_entries) * 100) if filtered_entries else 0

    # Get top tags
    tags = Counter()
    for entry in filtered_entries:
        for tag in entry.get("tags", []):
            tags[tag] += 1

    top_tags = dict(tags.most_common(10))

    # Detect anomalies
    anomalies = detect_anomalies(filtered_entries)

    return {
        "time_period": since,
        "entry_count": len(filtered_entries),
        "models_used": models_used,
        "cache_stats": {
            "hits": cache_hits,
            "misses": cache_misses,
            "hit_rate": cache_hit_rate
        },
        "fallback_stats": {
            "count": fallback_count,
            "rate": fallback_rate
        },
        "top_tags": top_tags,
        "anomalies": anomalies
    }

@api.get("/api/tags")
async def get_tags(
    refresh: bool = Query(False, description="Force refresh of cache")
):
    """Get all tags used in telemetry data."""
    entries = get_cached_telemetry(force_refresh=refresh)

    # Collect all tags
    tags = Counter()
    for entry in entries:
        for tag in entry.get("tags", []):
            tags[tag] += 1

    return {"tags": dict(tags)}

@api.get("/api/models")
async def get_models(
    refresh: bool = Query(False, description="Force refresh of cache")
):
    """Get all models used in telemetry data."""
    entries = get_cached_telemetry(force_refresh=refresh)

    # Collect all models
    models = Counter()
    for entry in entries:
        model = entry.get("model", "unknown")
        models[model] += 1

    return {"models": dict(models)}

@api.get("/api/anomalies")
async def get_anomalies(
    since: str = Query("24h", description="Time period to analyze (e.g., 24h, 7d)"),
    model: Optional[str] = Query(None, description="Filter by model"),
    tag: Optional[str] = Query(None, description="Filter by tag"),
    refresh: bool = Query(False, description="Force refresh of cache")
):
    """Get anomalies in telemetry data."""
    entries = get_cached_telemetry(force_refresh=refresh)

    # Parse time period
    try:
        if since.endswith('h'):
            hours = int(since[:-1])
            since_dt = timedelta(hours=hours)
        elif since.endswith('d'):
            days = int(since[:-1])
            since_dt = timedelta(days=days)
        else:
            since_dt = timedelta(hours=24)
    except ValueError:
        since_dt = timedelta(hours=24)

    # Parse tag filter
    tag_filter = [tag] if tag else None

    # Detect anomalies
    anomalies = detect_anomalies(entries, since=since_dt, model_filter=model, tag_filter=tag_filter)

    return {"anomalies": [a.__dict__ for a in anomalies]}

@api.get("/api/scan")
async def run_scan(
    since: str = Query("24h", description="Time period to analyze (e.g., 24h, 7d)"),
    model: Optional[str] = Query(None, description="Filter by model"),
    tag: Optional[str] = Query(None, description="Filter by tag"),
    limit: int = Query(10, description="Limit the number of anomalies to return"),
    verbose: bool = Query(False, description="Include suggested actions in the response"),
    refresh: bool = Query(False, description="Force refresh of cache")
):
    """Run anomaly detection and return a JSON report."""
    entries = get_cached_telemetry(force_refresh=refresh)

    # Parse time period
    try:
        if since.endswith('h'):
            hours = int(since[:-1])
            since_dt = timedelta(hours=hours)
        elif since.endswith('d'):
            days = int(since[:-1])
            since_dt = timedelta(days=days)
        else:
            since_dt = timedelta(hours=24)
    except ValueError:
        since_dt = timedelta(hours=24)

    # Format time period for display
    time_period = since

    # Parse tag filter
    tag_filter = [tag] if tag else None

    # Detect anomalies
    start_time = datetime.now()
    anomalies = detect_anomalies(
        entries=entries,
        since=since_dt,
        model_filter=model,
        tag_filter=tag_filter
    )
    end_time = datetime.now()
    scan_duration = (end_time - start_time).total_seconds()

    # Prepare anomalies data
    anomalies_data = []
    for anomaly in anomalies:
        anomaly_dict = {
            "type": anomaly.anomaly_type,
            "description": anomaly.description,
            "timestamp": anomaly.timestamp.isoformat(),
            "model": anomaly.model or "",
            "value": anomaly.value,
            "threshold": anomaly.threshold,
            "tags": anomaly.tags
        }
        if verbose:
            from codecrusher.anomaly_engine import get_suggested_action
            anomaly_dict["suggested_action"] = get_suggested_action(anomaly)
        anomalies_data.append(anomaly_dict)

    # Prepare response
    response = {
        "time_period": time_period,
        "model_filter": model or "All models",
        "tag_filter": tag or "All tags",
        "entries_analyzed": len(entries),
        "scan_duration_seconds": scan_duration,
        "anomalies_count": len(anomalies),
        "anomalies": anomalies_data[:limit]
    }

    return response

@api.get("/api/trends")
async def get_trends(
    since: str = Query("7d", description="Show data since this time period (e.g., 7d, 30d, 2023-01-01)"),
    until: str = Query("now", description="Show data until this time period (e.g., now, 1d, 2023-01-31)"),
    model: Optional[str] = Query(None, description="Filter by model"),
    tag: Optional[str] = Query(None, description="Filter by tag"),
    refresh: bool = Query(False, description="Force refresh of cache")
):
    """Get historical trend reports from telemetry data."""
    entries = get_cached_telemetry(force_refresh=refresh)

    # Parse time periods
    since_dt = parse_trends_time_period(since)
    until_dt = parse_trends_time_period(until)

    if not since_dt or not until_dt:
        raise HTTPException(status_code=400, detail="Invalid time period format")

    # Ensure since is before until
    if since_dt > until_dt:
        raise HTTPException(status_code=400, detail="Start date must be before end date")

    # Filter by date range
    entries = filter_entries_by_date_range(entries, since_dt, until_dt)

    # Filter by model if specified
    if model:
        entries = filter_entries_by_model(entries, model)

    # Filter by tag if specified
    if tag:
        entries = filter_entries_by_tag(entries, tag)

    # Check if we have data
    if not entries:
        return {
            "time_period": {
                "since": since_dt.isoformat() if since_dt else "",
                "until": until_dt.isoformat() if until_dt else "",
            },
            "filters": {
                "model": model or "all",
                "tag": tag or "all",
            },
            "metrics": {},
            "message": "No telemetry data found for the specified filters"
        }

    # Group entries by day
    grouped_entries = group_entries_by_day(entries)

    # Calculate daily metrics
    daily_metrics = calculate_daily_metrics(grouped_entries)

    # Prepare response
    response = {
        "time_period": {
            "since": since_dt.isoformat() if since_dt else "",
            "until": until_dt.isoformat() if until_dt else "",
        },
        "filters": {
            "model": model or "all",
            "tag": tag or "all",
        },
        "metrics": daily_metrics
    }

    return response

@app.command("run")
def run_serve(
    host: str = typer.Option("127.0.0.1", "--host", "-h", help="Host to bind to"),
    port: int = typer.Option(9000, "--port", "-p", help="Port to bind to"),
    open_browser: bool = typer.Option(True, "--open-browser/--no-open-browser", help="Open browser after starting server"),
    allow_remote: bool = typer.Option(False, "--allow-remote", help="Allow remote connections (binds to 0.0.0.0 instead of localhost)"),
):
    """
    Start a local HTTP server to expose telemetry, injection metadata, and anomaly detection data via a REST API.

    This command starts a FastAPI server that provides REST API endpoints for
    accessing telemetry data, status information, and anomaly detection for IDE plugins and CI/CD tools.

    Examples:
        codecrusher serve
        codecrusher serve --port 8080
        codecrusher serve --no-open-browser
        codecrusher serve --allow-remote
    """
    # Override host if allow_remote is set
    if allow_remote:
        host = "0.0.0.0"

    # Determine the URL to display and open in browser
    display_host = "localhost" if host == "0.0.0.0" or host == "127.0.0.1" else host

    console.print(Panel(
        f"[bold]CodeCrusher API Server[/bold]\n\n"
        f"[cyan]Host:[/cyan] {host}\n"
        f"[cyan]Port:[/cyan] {port}\n"
        f"[cyan]API Docs:[/cyan] http://{display_host}:{port}/docs\n"
        f"[cyan]Remote Access:[/cyan] {'Enabled' if allow_remote else 'Disabled'}\n",
        title="Server Configuration",
        border_style="blue"
    ))

    # Open browser if requested
    if open_browser:
        threading.Timer(1.0, lambda: webbrowser.open(f"http://{display_host}:{port}/docs")).start()

    # Log startup message
    console.print(f"[green]Server started at http://{display_host}:{port}[/green]")
    console.print("[yellow]Press CTRL+C to stop the server[/yellow]")

    # Start server
    uvicorn.run(api, host=host, port=port)

    return True
