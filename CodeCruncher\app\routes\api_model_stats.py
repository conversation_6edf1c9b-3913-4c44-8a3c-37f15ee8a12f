"""
Model Statistics API Routes for CodeCrusher

This module provides API endpoints for accessing model performance analytics,
leaderboards, and comparison data for different AI models.

Features:
- Model performance statistics endpoint
- Model leaderboard with ranking
- Model comparison functionality
- Analytics summary and insights
- Real-time performance tracking
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel, Field

from services.analytics import (
    analytics_service,
    get_model_stats,
    get_model_leaderboard,
    get_analytics_summary
)
from models.feedback import RatingType

logger = logging.getLogger(__name__)

router = APIRouter()

# Response models
class ModelStatsResponse(BaseModel):
    """Model statistics response model."""
    model: str
    total_feedback: int
    avg_rating: float
    satisfaction_rate: float
    success_rate: float
    avg_latency_ms: float
    min_latency_ms: int
    max_latency_ms: int
    avg_token_count: float
    retry_success_count: int
    retry_success_rate: float
    auto_fix_applied_count: int
    auto_fix_success_rate: float
    avg_prompt_quality_score: float
    avg_diff_precision_score: float
    avg_confidence_score: float
    performance_grade: str
    intent_distribution: Dict[str, int]
    positive_feedback_count: int
    negative_feedback_count: int
    neutral_feedback_count: int
    retry_saved_percentage: float  # New field for retry success on bad prompts

class LeaderboardResponse(BaseModel):
    """Model leaderboard response model."""
    rank: int
    model: str
    metric_value: float
    total_feedback: int
    satisfaction_rate: float
    performance_grade: str
    up_pct: float  # Percentage of upvotes
    meh_pct: float  # Percentage of neutral/meh votes
    down_pct: float  # Percentage of downvotes

class AnalyticsSummaryResponse(BaseModel):
    """Analytics summary response model."""
    total_feedback: int
    overall_satisfaction_rate: float
    positive_feedback_count: int
    negative_feedback_count: int
    best_performing_model: Optional[str]
    worst_performing_model: Optional[str]
    satisfaction_trend_7d: float
    total_models_tracked: int
    recent_feedback_count: int
    analytics_updated: str

class ModelComparisonResponse(BaseModel):
    """Model comparison response model."""
    model1: str
    model2: str
    satisfaction_rate_diff: float
    avg_rating_diff: float
    latency_diff_ms: float
    retry_success_diff: float
    winner: str
    comparison_timestamp: str

class TimeSeriesDataPoint(BaseModel):
    """Time series data point for charts."""
    date: str
    model: str
    avg_rating: float
    latency_ms: float
    satisfaction_rate: float
    total_feedback: int

class BestModelPredictionRequest(BaseModel):
    """Request model for best model prediction."""
    prompt: str

class BestModelPredictionResponse(BaseModel):
    """Response model for best model prediction."""
    best_model: str
    confidence: float
    reasoning: str
    alternatives: List[Dict[str, Any]]

@router.get("/api/model_stats", response_model=List[ModelStatsResponse])
async def get_model_statistics(
    days: int = Query(30, description="Number of days to include in analysis", ge=1, le=365)
):
    """
    Get model performance statistics.

    This endpoint provides comprehensive performance analytics for all AI models
    including ratings, latency, retry success, and quality metrics.

    Args:
        days: Number of days to include in the analysis (default: 30)

    Returns:
        List of model statistics

    Example:
        GET /api/model_stats?days=7
    """
    try:
        logger.info(f"Getting model statistics for {days} days")

        model_stats = get_model_stats(days)

        if not model_stats:
            return []

        # Convert to response format
        response_data = []
        for model_name, stats in model_stats.items():
            stats_dict = stats.to_dict()

            response_data.append(ModelStatsResponse(
                model=stats_dict["model"],
                total_feedback=stats_dict["total_feedback"],
                avg_rating=stats_dict["avg_rating"],
                satisfaction_rate=stats_dict["satisfaction_rate"],
                success_rate=stats_dict["success_rate"],
                avg_latency_ms=stats_dict["avg_latency_ms"],
                min_latency_ms=stats_dict["min_latency_ms"],
                max_latency_ms=stats_dict["max_latency_ms"],
                avg_token_count=stats_dict["avg_token_count"],
                retry_success_count=stats_dict["retry_success_count"],
                retry_success_rate=stats_dict["retry_success_rate"],
                auto_fix_applied_count=stats_dict["auto_fix_applied_count"],
                auto_fix_success_rate=stats_dict["auto_fix_success_rate"],
                avg_prompt_quality_score=stats_dict["avg_prompt_quality_score"],
                avg_diff_precision_score=stats_dict["avg_diff_precision_score"],
                avg_confidence_score=stats_dict["avg_confidence_score"],
                performance_grade=stats_dict["performance_grade"],
                intent_distribution=stats_dict["intent_distribution"],
                positive_feedback_count=stats_dict["positive_feedback_count"],
                negative_feedback_count=stats_dict["negative_feedback_count"],
                neutral_feedback_count=stats_dict["neutral_feedback_count"],
                retry_saved_percentage=stats_dict.get("retry_saved_percentage", 0.0)
            ))

        # Sort by satisfaction rate (descending)
        response_data.sort(key=lambda x: x.satisfaction_rate, reverse=True)

        logger.info(f"Returned statistics for {len(response_data)} models")
        return response_data

    except Exception as e:
        logger.error(f"Failed to get model statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get model statistics")

@router.get("/api/model_leaderboard", response_model=List[LeaderboardResponse])
async def get_model_leaderboard_endpoint(
    metric: str = Query("satisfaction_rate", description="Metric to rank by"),
    limit: int = Query(10, description="Maximum number of models to return", ge=1, le=50)
):
    """
    Get model leaderboard ranked by specified metric.

    This endpoint provides a ranked list of models based on various performance metrics.

    Args:
        metric: Metric to rank by (satisfaction_rate, avg_rating, success_rate, etc.)
        limit: Maximum number of models to return

    Returns:
        List of models ranked by the specified metric

    Example:
        GET /api/model_leaderboard?metric=satisfaction_rate&limit=5
    """
    try:
        logger.info(f"Getting model leaderboard by {metric}, limit {limit}")

        leaderboard = get_model_leaderboard(metric)

        if not leaderboard:
            return []

        # Apply limit
        leaderboard = leaderboard[:limit]

        # Convert to response format
        response_data = []
        for entry in leaderboard:
            # Calculate rating breakdown percentages
            total = entry["total_feedback"]
            if total > 0:
                up_pct = (entry.get("positive_feedback_count", 0) / total) * 100
                down_pct = (entry.get("negative_feedback_count", 0) / total) * 100
                meh_pct = (entry.get("neutral_feedback_count", 0) / total) * 100
            else:
                up_pct = meh_pct = down_pct = 0.0

            response_data.append(LeaderboardResponse(
                rank=entry["rank"],
                model=entry["model"],
                metric_value=entry.get(metric, 0),
                total_feedback=entry["total_feedback"],
                satisfaction_rate=entry["satisfaction_rate"],
                performance_grade=entry["performance_grade"],
                up_pct=round(up_pct, 1),
                meh_pct=round(meh_pct, 1),
                down_pct=round(down_pct, 1)
            ))

        logger.info(f"Returned leaderboard with {len(response_data)} models")
        return response_data

    except Exception as e:
        logger.error(f"Failed to get model leaderboard: {e}")
        raise HTTPException(status_code=500, detail="Failed to get model leaderboard")

@router.get("/api/analytics_summary", response_model=AnalyticsSummaryResponse)
async def get_analytics_summary_endpoint():
    """
    Get comprehensive analytics summary.

    This endpoint provides an overview of system-wide analytics including
    overall satisfaction rates, trends, and top performing models.

    Returns:
        Analytics summary with key metrics and insights

    Example:
        GET /api/analytics_summary
    """
    try:
        logger.info("Getting analytics summary")

        summary = get_analytics_summary()

        if not summary:
            # Return default summary if no data
            return AnalyticsSummaryResponse(
                total_feedback=0,
                overall_satisfaction_rate=0.0,
                positive_feedback_count=0,
                negative_feedback_count=0,
                best_performing_model=None,
                worst_performing_model=None,
                satisfaction_trend_7d=0.0,
                total_models_tracked=0,
                recent_feedback_count=0,
                analytics_updated=datetime.now().isoformat()
            )

        response = AnalyticsSummaryResponse(
            total_feedback=summary["total_feedback"],
            overall_satisfaction_rate=summary["overall_satisfaction_rate"],
            positive_feedback_count=summary["positive_feedback_count"],
            negative_feedback_count=summary["negative_feedback_count"],
            best_performing_model=summary["best_performing_model"],
            worst_performing_model=summary["worst_performing_model"],
            satisfaction_trend_7d=summary["satisfaction_trend_7d"],
            total_models_tracked=summary["total_models_tracked"],
            recent_feedback_count=summary["recent_feedback_count"],
            analytics_updated=summary["analytics_updated"]
        )

        logger.info("Returned analytics summary")
        return response

    except Exception as e:
        logger.error(f"Failed to get analytics summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get analytics summary")

@router.get("/api/model_comparison", response_model=ModelComparisonResponse)
async def compare_models(
    model1: str = Query(..., description="First model to compare"),
    model2: str = Query(..., description="Second model to compare")
):
    """
    Compare performance between two models.

    This endpoint provides a detailed comparison of performance metrics
    between two specified AI models.

    Args:
        model1: First model name
        model2: Second model name

    Returns:
        Comparison results between the two models

    Example:
        GET /api/model_comparison?model1=mixtral&model2=gpt-4
    """
    try:
        logger.info(f"Comparing models: {model1} vs {model2}")

        comparison = analytics_service.get_model_comparison(model1, model2)

        if "error" in comparison:
            raise HTTPException(status_code=404, detail=comparison["error"])

        response = ModelComparisonResponse(
            model1=comparison["model1"],
            model2=comparison["model2"],
            satisfaction_rate_diff=comparison["satisfaction_rate_diff"],
            avg_rating_diff=comparison["avg_rating_diff"],
            latency_diff_ms=comparison["latency_diff_ms"],
            retry_success_diff=comparison["retry_success_diff"],
            winner=comparison["winner"],
            comparison_timestamp=comparison["comparison_timestamp"]
        )

        logger.info(f"Model comparison completed: {comparison['winner']} wins")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to compare models: {e}")
        raise HTTPException(status_code=500, detail="Failed to compare models")

@router.get("/api/model_metrics")
async def get_available_metrics():
    """
    Get list of available metrics for analysis.

    This endpoint provides information about all available metrics
    that can be used for ranking and comparison.

    Returns:
        List of available metrics with descriptions
    """
    try:
        metrics = {
            "satisfaction_rate": {
                "description": "Percentage of positive feedback",
                "unit": "percentage",
                "higher_is_better": True
            },
            "avg_rating": {
                "description": "Average user rating score",
                "unit": "score",
                "range": "0.0 - 1.0",
                "higher_is_better": True
            },
            "success_rate": {
                "description": "Rate of successful injections",
                "unit": "percentage",
                "higher_is_better": True
            },
            "avg_latency_ms": {
                "description": "Average response time",
                "unit": "milliseconds",
                "higher_is_better": False
            },
            "retry_success_rate": {
                "description": "Success rate of retry attempts",
                "unit": "percentage",
                "higher_is_better": True
            },
            "auto_fix_success_rate": {
                "description": "Success rate of auto-fix suggestions",
                "unit": "percentage",
                "higher_is_better": True
            },
            "avg_prompt_quality_score": {
                "description": "Average prompt quality assessment",
                "unit": "score",
                "range": "0.0 - 1.0",
                "higher_is_better": True
            },
            "avg_confidence_score": {
                "description": "Average AI confidence in results",
                "unit": "score",
                "range": "0.0 - 1.0",
                "higher_is_better": True
            }
        }

        return {
            "available_metrics": metrics,
            "default_metric": "satisfaction_rate",
            "total_metrics": len(metrics)
        }

    except Exception as e:
        logger.error(f"Failed to get available metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get available metrics")

@router.get("/api/model_timeseries_stats", response_model=List[TimeSeriesDataPoint])
async def get_model_timeseries_stats(
    days: int = Query(30, description="Number of days to include", ge=1, le=365)
):
    """
    Get time-series data for model performance charts.

    This endpoint provides daily aggregated performance data for all models
    to enable time-series visualization of ratings and latency trends.

    Args:
        days: Number of days to include in the analysis

    Returns:
        List of time-series data points for charts

    Example:
        GET /api/model_timeseries_stats?days=30
    """
    try:
        logger.info(f"Getting time-series data for {days} days")

        # Get all feedback for the specified period
        all_feedback = analytics_service.get_all_feedback()

        if not all_feedback:
            return []

        # Filter by date range
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_feedback = [
            f for f in all_feedback
            if f.timestamp >= cutoff_date
        ]

        # Group by date and model
        daily_data = {}
        for feedback in recent_feedback:
            date_key = feedback.timestamp.strftime('%Y-%m-%d')
            model_key = feedback.model.value if hasattr(feedback.model, 'value') else str(feedback.model)

            key = f"{date_key}_{model_key}"
            if key not in daily_data:
                daily_data[key] = {
                    'date': date_key,
                    'model': model_key,
                    'ratings': [],
                    'latencies': [],
                    'total_feedback': 0,
                    'positive_feedback': 0
                }

            daily_data[key]['ratings'].append(feedback.get_rating_score())
            if feedback.latency_ms > 0:
                daily_data[key]['latencies'].append(feedback.latency_ms)
            daily_data[key]['total_feedback'] += 1
            if feedback.is_positive_feedback():
                daily_data[key]['positive_feedback'] += 1

        # Calculate aggregated metrics
        time_series_data = []
        for data in daily_data.values():
            if data['ratings']:  # Only include days with data
                avg_rating = sum(data['ratings']) / len(data['ratings'])
                avg_latency = sum(data['latencies']) / len(data['latencies']) if data['latencies'] else 0
                satisfaction_rate = (data['positive_feedback'] / data['total_feedback']) * 100

                time_series_data.append(TimeSeriesDataPoint(
                    date=data['date'],
                    model=data['model'],
                    avg_rating=round(avg_rating, 3),
                    latency_ms=round(avg_latency, 1),
                    satisfaction_rate=round(satisfaction_rate, 1),
                    total_feedback=data['total_feedback']
                ))

        # Sort by date
        time_series_data.sort(key=lambda x: x.date)

        logger.info(f"Returned {len(time_series_data)} time-series data points")
        return time_series_data

    except Exception as e:
        logger.error(f"Failed to get time-series data: {e}")
        raise HTTPException(status_code=500, detail="Failed to get time-series data")

@router.post("/api/best_model_predictor", response_model=BestModelPredictionResponse)
async def predict_best_model_endpoint(request: BestModelPredictionRequest):
    """
    Predict the best AI model for a given prompt.

    This endpoint analyzes the prompt characteristics and historical performance
    to recommend the most suitable AI model for the task.

    Args:
        request: Prediction request with prompt text

    Returns:
        Prediction result with best model and alternatives

    Example:
        POST /api/best_model_predictor
        {
            "prompt": "Refactor this function to improve performance"
        }
    """
    try:
        logger.info(f"Predicting best model for prompt: {request.prompt[:50]}...")

        # Import model predictor
        from services.model_predictor import predict_best_model

        # Get historical data for better predictions
        all_feedback = analytics_service.get_all_feedback()

        # Convert feedback to historical data format
        historical_data = []
        for feedback in all_feedback:
            historical_data.append({
                "intent": feedback.intent.value if hasattr(feedback.intent, 'value') else str(feedback.intent),
                "model": feedback.model.value if hasattr(feedback.model, 'value') else str(feedback.model),
                "rating_score": feedback.get_rating_score(),
                "prompt": feedback.prompt,
                "latency_ms": feedback.latency_ms
            })

        # Get prediction
        prediction = predict_best_model(request.prompt, historical_data)

        response = BestModelPredictionResponse(
            best_model=prediction["best_model"],
            confidence=prediction["confidence"],
            reasoning=prediction["reasoning"],
            alternatives=prediction["alternatives"]
        )

        logger.info(f"Predicted best model: {prediction['best_model']} (confidence: {prediction['confidence']:.2f})")
        return response

    except Exception as e:
        logger.error(f"Failed to predict best model: {e}")
        raise HTTPException(status_code=500, detail="Failed to predict best model")
