# CodeCrusher Recursive Injection Examples

This document provides examples of how to use the CodeCrusher recursive injection feature.

## Basic Usage

### 1. Discover Python files with injection tags (dry run)
```bash
codecrusher inject recursive -s ./src -t "Add error handling"
```

This command will:
- Recursively scan the `./src` directory
- Look for Python files (default extension)
- Find files containing injection tags like `# AI_INJECT: tag_name`
- Display a summary without performing actual injections

### 2. Discover multiple file types
```bash
codecrusher inject recursive -s ./src -t "Add error handling" --ext py,js,java
```

This command will scan for Python, JavaScript, and Java files.

### 3. Verbose discovery
```bash
codecrusher inject recursive -s ./src -t "Add error handling" --verbose
```

This command will show detailed information about each file discovered and each tag found.

## Advanced Usage

### 4. Actually perform injections (not just discovery)
```bash
codecrusher inject recursive -s ./src -t "Add error handling" --no-dry-run
```

**⚠️ Warning:** This will actually modify your files! Make sure to backup your code first.

### 5. Use specific AI provider and model
```bash
codecrusher inject recursive -s ./src -t "Add error handling" --provider groq --model llama3 --no-dry-run
```

### 6. Add custom tags and enable caching
```bash
codecrusher inject recursive -s ./src -t "Add error handling" --tag "refactor performance" --cache --no-dry-run
```

### 7. Scan entire project with multiple extensions
```bash
codecrusher inject recursive -s . -t "Improve code quality" --ext py,js,ts,java,cpp --verbose
```

## File Extension Support

The `--ext` parameter supports the following file extensions:

- **Python**: `py`
- **JavaScript**: `js`
- **TypeScript**: `ts`
- **Java**: `java`
- **C++**: `cpp`, `c`
- **C#**: `cs`
- **Go**: `go`
- **Rust**: `rs`
- **PHP**: `php`
- **Ruby**: `rb`
- **Swift**: `swift`
- **Kotlin**: `kt`
- **Scala**: `scala`
- **Shell**: `sh`
- **PowerShell**: `ps1`
- **SQL**: `sql`
- **HTML**: `html`
- **CSS**: `css`, `scss`, `less`
- **Vue**: `vue`
- **JSX/TSX**: `jsx`, `tsx`

## Injection Tag Formats

CodeCrusher supports multiple injection tag formats depending on the file type:

### Python, Shell, etc.
```python
# AI_INJECT: optimize_function
def my_function():
    pass
```

### JavaScript, C++, Java, etc.
```javascript
// AI_INJECT: add_validation
function myFunction() {
    // code here
}
```

### Multi-line comments
```javascript
/* AI_INJECT: refactor_logic */
function complexFunction() {
    // code here
}
```

### HTML, XML
```html
<!-- AI_INJECT: improve_layout -->
<div class="container">
    <p>Content here</p>
</div>
```

### SQL
```sql
-- AI_INJECT: optimize_query
SELECT * FROM users WHERE active = 1;
```

## Directory Skipping

The recursive scanner automatically skips common directories that shouldn't contain source code:

- `__pycache__`, `.git`, `.svn`, `.hg`, `.bzr`
- `node_modules`, `.vscode`, `.idea`, `.vs`
- `build`, `dist`, `target`, `bin`, `obj`
- `.pytest_cache`, `.coverage`, `.tox`
- `venv`, `env`, `.env`, `virtualenv`
- Any directory starting with `.`

## Example Workflow

1. **First, discover what files would be processed:**
   ```bash
   codecrusher inject recursive -s ./src -t "Add error handling" --verbose
   ```

2. **Review the output and make sure it looks correct**

3. **If satisfied, run the actual injection:**
   ```bash
   codecrusher inject recursive -s ./src -t "Add error handling" --no-dry-run
   ```

4. **Review the changes and test your code**

## Tips

- Always run with `--dry-run` first to see what files will be processed
- Use `--verbose` to see detailed information about file discovery
- Backup your code before running with `--no-dry-run`
- Start with a small directory to test the functionality
- Use specific prompts for better AI results
- Add meaningful tags to help with tracking and analysis

## Error Handling

If the command fails:

1. **Check that the source directory exists**
2. **Verify file permissions**
3. **Make sure injection tags are properly formatted**
4. **Check that the AI provider is configured correctly**

## Phase 2 Features (Now Available!)

### Async Batch Processing
```bash
# Process files with concurrent AI requests
codecrusher inject recursive -s ./src -t "Add error handling" --no-dry-run --max-workers 5
```

### Advanced Caching
```bash
# Enable caching for faster repeated operations
codecrusher inject recursive -s ./src -t "Add error handling" --no-dry-run --cache
```

### User Confirmation
```bash
# Skip confirmation for automated workflows
codecrusher inject recursive -s ./src -t "Add error handling" --no-dry-run --skip-confirmation
```

### Progress Tracking
The CLI now shows real-time progress bars:
```
Processing 15 files... ━━━━━━━━━━━━━ 73% 0:00:45
```

### Batch Results
Detailed results summary after processing:
```
✅ BATCH PROCESSING COMPLETED
Total files: 15
Successful: 13
Failed: 2
Cache hits: 8
Success rate: 86.7%
```

## Production Workflow Examples

### 1. Safe Exploration Workflow
```bash
# Step 1: Discover what files would be processed
codecrusher inject recursive -s ./src -t "Add comprehensive error handling" --verbose

# Step 2: Review the output, then proceed with processing
codecrusher inject recursive -s ./src -t "Add comprehensive error handling" --no-dry-run --cache
```

### 2. High-Performance Batch Processing
```bash
# Process large codebases efficiently
codecrusher inject recursive -s ./entire-project -t "Optimize performance" \
  --ext py,js,ts,java --no-dry-run --cache --max-workers 8 --verbose
```

### 3. Automated CI/CD Integration
```bash
# For automated environments (no user interaction)
codecrusher inject recursive -s ./src -t "Add logging statements" \
  --no-dry-run --skip-confirmation --cache --max-workers 3
```

### 4. Multi-Language Project Processing
```bash
# Process different file types with specific prompts
codecrusher inject recursive -s ./frontend -t "Add TypeScript types" \
  --ext ts,tsx,js,jsx --no-dry-run --cache

codecrusher inject recursive -s ./backend -t "Add error handling" \
  --ext py,java,go --no-dry-run --cache

codecrusher inject recursive -s ./scripts -t "Add documentation" \
  --ext sh,ps1,py --no-dry-run --cache
```

## Cache Management

### View Cache Statistics
The CLI automatically shows cache stats when `--cache` is enabled:
```
📊 Cache Statistics
Cache entries: 127
Cache file: ~/.codecrusher_batch_cache.json
Cache size: 2.34 MB
```

### Cache Benefits
- **Speed**: Cached results return instantly
- **Consistency**: Same prompt + file = same result
- **Cost Savings**: Avoid redundant AI API calls
- **Reliability**: Works offline for cached content

## Error Handling Examples

### Graceful Error Recovery
```bash
# Even if some files fail, processing continues
codecrusher inject recursive -s ./mixed-quality-code -t "Fix syntax errors" \
  --no-dry-run --cache --verbose
```

Output shows detailed error information:
```
✅ Successfully processed files:
  ✓ src/good_file.py
  ✓ src/another_good_file.py (cached)

❌ Failed files:
  ✗ src/corrupted_file.py
    Error: Unable to parse file syntax

📊 Final Results: 2 successful, 1 failed (66.7% success rate)
```

## Advanced Configuration

### Custom Concurrency Limits
```bash
# For rate-limited APIs
codecrusher inject recursive -s ./src -t "Add tests" --no-dry-run --max-workers 1

# For high-performance APIs
codecrusher inject recursive -s ./src -t "Add tests" --no-dry-run --max-workers 10
```

### Combining with Other CodeCrusher Features
```bash
# Use with custom tags and confidence levels
codecrusher inject recursive -s ./src -t "Refactor for maintainability" \
  --no-dry-run --cache --tag "refactor performance" --confidence high
```

## Troubleshooting

### Common Issues and Solutions

1. **"No files contain injection tags"**
   - Add injection tags like `# AI_INJECT: tag_name` to your files
   - Use `--verbose` to see which files are being scanned

2. **"Cannot connect to CodeCrusher API server"**
   - Start the API server: `codecrusher serve run`
   - Check if the server is running on localhost:9000

3. **Cache issues**
   - Cache is automatically invalidated when files change
   - Manual cache clearing: delete `~/.codecrusher_batch_cache.json`

4. **Performance issues**
   - Reduce `--max-workers` if hitting API rate limits
   - Enable `--cache` for repeated operations
   - Use `--ext` to limit file types being processed

## Best Practices

1. **Always start with dry-run** to understand what will be processed
2. **Use caching** for repeated operations and development workflows
3. **Set appropriate concurrency** based on your AI provider's limits
4. **Use specific prompts** for better AI results
5. **Add meaningful tags** to track different types of injections
6. **Monitor batch results** to identify patterns in failures
7. **Use version control** to track changes made by AI injections

## Next Steps

Phase 2 is now complete and production-ready! The next developments will focus on:

- Real AI provider integration (Groq, OpenAI, etc.)
- Advanced retry mechanisms with exponential backoff
- Distributed processing for very large codebases
- Integration with popular IDEs and CI/CD systems
- Machine learning-based optimization suggestions
