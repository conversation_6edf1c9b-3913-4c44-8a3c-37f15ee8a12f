# CodeCrusher Git Hooks

Git hooks for CodeCrusher integration.

## Features

### Pre-commit Hook
- Automatically scans staged code for injection points
- Suggests tags based on function names, class definitions, and TODO comments
- Auto-inserts tags when enabled
- Detects potential anomalies in code
- Can block commits if critical errors are detected

### Post-commit Hook
- Pushes telemetry summaries to a central log
- Adds branch information to telemetry data
- Tracks injections per branch for better team collaboration
- Can sync telemetry data to a remote server

## Installation

To install the Git hooks, run:

```bash
python cc_li.py githooks install
```

Options:
- `--repo-path, -r`: Path to the Git repository (default: current directory)
- `--block-on-errors, -b`: Block commits if critical injection errors are detected
- `--auto-insert-tags, -a`: Automatically insert missing tags based on context
- `--remote-sync, -s`: Sync telemetry metadata to a remote server
- `--remote-sync-url, -u`: URL of the remote server for telemetry sync

## Uninstallation

To uninstall the Git hooks, run:

```bash
python cc_li.py githooks uninstall
```

Options:
- `--repo-path, -r`: Path to the Git repository (default: current directory)

## Checking Status

To check the status of the Git hooks, run:

```bash
python cc_li.py githooks status
```

Options:
- `--repo-path, -r`: Path to the Git repository (default: current directory)

## How It Works

### Pre-commit Hook

The pre-commit hook scans all staged files for potential injection points:

1. Function definitions without injection tags
2. Class definitions without injection tags
3. TODO comments that could be converted to injection tags

It also scans for potential anomalies:

1. Suspicious patterns (sensitive data, destructive commands, code execution)
2. Unusually large code blocks
3. Multiple injection tags in a single file

If auto-insert tags is enabled, it will automatically insert tags for detected injection points.

### Post-commit Hook

The post-commit hook logs commit information and updates telemetry data:

1. Logs commit hash, message, branch, author, and timestamp
2. Extracts injection tags from committed files
3. Updates recent telemetry entries with branch information
4. Adds branch tags to telemetry data
5. Optionally syncs telemetry data to a remote server

## Configuration

The Git hooks use the same configuration as the CodeCrusher CLI. You can configure them by editing the `~/.codecrusherrc` file or the `codecrusher/config.yaml` file in your project.

Hook-specific configuration is stored in the following files:
- `.git/hooks/pre-commit.config.json`
- `.git/hooks/post-commit.config.json`

## Troubleshooting

If you encounter any issues with the Git hooks, check the following:

1. Make sure the hooks are executable (`chmod +x .git/hooks/pre-commit .git/hooks/post-commit`)
2. Make sure Python 3.8 or higher is installed and available in your PATH
3. Make sure the CodeCrusher CLI is installed and configured
4. Check the hook configuration files for any issues

## License

Same as the CodeCrusher project.
