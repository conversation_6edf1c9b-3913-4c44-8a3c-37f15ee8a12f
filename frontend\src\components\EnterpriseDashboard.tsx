import React, { useState, useRef, use<PERSON><PERSON>back, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Loader2,
  Sparkles,
  XCircle,
  CheckCircle,
  Play,
  Square,
  Trash2,
  Wifi,
  WifiOff,
  RefreshCw,
  Settings,
  Terminal,
  Activity,
  Zap,
  Shield,
  TrendingUp,
  Clock,
  Database,
  Cpu,
  BarChart3,
  Code2,
  Brain,
  Layers
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useWebSocket, WebSocketMessage } from "@/hooks/useWebSocket";
import { ConnectionStatusBadge } from "@/components/ConnectionOverlay";
import { StreamTimeoutBadge } from "@/components/FrozenStreamOverlay";
import { getWebSocketUrl } from "@/utils/client";
import { EnterpriseHeader, EnterpriseStatsBar } from "@/components/EnterpriseHeader";
import { EnterpriseFooter } from "@/components/EnterpriseFooter";
import { EnterpriseSidebar } from "@/components/EnterpriseSidebar";
import { LabelWithTooltip } from "@/components/ui/tooltip";
import { useLocation } from "react-router-dom";

interface EnterpriseDashboardProps {
  viewMode?: string;
  setViewMode?: (mode: any) => void;
  healthStatus?: any;
  wsConnected?: boolean;
  getStatusIcon?: () => React.ReactElement;
  getStatusText?: () => string;
}

export default function EnterpriseDashboard({
  viewMode,
  setViewMode,
  healthStatus,
  wsConnected,
  getStatusIcon,
  getStatusText
}: EnterpriseDashboardProps = {}) {

  const location = useLocation();

  console.log(`🏠 ENTERPRISE DASHBOARD RENDER: path=${location.pathname}, viewMode=${viewMode}, hasSetViewMode=${!!setViewMode}`);

  // Configuration state
  const [source, setSource] = useState("./src");
  const [promptText, setPromptText] = useState("Optimize code for enterprise-grade performance and maintainability");
  const [tag, setTag] = useState("enterprise-optimization");
  const [model] = useState("mixtral");

  // Auto-correct path function
  const autoCorrectPath = (path: string): string => {
    if (!path) return path;

    // Strip leading/trailing spaces
    let corrected = path.trim();

    // Replace > characters with proper path separators (Windows issue)
    corrected = corrected.replace(/>/g, '\\');

    // Normalize multiple slashes/backslashes
    corrected = corrected.replace(/[\\\/]+/g, '\\');

    // Remove trailing slashes unless it's a root path
    if (corrected.length > 1 && corrected.endsWith('\\')) {
      corrected = corrected.slice(0, -1);
    }

    return corrected;
  };

  // Handle source input with auto-correction
  const handleSourceChange = (value: string) => {
    const corrected = autoCorrectPath(value);
    setSource(corrected);

    // Log the correction if it was applied
    if (corrected !== value) {
      console.log(`🔧 Path auto-corrected: "${value}" → "${corrected}"`);
    }
  };

  // UI state
  const [status, setStatus] = useState("idle");
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const logContainerRef = useRef<HTMLDivElement>(null);

  // Enterprise WebSocket connection
  const {
    connectionStatus,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    retry,
    retryCount,
    isConnected,
    isFrozen,
    lastMessageTime,
    restartStream
  } = useWebSocket({
    url: getWebSocketUrl('/logs'),
    maxRetries: 5,
    maxReconnectDelay: 10000,
    timeoutMs: 15000,
    autoRestartOnTimeout: true,
    onMessage: handleWebSocketMessage,
    onStatusChange: useCallback((newStatus) => {
      console.log(`Enterprise connection status: ${newStatus}`);
    }, [])
  });

  // Enterprise message handler
  function handleWebSocketMessage(message: WebSocketMessage) {
    if (message.type === "progress" && message.value !== undefined) {
      setProgress(message.value);
    } else if (message.type === "error") {
      setError(message.message || "System error occurred");
      setStatus("error");
      setIsRunning(false);
    } else if (message.type === "done" || message.message?.includes("completed successfully")) {
      setStatus("done");
      setProgress(100);
      setIsRunning(false);
    } else if (message.message) {
      setLogs(prev => {
        const newLogs = [...prev, message.message!];
        return newLogs.slice(-150); // Enterprise log retention
      });

      if (message.type !== "progress" && message.value !== undefined) {
        setProgress(message.value);
      }

      if (message.message.includes("completed successfully")) {
        setStatus("done");
        setProgress(100);
        setIsRunning(false);
      } else if (message.message.includes("injection failed") || message.message.includes("Error:")) {
        setStatus("error");
        setError(message.message);
        setIsRunning(false);
      }
    }
  }

  // Auto-scroll with enterprise performance optimization
  React.useEffect(() => {
    if (logContainerRef.current && logs.length > 0) {
      const container = logContainerRef.current;
      const isScrolledToBottom = container.scrollHeight - container.clientHeight <= container.scrollTop + 1;

      if (isScrolledToBottom) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [logs]);

  const startInjection = useCallback(async () => {
    if (!isConnected) {
      setError("Enterprise connection not established");
      return;
    }

    setIsRunning(true);
    setStatus("running");
    setProgress(0);
    setError(null);
    setLogs([]);

    try {
      const response = await fetch("http://localhost:8001/inject", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          source,
          prompt_text: promptText,
          model,
          tag,
          auto_model: true,
          apply: false,
          use_fallback: true,
          recursive: true,
          ext: "py"
        })
      });

      if (!response.ok) {
        throw new Error(`Enterprise API Error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        setError(result.error || "Enterprise operation failed");
        setStatus("error");
        setIsRunning(false);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Enterprise system error";
      setError(errorMessage);
      setStatus("error");
      setIsRunning(false);
    }
  }, [isConnected, source, promptText, model, tag]);

  const stopInjection = useCallback(() => {
    setIsRunning(false);
    setStatus("connected");
    setLogs(prev => [...prev, "🛑 Enterprise operation terminated by administrator"]);
  }, []);

  const clearLogs = useCallback(() => {
    setLogs([]);
    setProgress(0);
    setError(null);
    setStatus(isConnected ? "connected" : "idle");
  }, [isConnected]);

  // Enterprise status badge
  const statusBadge = useMemo(() => {
    switch (status) {
      case "running":
        return (
          <Badge className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 shadow-lg">
            <Activity className="w-3 h-3 mr-1 animate-pulse" />
            Processing
          </Badge>
        );
      case "done":
        return (
          <Badge className="bg-gradient-to-r from-emerald-600 to-green-600 text-white border-0 shadow-lg">
            <CheckCircle className="w-3 h-3 mr-1" />
            Completed
          </Badge>
        );
      case "error":
        return (
          <Badge className="bg-gradient-to-r from-red-600 to-rose-600 text-white border-0 shadow-lg">
            <XCircle className="w-3 h-3 mr-1" />
            Error
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gradient-to-r from-slate-600 to-gray-600 text-white border-0 shadow-lg">
            <Shield className="w-3 h-3 mr-1" />
            Ready
          </Badge>
        );
    }
  }, [status]);

  return (
    <div className="min-h-screen flex relative overflow-x-hidden bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-100">
      {/* Enterprise Sidebar - Full Height */}
      <div className="fixed inset-y-0 left-0 z-40">
        <EnterpriseSidebar
          isOpen={isSidebarOpen}
          onToggle={() => setIsSidebarOpen(!isSidebarOpen)}
        />
      </div>

      {/* Main Content Area - With Sidebar Margin */}
      <div className={`flex-1 flex flex-col transition-all duration-300 ${isSidebarOpen ? 'ml-64' : 'ml-16'}`}>
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.05),transparent_50%)]"></div>
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-100/20 to-purple-100/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-emerald-100/20 to-green-100/20 rounded-full blur-3xl"></div>



        {/* Ingenious Multi-Row Navigation Layout */}
        {setViewMode && (
          <div className="relative z-50 bg-white/95 backdrop-blur-xl border-b border-gray-200/30 shadow-lg">
            <div className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3">
              {/* Top Row: Logo + Primary Navigation */}
              <div className="flex items-center justify-between mb-2">
                {/* Logo Section - Compact Left */}
                <div className="flex items-center min-w-0">
                  <h1 className="text-lg sm:text-xl lg:text-2xl font-bold flex items-center gap-1.5 text-gray-800">
                    <Brain className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6 text-blue-600 flex-shrink-0" />
                    <span className="hidden sm:inline truncate">CodeCrusher Dashboard</span>
                    <span className="sm:hidden truncate">CodeCrusher</span>
                  </h1>

                </div>

                {/* Primary Navigation Row - Full Text */}
                <div className="flex-1 flex justify-center mx-4">
                  <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-4xl">
                    <Button
                      variant={viewMode === 'main' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('main')}
                      className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                    >
                      Main Dashboard
                    </Button>
                    <Button
                      variant={viewMode === 'simple' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('simple')}
                      className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                    >
                      Full Dashboard
                    </Button>
                    <Button
                      variant={viewMode === 'streamlined' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('streamlined')}
                      className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                    >
                      Streamlined
                    </Button>
                    <Button
                      variant={viewMode === 'clean' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('clean')}
                      className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                    >
                      Clean
                    </Button>
                    <Button
                      variant={viewMode === 'enhanced' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setViewMode('enhanced')}
                      className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                    >
                      Enhanced
                    </Button>
                    <button
                      onClick={() => {
                        console.log('🔥 BACKEND BUTTON CLICKED - DIRECT!');
                        window.location.href = '/backend';
                      }}
                      className={`text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap rounded border transition-colors ${
                        viewMode === 'backend'
                          ? 'bg-gray-900 text-white border-gray-900'
                          : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      Backend
                    </button>
                  </div>
                </div>

                {/* Right Side Balance */}
                <div className="w-16 sm:w-20 lg:w-24 flex-shrink-0"></div>
              </div>

              {/* Bottom Row: Secondary Navigation */}
              <div className="flex justify-center">
                <div className="flex flex-wrap justify-center gap-1 sm:gap-2 max-w-3xl">
                  <button
                    onClick={() => {
                      console.log('🔥 UI BUTTON CLICKED - DIRECT!');
                      window.location.href = '/ui';
                    }}
                    className={`text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap rounded border transition-colors ${
                      viewMode === 'ui'
                        ? 'bg-gray-900 text-white border-gray-900'
                        : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    UI
                  </button>
                  <button
                    onClick={() => {
                      console.log('🔥 STATUS BUTTON CLICKED - DIRECT!');
                      window.location.href = '/status';
                    }}
                    className={`text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap rounded border transition-colors ${
                      viewMode === 'status'
                        ? 'bg-gray-900 text-white border-gray-900'
                        : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    Status
                  </button>
                  <button
                    onClick={() => {
                      console.log('🔥 STABLE BUTTON CLICKED - DIRECT!');
                      window.location.href = '/stable';
                    }}
                    className={`text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap rounded border transition-colors ${
                      viewMode === 'stable'
                        ? 'bg-gray-900 text-white border-gray-900'
                        : 'bg-white text-gray-900 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    Stable
                  </button>
                  <Button
                    variant={viewMode === 'enterprise' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('enterprise')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 hover:from-blue-700 hover:to-indigo-700"
                  >
                    Enterprise
                  </Button>
                  <Button
                    variant={viewMode === 'demo' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('demo')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap"
                  >
                    Log Panel Demo
                  </Button>
                  <Button
                    variant={viewMode === 'styletest' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('styletest')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-yellow-500 text-white border-0 hover:bg-yellow-600"
                  >
                    Style Test
                  </Button>
                  <Button
                    variant={viewMode === 'intelligence' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('intelligence')}
                    className="text-xs sm:text-sm px-2 sm:px-3 py-1.5 h-8 sm:h-9 whitespace-nowrap bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0 hover:from-purple-700 hover:to-blue-700"
                  >
                    🧠 Intelligence Hub
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Health Status - Restored */}
        {getStatusIcon && getStatusText && (
          <div className="relative z-40 bg-white/90 backdrop-blur-sm border-b border-gray-100 py-2">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center space-x-2">
                {getStatusIcon()}
                <span className="text-sm font-medium">{getStatusText()}</span>
              </div>
              <div className="flex items-center space-x-2">
                {wsConnected ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <span className="text-sm font-medium">
                  WebSocket {wsConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Health Alert - Restored */}
        {healthStatus && !healthStatus.codecrusher_available && (
          <div className="relative z-40 px-4 py-2">
            <Alert>
              <AlertDescription>
                CodeCrusher CLI not found. Please ensure it's installed and available in your PATH or virtual environment.
                {healthStatus.codecrusher_path && ` Detected path: ${healthStatus.codecrusher_path}`}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Enterprise Header - At Root Level */}
        <div className="relative z-50">
          <EnterpriseHeader />
          <EnterpriseStatsBar />
        </div>

        {/* Main Content Container */}
        <div className="flex-1 p-4 sm:p-6 lg:p-8 space-y-4 sm:space-y-6 relative z-10">
        {/* Premium 3D Enterprise Status Panel */}
        <div className="relative bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/30 p-6 sm:p-8 overflow-hidden transform hover:scale-[1.01] transition-all duration-500 group"
             style={{boxShadow: 'inset 0 2px 8px rgba(255,255,255,0.8), 0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(59,130,246,0.1)'}}>
          {/* Premium 3D Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-white/20 to-purple-50/40"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-gray-50/30 to-transparent"></div>
          <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/60 to-transparent"></div>
          <div className="absolute top-0 right-0 w-24 h-24 sm:w-40 sm:h-40 bg-gradient-to-br from-blue-200/30 via-indigo-200/20 to-purple-200/30 rounded-full -translate-y-12 translate-x-12 sm:-translate-y-20 sm:translate-x-20 blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-20 h-20 sm:w-32 sm:h-32 bg-gradient-to-tr from-emerald-200/20 to-green-200/20 rounded-full translate-y-10 -translate-x-10 sm:translate-y-16 sm:-translate-x-16 blur-xl"></div>

          <div className="relative flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3 sm:space-x-6 w-full sm:w-auto">
              <div className="flex items-center space-x-2 sm:space-x-3 flex-wrap gap-2">
                <ConnectionStatusBadge
                  status={connectionStatus}
                  retryCount={retryCount}
                  className="shadow-lg border-0 text-xs sm:text-sm"
                />
                <StreamTimeoutBadge
                  isFrozen={isFrozen}
                  lastMessageTime={lastMessageTime}
                  timeoutMs={15000}
                  className="text-xs sm:text-sm"
                />
                {statusBadge}
              </div>
            </div>
            <div className="text-left sm:text-right w-full sm:w-auto">
              <div className="text-sm sm:text-base font-semibold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-1">
                Enterprise Operations Center
              </div>
              <div className="text-xs sm:text-sm text-gray-500 flex items-center space-x-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                <span className="hidden sm:inline">Real-time AI Processing Dashboard</span>
                <span className="sm:hidden">AI Processing Dashboard</span>
              </div>
            </div>
          </div>
        </div>

        {/* Enterprise Error Alert - Inline */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 flex items-start space-x-3">
            <XCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
            <div>
              <div className="text-red-800 font-semibold text-sm">Enterprise System Alert</div>
              <div className="text-red-700 text-sm mt-1">{error}</div>
            </div>
          </div>
        )}

        {/* Connection Status Alerts - Inline */}
        {connectionStatus === 'disconnected' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <WifiOff className="h-5 w-5 text-yellow-600" />
              <div>
                <div className="text-yellow-800 font-semibold text-sm">Connection Lost</div>
                <div className="text-yellow-700 text-sm">Attempting to reconnect... (Attempt {retryCount}/5)</div>
              </div>
            </div>
            <Button onClick={retry} size="sm" variant="outline" className="border-yellow-300 text-yellow-700 hover:bg-yellow-100">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry Now
            </Button>
          </div>
        )}

        {isFrozen && (
          <div className="bg-orange-50 border border-orange-200 rounded-xl p-4 flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Clock className="h-5 w-5 text-orange-600" />
              <div>
                <div className="text-orange-800 font-semibold text-sm">Stream Timeout</div>
                <div className="text-orange-700 text-sm">No data received for 15 seconds</div>
              </div>
            </div>
            <Button onClick={restartStream} size="sm" variant="outline" className="border-orange-300 text-orange-700 hover:bg-orange-100">
              <RefreshCw className="w-4 h-4 mr-2" />
              Restart
            </Button>
          </div>
        )}

        {/* Enterprise Main Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 lg:gap-8">
          {/* Premium 3D Enterprise Configuration Panel */}
          <Card className="lg:col-span-5 xl:col-span-4 border-0 bg-white/95 backdrop-blur-xl rounded-2xl relative overflow-hidden transform hover:scale-[1.02] transition-all duration-500 group"
                style={{boxShadow: 'inset 0 2px 8px rgba(255,255,255,0.8), 0 12px 40px rgba(0,0,0,0.15), 0 4px 12px rgba(59,130,246,0.1)'}}>
            {/* Premium 3D Card Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-white/30 to-indigo-50/50"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-blue-50/20 to-transparent"></div>
            <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/70 to-transparent"></div>
            <div className="absolute -top-8 -right-8 sm:-top-12 sm:-right-12 w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-300/40 to-indigo-300/40 rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-tr from-indigo-200/30 to-blue-200/30 rounded-full translate-y-8 -translate-x-8 blur-xl"></div>

            <CardHeader className="pb-4 sm:pb-6 border-b border-gray-100/80 relative">
              <CardTitle className="flex items-center space-x-2 sm:space-x-3 text-lg sm:text-xl">
                <div className="p-1.5 sm:p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg shadow-lg">
                  <Settings className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
                <span className="bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent font-bold">
                  <span className="hidden sm:inline">Enterprise Configuration</span>
                  <span className="sm:hidden">Configuration</span>
                </span>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 sm:p-6 space-y-3 sm:space-y-4 relative">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Code2 className="w-4 h-4 text-gray-700" />
                  <LabelWithTooltip
                    htmlFor="source"
                    tooltip="Absolute or relative path to your project folder. This is the root directory CodeCrusher will scan for target files."
                    className="text-sm font-semibold text-gray-700"
                  >
                    Source Repository
                  </LabelWithTooltip>
                </div>
                <Input
                  id="source"
                  value={source}
                  onChange={(e) => handleSourceChange(e.target.value)}
                  placeholder="./src"
                  className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Brain className="w-4 h-4 text-gray-700" />
                  <LabelWithTooltip
                    htmlFor="prompt"
                    tooltip="Describe exactly what you want the AI to do. Be surgical. Include file names, features, layout requests, or code changes."
                    className="text-sm font-semibold text-gray-700"
                  >
                    AI Optimization Prompt
                  </LabelWithTooltip>
                </div>
                <Textarea
                  id="prompt"
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  rows={4}
                  placeholder="Enter enterprise optimization requirements..."
                  className="border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 rounded-xl resize-none transition-all duration-200"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Layers className="w-4 h-4 text-gray-700" />
                  <LabelWithTooltip
                    htmlFor="tag"
                    tooltip="Optional label for this injection. Useful for tracking runs, organizing logs, and versioning changes."
                    className="text-sm font-semibold text-gray-700"
                  >
                    Operation Tag
                  </LabelWithTooltip>
                </div>
                <Input
                  id="tag"
                  value={tag}
                  onChange={(e) => setTag(e.target.value)}
                  placeholder="enterprise-optimization"
                  className="h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500/20 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="h-px bg-gradient-to-r from-transparent via-gray-200 to-transparent my-6"></div>

              <div className="space-y-4">
                <Button
                  onClick={startInjection}
                  disabled={isRunning || !isConnected || !promptText.trim()}
                  className="w-full h-14 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-300 transform hover:scale-[1.02]"
                  size="lg"
                >
                  {isRunning ? (
                    <>
                      <Loader2 className="w-5 h-5 mr-3 animate-spin" />
                      Processing Enterprise Operation...
                    </>
                  ) : (
                    <>
                      <Zap className="w-5 h-5 mr-3" />
                      Execute AI Optimization
                    </>
                  )}
                </Button>

                <div className="grid grid-cols-2 gap-3">
                  {isRunning && (
                    <Button
                      onClick={stopInjection}
                      className="h-12 bg-gradient-to-r from-red-500 to-rose-600 hover:from-red-600 hover:to-rose-700 text-white font-medium rounded-xl shadow-lg transition-all duration-200"
                    >
                      <Square className="w-4 h-4 mr-2" />
                      Terminate
                    </Button>
                  )}

                  <Button
                    onClick={clearLogs}
                    variant="outline"
                    className={cn(
                      "h-12 border-gray-300 hover:bg-gray-50 font-medium rounded-xl transition-all duration-200",
                      isRunning ? "" : "col-span-2"
                    )}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Clear Logs
                  </Button>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-xl border border-blue-100">
                <div className="text-xs font-semibold text-blue-800 mb-2 flex items-center space-x-2">
                  <BarChart3 className="w-3 h-3" />
                  <span>Enterprise Configuration</span>
                </div>
                <div className="space-y-2 text-xs text-blue-700">
                  <div className="flex justify-between">
                    <LabelWithTooltip
                      tooltip="Choose the AI model to run the optimization. Lighter models are faster; stronger ones produce higher-quality output."
                      className="text-xs text-blue-700 border-blue-300 hover:border-blue-500"
                    >
                      AI Model:
                    </LabelWithTooltip>
                    <span className="font-medium">{model.toUpperCase()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Operation Mode:</span>
                    <span className="font-medium">Preview</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Security Level:</span>
                    <span className="font-medium">Enterprise</span>
                  </div>
                  <div className="flex justify-between">
                    <LabelWithTooltip
                      tooltip="Enable if you want automatic retries using a backup model if the first model fails or returns weak results."
                      className="text-xs text-blue-700 border-blue-300 hover:border-blue-500"
                    >
                      Fallback Routing:
                    </LabelWithTooltip>
                    <span className="font-medium">Enabled</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Premium 3D Enterprise Logs & Analytics Panel */}
          <Card className="lg:col-span-7 xl:col-span-8 border-0 bg-white/95 backdrop-blur-xl rounded-2xl relative overflow-hidden transform hover:scale-[1.02] transition-all duration-500 group"
                style={{boxShadow: 'inset 0 2px 8px rgba(255,255,255,0.8), 0 12px 40px rgba(0,0,0,0.15), 0 4px 12px rgba(16,185,129,0.1)'}}>
            {/* Premium 3D Card Background */}
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 via-white/30 to-green-50/50"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-emerald-50/20 to-transparent"></div>
            <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-white/70 to-transparent"></div>
            <div className="absolute -top-8 -left-8 sm:-top-12 sm:-left-12 w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-emerald-300/40 to-green-300/40 rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 right-0 w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-tl from-green-200/30 to-emerald-200/30 rounded-full translate-y-8 translate-x-8 blur-xl"></div>

            <CardHeader className="pb-4 sm:pb-6 border-b border-gray-100/80 relative">
              <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-2 sm:space-y-0">
                <div className="flex items-center space-x-2 sm:space-x-3 text-lg sm:text-xl">
                  <div className="p-1.5 sm:p-2 bg-gradient-to-r from-emerald-500 to-green-600 rounded-lg shadow-lg">
                    <Terminal className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                  </div>
                  <span className="bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent font-bold">
                    <span className="hidden sm:inline">Enterprise Console</span>
                    <span className="sm:hidden">Console</span>
                  </span>
                </div>
                <div className="flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Activity className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline">{logs.length} events</span>
                    <span className="sm:hidden">{logs.length}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline">{new Date().toLocaleTimeString()}</span>
                    <span className="sm:hidden">{new Date().toLocaleTimeString().slice(0, 5)}</span>
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 sm:p-6 space-y-3 sm:space-y-4 relative">
              {/* Enterprise Progress Section */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4" />
                    <span>Operation Progress</span>
                  </span>
                  <span className="text-lg font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    {progress}%
                  </span>
                </div>
                <div className="relative">
                  <Progress
                    value={progress}
                    className={cn(
                      "h-4 bg-gray-100 rounded-full overflow-hidden",
                      status === "done" && "bg-green-100"
                    )}
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-indigo-500/20 rounded-full"></div>
                </div>
              </div>

              {/* Enterprise Terminal */}
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-700 flex items-center space-x-2">
                  <Terminal className="w-4 h-4" />
                  <span>Real-time Enterprise Console</span>
                </Label>
                <div
                  ref={logContainerRef}
                  className="relative bg-gradient-to-br from-gray-900 via-slate-900 to-black text-green-400 font-mono p-4 sm:p-6 rounded-2xl h-48 sm:h-56 lg:h-64 overflow-y-auto text-xs sm:text-sm border border-gray-800/50 group"
                  style={{
                    fontFamily: 'JetBrains Mono, Consolas, Monaco, "Courier New", monospace',
                    boxShadow: 'inset 0 4px 12px rgba(0,0,0,0.6), inset 0 1px 2px rgba(255,255,255,0.1), 0 8px 24px rgba(0,0,0,0.3)'
                  }}
                >
                  {/* Premium 3D Terminal Effects */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent rounded-2xl"></div>
                  <div className="absolute inset-x-0 top-0 h-px bg-gradient-to-r from-transparent via-green-500/30 to-transparent"></div>
                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_20%,rgba(34,197,94,0.1),transparent_50%)] rounded-2xl"></div>
                  {logs.length === 0 ? (
                    <div className="text-gray-500 italic flex items-center space-x-2">
                      <Terminal className="w-4 h-4" />
                      <span>{isConnected ? 'Enterprise system ready for operations...' : 'Establishing enterprise connection...'}</span>
                    </div>
                  ) : (
                    logs.map((log, index) => (
                      <div key={index} className="mb-2 leading-relaxed group hover:bg-gray-800/30 px-2 py-1 rounded transition-colors">
                        <span className="text-gray-600 text-xs mr-3 opacity-70">
                          [{new Date().toLocaleTimeString()}]
                        </span>
                        <span className="text-green-400">{log}</span>
                      </div>
                    ))
                  )}
                </div>
              </div>

              {/* Enterprise Status Footer */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                <div className="flex items-center space-x-6 text-xs text-gray-500">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Status:</span>
                    <span className="capitalize">{status}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Connection:</span>
                    <span className="capitalize">{connectionStatus}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Security:</span>
                    <span>Enterprise TLS</span>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {isConnected ? (
                    <div className="flex items-center space-x-2 text-emerald-600">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                      <Wifi className="w-4 h-4" />
                      <span className="text-xs font-medium">Enterprise Online</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2 text-red-600">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <WifiOff className="w-4 h-4" />
                      <span className="text-xs font-medium">Offline</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enterprise Footer */}
        <EnterpriseFooter />
        </div>
      </div>
    </div>
  );
}
