import { useAuth } from '@/contexts/AuthContext';
import { useCallback } from 'react';

interface FetchOptions extends RequestInit {
  requireAuth?: boolean;
}

export function useAuthenticatedFetch() {
  const { getAuthHeaders, isAuthenticated, logout } = useAuth();

  const authenticatedFetch = useCallback(async (
    url: string, 
    options: FetchOptions = {}
  ): Promise<Response> => {
    const { requireAuth = true, headers = {}, ...restOptions } = options;

    // Check authentication requirement
    if (requireAuth && !isAuthenticated) {
      throw new Error('Authentication required');
    }

    // Prepare headers
    const authHeaders = requireAuth ? getAuthHeaders() : {};
    const finalHeaders = {
      'Content-Type': 'application/json',
      ...authHeaders,
      ...headers,
    };

    // Make the request
    const response = await fetch(url, {
      ...restOptions,
      headers: finalHeaders,
    });

    // Handle authentication errors
    if (response.status === 401 && requireAuth) {
      console.log('Authentication failed, logging out');
      await logout();
      throw new Error('Authentication expired');
    }

    return response;
  }, [getAuthHeaders, isAuthenticated, logout]);

  return authenticatedFetch;
}

// Convenience hook for common API patterns
export function useAuthenticatedAPI() {
  const authenticatedFetch = useAuthenticatedFetch();

  const get = useCallback(async (url: string, requireAuth = true) => {
    const response = await authenticatedFetch(url, { 
      method: 'GET',
      requireAuth 
    });
    
    if (!response.ok) {
      throw new Error(`GET ${url} failed: ${response.status}`);
    }
    
    return response.json();
  }, [authenticatedFetch]);

  const post = useCallback(async (url: string, data: any, requireAuth = true) => {
    const response = await authenticatedFetch(url, {
      method: 'POST',
      body: JSON.stringify(data),
      requireAuth
    });
    
    if (!response.ok) {
      throw new Error(`POST ${url} failed: ${response.status}`);
    }
    
    return response.json();
  }, [authenticatedFetch]);

  const put = useCallback(async (url: string, data: any, requireAuth = true) => {
    const response = await authenticatedFetch(url, {
      method: 'PUT',
      body: JSON.stringify(data),
      requireAuth
    });
    
    if (!response.ok) {
      throw new Error(`PUT ${url} failed: ${response.status}`);
    }
    
    return response.json();
  }, [authenticatedFetch]);

  const del = useCallback(async (url: string, requireAuth = true) => {
    const response = await authenticatedFetch(url, {
      method: 'DELETE',
      requireAuth
    });
    
    if (!response.ok) {
      throw new Error(`DELETE ${url} failed: ${response.status}`);
    }
    
    return response.json();
  }, [authenticatedFetch]);

  return {
    get,
    post,
    put,
    delete: del,
    fetch: authenticatedFetch,
  };
}
