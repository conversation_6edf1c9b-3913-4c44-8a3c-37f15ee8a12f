import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Brain,
  Shield,
  Cpu,
  Database,
  TrendingUp,
  Settings,
  Bell,
  User,
  ChevronDown,
  Globe,
  Lock,
  Zap,
  Menu,
  X,
  LogIn,
  LogOut
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { LoginModal } from './LoginModal';

interface EnterpriseHeaderProps {
  className?: string;
}

export function EnterpriseHeader({ className }: EnterpriseHeaderProps) {
  const navigate = useNavigate();
  const { user, isAuthenticated, logout } = useAuth();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLoginSuccess = (userData: any) => {
    console.log('Login successful:', userData);
    setShowLoginModal(false);
  };

  const handleLogout = async () => {
    await logout();
    setShowUserMenu(false);
  };

  return (
    <header className={cn(
      "relative bg-gradient-to-r from-white via-slate-50 to-white backdrop-blur-xl border-b border-gray-200/30 shadow-2xl sticky top-0 z-50 transition-all duration-300 overflow-x-hidden",
      className
    )}>
      {/* Premium Header Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-transparent to-purple-500/5"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent"></div>

      <div className="relative px-4 sm:px-6 lg:px-8 py-4 sm:py-5">
        <div className="flex items-center justify-between">
          {/* Enterprise Branding */}
          <div className="flex items-center space-x-3 sm:space-x-6">
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="relative group">
                <div className="p-3 sm:p-4 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-700 rounded-2xl shadow-2xl transform transition-all duration-500 group-hover:scale-110 group-hover:shadow-blue-500/30 group-hover:rotate-3">
                  <Brain className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-3 h-3 sm:w-4 sm:h-4 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-600/30 to-purple-700/30 rounded-2xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 scale-150"></div>
              </div>
              <div className="min-w-0">
                <h1 className="text-xl sm:text-3xl font-black bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent truncate">
                  CodeCrusher Enterprise
                </h1>
                <p className="text-sm sm:text-base text-blue-600 font-bold tracking-wide hidden sm:block">AI-POWERED DEVELOPMENT PLATFORM</p>
              </div>
            </div>

            {/* Complete Navigation - All Major Functionalities Restored */}
            <nav className="hidden lg:flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/dashboard')}
                className="relative px-3 py-2 text-gray-700 hover:text-white font-medium text-sm rounded-lg transition-all duration-300 group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                <span className="relative z-10">Dashboard</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/status')}
                className="relative px-3 py-2 text-gray-700 hover:text-white font-medium text-sm rounded-lg transition-all duration-300 group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-500 to-green-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                <span className="relative z-10">Analytics</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/backend')}
                className="relative px-3 py-2 text-gray-700 hover:text-white font-medium text-sm rounded-lg transition-all duration-300 group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                <span className="relative z-10">Projects</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/ui')}
                className="relative px-3 py-2 text-gray-700 hover:text-white font-medium text-sm rounded-lg transition-all duration-300 group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500 to-red-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                <span className="relative z-10">Reports</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/enhanced')}
                className="relative px-3 py-2 text-gray-700 hover:text-white font-medium text-sm rounded-lg transition-all duration-300 group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-teal-500 to-cyan-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                <span className="relative z-10">API</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate('/clean')}
                className="relative px-3 py-2 text-gray-700 hover:text-white font-medium text-sm rounded-lg transition-all duration-300 group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-indigo-500 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                <span className="relative z-10">Settings</span>
              </Button>
            </nav>
          </div>

          {/* Premium Enterprise Status & Controls - Responsive */}
          <div className="flex items-center space-x-1">
            {/* Premium Enterprise Badges - Hide on smaller screens */}
            <div className="hidden xl:flex items-center space-x-1">
              <div className="px-1 py-0.5 bg-gradient-to-r from-emerald-500/10 to-green-500/10 border border-emerald-500/30 rounded-full backdrop-blur-sm hover:from-emerald-500/20 hover:to-green-500/20 transition-all duration-300">
                <div className="flex items-center space-x-1 text-emerald-600">
                  <Globe className="w-2 h-2" />
                  <span className="font-semibold text-xs">Prod</span>
                </div>
              </div>
              <div className="px-1 py-0.5 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 border border-blue-500/30 rounded-full backdrop-blur-sm hover:from-blue-500/20 hover:to-indigo-500/20 transition-all duration-300">
                <div className="flex items-center space-x-1 text-blue-600">
                  <Lock className="w-2 h-2" />
                  <span className="font-semibold text-xs">Ent</span>
                </div>
              </div>
            </div>

            {/* Premium Enterprise Controls - Essential Only */}
            <div className="flex items-center space-x-1">
              <Button variant="ghost" size="sm" className="relative p-1.5 rounded-lg hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 transition-all duration-300 group">
                <Bell className="w-3 h-3 text-gray-600 group-hover:text-red-600 transition-colors duration-300" />
                <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-gradient-to-r from-red-500 to-rose-500 rounded-full animate-pulse"></div>
              </Button>

              <Button variant="ghost" size="sm" className="hidden sm:flex p-1.5 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group">
                <Settings className="w-3 h-3 group-hover:rotate-180 transition-transform duration-500" />
              </Button>

              <div className="hidden sm:block w-px h-4 bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>

              {/* Authentication Section */}
              {isAuthenticated ? (
                <div className="relative">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    className="flex items-center space-x-1 px-1 py-1 rounded-lg text-gray-700 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group"
                  >
                    <div className="relative">
                      <div className="w-6 h-6 bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-blue-500/30 transition-all duration-300">
                        <User className="w-3 h-3 text-white" />
                      </div>
                      <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full border border-white"></div>
                    </div>
                    <div className="hidden xl:block">
                      <div className="text-xs font-bold text-gray-900 truncate max-w-20">
                        {user?.email?.split('@')[0] || 'User'}
                      </div>
                    </div>
                    <ChevronDown className="w-2 h-2 transition-transform duration-300 group-hover:rotate-180" />
                  </Button>

                  {/* User Dropdown Menu */}
                  {showUserMenu && (
                    <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-2 z-50">
                      <div className="px-4 py-2 border-b border-gray-100">
                        <div className="text-sm font-medium text-gray-900">{user?.email}</div>
                        <div className="text-xs text-gray-500 capitalize">{user?.role || 'user'}</div>
                      </div>
                      <button
                        onClick={handleLogout}
                        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                      >
                        <LogOut className="w-4 h-4" />
                        <span>Logout</span>
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowLoginModal(true)}
                  className="flex items-center space-x-1 px-2 py-1 rounded-lg text-gray-700 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group"
                >
                  <LogIn className="w-4 h-4" />
                  <span className="hidden sm:inline text-sm font-medium">Login</span>
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Mobile menu removed - navigation now in sidebar */}
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLoginSuccess={handleLoginSuccess}
      />
    </header>
  );
}

interface EnterpriseStatsBarProps {
  className?: string;
}

export function EnterpriseStatsBar({ className }: EnterpriseStatsBarProps) {
  return (
    <div className={cn(
      "relative bg-gradient-to-r from-slate-50 via-white to-slate-50 backdrop-blur-xl border-b border-gray-200/30 py-3 sm:py-4 transition-all duration-300 overflow-hidden",
      className
    )}>
      {/* Premium Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/3 via-transparent to-purple-500/3"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/20 to-transparent"></div>

      <div className="relative px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 sm:space-x-8 lg:space-x-12">
            <div className="flex items-center space-x-3 px-4 py-2 rounded-full bg-emerald-50 border border-emerald-200 text-sm whitespace-nowrap group hover:bg-emerald-100 transition-all duration-300">
              <TrendingUp className="w-4 h-4 text-emerald-600 group-hover:scale-110 transition-transform duration-200" />
              <span className="text-emerald-700 font-medium hidden sm:inline">Performance:</span>
              <span className="text-emerald-700 font-medium sm:hidden">Perf:</span>
              <span className="font-bold text-emerald-600">98.7%</span>
            </div>
            <div className="flex items-center space-x-3 px-4 py-2 rounded-full bg-blue-50 border border-blue-200 text-sm whitespace-nowrap group hover:bg-blue-100 transition-all duration-300">
              <Zap className="w-4 h-4 text-blue-600 group-hover:scale-110 transition-transform duration-200" />
              <span className="text-blue-700 font-medium hidden sm:inline">Processing:</span>
              <span className="text-blue-700 font-medium sm:hidden">Proc:</span>
              <span className="font-bold text-blue-600">2.3s</span>
            </div>
            <div className="flex items-center space-x-3 px-4 py-2 rounded-full bg-purple-50 border border-purple-200 text-sm whitespace-nowrap group hover:bg-purple-100 transition-all duration-300">
              <Database className="w-4 h-4 text-purple-600 group-hover:scale-110 transition-transform duration-200" />
              <span className="text-purple-700 font-medium hidden lg:inline">Cache Hit:</span>
              <span className="text-purple-700 font-medium lg:hidden">Cache:</span>
              <span className="font-bold text-purple-600">94.2%</span>
            </div>
          </div>

          <div className="hidden md:flex items-center space-x-4 lg:space-x-6">
            <div className="flex items-center space-x-3 px-3 py-1 rounded-full bg-gray-50 border border-gray-200 text-xs text-gray-600">
              <span className="whitespace-nowrap font-medium">Updated: {new Date().toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-3 text-xs text-gray-500">
              <span className="whitespace-nowrap font-medium">v2.1.0</span>
              <div className="w-px h-4 bg-gray-300"></div>
              <span className="whitespace-nowrap font-medium hidden lg:inline">Enterprise Edition</span>
              <span className="whitespace-nowrap font-medium lg:hidden">Enterprise</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
