{"version": 3, "file": "hoverProvider.js", "sourceRoot": "", "sources": ["../src/hoverProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,iCAAiC;AACjC,+BAAuD;AAEvD;;GAEG;AACH,SAAgB,qBAAqB,CAAC,OAAgC;IAClE,MAAM,aAAa,GAAG,IAAI,wBAAwB,EAAE,CAAC;IAErD,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAClC,EAAE,MAAM,EAAE,MAAM,EAAE,EAClB,aAAa,CAChB,CACJ,CAAC;IAEF,OAAO,aAAa,CAAC;AACzB,CAAC;AAXD,sDAWC;AAED;;GAEG;AACH,MAAa,wBAAwB;IAIjC;QAHQ,sBAAiB,GAAqB,EAAE,CAAC;QAI7C,yCAAyC;QACzC,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEtF,kBAAkB;QAClB,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAED;;OAEG;IACU,oBAAoB;;YAC7B,IAAI;gBACA,6BAA6B;gBAC7B,MAAM,eAAe,GAAG,MAAM,oBAAc,CAAC,eAAe,EAAE,CAAC;gBAC/D,IAAI,CAAC,eAAe,EAAE;oBAClB,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;oBAC5B,OAAO;iBACV;gBAED,qBAAqB;gBACrB,MAAM,aAAa,GAAG,MAAM,oBAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBAC7D,IAAI,CAAC,iBAAiB,GAAG,aAAa,CAAC,OAAO,CAAC;aAClD;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACzD,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;aAC/B;QACL,CAAC;KAAA;IAED;;OAEG;IACU,YAAY,CACrB,QAA6B,EAC7B,QAAyB,EACzB,KAA+B;;YAE/B,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACrC,OAAO,IAAI,CAAC;aACf;YAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC;YACrC,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,iCAAiC;YAEvE,gDAAgD;YAChD,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAClD,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,WAAW;gBACpC,CAAC,KAAK,CAAC,SAAS,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACpE,KAAK,CAAC,WAAW,KAAK,UAAU,CACnC,CAAC;YAEF,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,OAAO,IAAI,CAAC;aACf;YAED,uBAAuB;YACvB,MAAM,eAAe,GAA4B,EAAE,CAAC;YAEpD,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE;gBACzB,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC7C,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;gBAE1B,aAAa;gBACb,QAAQ,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAC;gBAExD,gBAAgB;gBAChB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,CAAC;gBAC7D,QAAQ,CAAC,cAAc,CAAC,kBAAkB,SAAS,MAAM,CAAC,CAAC;gBAE3D,iBAAiB;gBACjB,QAAQ,CAAC,cAAc,CAAC,cAAc,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,QAAQ,OAAO,CAAC,CAAC;gBAE7E,kBAAkB;gBAClB,QAAQ,CAAC,cAAc,CAAC,eAAe,KAAK,CAAC,SAAS,SAAS,KAAK,CAAC,UAAU,UAAU,CAAC,CAAC;gBAE3F,iBAAiB;gBACjB,QAAQ,CAAC,cAAc,CAAC,eAAe,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC;gBAE1E,kCAAkC;gBAClC,IAAI,KAAK,CAAC,QAAQ,EAAE;oBAChB,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAAC,CAAC;oBACpD,QAAQ,CAAC,cAAc,CAAC,6GAA6G,CAAC,CAAC;oBACvI,QAAQ,CAAC,cAAc,CAAC,wFAAwF,CAAC,CAAC;iBACrH;gBAED,+BAA+B;gBAC/B,IAAI,KAAK,CAAC,KAAK,EAAE;oBACb,QAAQ,CAAC,cAAc,CAAC,gBAAgB,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC;oBAC3D,QAAQ,CAAC,cAAc,CAAC,sEAAsE,CAAC,CAAC;iBACnG;gBAED,eAAe;gBACf,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrC,QAAQ,CAAC,cAAc,CAAC,aAAa,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;iBACrE;qBAAM;oBACH,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAC;oBACnD,QAAQ,CAAC,cAAc,CAAC,kFAAkF,CAAC,CAAC;iBAC/G;gBAED,cAAc;gBACd,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;gBACnC,QAAQ,CAAC,cAAc,CAAC,0DAA0D,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAElI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAClC;YAED,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC7C,CAAC;KAAA;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACxC;IACL,CAAC;CACJ;AAzHD,4DAyHC"}