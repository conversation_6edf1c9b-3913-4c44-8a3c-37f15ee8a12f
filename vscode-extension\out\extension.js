"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const path = require("path");
const axios_1 = require("axios");
// Import our new components
const panel_1 = require("./panel");
const lensProvider_1 = require("./lensProvider");
const diagnostics_1 = require("./diagnostics");
const hoverProvider_1 = require("./hoverProvider");
const api_1 = require("./api");
// Status bar item for CodeCrusher
let statusBarItem;
let telemetryRefreshInterval;
class SettingsSync {
    constructor(context) {
        this.isLocalChange = false;
        this.currentWorkspaceId = null;
        this.context = context;
        const config = vscode.workspace.getConfiguration('codecrusher');
        this.dashboardUrl = config.get('dashboardUrl') || 'http://localhost:8000';
        this.currentWorkspaceId = this.getWorkspaceId();
    }
    getWorkspaceId() {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return null;
        }
        // Use the first workspace folder as the primary workspace
        const rootPath = workspaceFolders[0].uri.fsPath;
        // Create a hash of the workspace path for privacy and consistency
        const crypto = require('crypto');
        const hash = crypto.createHash('sha256').update(rootPath).digest('hex');
        return hash.substring(0, 16); // Use first 16 characters for brevity
    }
    getWorkspaceSettingsKey() {
        if (!this.currentWorkspaceId) {
            return 'cc-settings:global';
        }
        return `cc-settings:${this.currentWorkspaceId}`;
    }
    loadWorkspaceSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.currentWorkspaceId) {
                return null;
            }
            const settingsKey = this.getWorkspaceSettingsKey();
            return this.context.workspaceState.get(settingsKey, null);
        });
    }
    getEffectiveSettings() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            // Try to load workspace-specific settings first
            const workspaceSettings = yield this.loadWorkspaceSettings();
            if (workspaceSettings) {
                return workspaceSettings;
            }
            // Fallback to global configuration
            const config = vscode.workspace.getConfiguration('codecrusher');
            return {
                model: config.get('defaultModel') || 'auto',
                fallback: (_a = config.get('enableFallback')) !== null && _a !== void 0 ? _a : true,
                mode: config.get('defaultMode') || 'preview',
                workspace_id: this.currentWorkspaceId || undefined
            };
        });
    }
    copySettingsToGlobal() {
        return __awaiter(this, void 0, void 0, function* () {
            const workspaceSettings = yield this.loadWorkspaceSettings();
            if (!workspaceSettings) {
                vscode.window.showWarningMessage('No workspace settings found to copy');
                return;
            }
            const config = vscode.workspace.getConfiguration('codecrusher');
            yield config.update('defaultModel', workspaceSettings.model, vscode.ConfigurationTarget.Global);
            yield config.update('enableFallback', workspaceSettings.fallback, vscode.ConfigurationTarget.Global);
            yield config.update('defaultMode', workspaceSettings.mode, vscode.ConfigurationTarget.Global);
            vscode.window.showInformationMessage(`Workspace settings copied to global: Model=${workspaceSettings.model}, Fallback=${workspaceSettings.fallback}, Mode=${workspaceSettings.mode}`);
        });
    }
    initialize() {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            const config = vscode.workspace.getConfiguration('codecrusher');
            const enableSync = (_a = config.get('enableSettingsSync')) !== null && _a !== void 0 ? _a : true;
            const syncInterval = config.get('syncInterval') || 120;
            if (!enableSync) {
                console.log('Settings sync disabled by configuration');
                return;
            }
            // Initial sync on activation
            yield this.pullRemoteSettings();
            // Start periodic sync if interval > 0
            if (syncInterval > 0) {
                this.syncInterval = setInterval(() => {
                    this.pullRemoteSettings();
                }, syncInterval * 1000);
            }
            // Watch for local configuration changes
            vscode.workspace.onDidChangeConfiguration(event => {
                if (event.affectsConfiguration('codecrusher')) {
                    this.onLocalSettingsChange();
                }
            });
        });
    }
    pullRemoteSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.currentWorkspaceId) {
                console.log('No workspace detected, skipping settings sync');
                return;
            }
            try {
                const url = `${this.dashboardUrl}/api/settings/vscode/${this.currentWorkspaceId}`;
                const response = yield axios_1.default.get(url, {
                    timeout: 5000
                });
                const remoteSettings = response.data;
                const settingsKey = this.getWorkspaceSettingsKey();
                const lastSyncTimestamp = this.context.workspaceState.get(`${settingsKey}:timestamp`, 0);
                // Check if remote settings are newer
                if (remoteSettings.lastSyncTimestamp && remoteSettings.lastSyncTimestamp > lastSyncTimestamp) {
                    yield this.applyRemoteSettings(remoteSettings);
                    yield this.context.workspaceState.update(`${settingsKey}:timestamp`, remoteSettings.lastSyncTimestamp);
                }
            }
            catch (error) {
                // Silently fail if dashboard is not available
                console.log('Dashboard not available for workspace settings sync:', error.message);
            }
        });
    }
    pushLocalSettings(force = false) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            if (this.isLocalChange && !force) {
                this.isLocalChange = false; // Reset flag
                return; // Don't push if this was triggered by a remote change
            }
            if (!this.currentWorkspaceId) {
                console.log('No workspace detected, skipping settings push');
                return;
            }
            try {
                const config = vscode.workspace.getConfiguration('codecrusher');
                const localSettings = {
                    model: config.get('defaultModel') || 'auto',
                    fallback: (_a = config.get('enableFallback')) !== null && _a !== void 0 ? _a : true,
                    mode: config.get('defaultMode') || 'preview',
                    lastSyncTimestamp: Date.now(),
                    workspace_id: this.currentWorkspaceId
                };
                const url = `${this.dashboardUrl}/api/settings/vscode/${this.currentWorkspaceId}`;
                yield axios_1.default.post(url, localSettings, {
                    timeout: 5000,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const settingsKey = this.getWorkspaceSettingsKey();
                yield this.context.workspaceState.update(`${settingsKey}:timestamp`, localSettings.lastSyncTimestamp);
                console.log('Workspace settings pushed to dashboard successfully');
            }
            catch (error) {
                console.log('Failed to push workspace settings to dashboard:', error.message);
            }
        });
    }
    applyRemoteSettings(remoteSettings) {
        var _a, _b;
        return __awaiter(this, void 0, void 0, function* () {
            this.isLocalChange = true; // Flag to prevent push loop
            const config = vscode.workspace.getConfiguration('codecrusher');
            // Update workspace-scoped settings to match remote
            yield config.update('defaultModel', remoteSettings.model, vscode.ConfigurationTarget.Workspace);
            yield config.update('enableFallback', remoteSettings.fallback, vscode.ConfigurationTarget.Workspace);
            yield config.update('defaultMode', remoteSettings.mode, vscode.ConfigurationTarget.Workspace);
            // Store workspace settings in workspaceState for persistence
            const settingsKey = this.getWorkspaceSettingsKey();
            yield this.context.workspaceState.update(settingsKey, remoteSettings);
            console.log('Applied remote workspace settings:', remoteSettings);
            // Show notification to user
            const workspaceName = ((_b = (_a = vscode.workspace.workspaceFolders) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.name) || 'workspace';
            vscode.window.showInformationMessage(`CodeCrusher settings synced for ${workspaceName}: Model=${remoteSettings.model}, Fallback=${remoteSettings.fallback}, Mode=${remoteSettings.mode}`);
        });
    }
    onLocalSettingsChange() {
        return __awaiter(this, void 0, void 0, function* () {
            // Debounce to avoid multiple rapid calls
            setTimeout(() => {
                this.pushLocalSettings();
            }, 1000);
        });
    }
    dispose() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
    }
}
// Global settings sync instance
let settingsSync;
// Activation function
function activate(context) {
    console.log('CodeCrusher extension is now active');
    // Initialize settings sync
    settingsSync = new SettingsSync(context);
    settingsSync.initialize();
    context.subscriptions.push({ dispose: () => settingsSync.dispose() });
    // Register commands
    const injectCmd = vscode.commands.registerCommand('codecrusher.inject', injectWithPreview);
    const showLogsCmd = vscode.commands.registerCommand('codecrusher.showLogs', showLiveLogs);
    const copySettingsCmd = vscode.commands.registerCommand('codecrusher.copySettingsToGlobal', () => settingsSync.copySettingsToGlobal());
    const runInjectionCmd = vscode.commands.registerCommand('codecrusher.runInjection', runInjection);
    const viewTelemetryCmd = vscode.commands.registerCommand('codecrusher.viewTelemetry', viewTelemetry);
    const replayInjectionCmd = vscode.commands.registerCommand('codecrusher.replayInjection', replayInjection);
    const insertTagCmd = vscode.commands.registerCommand('codecrusher.insertTag', insertTag);
    const showStatusCmd = vscode.commands.registerCommand('codecrusher.showStatus', showStatus);
    // Add commands to context
    context.subscriptions.push(injectCmd);
    context.subscriptions.push(showLogsCmd);
    context.subscriptions.push(copySettingsCmd);
    context.subscriptions.push(runInjectionCmd);
    context.subscriptions.push(viewTelemetryCmd);
    context.subscriptions.push(replayInjectionCmd);
    context.subscriptions.push(insertTagCmd);
    context.subscriptions.push(showStatusCmd);
    // Register the sidebar view provider
    const codeCrusherPanel = new panel_1.CodeCrusherPanel(context.extensionUri);
    context.subscriptions.push(vscode.window.registerWebviewViewProvider(panel_1.CodeCrusherPanel.viewType, codeCrusherPanel));
    // Register the CodeLens provider
    const codeLensProvider = new lensProvider_1.CodeCrusherLensProvider();
    context.subscriptions.push(vscode.languages.registerCodeLensProvider({ pattern: '**/*' }, codeLensProvider));
    // Register the diagnostics provider
    const diagnosticsProvider = new diagnostics_1.CodeCrusherDiagnostics();
    context.subscriptions.push(diagnosticsProvider);
    // Register the hover provider for injections
    const injectionHoverProvider = new hoverProvider_1.CodeCrusherHoverProvider();
    context.subscriptions.push(vscode.languages.registerHoverProvider({ pattern: '**/*' }, injectionHoverProvider));
    // Register new commands
    context.subscriptions.push(vscode.commands.registerCommand('codecrusher.openPanel', () => {
        vscode.commands.executeCommand('workbench.view.extension.codecrusher-sidebar');
    }));
    context.subscriptions.push(vscode.commands.registerCommand('codecrusher.scanNow', () => __awaiter(this, void 0, void 0, function* () {
        try {
            // Check if server is running
            const isServerRunning = yield api_1.CodeCrusherApi.isServerRunning();
            if (!isServerRunning) {
                vscode.window.showErrorMessage('Cannot connect to CodeCrusher API server. Make sure it\'s running with: codecrusher serve run');
                return;
            }
            // Show progress notification
            vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: 'CodeCrusher: Scanning project...',
                cancellable: false
            }, (progress) => __awaiter(this, void 0, void 0, function* () {
                try {
                    // Run scan
                    const scan = yield api_1.CodeCrusherApi.runScan();
                    // Refresh CodeLens and diagnostics
                    codeLensProvider.refreshTelemetryData();
                    diagnosticsProvider.refreshTelemetryData();
                    // Show notification
                    vscode.window.showInformationMessage(`CodeCrusher scan complete: ${scan.anomalies_count} anomalies detected`);
                    // Refresh panel
                    codeCrusherPanel.refresh();
                }
                catch (error) {
                    console.error('Error scanning project:', error);
                    vscode.window.showErrorMessage('Failed to scan project. Make sure the CodeCrusher API server is running.');
                }
            }));
        }
        catch (error) {
            console.error('Error scanning project:', error);
            vscode.window.showErrorMessage('Failed to scan project. Make sure the CodeCrusher API server is running.');
        }
    })));
    context.subscriptions.push(vscode.commands.registerCommand('codecrusher.openTrends', () => __awaiter(this, void 0, void 0, function* () {
        try {
            // Check if server is running
            const isServerRunning = yield api_1.CodeCrusherApi.isServerRunning();
            if (!isServerRunning) {
                vscode.window.showErrorMessage('Cannot connect to CodeCrusher API server. Make sure it\'s running with: codecrusher serve run');
                return;
            }
            // Create and show webview panel
            const panel = vscode.window.createWebviewPanel('codecrusher.trends', 'CodeCrusher Trends', vscode.ViewColumn.One, {
                enableScripts: true
            });
            // Show loading message
            panel.webview.html = `
          <html>
            <body style="font-family: var(--vscode-font-family); padding: 20px;">
              <h1>Loading CodeCrusher Trends...</h1>
            </body>
          </html>
        `;
            // Get trends data
            const trends = yield api_1.CodeCrusherApi.getTrends();
            // Generate HTML for trends
            let html = `
          <html>
            <head>
              <style>
                body {
                  font-family: var(--vscode-font-family);
                  padding: 20px;
                }
                h1 {
                  color: var(--vscode-editor-foreground);
                }
                .section {
                  margin-bottom: 20px;
                }
                table {
                  border-collapse: collapse;
                  width: 100%;
                }
                th, td {
                  border: 1px solid var(--vscode-panel-border);
                  padding: 8px;
                  text-align: left;
                }
                th {
                  background-color: var(--vscode-editor-selectionBackground);
                }
              </style>
            </head>
            <body>
              <h1>CodeCrusher Trends</h1>

              <div class="section">
                <h2>Time Period</h2>
                <p>From: ${trends.time_period.since}</p>
                <p>To: ${trends.time_period.until}</p>
              </div>

              <div class="section">
                <h2>Daily Metrics</h2>
                <table>
                  <tr>
                    <th>Date</th>
                    <th>Injections</th>
                    <th>Fallbacks</th>
                    <th>Fallback Rate</th>
                    <th>Errors</th>
                  </tr>
        `;
            // Add rows for each day
            for (const [date, metrics] of Object.entries(trends.metrics)) {
                html += `
            <tr>
              <td>${date}</td>
              <td>${metrics.total_injections}</td>
              <td>${metrics.fallbacks}</td>
              <td>${metrics.fallback_rate}%</td>
              <td>${metrics.errors}</td>
            </tr>
          `;
            }
            html += `
                </table>
              </div>
            </body>
          </html>
        `;
            // Update webview with trends data
            panel.webview.html = html;
        }
        catch (error) {
            console.error('Error opening trends:', error);
            vscode.window.showErrorMessage('Failed to open trends. Make sure the CodeCrusher API server is running.');
        }
    })));
    context.subscriptions.push(vscode.commands.registerCommand('codecrusher.openScan', () => __awaiter(this, void 0, void 0, function* () {
        try {
            // Check if server is running
            const isServerRunning = yield api_1.CodeCrusherApi.isServerRunning();
            if (!isServerRunning) {
                vscode.window.showErrorMessage('Cannot connect to CodeCrusher API server. Make sure it\'s running with: codecrusher serve run');
                return;
            }
            // Create and show webview panel
            const panel = vscode.window.createWebviewPanel('codecrusher.scan', 'CodeCrusher Scan Results', vscode.ViewColumn.One, {
                enableScripts: true
            });
            // Show loading message
            panel.webview.html = `
          <html>
            <body style="font-family: var(--vscode-font-family); padding: 20px;">
              <h1>Loading CodeCrusher Scan Results...</h1>
            </body>
          </html>
        `;
            // Get scan data
            const scan = yield api_1.CodeCrusherApi.runScan('24h', undefined, undefined, 100, true);
            // Generate HTML for scan results
            let html = `
          <html>
            <head>
              <style>
                body {
                  font-family: var(--vscode-font-family);
                  padding: 20px;
                }
                h1 {
                  color: var(--vscode-editor-foreground);
                }
                .section {
                  margin-bottom: 20px;
                }
                .anomaly {
                  margin-bottom: 15px;
                  padding: 10px;
                  border: 1px solid var(--vscode-panel-border);
                  border-radius: 5px;
                }
                .anomaly-header {
                  font-weight: bold;
                  margin-bottom: 5px;
                }
                .anomaly-type {
                  color: var(--vscode-errorForeground);
                }
                .anomaly-timestamp {
                  color: var(--vscode-descriptionForeground);
                  font-size: 0.9em;
                }
                .anomaly-description {
                  margin-bottom: 5px;
                }
                .anomaly-action {
                  font-style: italic;
                  color: var(--vscode-textLink-foreground);
                }
              </style>
            </head>
            <body>
              <h1>CodeCrusher Scan Results</h1>

              <div class="section">
                <h2>Scan Summary</h2>
                <p>Time Period: ${scan.time_period}</p>
                <p>Model Filter: ${scan.model_filter}</p>
                <p>Tag Filter: ${scan.tag_filter}</p>
                <p>Entries Analyzed: ${scan.entries_analyzed}</p>
                <p>Scan Duration: ${scan.scan_duration_seconds.toFixed(3)} seconds</p>
                <p>Anomalies Found: ${scan.anomalies_count}</p>
              </div>

              <div class="section">
                <h2>Anomalies</h2>
        `;
            if (scan.anomalies.length === 0) {
                html += '<p>No anomalies found.</p>';
            }
            else {
                for (const anomaly of scan.anomalies) {
                    html += `
              <div class="anomaly">
                <div class="anomaly-header">
                  <span class="anomaly-type">${anomaly.type}</span>
                  ${anomaly.model ? ` | Model: ${anomaly.model}` : ''}
                  <span class="anomaly-timestamp"> | ${new Date(anomaly.timestamp).toLocaleString()}</span>
                </div>
                <div class="anomaly-description">${anomaly.description}</div>
                ${anomaly.suggested_action ? `<div class="anomaly-action">Suggestion: ${anomaly.suggested_action}</div>` : ''}
              </div>
            `;
                }
            }
            html += `
              </div>
            </body>
          </html>
        `;
            // Update webview with scan results
            panel.webview.html = html;
        }
        catch (error) {
            console.error('Error opening scan results:', error);
            vscode.window.showErrorMessage('Failed to open scan results. Make sure the CodeCrusher API server is running.');
        }
    })));
    context.subscriptions.push(vscode.commands.registerCommand('codecrusher.showInjectionDetails', (entry) => {
        // Create and show webview panel
        const panel = vscode.window.createWebviewPanel('codecrusher.injectionDetails', 'CodeCrusher Injection Details', vscode.ViewColumn.One, {
            enableScripts: true
        });
        // Generate HTML for injection details
        const html = `
        <html>
          <head>
            <style>
              body {
                font-family: var(--vscode-font-family);
                padding: 20px;
              }
              h1 {
                color: var(--vscode-editor-foreground);
              }
              .section {
                margin-bottom: 20px;
              }
              .label {
                font-weight: bold;
              }
              .value {
                margin-left: 10px;
              }
              .error {
                color: var(--vscode-errorForeground);
              }
              .warning {
                color: var(--vscode-warningForeground);
              }
              .info {
                color: var(--vscode-infoForeground);
              }
              .tags {
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
              }
              .tag {
                background-color: var(--vscode-badge-background);
                color: var(--vscode-badge-foreground);
                padding: 2px 8px;
                border-radius: 10px;
                font-size: 0.9em;
              }
            </style>
          </head>
          <body>
            <h1>CodeCrusher Injection Details</h1>

            <div class="section">
              <div><span class="label">Timestamp:</span> <span class="value">${new Date(entry.timestamp).toLocaleString()}</span></div>
              <div><span class="label">Model:</span> <span class="value">${entry.model} (${entry.provider})</span></div>
              <div><span class="label">Operation Type:</span> <span class="value">${entry.operation_type}</span></div>
              <div><span class="label">Tokens:</span> <span class="value">${entry.tokens_in} in / ${entry.tokens_out} out</span></div>
              <div><span class="label">Cached:</span> <span class="value">${entry.cached ? 'Yes' : 'No'}</span></div>
              <div><span class="label">Fallback:</span> <span class="value ${entry.fallback ? 'warning' : ''}">${entry.fallback ? 'Yes' : 'No'}</span></div>
              <div><span class="label">Error:</span> <span class="value ${entry.error ? 'error' : ''}">${entry.error || 'None'}</span></div>
            </div>

            <div class="section">
              <div class="label">Tags:</div>
              ${entry.tags && entry.tags.length > 0 ? `
                <div class="tags">
                  ${entry.tags.map((tag) => `<span class="tag">${tag}</span>`).join('')}
                </div>
              ` : '<div class="value warning">No tags</div>'}
            </div>

            <div class="section">
              <div class="label">File:</div>
              <div class="value">${entry.file_path || 'Unknown'}</div>
              <div class="label">Line:</div>
              <div class="value">${entry.line_number || 'Unknown'}</div>
            </div>
          </body>
        </html>
      `;
        // Update webview with injection details
        panel.webview.html = html;
    }));
    context.subscriptions.push(vscode.commands.registerCommand('codecrusher.resendInjection', (entry) => __awaiter(this, void 0, void 0, function* () {
        // TODO: Implement resending injection to AI
        vscode.window.showInformationMessage('Resending injection is not yet implemented.');
    })));
    // Create status bar item
    if (vscode.workspace.getConfiguration('codecrusher').get('showStatusBar')) {
        statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        statusBarItem.command = 'codecrusher.showStatus';
        statusBarItem.text = '$(rocket) CodeCrusher';
        statusBarItem.tooltip = 'CodeCrusher Status';
        statusBarItem.show();
        context.subscriptions.push(statusBarItem);
        // Start telemetry refresh interval
        const refreshInterval = vscode.workspace.getConfiguration('codecrusher').get('telemetryRefreshInterval');
        if (refreshInterval > 0) {
            telemetryRefreshInterval = setInterval(updateStatusBar, refreshInterval * 1000);
            context.subscriptions.push({ dispose: () => clearInterval(telemetryRefreshInterval) });
        }
        // Initial update
        updateStatusBar();
    }
    // Check if server is running on startup
    checkServerStatus();
    // Register hover provider for injection tags
    const hoverProvider = vscode.languages.registerHoverProvider(['python', 'javascript', 'typescript', 'java', 'csharp', 'go'], {
        provideHover(document, position, token) {
            const lineText = document.lineAt(position.line).text;
            const tagMatch = lineText.match(/# AI_INJECT: (\w+)/);
            if (tagMatch) {
                // Get telemetry data for this tag
                return getTelemetryForTag(tagMatch[1]).then(telemetry => {
                    if (telemetry) {
                        const markdown = new vscode.MarkdownString();
                        markdown.appendMarkdown(`## CodeCrusher Injection: ${tagMatch[1]}\n\n`);
                        markdown.appendMarkdown(`**Model:** ${telemetry.model}\n\n`);
                        markdown.appendMarkdown(`**Provider:** ${telemetry.provider}\n\n`);
                        markdown.appendMarkdown(`**Last Updated:** ${telemetry.timestamp}\n\n`);
                        markdown.appendMarkdown(`**Tokens:** ${telemetry.tokens_in} → ${telemetry.tokens_out}\n\n`);
                        markdown.appendMarkdown(`**Cached:** ${telemetry.cached ? 'Yes' : 'No'}\n\n`);
                        markdown.appendMarkdown(`**Tags:** ${telemetry.tags.join(', ')}\n\n`);
                        if (telemetry.error) {
                            markdown.appendMarkdown(`**Error:** ${telemetry.error}\n\n`);
                        }
                        markdown.appendMarkdown(`[Run Injection](command:codecrusher.runInjection) | [View Telemetry](command:codecrusher.viewTelemetry) | [Replay](command:codecrusher.replayInjection)`);
                        return new vscode.Hover(markdown);
                    }
                    return null;
                });
            }
            return null;
        }
    });
    context.subscriptions.push(hoverProvider);
    // Register completion provider for injection tags
    const completionProvider = vscode.languages.registerCompletionItemProvider(['python', 'javascript', 'typescript', 'java', 'csharp', 'go'], {
        provideCompletionItems(document, position, token, context) {
            const linePrefix = document.lineAt(position).text.substr(0, position.character);
            // Suggest AI_INJECT tag when typing a comment
            if (linePrefix.match(/^(\s*)(#|\/\/|\/\*)\s*AI/i)) {
                const injectCompletion = new vscode.CompletionItem('AI_INJECT', vscode.CompletionItemKind.Snippet);
                injectCompletion.insertText = new vscode.SnippetString('AI_INJECT: ${1:tag_name}');
                injectCompletion.documentation = new vscode.MarkdownString('Insert a CodeCrusher injection tag');
                injectCompletion.detail = 'CodeCrusher Injection Tag';
                return [injectCompletion];
            }
            return undefined;
        }
    }, '_' // Trigger completion when typing '_'
    );
    context.subscriptions.push(completionProvider);
}
exports.activate = activate;
// Deactivation function
function deactivate() {
    if (telemetryRefreshInterval) {
        clearInterval(telemetryRefreshInterval);
    }
}
exports.deactivate = deactivate;
// Show Live Logs WebView Panel
function showLiveLogs() {
    var _a, _b;
    return __awaiter(this, void 0, void 0, function* () {
        const config = vscode.workspace.getConfiguration('codecrusher');
        const logServerPort = config.get('logServerPort') || 11434;
        // Create or show the WebView panel
        if (liveLogsPanel) {
            liveLogsPanel.reveal(vscode.ViewColumn.Beside);
        }
        else {
            liveLogsPanel = vscode.window.createWebviewPanel('codecrusherLiveLogs', 'CodeCrusher Live Logs', vscode.ViewColumn.Beside, {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [vscode.Uri.joinPath(((_b = (_a = vscode.workspace.workspaceFolders) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.uri) || vscode.Uri.file(''), 'vscode-extension', 'media')]
            });
            // Load the HTML content
            const htmlPath = path.join(__dirname, '..', 'media', 'logPanel.html');
            let htmlContent = '';
            try {
                htmlContent = require('fs').readFileSync(htmlPath, 'utf8');
            }
            catch (error) {
                console.error('Failed to load log panel HTML:', error);
                htmlContent = getDefaultLogPanelHTML();
            }
            liveLogsPanel.webview.html = htmlContent;
            // Send initial configuration to the WebView
            liveLogsPanel.webview.postMessage({
                command: 'setPort',
                port: logServerPort
            });
            // Auto-connect to WebSocket
            liveLogsPanel.webview.postMessage({
                command: 'connect',
                port: logServerPort
            });
            // Handle panel disposal
            liveLogsPanel.onDidDispose(() => {
                liveLogsPanel = null;
            });
            // Handle messages from the WebView
            liveLogsPanel.webview.onDidReceiveMessage(message => {
                switch (message.command) {
                    case 'reconnect':
                        // Send reconnect command back to WebView
                        liveLogsPanel === null || liveLogsPanel === void 0 ? void 0 : liveLogsPanel.webview.postMessage({
                            command: 'connect',
                            port: logServerPort
                        });
                        break;
                }
            });
        }
        // Send a test log to verify connection
        setTimeout(() => {
            if (liveLogsPanel) {
                liveLogsPanel.webview.postMessage({
                    command: 'addLog',
                    level: 'info',
                    message: 'Live logs panel opened successfully',
                    timestamp: new Date().toISOString()
                });
            }
        }, 1000);
    });
}
// Get default HTML content if file loading fails
function getDefaultLogPanelHTML() {
    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CodeCrusher Live Logs</title>
        <style>
            body {
                font-family: 'Segoe UI', sans-serif;
                margin: 20px;
                background-color: var(--vscode-editor-background);
                color: var(--vscode-editor-foreground);
            }
            .header {
                border-bottom: 1px solid var(--vscode-panel-border);
                padding-bottom: 10px;
                margin-bottom: 20px;
            }
            .log-container {
                font-family: 'Consolas', monospace;
                font-size: 13px;
                line-height: 1.4;
            }
            .log-entry {
                margin-bottom: 8px;
                padding: 8px;
                border-radius: 4px;
                background-color: var(--vscode-editor-background);
            }
            .status {
                color: var(--vscode-descriptionForeground);
                font-style: italic;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🚀 CodeCrusher Live Logs</h1>
            <p class="status">WebSocket connection: <span id="status">Connecting...</span></p>
        </div>
        <div class="log-container" id="logs">
            <div class="log-entry">Waiting for logs...</div>
        </div>
        <script>
            // Basic WebSocket connection
            let ws = null;
            const port = 11434;

            function connect() {
                try {
                    ws = new WebSocket(\`ws://localhost:\${port}/logs\`);
                    document.getElementById('status').textContent = 'Connecting...';

                    ws.onopen = () => {
                        document.getElementById('status').textContent = 'Connected';
                        addLog('info', 'Connected to CodeCrusher log server');
                    };

                    ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            addLog(data.level || 'info', data.message);
                        } catch (e) {
                            addLog('info', event.data);
                        }
                    };

                    ws.onclose = () => {
                        document.getElementById('status').textContent = 'Disconnected';
                        addLog('warning', 'Connection lost');
                    };

                    ws.onerror = () => {
                        document.getElementById('status').textContent = 'Error';
                        addLog('error', 'Connection error');
                    };
                } catch (error) {
                    document.getElementById('status').textContent = 'Failed';
                    addLog('error', 'Failed to connect: ' + error.message);
                }
            }

            function addLog(level, message) {
                const logs = document.getElementById('logs');
                const entry = document.createElement('div');
                entry.className = 'log-entry';
                entry.innerHTML = \`[\${new Date().toLocaleTimeString()}] [\${level.toUpperCase()}] \${message}\`;
                logs.appendChild(entry);
                logs.scrollTop = logs.scrollHeight;
            }

            // Listen for messages from extension
            window.addEventListener('message', event => {
                const message = event.data;
                if (message.command === 'connect') {
                    connect();
                } else if (message.command === 'addLog') {
                    addLog(message.level, message.message);
                }
            });
        </script>
    </body>
    </html>
  `;
}
// Global variable to track the live logs panel
let liveLogsPanel = null;
// Inject with Preview/Apply command - Main CLI Hook Function
function injectWithPreview() {
    var _a;
    return __awaiter(this, void 0, void 0, function* () {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const document = editor.document;
        const filePath = document.uri.fsPath;
        // Save the document first
        yield document.save();
        // Get prompt from user
        const promptText = yield vscode.window.showInputBox({
            prompt: 'Enter prompt for CodeCrusher injection',
            placeHolder: 'e.g., add modern navigation bar, optimize this function, add error handling'
        });
        if (!promptText) {
            return; // User cancelled
        }
        // Get configuration
        const config = vscode.workspace.getConfiguration('codecrusher');
        const pythonPath = config.get('pythonPath') || 'python';
        const defaultMode = config.get('defaultMode') || 'preview';
        const defaultModel = config.get('defaultModel') || 'auto';
        const defaultFallback = (_a = config.get('enableFallback')) !== null && _a !== void 0 ? _a : true;
        const autoShowLogs = config.get('autoShowLogs') || false;
        // Show QuickPick for mode selection
        const modeSelection = yield vscode.window.showQuickPick([
            {
                label: '$(eye) Preview changes (safe mode)',
                description: 'Show changes in a preview document without modifying the original file',
                detail: 'Recommended for reviewing changes before applying',
                mode: 'preview'
            },
            {
                label: '$(pencil) Apply changes (overwrite file)',
                description: 'Directly apply changes to the current file',
                detail: 'Warning: This will modify your file immediately',
                mode: 'apply'
            }
        ], {
            placeHolder: 'How do you want to run CodeCrusher?',
            title: 'CodeCrusher Injection Mode',
            ignoreFocusOut: true
        });
        if (!modeSelection) {
            return; // User cancelled
        }
        // Show QuickPick for model selection
        const modelOptions = [
            {
                label: '$(zap) auto',
                description: 'Automatically choose the best model for this task',
                detail: 'Recommended - Let CodeCrusher decide the optimal model',
                model: 'auto'
            },
            {
                label: '$(robot) mistral',
                description: 'Mistral AI model - Fast and efficient',
                detail: 'Good for general code tasks and quick responses',
                model: 'mistral'
            },
            {
                label: '$(star) gemma',
                description: 'Google Gemma model - Balanced performance',
                detail: 'Reliable for most coding tasks',
                model: 'gemma'
            },
            {
                label: '$(rocket) mixtral',
                description: 'Mixtral model - High quality output',
                detail: 'Best for complex refactoring and optimization',
                model: 'mixtral'
            },
            {
                label: '$(flame) llama3-8b',
                description: 'Llama 3 8B - Fast and lightweight',
                detail: 'Quick responses for simple tasks',
                model: 'llama3-8b'
            },
            {
                label: '$(shield) llama3-70b',
                description: 'Llama 3 70B - Most powerful',
                detail: 'Best quality for complex code generation',
                model: 'llama3-70b'
            }
        ];
        // Highlight the default model
        const defaultModelOption = modelOptions.find(option => option.model === defaultModel);
        if (defaultModelOption) {
            defaultModelOption.label += ' (default)';
            defaultModelOption.detail = '⭐ ' + defaultModelOption.detail;
        }
        const modelSelection = yield vscode.window.showQuickPick(modelOptions, {
            placeHolder: `Choose AI model for this injection (default: ${defaultModel})`,
            title: 'CodeCrusher Model Selection',
            ignoreFocusOut: true
        });
        if (!modelSelection) {
            return; // User cancelled
        }
        // Show QuickPick for fallback selection
        const fallbackOptions = [
            {
                label: '$(check) Enable fallback',
                description: 'Use backup models if primary model fails',
                detail: 'Recommended - Ensures injection completes even if primary model is unavailable',
                fallback: true
            },
            {
                label: '$(x) Disable fallback',
                description: 'Only use the selected model',
                detail: 'Faster but may fail if selected model is unavailable',
                fallback: false
            }
        ];
        // Highlight the default fallback option
        const defaultFallbackOption = fallbackOptions.find(option => option.fallback === defaultFallback);
        if (defaultFallbackOption) {
            defaultFallbackOption.label += ' (default)';
            defaultFallbackOption.detail = '⭐ ' + defaultFallbackOption.detail;
        }
        const fallbackSelection = yield vscode.window.showQuickPick(fallbackOptions, {
            placeHolder: `Enable fallback to other models? (default: ${defaultFallback ? 'enabled' : 'disabled'})`,
            title: 'CodeCrusher Fallback Settings',
            ignoreFocusOut: true
        });
        if (!fallbackSelection) {
            return; // User cancelled
        }
        // Check if user made different selections than defaults and sync to dashboard
        const userSelectedDifferentSettings = modelSelection.model !== defaultModel ||
            fallbackSelection.fallback !== defaultFallback ||
            modeSelection.mode !== defaultMode;
        if (userSelectedDifferentSettings && settingsSync) {
            // Update local configuration with user selections
            const config = vscode.workspace.getConfiguration('codecrusher');
            yield config.update('defaultModel', modelSelection.model, vscode.ConfigurationTarget.Global);
            yield config.update('enableFallback', fallbackSelection.fallback, vscode.ConfigurationTarget.Global);
            yield config.update('defaultMode', modeSelection.mode, vscode.ConfigurationTarget.Global);
            // Trigger immediate sync to dashboard
            setTimeout(() => {
                settingsSync.pushLocalSettings(true); // Force push user selections
            }, 500);
        }
        const isApplyMode = modeSelection.mode === 'apply';
        const cliFlag = isApplyMode ? '--apply' : '--preview';
        // Get workspace root to find codecrusher_cli.py
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('No workspace folder found. Please open the CodeCrusher project folder.');
            return;
        }
        // Build CLI command with model and fallback flags using the working CLI script
        const path = require('path');
        const cliScript = path.join(workspaceRoot, 'codecrusher_cli.py');
        const modelFlag = `--model ${modelSelection.model}`;
        const fallbackFlag = `--fallback ${fallbackSelection.fallback}`;
        const command = `"${pythonPath}" "${cliScript}" inject ${cliFlag} "${filePath}" --prompt-text "${promptText}" ${modelFlag} ${fallbackFlag}`;
        // Auto-show logs panel if enabled
        if (autoShowLogs) {
            yield showLiveLogs();
            // Send injection start log
            if (liveLogsPanel) {
                liveLogsPanel.webview.postMessage({
                    command: 'addLog',
                    level: 'info',
                    message: `Starting injection: ${promptText}`,
                    timestamp: new Date().toISOString()
                });
                liveLogsPanel.webview.postMessage({
                    command: 'addLog',
                    level: 'model',
                    message: `Using model: ${modelSelection.model}, fallback: ${fallbackSelection.fallback ? 'enabled' : 'disabled'}`,
                    timestamp: new Date().toISOString()
                });
            }
        }
        // Show progress notification with appropriate title
        const progressTitle = isApplyMode ? 'CodeCrusher: Applying changes...' : 'CodeCrusher: Generating preview...';
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: progressTitle,
            cancellable: true
        }, (progress, token) => __awaiter(this, void 0, void 0, function* () {
            try {
                progress.report({ message: 'Running CLI injection...' });
                // Execute CLI command using child_process.exec
                const output = yield executeCliCommand(command);
                if (isApplyMode) {
                    // Apply mode: refresh the document and show success message
                    progress.report({ message: 'Refreshing document...' });
                    // Reload the document to show changes
                    yield reloadDocument(document);
                    // Show success message
                    vscode.window.showInformationMessage('✅ CodeCrusher changes applied successfully!', 'View Changes').then(selection => {
                        if (selection === 'View Changes') {
                            // Show the output in a preview for reference
                            showPreviewDocument(output, filePath, promptText, 'Applied Changes Log');
                        }
                    });
                    progress.report({ message: 'Changes applied!' });
                }
                else {
                    // Preview mode: show side-by-side diff
                    progress.report({ message: 'Creating diff view...' });
                    // Read original file content
                    const originalContent = document.getText();
                    // Extract the modified content from CLI output
                    const modifiedContent = extractModifiedContentFromOutput(output, originalContent, promptText);
                    // Show diff viewer
                    yield showDiffViewer(originalContent, modifiedContent, filePath, promptText);
                    progress.report({ message: 'Diff view ready!' });
                }
            }
            catch (error) {
                console.error('CodeCrusher injection failed:', error);
                const errorMessage = isApplyMode
                    ? `CodeCrusher failed to apply changes: ${error}`
                    : `CodeCrusher preview failed: ${error}`;
                vscode.window.showErrorMessage(errorMessage);
            }
        }));
    });
}
// Execute CLI command and return output
function executeCliCommand(command) {
    return new Promise((resolve, reject) => {
        const { exec } = require('child_process');
        exec(command, {
            timeout: 30000,
            maxBuffer: 1024 * 1024 // 1MB buffer
        }, (error, stdout, stderr) => {
            if (error) {
                // Handle different types of errors gracefully
                if (error.code === 'ENOENT') {
                    reject(new Error('CodeCrusher CLI not found. Please install CodeCrusher or check your cliPath setting.'));
                }
                else if (error.killed) {
                    reject(new Error('CodeCrusher CLI command timed out.'));
                }
                else {
                    reject(new Error(`CLI Error: ${error.message}\nStderr: ${stderr}`));
                }
                return;
            }
            if (stderr && stderr.trim()) {
                console.warn('CodeCrusher CLI stderr:', stderr);
            }
            resolve(stdout || 'No output from CLI');
        });
    });
}
// Reload document to show applied changes
function reloadDocument(document) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // Close and reopen the document to refresh content
            const uri = document.uri;
            // Find the text editor for this document
            const editor = vscode.window.visibleTextEditors.find(e => e.document.uri.toString() === uri.toString());
            if (editor) {
                // Reload the file content
                yield vscode.commands.executeCommand('workbench.action.files.revert');
            }
        }
        catch (error) {
            console.warn('Failed to reload document:', error);
            // Fallback: show a message to manually refresh
            vscode.window.showInformationMessage('Please manually refresh the file to see changes (Ctrl+Z then Ctrl+Y or close/reopen file)');
        }
    });
}
// Extract modified content from CLI output
function extractModifiedContentFromOutput(output, originalContent, prompt) {
    try {
        // Try to extract the actual file content from CLI output
        // The CLI might return the full modified file content or just the changes
        // Look for common patterns in CLI output that indicate the modified content
        const patterns = [
            // Pattern 1: Full file content after a separator
            /(?:Modified content:|Generated content:|Result:)\s*\n([\s\S]*?)(?:\n---|\n===|$)/i,
            // Pattern 2: Content between specific markers
            /```[\w]*\n([\s\S]*?)\n```/,
            // Pattern 3: Content after "Here is the modified file:"
            /Here is the modified file:?\s*\n([\s\S]*?)(?:\n---|\n===|$)/i,
            // Pattern 4: Everything after the last newline if it looks like code
            /\n([^#\n][^\n]*(?:\n[^#\n][^\n]*)*)\s*$/
        ];
        for (const pattern of patterns) {
            const match = output.match(pattern);
            if (match && match[1] && match[1].trim().length > 50) {
                // Validate that this looks like actual code content
                const content = match[1].trim();
                if (content.includes('{') || content.includes('def ') || content.includes('function') ||
                    content.includes('class ') || content.includes('<') || content.includes('import')) {
                    return content;
                }
            }
        }
        // Fallback: If we can't extract clean content, return the original with a comment
        const timestamp = new Date().toISOString();
        const fallbackContent = `${originalContent}

// CodeCrusher Preview - Generated ${timestamp}
// Prompt: ${prompt}
// Note: Could not extract clean modified content from CLI output
// Raw CLI Output:
/*
${output}
*/`;
        return fallbackContent;
    }
    catch (error) {
        console.warn('Failed to extract modified content:', error);
        return originalContent + '\n\n// CodeCrusher: Failed to extract modified content\n// Raw output: ' + output;
    }
}
// Show side-by-side diff viewer
function showDiffViewer(originalContent, modifiedContent, filePath, prompt) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const fileName = path.basename(filePath);
            const timestamp = Date.now();
            // Create unique URIs for the diff
            const originalUri = vscode.Uri.parse(`codecrusher-original-${timestamp}:Original - ${fileName}`);
            const modifiedUri = vscode.Uri.parse(`codecrusher-modified-${timestamp}:CodeCrusher Preview - ${fileName}`);
            // Register content providers if not already registered
            if (!diffContentProvider) {
                diffContentProvider = new CodeCrusherDiffContentProvider();
                vscode.workspace.registerTextDocumentContentProvider('codecrusher-original', diffContentProvider);
                vscode.workspace.registerTextDocumentContentProvider('codecrusher-modified', diffContentProvider);
            }
            // Set content for both documents
            diffContentProvider.setContent(originalUri.toString(), {
                content: originalContent,
                filePath,
                prompt,
                timestamp: new Date().toISOString(),
                type: 'original'
            });
            diffContentProvider.setContent(modifiedUri.toString(), {
                content: modifiedContent,
                filePath,
                prompt,
                timestamp: new Date().toISOString(),
                type: 'modified'
            });
            // Open the diff view
            yield vscode.commands.executeCommand('vscode.diff', originalUri, modifiedUri, `CodeCrusher: Preview Diff - ${fileName}`, { preview: true });
            // Show info message with options
            vscode.window.showInformationMessage('🔍 CodeCrusher diff preview ready!', 'Apply Changes', 'View Raw Output').then(selection => {
                if (selection === 'Apply Changes') {
                    // Apply the changes to the original file
                    applyChangesToFile(filePath, modifiedContent);
                }
                else if (selection === 'View Raw Output') {
                    // Show the raw CLI output for debugging
                    showPreviewDocument(modifiedContent, filePath, prompt, 'Raw CLI Output');
                }
            });
        }
        catch (error) {
            console.error('Failed to show diff viewer:', error);
            // Fallback to the old preview method
            yield showPreviewDocument(modifiedContent, filePath, prompt);
        }
    });
}
// Apply changes to file (for the "Apply Changes" button in diff view)
function applyChangesToFile(filePath, newContent) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const uri = vscode.Uri.file(filePath);
            const document = yield vscode.workspace.openTextDocument(uri);
            const editor = yield vscode.window.showTextDocument(document);
            // Replace entire document content
            const fullRange = new vscode.Range(document.positionAt(0), document.positionAt(document.getText().length));
            yield editor.edit(editBuilder => {
                editBuilder.replace(fullRange, newContent);
            });
            // Save the document
            yield document.save();
            vscode.window.showInformationMessage('✅ CodeCrusher changes applied successfully!');
        }
        catch (error) {
            console.error('Failed to apply changes:', error);
            vscode.window.showErrorMessage(`Failed to apply changes: ${error}`);
        }
    });
}
// Content provider for diff documents
class CodeCrusherDiffContentProvider {
    constructor() {
        this.content = new Map();
    }
    setContent(uri, data) {
        this.content.set(uri, data);
    }
    provideTextDocumentContent(uri) {
        const data = this.content.get(uri.toString());
        if (!data) {
            return 'No content available';
        }
        return data.content;
    }
    // Clean up content when no longer needed
    dispose(uri) {
        this.content.delete(uri);
    }
}
// Global diff content provider instance
let diffContentProvider = null;
// Show preview in virtual document tab
function showPreviewDocument(output, originalFilePath, prompt, customTitle) {
    return __awaiter(this, void 0, void 0, function* () {
        // Create virtual document URI with custom title if provided
        const title = customTitle || 'CodeCrusher Preview';
        const previewUri = vscode.Uri.parse(`codecrusher-preview:${title} - ${path.basename(originalFilePath)}`);
        // Register text document content provider if not already registered
        if (!previewContentProvider) {
            previewContentProvider = new CodeCrusherPreviewProvider();
            vscode.workspace.registerTextDocumentContentProvider('codecrusher-preview', previewContentProvider);
        }
        // Set content for this preview
        previewContentProvider.setContent(previewUri.toString(), {
            output,
            originalFilePath,
            prompt,
            timestamp: new Date().toISOString(),
            title: title
        });
        // Open the preview document
        const doc = yield vscode.workspace.openTextDocument(previewUri);
        yield vscode.window.showTextDocument(doc, vscode.ViewColumn.Beside);
    });
}
// Preview content provider for virtual documents
class CodeCrusherPreviewProvider {
    constructor() {
        this.content = new Map();
    }
    setContent(uri, data) {
        this.content.set(uri, data);
    }
    provideTextDocumentContent(uri) {
        const data = this.content.get(uri.toString());
        if (!data) {
            return 'No preview content available';
        }
        // Use custom title if provided
        const title = data.title || 'CodeCrusher Preview';
        const isAppliedChanges = title.includes('Applied Changes');
        // Format the preview content
        return `${title}
${'='.repeat(50)}

Original File: ${data.originalFilePath}
Prompt: ${data.prompt}
Generated: ${new Date(data.timestamp).toLocaleString()}
${isAppliedChanges ? 'Status: Changes have been applied to the original file' : 'Status: Preview mode (no changes applied)'}

${'='.repeat(50)}
CLI Output:
${'='.repeat(50)}

${data.output}

${'='.repeat(50)}
End of ${title}
${'='.repeat(50)}

${isAppliedChanges
            ? 'Note: These changes have been applied to your file. This is a log for reference.'
            : 'Note: This is a preview only. Use "Apply changes" mode to modify your file.'}
`;
    }
}
// Global preview content provider instance
let previewContentProvider = null;
// Run injection command
function runInjection() {
    return __awaiter(this, void 0, void 0, function* () {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        const document = editor.document;
        const filePath = document.uri.fsPath;
        // Save the document first
        yield document.save();
        // Get prompt from user
        const promptText = yield vscode.window.showInputBox({
            prompt: 'Enter prompt for CodeCrusher injection',
            placeHolder: 'e.g., Implement a function that calculates factorial'
        });
        if (!promptText) {
            return; // User cancelled
        }
        // Get tag if there's a selection or cursor is on a line with a tag
        let tag;
        const selection = editor.selection;
        if (!selection.isEmpty) {
            const selectedText = document.getText(selection);
            const tagMatch = selectedText.match(/# AI_INJECT: (\w+)/);
            if (tagMatch) {
                tag = tagMatch[1];
            }
        }
        else {
            const lineText = document.lineAt(selection.active.line).text;
            const tagMatch = lineText.match(/# AI_INJECT: (\w+)/);
            if (tagMatch) {
                tag = tagMatch[1];
            }
        }
        // Get configuration
        const config = vscode.workspace.getConfiguration('codecrusher');
        const pythonPath = config.get('pythonPath');
        const cliPath = config.get('cliPath');
        const provider = config.get('defaultProvider');
        const model = config.get('defaultModel');
        const useCache = config.get('useCache');
        // Build command
        const args = [
            cliPath,
            'inject',
            'run',
            '-i', filePath,
            '-t', promptText,
            '--provider', provider,
            '--model', model
        ];
        if (tag) {
            args.push('--tag', tag);
        }
        if (useCache) {
            args.push('--cache');
        }
        args.push('--verbose');
        // Show progress
        vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Running CodeCrusher Injection',
            cancellable: true
        }, (progress, token) => __awaiter(this, void 0, void 0, function* () {
            progress.report({ message: 'Sending request to AI...' });
            // Create terminal and run command
            const terminal = vscode.window.createTerminal('CodeCrusher');
            terminal.show();
            terminal.sendText(`${pythonPath} ${args.join(' ')}`);
            // Wait for completion (this is a simple approach, ideally we'd use the API)
            yield new Promise(resolve => setTimeout(resolve, 2000));
            progress.report({ message: 'Injection completed' });
            // Update status bar
            updateStatusBar();
            return new Promise(resolve => setTimeout(resolve, 1000));
        }));
    });
}
// View telemetry command
function viewTelemetry() {
    return __awaiter(this, void 0, void 0, function* () {
        // Get configuration
        const config = vscode.workspace.getConfiguration('codecrusher');
        const pythonPath = config.get('pythonPath') || 'python';
        // Get workspace root to find codecrusher_cli.py
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('No workspace folder found. Please open the CodeCrusher project folder.');
            return;
        }
        // Create terminal and run command using the working CLI script
        const path = require('path');
        const cliScript = path.join(workspaceRoot, 'codecrusher_cli.py');
        const terminal = vscode.window.createTerminal('CodeCrusher Telemetry');
        terminal.show();
        terminal.sendText(`"${pythonPath}" "${cliScript}" status run`);
    });
}
// Replay injection command
function replayInjection() {
    return __awaiter(this, void 0, void 0, function* () {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        // Get tag if cursor is on a line with a tag
        const document = editor.document;
        const selection = editor.selection;
        const lineText = document.lineAt(selection.active.line).text;
        const tagMatch = lineText.match(/# AI_INJECT: (\w+)/);
        if (!tagMatch) {
            vscode.window.showErrorMessage('No injection tag found at cursor position');
            return;
        }
        const tag = tagMatch[1];
        // Get configuration
        const config = vscode.workspace.getConfiguration('codecrusher');
        const pythonPath = config.get('pythonPath') || 'python';
        // Get workspace root to find codecrusher_cli.py
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('No workspace folder found. Please open the CodeCrusher project folder.');
            return;
        }
        // Create terminal and run command using the working CLI script
        const path = require('path');
        const cliScript = path.join(workspaceRoot, 'codecrusher_cli.py');
        const terminal = vscode.window.createTerminal('CodeCrusher Replay');
        terminal.show();
        terminal.sendText(`"${pythonPath}" "${cliScript}" replay run -t ${tag}`);
    });
}
// Insert tag command
function insertTag() {
    return __awaiter(this, void 0, void 0, function* () {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showErrorMessage('No active editor found');
            return;
        }
        // Get tag name from user
        const tagName = yield vscode.window.showInputBox({
            prompt: 'Enter tag name for CodeCrusher injection',
            placeHolder: 'e.g., factorial_function'
        });
        if (!tagName) {
            return; // User cancelled
        }
        // Determine comment style based on language
        let commentPrefix = '# ';
        const languageId = editor.document.languageId;
        if (['javascript', 'typescript', 'java', 'csharp', 'go'].includes(languageId)) {
            commentPrefix = '// ';
        }
        // Insert tag at cursor position
        editor.edit(editBuilder => {
            editBuilder.insert(editor.selection.active, `${commentPrefix}AI_INJECT: ${tagName}\n`);
        });
    });
}
// Show status command
function showStatus() {
    return __awaiter(this, void 0, void 0, function* () {
        // Get configuration
        const config = vscode.workspace.getConfiguration('codecrusher');
        const pythonPath = config.get('pythonPath') || 'python';
        // Get workspace root to find codecrusher_cli.py
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!workspaceRoot) {
            vscode.window.showErrorMessage('No workspace folder found. Please open the CodeCrusher project folder.');
            return;
        }
        // Create terminal and run command using the working CLI script
        const path = require('path');
        const cliScript = path.join(workspaceRoot, 'codecrusher_cli.py');
        const terminal = vscode.window.createTerminal('CodeCrusher Status');
        terminal.show();
        terminal.sendText(`"${pythonPath}" "${cliScript}" status run --compact`);
    });
}
// Update status bar with telemetry data
function updateStatusBar() {
    return __awaiter(this, void 0, void 0, function* () {
        if (!statusBarItem)
            return;
        try {
            // Get telemetry data (ideally from a local API endpoint)
            // For now, we'll use a simple approach with a mock
            const stats = yield getCodeCrusherStats();
            if (stats) {
                statusBarItem.text = `$(rocket) CC: ${stats.cacheHitRate}% cache | ${stats.modelName}`;
                statusBarItem.tooltip = `CodeCrusher Stats\nCache Hit Rate: ${stats.cacheHitRate}%\nActive Model: ${stats.modelName}\nInjections: ${stats.injectionCount}`;
            }
            else {
                statusBarItem.text = '$(rocket) CodeCrusher';
                statusBarItem.tooltip = 'CodeCrusher Status';
            }
        }
        catch (error) {
            console.error('Error updating status bar:', error);
            statusBarItem.text = '$(rocket) CodeCrusher';
            statusBarItem.tooltip = 'CodeCrusher Status';
        }
    });
}
// Get CodeCrusher stats (mock implementation)
function getCodeCrusherStats() {
    return __awaiter(this, void 0, void 0, function* () {
        // In a real implementation, this would call the local API
        // For now, return mock data
        return {
            cacheHitRate: 75,
            modelName: 'llama3',
            injectionCount: 42
        };
    });
}
// Get telemetry for a specific tag (mock implementation)
function getTelemetryForTag(tag) {
    return __awaiter(this, void 0, void 0, function* () {
        // In a real implementation, this would call the local API
        // For now, return mock data
        return {
            model: 'llama3',
            provider: 'groq',
            timestamp: new Date().toISOString(),
            tokens_in: 120,
            tokens_out: 450,
            cached: false,
            tags: ['inject-ai', 'augment', tag],
            error: null
        };
    });
}
// Check if the CodeCrusher API server is running
function checkServerStatus() {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const isServerRunning = yield api_1.CodeCrusherApi.isServerRunning();
            if (!isServerRunning) {
                vscode.window.showWarningMessage('CodeCrusher API server is not running. Start it with: codecrusher serve run', 'Start Server').then(selection => {
                    if (selection === 'Start Server') {
                        // Open terminal and run server
                        const terminal = vscode.window.createTerminal('CodeCrusher Server');
                        terminal.sendText('codecrusher serve run');
                        terminal.show();
                    }
                });
            }
        }
        catch (error) {
            console.error('Error checking server status:', error);
        }
    });
}
//# sourceMappingURL=extension.js.map