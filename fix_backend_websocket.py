#!/usr/bin/env python3
"""
Fix backend WebSocket to prevent immediate disconnections.
"""

import os

def fix_backend_websocket():
    """Fix backend WebSocket endpoint to prevent immediate disconnections."""
    
    backend_file = "app/backend_main.py"
    
    if not os.path.exists(backend_file):
        print(f"❌ Backend file not found: {backend_file}")
        return False
    
    print(f"🔧 Checking backend WebSocket configuration in {backend_file}")
    
    try:
        with open(backend_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if WebSocket endpoint exists and is properly configured
        if 'websocket' in content.lower():
            print("✅ WebSocket endpoint found in backend")
            
            # Check for common issues
            issues_found = []
            
            if 'ping_interval' not in content:
                issues_found.append("Missing ping_interval configuration")
            
            if 'ping_timeout' not in content:
                issues_found.append("Missing ping_timeout configuration")
            
            if 'close_timeout' not in content:
                issues_found.append("Missing close_timeout configuration")
            
            if issues_found:
                print("⚠️ Potential WebSocket configuration issues found:")
                for issue in issues_found:
                    print(f"   - {issue}")
            else:
                print("✅ Backend WebSocket configuration looks good")
        else:
            print("⚠️ WebSocket endpoint not found in backend")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking backend WebSocket: {e}")
        return False

def restart_backend_with_websocket_fix():
    """Restart backend with WebSocket debugging enabled."""
    
    print("\n🔄 Backend WebSocket Status Check")
    print("=" * 40)
    
    # Check if backend is running
    try:
        import requests
        response = requests.get("http://127.0.0.1:8001/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend is running")
            print(f"📊 Connected clients: {data.get('connected_clients', 0)}")
            print(f"📊 Status: {data.get('status', 'unknown')}")
            
            # Test WebSocket endpoint directly
            print("\n🧪 Testing WebSocket endpoint...")
            try:
                import asyncio
                import websockets
                
                async def test_ws():
                    try:
                        async with websockets.connect("ws://127.0.0.1:8001/ws/logs") as websocket:
                            print("✅ WebSocket connection successful")
                            
                            # Send a test message
                            await websocket.send('{"type": "test", "message": "connection test"}')
                            
                            # Wait for response
                            try:
                                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                                print(f"📨 Received: {response}")
                            except asyncio.TimeoutError:
                                print("⏰ No response (this is normal)")
                            
                            return True
                    except Exception as e:
                        print(f"❌ WebSocket test failed: {e}")
                        return False
                
                result = asyncio.run(test_ws())
                if result:
                    print("✅ Backend WebSocket is working correctly")
                else:
                    print("❌ Backend WebSocket has issues")
                
            except ImportError:
                print("⚠️ Cannot test WebSocket (websockets module not available)")
            
        else:
            print(f"⚠️ Backend responded with status {response.status_code}")
            
    except Exception as e:
        print(f"❌ Backend health check failed: {e}")
        print("🔧 Backend may not be running or accessible")

if __name__ == "__main__":
    print("🔧 Backend WebSocket Fix Tool")
    print("=" * 40)
    
    # Check backend configuration
    fix_backend_websocket()
    
    # Test backend WebSocket
    restart_backend_with_websocket_fix()
    
    print("\n💡 Recommendations:")
    print("1. Hard refresh the browser (Ctrl+F5)")
    print("2. Check browser console for updated WebSocket behavior")
    print("3. The WebSocket should now stay connected longer")
    print("4. If issues persist, check browser network tab for WebSocket frames")
