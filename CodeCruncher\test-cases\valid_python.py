#!/usr/bin/env python3
"""
Valid Python file for testing CodeCrusher injection
This file contains AI_INJECT tags for testing purposes
"""

import os
import sys
from typing import List, Dict, Optional

class Calculator:
    """Simple calculator class for testing."""

    def __init__(self):
        self.history: List[str] = []

    def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        result = a + b
        self.history.append(f"{a} + {b} = {result}")
        return result

    def subtract(self, a: float, b: float) -> float:
        """Subtract two numbers."""
        result = a - b
        self.history.append(f"{a} - {b} = {result}")
        return result

    # AI_INJECT:multiply
    def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        try:
            result = a * b
            self.history.append(f"{a} * {b} = {result}")
            return result
        except Exception as e:
            print(f"Error in multiplication: {e}")
            return 0.0
# AI_INJECT:multiply:end

    # AI_INJECT:divide
    def divide(self, a: float, b: float) -> float:
        """Divide two numbers with error handling."""
        try:
            if b == 0:
                raise ValueError("Cannot divide by zero")
            result = a / b
            self.history.append(f"{a} / {b} = {result}")
            return result
        except Exception as e:
            print(f"Error in division: {e}")
            return 0.0
# AI_INJECT:divide:end

    def get_history(self) -> List[str]:
        """Get calculation history."""
        return self.history.copy()

    def clear_history(self) -> None:
        """Clear calculation history."""
        self.history.clear()

def main():
    """Main function for testing."""
    calc = Calculator()

    # Basic operations
    print(f"2 + 3 = {calc.add(2, 3)}")
    print(f"5 - 2 = {calc.subtract(5, 2)}")

    # AI_INJECT:advanced_operations
    # Test the new multiply and divide methods
    print(f"4 * 6 = {calc.multiply(4, 6)}")
    print(f"10 / 2 = {calc.divide(10, 2)}")
    print(f"10 / 0 = {calc.divide(10, 0)}")  # Test error handling

    # Fibonacci calculator
    def fibonacci(n):
        """Calculate the nth Fibonacci number."""
        if n <= 1:
            return n
        return fibonacci(n-1) + fibonacci(n-2)

    # Test Fibonacci
    print(f"\nFibonacci sequence (first 10 numbers):")
    for i in range(10):
        print(f"F({i}) = {fibonacci(i)}")
# AI_INJECT:advanced_operations:end

    # Display history
    print("\nCalculation History:")
    for entry in calc.get_history():
        print(f"  {entry}")

if __name__ == "__main__":
    main()
