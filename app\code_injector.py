"""
CodeCrusher Code Injection Module

This module provides the core code injection functionality with preview and apply modes.
It reuses the existing CLI injection logic to ensure consistency between CLI and API.
"""

import os
import sys
import difflib
from typing import List, Dict, Optional
from pathlib import Path
import logging
from datetime import datetime

# Add the parent directory to the path to import codecrusher modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from codecrusher.injector import inject_code as cli_inject_code
    from codecrusher.injector import _process_single_file
    CLI_AVAILABLE = True
except ImportError as e:
    logging.warning(f"CLI injection logic not available: {e}")
    CLI_AVAILABLE = False

logger = logging.getLogger(__name__)

def inject_code(file_path: str, prompt: str, model: str, mode: str, tags: List[str]) -> Dict:
    """
    Inject code into a file based on a prompt using the specified AI model.

    This function serves as a bridge between the FastAPI and the existing CLI injection logic.
    It reuses the robust CLI implementation when available, falling back to a simple implementation.

    Args:
        file_path: Path to the target file
        prompt: The injection prompt/instruction
        model: AI model to use (e.g., 'mixtral', 'llama3-70b')
        mode: 'preview' or 'apply'
        tags: List of tags for categorization

    Returns:
        Dict with injection results including preview, diff, and status
    """
    logger.info(f"Starting code injection: {file_path} with model {model} in {mode} mode")

    # Validate input parameters
    validation_error = validate_injection_request(file_path, prompt, model, mode)
    if validation_error:
        return {"error": validation_error, "success": False}

    # Use CLI injection logic if available
    if CLI_AVAILABLE:
        return _use_cli_injection(file_path, prompt, model, mode, tags)
    else:
        # Fallback to simple implementation
        return _simple_injection(file_path, prompt, model, mode, tags)

def _use_cli_injection(file_path: str, prompt: str, model: str, mode: str, tags: List[str]) -> Dict:
    """Use the existing CLI injection logic for consistent behavior."""
    try:
        # Convert mode to CLI parameters
        apply = (mode == "apply")
        preview = (mode == "preview")

        # Call the CLI injection function
        cli_result = cli_inject_code(
            source_path=file_path,
            prompt_text=prompt,
            use_ai=True,
            preview=preview,
            model=model,
            auto_model_routing=(model == "auto"),
            refresh_cache=False,
            apply=apply,
            force=False,  # Don't force without user confirmation
            recursive=False,  # Single file only for API
            extensions=None  # Not needed for single file
        )

        # Convert CLI result to API format
        if cli_result.get("success", False):
            # Extract information from CLI result
            file_results = cli_result.get("results", [])
            if file_results:
                file_result = file_results[0]  # Get first (and only) file result

                # Create preview data from CLI result
                preview = {
                    "has_changes": file_result.get("injections", 0) > 0,
                    "injections_count": file_result.get("injections", 0),
                    "tags_processed": file_result.get("tags", []),
                    "skipped_count": file_result.get("skipped_count", 0),
                    "model_used": file_result.get("model_used", model)
                }

                return {
                    "success": True,
                    "file": file_path,
                    "model": file_result.get("model_used", model),
                    "mode": mode,
                    "tags": tags,
                    "prompt": prompt,
                    "preview": preview,
                    "result": "CLI injection completed successfully",
                    "applied": file_result.get("applied", False),
                    "timestamp": file_result.get("timestamp", datetime.now().isoformat()),
                    "cli_result": cli_result  # Include full CLI result for debugging
                }
            else:
                return {
                    "success": False,
                    "error": "No file results returned from CLI injection",
                    "cli_result": cli_result
                }
        else:
            return {
                "success": False,
                "error": cli_result.get("error", "CLI injection failed"),
                "cli_result": cli_result
            }

    except Exception as e:
        logger.error(f"Error using CLI injection: {e}")
        return {
            "success": False,
            "error": f"CLI injection error: {str(e)}"
        }

def _simple_injection(file_path: str, prompt: str, model: str, mode: str, tags: List[str]) -> Dict:
    """
    Simple fallback injection implementation when CLI logic is not available.
    """
    logger.warning("Using fallback injection logic (CLI not available)")

    file_path = Path(file_path)

    try:
        # Read original file content
        with open(file_path, "r", encoding="utf-8") as f:
            original_code = f.read()

        logger.info(f"Read {len(original_code)} characters from {file_path}")

        # Simple placeholder modification
        modified_code = generate_modified_code(original_code, prompt, model, tags)

        # Generate diff for preview
        diff_lines = list(difflib.unified_diff(
            original_code.splitlines(keepends=True),
            modified_code.splitlines(keepends=True),
            fromfile=f"a/{file_path.name}",
            tofile=f"b/{file_path.name}",
            lineterm=""
        ))

        # Create preview data
        preview = {
            "original_length": len(original_code),
            "modified_length": len(modified_code),
            "original_lines": len(original_code.splitlines()),
            "modified_lines": len(modified_code.splitlines()),
            "before_snippet": original_code[:500] + ("..." if len(original_code) > 500 else ""),
            "after_snippet": modified_code[:500] + ("..." if len(modified_code) > 500 else ""),
            "diff": "".join(diff_lines),
            "has_changes": original_code != modified_code
        }

        # Prepare result structure
        result = {
            "file": str(file_path),
            "model": model,
            "mode": mode,
            "tags": tags,
            "prompt": prompt,
            "preview": preview,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }

        # Apply changes if mode is 'apply'
        if mode == "apply" and preview["has_changes"]:
            # Create backup
            backup_path = create_backup(file_path)
            result["backup_path"] = str(backup_path)

            # Write modified content
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(modified_code)

            result["result"] = "Changes applied successfully (fallback mode)"
            result["applied"] = True
            logger.info(f"Applied changes to {file_path}, backup created at {backup_path}")

        elif mode == "apply" and not preview["has_changes"]:
            result["result"] = "No changes needed"
            result["applied"] = False

        else:
            result["result"] = "Preview generated (fallback mode)"
            result["applied"] = False

        return result

    except UnicodeDecodeError as e:
        logger.error(f"Unicode decode error reading {file_path}: {e}")
        return {"error": f"Cannot read file (encoding issue): {e}", "success": False}

    except PermissionError as e:
        logger.error(f"Permission error accessing {file_path}: {e}")
        return {"error": f"Permission denied: {e}", "success": False}

    except Exception as e:
        logger.error(f"Unexpected error during injection: {e}")
        return {"error": f"Injection failed: {str(e)}", "success": False}

def generate_modified_code(original_code: str, prompt: str, model: str, tags: List[str]) -> str:
    """
    Generate modified code based on the prompt and model.

    This is a placeholder implementation that will be replaced with actual AI model calls.
    """
    # 🧠 Placeholder AI Logic - Replace with actual model integration

    # Add header comment with injection info
    header = f"""# AI Code Injection
# Prompt: {prompt}
# Model: {model}
# Tags: {', '.join(tags)}
# Timestamp: {datetime.now().isoformat()}

"""

    # Simple placeholder modifications based on prompt keywords
    modified_code = original_code

    if "add logging" in prompt.lower():
        # Add logging import if not present
        if "import logging" not in modified_code:
            modified_code = "import logging\n" + modified_code

        # Add logger setup
        if "logger = logging.getLogger" not in modified_code:
            modified_code = modified_code.replace(
                "import logging\n",
                "import logging\n\nlogger = logging.getLogger(__name__)\n"
            )

    if "add docstring" in prompt.lower():
        # Add basic docstring to functions without them
        lines = modified_code.split('\n')
        new_lines = []
        for i, line in enumerate(lines):
            new_lines.append(line)
            if line.strip().startswith('def ') and ':' in line:
                # Check if next line is already a docstring
                next_line = lines[i + 1] if i + 1 < len(lines) else ""
                if not next_line.strip().startswith('"""') and not next_line.strip().startswith("'''"):
                    function_name = line.strip().split('(')[0].replace('def ', '')
                    docstring = f'    """{function_name} function - AI generated docstring."""'
                    new_lines.append(docstring)

        modified_code = '\n'.join(new_lines)

    if "add error handling" in prompt.lower():
        # Wrap main code in try-catch if not present
        if "try:" not in modified_code and "except:" not in modified_code:
            lines = modified_code.split('\n')
            # Find main execution block
            main_start = -1
            for i, line in enumerate(lines):
                if line.strip().startswith('if __name__'):
                    main_start = i
                    break

            if main_start >= 0:
                # Wrap main block in try-catch
                lines.insert(main_start + 1, "    try:")
                lines.append("    except Exception as e:")
                lines.append("        logger.error(f'Error: {e}')")
                modified_code = '\n'.join(lines)

    # Add the header comment
    return header + modified_code

def create_backup(file_path: Path) -> Path:
    """Create a backup of the original file before modification."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = file_path.with_suffix(f".backup_{timestamp}{file_path.suffix}")

    # Copy original file to backup
    with open(file_path, "r", encoding="utf-8") as original:
        content = original.read()

    with open(backup_path, "w", encoding="utf-8") as backup:
        backup.write(content)

    logger.info(f"Created backup: {backup_path}")
    return backup_path

def validate_injection_request(file_path: str, prompt: str, model: str, mode: str) -> Optional[str]:
    """
    Validate injection request parameters.

    Returns:
        None if valid, error message string if invalid
    """
    if not file_path:
        return "File path is required"

    if not prompt or len(prompt.strip()) < 3:
        return "Prompt must be at least 3 characters long"

    if mode not in ["preview", "apply"]:
        return "Mode must be 'preview' or 'apply'"

    if not model:
        return "Model is required"

    # Check file extension for supported types
    supported_extensions = {'.py', '.js', '.ts', '.java', '.c', '.cpp', '.cs', '.go', '.rs', '.php', '.rb', '.swift'}
    file_ext = Path(file_path).suffix.lower()

    if file_ext not in supported_extensions:
        return f"Unsupported file type: {file_ext}. Supported: {', '.join(supported_extensions)}"

    return None

def get_injection_stats(results: List[Dict]) -> Dict:
    """Generate statistics from multiple injection results."""
    if not results:
        return {"total": 0, "successful": 0, "failed": 0, "applied": 0, "previewed": 0}

    total = len(results)
    successful = sum(1 for r in results if r.get("success", False))
    failed = total - successful
    applied = sum(1 for r in results if r.get("applied", False))
    previewed = sum(1 for r in results if not r.get("applied", False) and r.get("success", False))

    return {
        "total": total,
        "successful": successful,
        "failed": failed,
        "applied": applied,
        "previewed": previewed,
        "success_rate": round((successful / total) * 100, 2) if total > 0 else 0
    }
