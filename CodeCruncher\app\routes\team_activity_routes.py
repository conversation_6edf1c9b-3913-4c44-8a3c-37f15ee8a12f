"""
Team Activity WebSocket Routes for CodeCrusher
Handles real-time team activity feeds and notifications
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List, Optional
import json
import logging

from auth import get_current_user_from_token, get_current_user
from database import get_db
from team_events import team_event_manager

router = APIRouter(prefix="/team-activity", tags=["team-activity"])
security = HTTPBearer()
logger = logging.getLogger(__name__)

async def get_user_from_websocket_token(token: str):
    """Get user from WebSocket token."""
    try:
        user = await get_current_user_from_token(token)
        return user
    except Exception as e:
        logger.error(f"WebSocket authentication failed: {e}")
        return None

@router.websocket("/ws/{team_id}")
async def team_activity_websocket(websocket: WebSocket, team_id: int, token: str = Query(...)):
    """WebSocket endpoint for team activity feed."""
    await websocket.accept()

    try:
        # Authenticate user
        user = await get_user_from_websocket_token(token)
        if not user:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Authentication failed"
            }))
            await websocket.close()
            return

        # Verify team membership
        db = get_db()
        role = db.check_team_membership(user["id"], team_id)
        if not role:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "You are not a member of this team"
            }))
            await websocket.close()
            return

        # Add connection to team
        team_event_manager.add_team_connection(team_id, websocket, user["id"])

        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connected",
            "message": f"Connected to team {team_id} activity feed",
            "user_id": user["id"],
            "team_id": team_id
        }))

        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages (ping/pong, etc.)
                data = await websocket.receive_text()
                message = json.loads(data)

                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }))
                elif message.get("type") == "get_recent_activity":
                    # Send recent team activity
                    recent_activity = team_event_manager.get_team_activity_summary(
                        team_id,
                        limit=message.get("limit", 20)
                    )
                    await websocket.send_text(json.dumps({
                        "type": "recent_activity",
                        "events": recent_activity
                    }))
                elif message.get("type") == "get_active_members":
                    # Send active team members
                    active_members = team_event_manager.get_active_team_members(team_id)
                    await websocket.send_text(json.dumps({
                        "type": "active_members",
                        "members": active_members
                    }))

            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"WebSocket message handling error: {e}")
                break

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
    finally:
        # Remove connection
        team_event_manager.remove_team_connection(team_id, websocket)

@router.get("/{team_id}/recent", response_model=List[dict])
async def get_recent_team_activity(
    team_id: int,
    limit: int = 20,
    current_user: dict = Depends(get_current_user)
):
    """Get recent team activity via REST API."""
    db = get_db()

    # Verify team membership
    role = db.check_team_membership(current_user["id"], team_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this team"
        )

    # Get recent activity
    recent_activity = team_event_manager.get_team_activity_summary(team_id, limit)

    return recent_activity

@router.get("/{team_id}/active-members", response_model=List[dict])
async def get_active_team_members(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get currently active team members."""
    db = get_db()

    # Verify team membership
    role = db.check_team_membership(current_user["id"], team_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this team"
        )

    # Get active members
    active_members = team_event_manager.get_active_team_members(team_id)

    # Enrich with user details
    enriched_members = []
    for member in active_members:
        user = db.get_user_by_id(member["user_id"])
        if user:
            enriched_members.append({
                "user_id": member["user_id"],
                "email": user["email"],
                "connected_at": member["connected_at"]
            })

    return enriched_members

@router.post("/{team_id}/test-event", response_model=dict)
async def create_test_event(
    team_id: int,
    event_type: str = "injection_created",
    current_user: dict = Depends(get_current_user)
):
    """Create a test event for development/testing."""
    db = get_db()

    # Verify team membership
    role = db.check_team_membership(current_user["id"], team_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this team"
        )

    # Create test event based on type
    if event_type == "injection_created":
        event = team_event_manager.create_injection_event(
            team_id=team_id,
            user_id=current_user["id"],
            user_email=current_user["email"],
            injection_data={
                "id": 999,
                "file_path": "./test/example.py",
                "model": "test-model",
                "mode": "preview",
                "tags": ["test"],
                "intent": "test",
                "success": True
            }
        )
    elif event_type == "injection_rated":
        event = team_event_manager.create_rating_event(
            team_id=team_id,
            user_id=current_user["id"],
            user_email=current_user["email"],
            injection_id=999,
            rating=5,
            feedback="Test rating feedback"
        )
    elif event_type == "prompt_shaped":
        event = team_event_manager.create_prompt_shaped_event(
            team_id=team_id,
            user_id=current_user["id"],
            user_email=current_user["email"],
            prompt_data={
                "prompt_hash": "test_hash",
                "original_prompt": "Test prompt for shaping",
                "model": "test-model",
                "intent": "test"
            }
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid event type"
        )

    # Broadcast the test event
    await team_event_manager.broadcast_event(event)

    return {
        "message": f"Test {event_type} event created and broadcasted",
        "event_id": event.event_id
    }

@router.get("/{team_id}/stats", response_model=dict)
async def get_team_activity_stats(
    team_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get team activity statistics."""
    db = get_db()

    # Verify team membership
    role = db.check_team_membership(current_user["id"], team_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You are not a member of this team"
        )

    # Get activity stats
    recent_activity = team_event_manager.get_team_activity_summary(team_id, 100)
    active_members = team_event_manager.get_active_team_members(team_id)

    # Calculate stats
    event_counts = {}
    for event in recent_activity:
        event_type = event["event_type"]
        event_counts[event_type] = event_counts.get(event_type, 0) + 1

    return {
        "team_id": team_id,
        "total_events": len(recent_activity),
        "active_members": len(active_members),
        "event_breakdown": event_counts,
        "recent_activity_count": len(recent_activity)
    }
