#!/usr/bin/env python3
"""
Example script demonstrating the CLI usage of CodeCrusher
"""

import subprocess
import os
import sys
from rich.console import Console
from rich.panel import Panel

# Initialize rich console
console = Console()

def run_command(command):
    """Run a command and return the output"""
    console.print(f"[bold cyan]Running command:[/bold cyan] [yellow]{' '.join(command)}[/yellow]")
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        console.print(f"[bold red]Error running command:[/bold red] {e}")
        console.print(f"[red]Error output:[/red] {e.stderr}")
        return None

def main():
    """Main function demonstrating CLI usage"""
    # Check if codecrusher is installed
    try:
        subprocess.run(["codecrusher", "--help"], capture_output=True, check=True)
        console.print("[bold green]✅ CodeCrusher is installed and available[/bold green]")
    except (subprocess.CalledProcessError, FileNotFoundError):
        console.print("[bold red]❌ CodeCrusher is not installed or not available in PATH[/bold red]")
        console.print("[yellow]Try installing it with:[/yellow] pip install -e .")
        return

    # Create a test file with injection tags
    test_file = "example_for_cli.py"
    with open(test_file, "w") as f:
        f.write("""
# Example file for CodeCrusher CLI demonstration

# AI_INJECT: factorial_function
# This tag will be replaced with a factorial function

# AI_INJECT: fibonacci_function
# This tag will be replaced with a fibonacci function
""")
    
    console.print(f"[green]Created test file:[/green] {test_file}")
    
    # Example 1: Basic usage
    console.print(Panel(
        "[bold]Example 1: Basic Usage[/bold]\n"
        "Generate code with a specific model",
        title="[bold cyan]CLI Example 1[/bold cyan]",
        border_style="cyan"
    ))
    
    command1 = [
        "codecrusher",
        "--source", test_file,
        "--prompt-text", "Implement a factorial function",
        "--ai",
        "--model", "llama3-8b-8192",
        "--preview"
    ]
    
    run_command(command1)
    
    # Example 2: Auto-model routing
    console.print(Panel(
        "[bold]Example 2: Auto-Model Routing[/bold]\n"
        "Use the best result from multiple models",
        title="[bold cyan]CLI Example 2[/bold cyan]",
        border_style="cyan"
    ))
    
    command2 = [
        "codecrusher",
        "--source", test_file,
        "--prompt-text", "Implement a fibonacci function",
        "--ai",
        "--auto-model-routing",
        "--preview"
    ]
    
    run_command(command2)
    
    # Example 3: Cache bypass
    console.print(Panel(
        "[bold]Example 3: Cache Bypass[/bold]\n"
        "Force regeneration by bypassing the cache",
        title="[bold cyan]CLI Example 3[/bold cyan]",
        border_style="cyan"
    ))
    
    command3 = [
        "codecrusher",
        "--source", test_file,
        "--prompt-text", "Implement a fibonacci function",
        "--ai",
        "--auto-model-routing",
        "--refresh-cache",
        "--preview"
    ]
    
    run_command(command3)
    
    # Clean up
    console.print("[yellow]Cleaning up...[/yellow]")
    os.remove(test_file)
    console.print("[green]Test file removed[/green]")

if __name__ == "__main__":
    main()
