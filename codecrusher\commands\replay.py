import typer
import hashlib
import json
import os
import uuid
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.syntax import Syntax
from rich.table import Table
from rich.text import Text
from rich.box import ROUNDED
from typing import Optional, List

# Import tag manager
from codecrusher.tag_manager import (
    parse_user_tags,
    filter_by_tags,
    extract_tags_from_content,
    inherit_tags,
    tag_matches,
    highlight_matched_tags,
    REPLAY_TAG_PREFIX,
    PARENT_TAG_PREFIX
)

# Import file utils
from codecrusher.file_utils import (
    get_comment_syntax,
    is_code_file,
    format_annotation
)

# Import filter utilities
from codecrusher.filter_utils import (
    parse_filter,
    apply_filters,
    format_entry_preview,
    PreviewMode,
    truncate_text
)



# Initialize rich console
console = Console()

# Create the Typer app
app = typer.Typer()

# Cache file paths
CACHE_DIR = Path.home() / ".codecrusher"
CACHE_FILE = CACHE_DIR / "cache.json"
SIMPLE_CACHE_FILE = Path.home() / ".codecrusher_cache.json"

def load_cache_from_json():
    """Load cache from JSON files."""
    cache_data = {}

    # Try to load from the main cache file
    try:
        if CACHE_FILE.exists():
            with open(CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data.update(json.load(f))
    except Exception as e:
        console.print(f"[yellow]Warning: Failed to load main cache file: {str(e)}[/yellow]")

    # Try to load from the simple cache file
    try:
        if SIMPLE_CACHE_FILE.exists():
            with open(SIMPLE_CACHE_FILE, "r", encoding="utf-8") as f:
                cache_data.update(json.load(f))
    except Exception as e:
        console.print(f"[yellow]Warning: Failed to load simple cache file: {str(e)}[/yellow]")

    return cache_data

def get_cached_entry(key: str):
    """
    Get a cached entry by key.

    Args:
        key: The cache key to look up

    Returns:
        The cached entry or None if not found
    """
    # Try Redis first if available
    try:
        import redis
        # Try to get Redis connection details from environment or config
        import os
        redis_host = os.environ.get("CODECRUSHER_REDIS_HOST", "localhost")
        redis_port = int(os.environ.get("CODECRUSHER_REDIS_PORT", "6379"))
        redis_db = int(os.environ.get("CODECRUSHER_REDIS_DB", "0"))
        redis_password = os.environ.get("CODECRUSHER_REDIS_PASSWORD", None)

        # Connect to Redis
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            db=redis_db,
            password=redis_password,
            decode_responses=True  # Return strings instead of bytes
        )

        # Test connection
        r.ping()

        # Try to get cached data
        cached_data = r.get(f"codecrusher:cache:{key}")
        if cached_data:
            return json.loads(cached_data)
    except:
        # If Redis is not available or connection fails, continue to file cache
        pass

    # Fallback to JSON file cache
    cache_data = load_cache_from_json()

    # Check if the key exists directly
    if key in cache_data:
        return cache_data[key]

    # Check if the key exists in any nested structure
    for cache_key, cache_value in cache_data.items():
        if isinstance(cache_value, dict) and key in cache_value:
            return cache_value[key]

        # Check one level deeper for model-specific caches
        if isinstance(cache_value, dict):
            for model_key, model_value in cache_value.items():
                if isinstance(model_value, dict) and key == model_key:
                    return model_value

    return None

@app.command("run")
def run_replay(
    input_file: str = typer.Option(..., "--input", "-i", help="Path to the original file"),
    prompt: str = typer.Option(..., "--prompt", "-t", help="The same prompt used during injection"),
    model: str = typer.Option("llama3", "--model", "-m", help="Model used during injection"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Filter by tag (space-separated, AND logic)"),
    tag_exclude: Optional[str] = typer.Option(None, "--tag-exclude", help="Exclude entries with these tags (space-separated)"),
    filter: Optional[str] = typer.Option(None, "--filter", help="Filter by fields (field=value,...)"),
    preview: str = typer.Option("full", "--preview", help="Preview mode (short, full, metadata)"),
    annotate_output: bool = typer.Option(False, "--annotate-output", help="Annotate output with tags"),
    no_telemetry: bool = typer.Option(False, "--no-telemetry", help="Disable telemetry logging"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Show detailed information"),
):
    """
    Replay a cached injection result without making a new AI request.

    This command computes the cache key from the file contents, prompt, and model,
    then retrieves the cached result if it exists. No new AI requests are made.

    You can filter results by tags using the --tag option. If multiple tags are provided,
    entries must match ALL tags (AND logic). Tag matching is case-insensitive and exact.

    Use the --tag-exclude option to exclude entries with specific tags.

    Use the --annotate-output flag to add a comment block at the top of the output
    that includes all tags. The comment syntax is automatically adjusted based on
    the file extension.

    Preview modes:
        --preview short      # show ID, date, truncated input/output (1-2 lines each)
        --preview full       # default: shows everything
        --preview metadata   # only ID, model, tags, and timestamps

    Filter options:
        --tag                # Include only entries with ALL these tags (AND logic)
        --tag-exclude        # Exclude entries with ANY of these tags
        --filter model=mistral
        --filter fallback=true
        --filter tag=teamA
        --filter error=timeout

    Examples:
        codecrusher replay run -i file.py -t "Add error handling"
        codecrusher replay run -i file.py -t "Add error handling" --tag performance
        codecrusher replay run -i file.py -t "Add error handling" --tag "bugfix @model:mistral"
        codecrusher replay run -i file.py -t "Add error handling" --tag-exclude testdata
        codecrusher replay run -i file.py -t "Add error handling" --tag performance --tag-exclude "draft testdata"
        codecrusher replay run -i file.py -t "Add error handling" --filter model=mistral
        codecrusher replay run -i file.py -t "Add error handling" --preview metadata
        codecrusher replay run -i file.py -t "Add error handling" --annotate-output
    """
    # Parse filter tags
    filter_tags = parse_user_tags(tag)

    # Parse exclude tags
    exclude_tags = parse_user_tags(tag_exclude)

    # Parse field filters
    field_filters = parse_filter(filter)

    # Determine preview mode
    try:
        preview_mode = PreviewMode(preview.lower())
    except ValueError:
        console.print(f"[bold red]❌ Error:[/bold red] Invalid preview mode: {preview}")
        console.print("[yellow]Valid preview modes: short, full, metadata[/yellow]")
        preview_mode = PreviewMode.FULL

    # Default provider (used for cache key generation)
    provider = "groq"

    # Convert input_file to Path
    input_path = Path(input_file)

    # Validate file exists
    if not input_path.exists():
        console.print(f"[bold red]❌ Error:[/bold red] File not found: {input_file}")
        raise typer.Exit(code=1)

    try:
        # Read file content
        file_content = input_path.read_text(encoding="utf-8")
    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] Failed to read file: {str(e)}")
        raise typer.Exit(code=1)

    # Format filter tags for display
    filter_tags_display = ", ".join(filter_tags) if filter_tags else "None"

    # Format exclude tags for display
    exclude_tags_display = ", ".join(exclude_tags) if exclude_tags else "None"

    # Format field filters for display
    field_filters_display = ", ".join([f"{k}={v}" for k, v in field_filters.items()]) if field_filters else "None"

    console.print(Panel(
        f"[bold]CodeCrusher Cache Replay[/bold]\n\n"
        f"[cyan]Input file:[/cyan] {input_file}\n"
        f"[cyan]Prompt:[/cyan] {prompt}\n"
        f"[cyan]Model:[/cyan] {model}\n"
        f"[cyan]Include Tags:[/cyan] {filter_tags_display}\n"
        f"[cyan]Exclude Tags:[/cyan] {exclude_tags_display}\n"
        f"[cyan]Field Filters:[/cyan] {field_filters_display}\n"
        f"[cyan]Preview Mode:[/cyan] {preview_mode.value}\n"
        f"[cyan]Annotate Output:[/cyan] {'Enabled' if annotate_output else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))

    # Generate cache key
    # First try the simple format used in main.py and cli/inject.py
    simple_cache_key = f"{input_file}:{prompt}"
    if model != "llama3":
        simple_cache_key += f":{model}"

    # Also try the unified_ai_engine format (MD5 hash)
    content_hash = f"{file_content}|{prompt}|{provider}|{model}"
    hash_cache_key = hashlib.md5(content_hash.encode()).hexdigest()

    # Also try the inject:: prefix format
    file_hash = hashlib.md5(file_content.encode()).hexdigest()[:8]
    inject_cache_key = f"inject::{file_hash}::{prompt}"

    # Also try the optimize format
    optimize_cache_key = f"optimize_{input_path.name}_{provider}_{model}"

    console.print(f"[dim]Checking cache with keys:[/dim]")
    console.print(f"[dim]1. {simple_cache_key}[/dim]")
    console.print(f"[dim]2. {hash_cache_key}[/dim]")
    console.print(f"[dim]3. {inject_cache_key}[/dim]")
    console.print(f"[dim]4. {optimize_cache_key}[/dim]")

    # Try to get cached entry with all possible keys
    cached_entry = None
    used_key = None

    # Try all cache keys
    for key in [hash_cache_key, simple_cache_key, inject_cache_key, optimize_cache_key]:
        cached_entry = get_cached_entry(key)
        if cached_entry:
            used_key = key
            break

    # If no cache entry found
    if not cached_entry:
        console.print(f"[bold yellow]⚠️ Warning:[/bold yellow] No cached result found for this combination")
        console.print("[yellow]Try running the inject command first with --cache to store the result[/yellow]")
        raise typer.Exit(code=1)

    console.print(f"[bold green]✅ Cache hit![/bold green] Found cached result with key: [dim]{used_key}[/dim]")

    # Extract content and tags from the cached entry
    content, original_tags = extract_content_from_cache(cached_entry, model)

    # Generate a unique ID for this replay for provenance tracking
    replay_id = str(uuid.uuid4())[:8]

    # Inherit tags from the original injection
    tags = inherit_tags(original_tags, is_replay=True, parent_id=used_key)

    # Log telemetry for the replay if not disabled
    if not no_telemetry:
        try:
            # Import telemetry logger
            from codecrusher.telemetry_logger import log_telemetry

            # Log the replay operation
            log_telemetry(
                model=model,
                provider="cache",
                cached=True,
                fallback=False,
                tags=tags,
                operation_id=replay_id,
                parent_id=used_key,
                operation_type="replay",
                no_telemetry=no_telemetry
            )

            if verbose:
                console.print("[cyan]Telemetry logged for replay operation[/cyan]")
        except ImportError:
            if verbose:
                console.print("[yellow]Telemetry logging not available[/yellow]")
        except Exception as e:
            if verbose:
                console.print(f"[yellow]Failed to log telemetry: {e}[/yellow]")

    # Create a combined entry for filtering
    combined_entry = {
        "model": model,
        "tags": tags,
        "output": content,
        "timestamp": datetime.now().isoformat(),
        "id": used_key
    }

    # Filter by tags if filter_tags or exclude_tags are provided
    if (filter_tags or exclude_tags) and not tag_matches(tags, filter_tags, exclude_tags):
        if filter_tags:
            console.print(f"[bold yellow]⚠️ Warning:[/bold yellow] Cached result does not match filter tags: {filter_tags_display}")
        if exclude_tags:
            console.print(f"[bold yellow]⚠️ Warning:[/bold yellow] Cached result matches exclude tags: {exclude_tags_display}")
        console.print("[yellow]Available tags: " + (", ".join(tags) if tags else "None") + "[/yellow]")
        raise typer.Exit(code=1)

    # Filter by fields if field_filters is provided
    if field_filters and not apply_filters(combined_entry, field_filters):
        console.print(f"[bold yellow]⚠️ Warning:[/bold yellow] Cached result does not match field filters: {field_filters_display}")
        raise typer.Exit(code=1)

    # Display tags based on preview mode
    if tags and preview_mode != PreviewMode.SHORT:
        console.print("\n[bold]Tags:[/bold]")

        # Highlight matched tags
        highlighted_tags = highlight_matched_tags(tags, filter_tags, exclude_tags)

        # Sort tags alphabetically
        highlighted_tags.sort(key=lambda x: x.lower())

        # Display tags
        for tag in highlighted_tags:
            console.print(tag)

    # Add annotation if enabled
    if annotate_output:
        # Determine if the content is code
        is_code = bool(content and (content.startswith("#") or "def " in content or "class " in content or "import " in content))

        # Format the annotation
        annotation = format_annotation(tags, input_file, is_code)

        # Add the annotation to the content
        content = annotation + content

    # Display content based on preview mode
    if preview_mode == PreviewMode.SHORT:
        # Short preview: truncated content
        console.print("\n[bold blue][REPLAY][/bold blue] [bold]Cached Content (Short Preview):[/bold]")

        # Show first few lines
        lines = content.split("\n")
        preview_lines = lines[:3]

        for line in preview_lines:
            console.print(truncate_text(line, 80))

        if len(lines) > 3:
            console.print("[dim]... (truncated)[/dim]")

    elif preview_mode == PreviewMode.METADATA:
        # Metadata only: no content
        console.print("\n[bold blue][REPLAY][/bold blue] [bold]Cached Content (Metadata Only):[/bold]")
        console.print("[dim](Content preview disabled in metadata mode)[/dim]")

    else:  # FULL
        # Full preview: complete content
        console.print("\n[bold blue][REPLAY][/bold blue] [bold]Cached Content:[/bold]")

        # Try to detect if it's code and use syntax highlighting
        if content and (content.startswith("#") or "def " in content or "class " in content or "import " in content):
            console.print(Syntax(content, "python", theme="monokai", line_numbers=True))
        else:
            console.print(content)

    console.print("\n[bold green][SUCCESS] Cache replay completed[/bold green]")
    console.print("[dim](No AI request was made - result retrieved from cache)[/dim]")

    return content

def extract_content_from_cache(cached_entry, model):
    """Extract content and tags from a cached entry."""
    content = None
    tags = []

    # Try to extract content based on the type and structure of the cached entry
    if isinstance(cached_entry, str):
        # Simple string content (legacy format)
        content = cached_entry
    elif isinstance(cached_entry, dict):
        # Extract tags if they exist
        if "tags" in cached_entry:
            tags = cached_entry.get("tags", [])

        # Check if it's a direct output entry
        if "output" in cached_entry:
            content = cached_entry.get("output", "")
        # Check if it's a result entry
        elif "result" in cached_entry:
            content = cached_entry.get("result", "")
        # Check if it's a model-keyed entry
        elif model in cached_entry:
            model_entry = cached_entry[model]
            if isinstance(model_entry, dict):
                # Extract tags from model entry if they exist
                if "tags" in model_entry:
                    tags = model_entry.get("tags", [])

                if "output" in model_entry:
                    content = model_entry.get("output", "")
                else:
                    # Try to convert the model entry to a string
                    content = str(model_entry)
            else:
                # If model_entry is not a dict, use it directly
                content = str(model_entry)
        else:
            # If no recognizable structure, convert the whole entry to a string
            content = str(cached_entry)
    else:
        # For any other type, convert to string
        content = str(cached_entry)

    # If content is still None, use a default message
    if content is None:
        content = "No content could be extracted from the cache entry."

    # If no tags were found in the cache entry, try to extract them from the content
    if not tags and content:
        tags = extract_tags_from_content(content)

    return content, tags
