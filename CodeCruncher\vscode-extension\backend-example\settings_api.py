#!/usr/bin/env python3
"""
CodeCrusher Settings Sync API Example
=====================================

This is a simple FastAPI backend example that demonstrates the settings sync
endpoints required for the VS Code extension live sync functionality.

To run this example:
1. pip install fastapi uvicorn
2. python settings_api.py
3. The API will be available at http://localhost:8000

Endpoints:
- GET /api/settings/vscode - Get current VS Code settings
- POST /api/settings/vscode - Update VS Code settings
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import time
import json
import os

app = FastAPI(
    title="CodeCrusher Settings Sync API",
    description="Backend API for syncing VS Code extension settings",
    version="1.0.0"
)

# Enable CORS for VS Code extension
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your VS Code extension origin
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Settings model matching the VS Code extension interface
class VSCodeSettings(BaseModel):
    model: str = "auto"
    fallback: bool = True
    mode: str = "preview"
    lastSyncTimestamp: Optional[int] = None

# In-memory storage (in production, use a database)
# Structure: workspace_id -> settings
settings_storage = {
    "global": VSCodeSettings(
        model="auto",
        fallback=True,
        mode="preview",
        lastSyncTimestamp=int(time.time() * 1000)
    )
}

# File-based persistence (optional)
SETTINGS_FILE = "codecrusher_settings.json"

def load_settings():
    """Load settings from file if it exists"""
    if os.path.exists(SETTINGS_FILE):
        try:
            with open(SETTINGS_FILE, 'r') as f:
                data = json.load(f)
                # Load workspace-scoped settings
                for workspace_id, settings_data in data.items():
                    settings_storage[workspace_id] = VSCodeSettings(**settings_data)
                print(f"Loaded settings from {SETTINGS_FILE}")
        except Exception as e:
            print(f"Error loading settings: {e}")

def save_settings():
    """Save settings to file"""
    try:
        # Convert all settings to dict format for JSON serialization
        serializable_storage = {
            workspace_id: settings.model_dump()
            for workspace_id, settings in settings_storage.items()
        }
        with open(SETTINGS_FILE, 'w') as f:
            json.dump(serializable_storage, f, indent=2)
        print(f"Saved settings to {SETTINGS_FILE}")
    except Exception as e:
        print(f"Error saving settings: {e}")

@app.on_event("startup")
async def startup_event():
    """Load settings on startup"""
    load_settings()
    print("CodeCrusher Settings Sync API started")
    print("Available endpoints:")
    print("  GET  /api/settings/vscode - Get current settings")
    print("  POST /api/settings/vscode - Update settings")
    print("  GET  /docs - API documentation")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "CodeCrusher Settings Sync API",
        "version": "1.0.0",
        "endpoints": {
            "get_settings": "/api/settings/vscode",
            "update_settings": "/api/settings/vscode",
            "docs": "/docs"
        }
    }

@app.get("/api/settings/vscode/{workspace_id}", response_model=VSCodeSettings)
async def get_vscode_settings(workspace_id: str):
    """
    Get current VS Code settings for a specific workspace

    Returns the current settings with timestamp for conflict resolution.
    """
    if workspace_id not in settings_storage:
        # Return default settings for new workspaces
        default_settings = VSCodeSettings(
            model="auto",
            fallback=True,
            mode="preview",
            lastSyncTimestamp=int(time.time() * 1000)
        )
        settings_storage[workspace_id] = default_settings

    current_settings = settings_storage[workspace_id]
    print(f"GET /api/settings/vscode/{workspace_id} - Returning: {current_settings.model_dump()}")
    return current_settings

@app.get("/api/settings/vscode", response_model=VSCodeSettings)
async def get_global_vscode_settings():
    """
    Get global VS Code settings (fallback for backward compatibility)
    """
    return await get_vscode_settings("global")

@app.post("/api/settings/vscode/{workspace_id}", response_model=VSCodeSettings)
async def update_vscode_settings(workspace_id: str, settings: VSCodeSettings):
    """
    Update VS Code settings for a specific workspace

    Accepts new settings and updates the stored configuration.
    Automatically sets lastSyncTimestamp if not provided.
    """
    # Set timestamp if not provided
    if settings.lastSyncTimestamp is None:
        settings.lastSyncTimestamp = int(time.time() * 1000)

    # Validate model
    valid_models = ["auto", "mistral", "gemma", "mixtral", "llama3-8b", "llama3-70b"]
    if settings.model not in valid_models:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid model. Must be one of: {valid_models}"
        )

    # Validate mode
    valid_modes = ["preview", "apply"]
    if settings.mode not in valid_modes:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid mode. Must be one of: {valid_modes}"
        )

    # Update workspace-specific settings
    settings_storage[workspace_id] = settings
    save_settings()

    print(f"POST /api/settings/vscode/{workspace_id} - Updated: {settings.model_dump()}")

    return settings

@app.post("/api/settings/vscode", response_model=VSCodeSettings)
async def update_global_vscode_settings(settings: VSCodeSettings):
    """
    Update global VS Code settings (fallback for backward compatibility)
    """
    return await update_vscode_settings("global", settings)

@app.get("/api/settings/vscode/history")
async def get_settings_history():
    """
    Get settings change history (example endpoint)

    In a real implementation, this would return a history of settings changes.
    """
    return {
        "message": "Settings history endpoint",
        "workspaces": {workspace_id: settings.model_dump() for workspace_id, settings in settings_storage.items()},
        "note": "In a real implementation, this would return change history"
    }

@app.delete("/api/settings/vscode/{workspace_id}")
async def reset_workspace_settings(workspace_id: str):
    """
    Reset workspace VS Code settings to defaults
    """
    default_settings = VSCodeSettings(
        model="auto",
        fallback=True,
        mode="preview",
        lastSyncTimestamp=int(time.time() * 1000)
    )

    settings_storage[workspace_id] = default_settings
    save_settings()

    print(f"Workspace {workspace_id} settings reset to defaults")
    return default_settings

@app.delete("/api/settings/vscode")
async def reset_global_settings():
    """
    Reset global VS Code settings to defaults
    """
    return await reset_workspace_settings("global")

if __name__ == "__main__":
    import uvicorn

    print("Starting CodeCrusher Settings Sync API...")
    print("Install dependencies: pip install fastapi uvicorn")
    print("API will be available at: http://localhost:8000")
    print("API docs will be available at: http://localhost:8000/docs")

    uvicorn.run(
        "settings_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
