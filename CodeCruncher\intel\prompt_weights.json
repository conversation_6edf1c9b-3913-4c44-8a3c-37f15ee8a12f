{"tone": "formal", "fallback_level": 2, "model_hint": "gpt-4", "shaping_strength": 0.7, "fallback_sensitivity": 0.6, "last_updated": "2024-01-15T10:30:00.000Z", "version": "1.0", "metadata": {"description": "Shared prompt shaping configuration for CLI and VS Code extension", "auto_tuned": true, "performance_score": 87.5, "usage_count": 42}, "tone_history": {"formal": {"usage_count": 25, "avg_score": 89.2, "last_used": "2024-01-15T10:25:00.000Z"}, "assertive": {"usage_count": 12, "avg_score": 85.1, "last_used": "2024-01-14T16:45:00.000Z"}, "friendly": {"usage_count": 8, "avg_score": 82.3, "last_used": "2024-01-14T14:20:00.000Z"}, "neutral": {"usage_count": 15, "avg_score": 78.9, "last_used": "2024-01-13T09:15:00.000Z"}}, "model_performance": {"gpt-4": {"usage_count": 18, "avg_score": 92.1, "fallback_rate": 0.05, "last_used": "2024-01-15T10:30:00.000Z"}, "mixtral": {"usage_count": 24, "avg_score": 84.7, "fallback_rate": 0.12, "last_used": "2024-01-15T09:45:00.000Z"}, "llama3-70b": {"usage_count": 15, "avg_score": 81.3, "fallback_rate": 0.18, "last_used": "2024-01-14T18:30:00.000Z"}, "claude": {"usage_count": 3, "avg_score": 88.9, "fallback_rate": 0.08, "last_used": "2024-01-12T15:20:00.000Z"}}, "tuning_rules": {"auto_adjust_tone": true, "auto_adjust_model": true, "min_score_threshold": 75, "max_shaping_strength": 0.9, "learning_rate": 0.1}, "context_preferences": {"python": {"preferred_tone": "formal", "shaping_strength": 0.8, "model_hint": "gpt-4"}, "javascript": {"preferred_tone": "assertive", "shaping_strength": 0.6, "model_hint": "mixtral"}, "typescript": {"preferred_tone": "formal", "shaping_strength": 0.7, "model_hint": "gpt-4"}, "java": {"preferred_tone": "formal", "shaping_strength": 0.9, "model_hint": "gpt-4"}}}