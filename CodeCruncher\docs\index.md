# Welcome to CodeCrusher Documentation

<div align="center">

**🚀 Enterprise-grade AI-powered code injector with self-improving intelligence**

*The only AI code tool that learns from your feedback and gets better over time*

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![CodeCrusher vs Augment](https://img.shields.io/badge/vs%20Augment-🏆%203%20Wins%20🤝%201%20Draw-green)](../e2e-comparison/)

</div>

---

## 🎯 What is CodeCrusher?

CodeCrusher is an **intelligent AI code injection system** that revolutionizes how developers work with AI-generated code. Unlike static AI tools, CodeCrusher features a **self-improving intelligence loop** that learns from your feedback and automatically enhances code quality over time.

### 🏆 **Proven Superior Performance**
- **75% Win Rate** against Augment in side-by-side comparisons
- **92.5/100** average quality score vs competitors' 77.5/100
- **Self-improving intelligence** that adapts from user feedback

---

## 🚀 Quick Start

### Installation
```bash
# Install from PyPI
pip install codecrusher

# Or install from source
git clone https://github.com/Codegx-Technology/CodeCruncher.git
cd CodeCruncher
pip install -e .
```

### Basic Usage
```bash
# Inject AI-generated code
codecrusher inject ./src --prompt "Add error handling" --preview

# Rate the results to improve future responses
codecrusher rate ./src --rating 4 --comment "Good but needs more logging"

# Trigger intelligence learning
codecrusher learn --apply
```

---

## 📚 Documentation Structure

### 👤 **User Documentation**
- **[Installation Guide](installation.md)** - Get CodeCrusher running on your system
- **[Usage Guide](usage.md)** - Learn CLI commands and injection workflows
- **[Tuning Guide](tuning.md)** - Configure fallback sensitivity and model preferences
- **[Dashboard Guide](dashboard.md)** - Use the web interface for real-time monitoring
- **[Troubleshooting](troubleshooting.md)** - Solve common issues and debug problems

### 🛠️ **Developer Documentation**
- **[Contributing Guide](contributing.md)** - How to contribute to CodeCrusher development
- **[Architecture Overview](architecture.md)** - Understand the system design and components
- **[Changelog](changelog.md)** - Version history and release notes

---

## 🧠 Key Features

### 🤖 **Intelligent AI Injection**
- **Multi-model support**: Groq, Mistral, OpenAI with automatic fallback
- **Smart escalation**: Automatically upgrades to better models for complex tasks
- **Context-aware prompting**: Understands your codebase and coding style

### 🔄 **Self-Improving Intelligence**
- **Feedback learning**: Rate injections to improve future responses
- **Adaptive parameters**: Automatically adjusts prompt style, verbosity, and error handling
- **Quality scoring**: Built-in assessment and improvement tracking

### 🛠️ **Professional CLI**
- **Interactive preview**: See changes before applying
- **Rich console output**: Beautiful tables, panels, and progress indicators
- **Recursive processing**: Handle entire directories with `--recursive`
- **Comprehensive logging**: Track all injections and improvements

### 📊 **Enterprise Features**
- **Team collaboration**: Share intelligence improvements across teams
- **Quality assurance**: Built-in validation and improvement tracking
- **Professional documentation**: Comprehensive guides and API references
- **CI/CD integration**: GitHub Actions and automated publishing

---

## 🎯 Why Choose CodeCrusher?

### 🏆 **vs Traditional AI Tools**
- ✅ **Self-Improving**: Learns from feedback, gets better over time
- ✅ **Comprehensive**: Production-ready code with error handling
- ✅ **Reliable**: Multi-model fallback ensures consistent results
- ✅ **Professional**: Enterprise-grade CLI and reporting

### 🧠 **vs Static AI**
- ✅ **Adaptive**: Intelligence that improves based on usage
- ✅ **Context-Aware**: Understands your specific coding patterns
- ✅ **Quality-Focused**: Prioritizes maintainable, robust code
- ✅ **Feedback-Driven**: Real user input drives continuous improvement

---

## 🚀 Getting Started

### 1. **Install CodeCrusher**
Follow our [Installation Guide](installation.md) to get CodeCrusher running on your system.

### 2. **Learn the Basics**
Check out the [Usage Guide](usage.md) to understand core commands and workflows.

### 3. **Configure Your Setup**
Use the [Tuning Guide](tuning.md) to optimize CodeCrusher for your specific needs.

### 4. **Explore Advanced Features**
Discover the [Dashboard](dashboard.md) for real-time monitoring and team collaboration.

### 5. **Get Help When Needed**
Consult our [Troubleshooting Guide](troubleshooting.md) for common issues and solutions.

---

## 🤝 Community & Support

### 📞 **Get Help**
- **GitHub Issues**: Report bugs and request features
- **Documentation**: Comprehensive guides and tutorials
- **Community**: Join discussions and share experiences

### 🛠️ **Contribute**
- **Code Contributions**: Follow our [Contributing Guide](contributing.md)
- **Documentation**: Help improve guides and tutorials
- **Testing**: Validate new features and report issues
- **Feedback**: Share your experience and suggestions

---

## 📈 Proven Results

### 🏆 **Comparison Results**
```
CodeCrusher vs Augment: 🏆 3 Wins 🤝 1 Draw ❌ 0 Losses
Quality Score: 92.5/100 vs 77.5/100 (+15 points)
Key Advantage: Self-improving intelligence + comprehensive error handling
```

### 🧠 **Intelligence Learning**
- **Before Learning**: 3-line placeholder comments
- **After Learning**: 30+ line comprehensive implementations
- **Improvement**: 18+ quality indicators automatically applied
- **Adaptation**: All 4 learning parameters updated from user feedback

---

## 🎉 Ready to Get Started?

CodeCrusher is designed to make AI-powered development more intelligent, reliable, and productive. Whether you're working on a small script or a large enterprise application, CodeCrusher adapts to your needs and improves with every use.

**[Start with Installation →](installation.md)**

---

<div align="center">

*Built with ❤️ by the CodeCrusher team*

[Installation](installation.md) • [Usage](usage.md) • [Contributing](contributing.md) • [GitHub](https://github.com/Codegx-Technology/CodeCruncher)

</div>
