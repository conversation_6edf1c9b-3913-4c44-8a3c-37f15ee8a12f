import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { TrendingUp, TrendingDown, Clock, Target, Zap, RefreshCw } from 'lucide-react';

interface ModelStats {
  model: string;
  total_feedback: number;
  avg_rating: number;
  satisfaction_rate: number;
  success_rate: number;
  avg_latency_ms: number;
  min_latency_ms: number;
  max_latency_ms: number;
  avg_token_count: number;
  retry_success_count: number;
  retry_success_rate: number;
  retry_saved_percentage: number;
  auto_fix_applied_count: number;
  auto_fix_success_rate: number;
  performance_grade: string;
  intent_distribution: Record<string, number>;
  positive_feedback_count: number;
  negative_feedback_count: number;
  neutral_feedback_count: number;
}

interface TimeSeriesData {
  date: string;
  model: string;
  avg_rating: number;
  latency_ms: number;
  satisfaction_rate: number;
  total_feedback: number;
}

interface BestModelPrediction {
  best_model: string;
  confidence: number;
  reasoning: string;
  alternatives: Array<{
    model: string;
    confidence: number;
    reasoning: string;
  }>;
}

const ModelAnalyticsPanel: React.FC = () => {
  const [modelStats, setModelStats] = useState<ModelStats[]>([]);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDays, setSelectedDays] = useState(30);
  const [testPrompt, setTestPrompt] = useState('');
  const [prediction, setPrediction] = useState<BestModelPrediction | null>(null);
  const [predictionLoading, setPredictionLoading] = useState(false);

  const fetchModelStats = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/model_stats?days=${selectedDays}`);
      if (!response.ok) throw new Error('Failed to fetch model stats');
      const data = await response.json();
      setModelStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  const fetchTimeSeriesData = async () => {
    try {
      const response = await fetch(`/api/model_timeseries_stats?days=${selectedDays}`);
      if (!response.ok) throw new Error('Failed to fetch time series data');
      const data = await response.json();
      setTimeSeriesData(data);
    } catch (err) {
      console.error('Failed to fetch time series data:', err);
    }
  };

  const predictBestModel = async () => {
    if (!testPrompt.trim()) return;
    
    try {
      setPredictionLoading(true);
      const response = await fetch('/api/best_model_predictor', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt: testPrompt })
      });
      
      if (!response.ok) throw new Error('Failed to predict best model');
      const data = await response.json();
      setPrediction(data);
    } catch (err) {
      console.error('Failed to predict best model:', err);
    } finally {
      setPredictionLoading(false);
    }
  };

  useEffect(() => {
    fetchModelStats();
    fetchTimeSeriesData();
  }, [selectedDays]);

  const getRowColorClass = (avgRating: number) => {
    if (avgRating < 0.5) return 'bg-red-100 hover:bg-red-200';
    if (avgRating < 0.7) return 'bg-yellow-100 hover:bg-yellow-200';
    return 'hover:bg-gray-50';
  };

  const getPerformanceIcon = (grade: string) => {
    switch (grade) {
      case 'A+':
      case 'A':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'B+':
      case 'B':
        return <Target className="h-4 w-4 text-blue-600" />;
      case 'C':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <TrendingDown className="h-4 w-4 text-red-600" />;
    }
  };

  const formatLatency = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Loading analytics...
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6">
        <div className="text-red-600">Error: {error}</div>
        <Button onClick={fetchModelStats} className="mt-2">
          Retry
        </Button>
      </Card>
    );
  }

  // Prepare chart data
  const ratingChartData = timeSeriesData.reduce((acc, item) => {
    const existing = acc.find(d => d.date === item.date);
    if (existing) {
      existing[item.model] = item.avg_rating;
    } else {
      acc.push({ date: item.date, [item.model]: item.avg_rating });
    }
    return acc;
  }, [] as any[]);

  const latencyChartData = timeSeriesData.reduce((acc, item) => {
    const existing = acc.find(d => d.date === item.date);
    if (existing) {
      existing[item.model] = item.latency_ms;
    } else {
      acc.push({ date: item.date, [item.model]: item.latency_ms });
    }
    return acc;
  }, [] as any[]);

  const uniqueModels = [...new Set(timeSeriesData.map(d => d.model))];
  const modelColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Model Performance Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <label htmlFor="days-select" className="text-sm font-medium">
                Time Period:
              </label>
              <select
                id="days-select"
                value={selectedDays}
                onChange={(e) => setSelectedDays(Number(e.target.value))}
                className="border rounded px-2 py-1 text-sm"
              >
                <option value={7}>Last 7 days</option>
                <option value={30}>Last 30 days</option>
                <option value={90}>Last 90 days</option>
              </select>
            </div>
            <Button onClick={fetchModelStats} size="sm" variant="outline">
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Best Model Predictor */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Best Model Predictor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex gap-2">
              <input
                type="text"
                placeholder="Enter a prompt to get model recommendation..."
                value={testPrompt}
                onChange={(e) => setTestPrompt(e.target.value)}
                className="flex-1 border rounded px-3 py-2 text-sm"
                onKeyPress={(e) => e.key === 'Enter' && predictBestModel()}
              />
              <Button 
                onClick={predictBestModel} 
                disabled={!testPrompt.trim() || predictionLoading}
                size="sm"
              >
                {predictionLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  'Predict'
                )}
              </Button>
            </div>
            
            {prediction && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="default" className="bg-blue-600">
                    Recommended: {prediction.best_model}
                  </Badge>
                  <span className="text-sm text-gray-600">
                    Confidence: {(prediction.confidence * 100).toFixed(1)}%
                  </span>
                </div>
                <p className="text-sm text-gray-700 mb-2">{prediction.reasoning}</p>
                {prediction.alternatives.length > 0 && (
                  <div className="text-xs text-gray-600">
                    Alternatives: {prediction.alternatives.map(alt => alt.model).join(', ')}
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Model Performance Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Model</th>
                  <th className="text-left p-2">Grade</th>
                  <th className="text-left p-2">Avg Rating</th>
                  <th className="text-left p-2">Success Rate</th>
                  <th className="text-left p-2">Avg Latency</th>
                  <th className="text-left p-2">Retry Saved %</th>
                  <th className="text-left p-2">Total Feedback</th>
                </tr>
              </thead>
              <tbody>
                {modelStats.map((model) => (
                  <tr 
                    key={model.model} 
                    className={`border-b transition-colors ${getRowColorClass(model.avg_rating)}`}
                  >
                    <td className="p-2 font-medium">{model.model}</td>
                    <td className="p-2">
                      <div className="flex items-center gap-1">
                        {getPerformanceIcon(model.performance_grade)}
                        <Badge variant="outline">{model.performance_grade}</Badge>
                      </div>
                    </td>
                    <td className="p-2">{model.avg_rating.toFixed(2)}</td>
                    <td className="p-2">{(model.success_rate * 100).toFixed(1)}%</td>
                    <td className="p-2">{formatLatency(model.avg_latency_ms)}</td>
                    <td className="p-2">
                      <span className={`font-medium ${
                        model.retry_saved_percentage > 50 ? 'text-green-600' : 
                        model.retry_saved_percentage > 25 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {model.retry_saved_percentage.toFixed(1)}%
                      </span>
                    </td>
                    <td className="p-2">{model.total_feedback}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Time Series Charts */}
      {timeSeriesData.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Average Rating Over Time */}
          <Card>
            <CardHeader>
              <CardTitle>Average Rating Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={ratingChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis domain={[0, 1]} />
                  <Tooltip />
                  <Legend />
                  {uniqueModels.map((model, index) => (
                    <Line
                      key={model}
                      type="monotone"
                      dataKey={model}
                      stroke={modelColors[index % modelColors.length]}
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  ))}
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Latency Over Time */}
          <Card>
            <CardHeader>
              <CardTitle>Latency Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={latencyChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value}ms`, 'Latency']} />
                  <Legend />
                  {uniqueModels.map((model, index) => (
                    <Line
                      key={model}
                      type="monotone"
                      dataKey={model}
                      stroke={modelColors[index % modelColors.length]}
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  ))}
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default ModelAnalyticsPanel;
