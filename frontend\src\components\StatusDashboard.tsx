import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Sparkles, XCircle, CheckCircle, Play, Square, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { useWebSocket, WebSocketMessage } from "@/hooks/useWebSocket";
import { ConnectionOverlay, ConnectionStatusBadge } from "@/components/ConnectionOverlay";
import { getWebSocketUrl } from "@/utils/client";

export default function StatusDashboard() {
  // Connection and status state
  const [status, setStatus] = useState("idle");
  const [model, setModel] = useState("mixtral");
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  // Configuration state
  const [source, setSource] = useState("./src");
  const [promptText, setPromptText] = useState("Optimize code for clarity and performance");
  const [tag, setTag] = useState("status-dashboard");

  const logContainerRef = useRef<HTMLDivElement>(null);

  // WebSocket hook with auto-reconnection
  const {
    connectionStatus,
    lastMessage,
    sendMessage,
    connect,
    disconnect,
    retry,
    retryCount,
    isConnected
  } = useWebSocket({
    url: getWebSocketUrl('/logs'),
    maxRetries: 5,
    maxReconnectDelay: 10000,
    autoConnect: true, // ✅ Enable auto-connect for immediate connection
    timeoutMs: 5000, // Reduce timeout for faster failure detection
    onMessage: handleWebSocketMessage,
    onStatusChange: (newStatus) => {
      console.log(`WebSocket status changed: ${newStatus}`);
    }
  });

  // Handle WebSocket messages
  function handleWebSocketMessage(message: WebSocketMessage) {
    if (message.type === "progress" && message.value !== undefined) {
      setProgress(message.value);
    } else if (message.type === "error") {
      setError(message.message || "Unknown error");
      setStatus("error");
      setIsRunning(false);
    } else if (message.type === "done" || message.message?.includes("completed successfully")) {
      setStatus("done");
      setProgress(100);
      setIsRunning(false);
    } else if (message.message) {
      setLogs(prev => [...prev, message.message!]);

      // Extract progress from plain text if not already handled
      if (message.type !== "progress" && message.value !== undefined) {
        setProgress(message.value);
      }

      // Check for completion in plain text
      if (message.message.includes("completed successfully")) {
        setStatus("done");
        setProgress(100);
        setIsRunning(false);
      } else if (message.message.includes("injection failed") || message.message.includes("Error:")) {
        setStatus("error");
        setError(message.message);
        setIsRunning(false);
      }
    }
  }

  // Auto-scroll logs
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = logContainerRef.current.scrollHeight;
    }
  }, [logs]);

  // Update status based on connection status
  useEffect(() => {
    if (connectionStatus === "connected" && status === "idle") {
      setStatus("connected");
    } else if (connectionStatus === "error") {
      setStatus("error");
    }
  }, [connectionStatus, status]);

  const startInjection = async () => {
    if (!isConnected) {
      setError("WebSocket not connected");
      return;
    }

    setIsRunning(true);
    setStatus("running");
    setProgress(0);
    setError(null);
    setLogs([]);

    try {
      const response = await fetch("http://127.0.0.1:8001/inject", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: promptText,
          file_path: source,
          model,
          apply: false, // Preview mode
          tags: [tag]
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        setError(result.error || "Injection failed");
        setStatus("error");
        setIsRunning(false);
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Request failed";
      setError(errorMessage);
      setStatus("error");
      setIsRunning(false);
    }
  };

  const stopInjection = () => {
    setIsRunning(false);
    setStatus("connected");
    setLogs(prev => [...prev, "🛑 Injection stopped by user"]);
  };

  const clearLogs = () => {
    setLogs([]);
    setProgress(0);
    setError(null);
    setStatus("connected");
  };

  const getStatusBadge = () => {
    switch (status) {
      case "connecting":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            <Loader2 className="w-4 h-4 mr-1 animate-spin" />
            Connecting...
          </Badge>
        );
      case "connected":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            <CheckCircle className="w-4 h-4 mr-1" />
            Connected to {model}
          </Badge>
        );
      case "running":
        return (
          <Badge variant="outline" className="bg-purple-100 text-purple-800">
            <Loader2 className="w-4 h-4 mr-1 animate-spin" />
            Running...
          </Badge>
        );
      case "done":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            <Sparkles className="w-4 h-4 mr-1" />
            Completed!
          </Badge>
        );
      case "error":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            <XCircle className="w-4 h-4 mr-1" />
            Error
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800">
            Idle
          </Badge>
        );
    }
  };

  return (
    <div className="p-6 space-y-6 max-w-6xl mx-auto">
      {/* Connection Overlay */}
      <ConnectionOverlay
        status={connectionStatus}
        retryCount={retryCount}
        maxRetries={10}
        onRetry={retry}
        onRefresh={() => window.location.reload()}
      />

      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">CodeCrusher Status Dashboard</h1>
        <div className="flex items-center gap-3">
          <ConnectionStatusBadge
            status={connectionStatus}
            retryCount={retryCount}
          />
          {getStatusBadge()}
        </div>
      </div>

      {/* Status Indicators */}
      {status === "connecting" && (
        <div className="flex items-center space-x-2 text-yellow-600">
          <Loader2 className="animate-spin h-5 w-5" />
          <span>Connecting to AI engine...</span>
        </div>
      )}

      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Progress</span>
          <span>{progress}%</span>
        </div>
        <Progress
          value={progress}
          className={cn(
            "h-3",
            status === "done" && "bg-green-500"
          )}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <Card>
          <CardHeader>
            <CardTitle>Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="source">Source Path</Label>
              <Input
                id="source"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                placeholder="./src"
              />
            </div>

            <div>
              <Label htmlFor="prompt">AI Prompt</Label>
              <Textarea
                id="prompt"
                value={promptText}
                onChange={(e) => setPromptText(e.target.value)}
                rows={3}
                placeholder="Enter your optimization prompt..."
              />
            </div>

            <div>
              <Label htmlFor="tag">Tag</Label>
              <Input
                id="tag"
                value={tag}
                onChange={(e) => setTag(e.target.value)}
                placeholder="status-dashboard"
              />
            </div>

            <div className="flex gap-2">
              {!isConnected ? (
                <Button
                  onClick={connect}
                  disabled={connectionStatus === 'connecting'}
                  className="flex-1"
                  variant="outline"
                >
                  {connectionStatus === 'connecting' ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Connecting...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Connect to Server
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={startInjection}
                  disabled={isRunning || !isConnected || !promptText.trim()}
                  className="flex-1"
                >
                  {isRunning ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Running...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Start Injection
                    </>
                  )}
                </Button>
              )}

              {isRunning && (
                <Button
                  onClick={stopInjection}
                  variant="destructive"
                  size="sm"
                >
                  <Square className="w-4 h-4" />
                </Button>
              )}
            </div>

            <Button
              onClick={clearLogs}
              variant="outline"
              className="w-full"
            >
              Clear Logs
            </Button>
          </CardContent>
        </Card>

        {/* Real-time Logs */}
        <Card>
          <CardHeader>
            <CardTitle>Real-time Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div
              ref={logContainerRef}
              className="bg-black text-green-400 font-mono p-4 rounded-lg h-80 overflow-y-auto text-sm"
            >
              {logs.length === 0 ? (
                <div className="text-gray-500 italic">
                  Awaiting real-time logs...
                </div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>

            <div className="mt-2 text-xs text-gray-600 flex justify-between">
              <span>{logs.length} messages</span>
              <span>Status: {status}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
