#!/usr/bin/env python3
"""
CLI Prompt Shaper - Shared Intelligence Integration

This module provides prompt shaping functionality for the CLI that integrates
with the shared intelligence system via intel_api.py.
"""

import os
import sys
import logging
from typing import Dict, Any, Optional

# Add parent directory to path for intel_api import
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from intel_api import load_prompt_weights, save_prompt_weights, update_prompt_weights
    INTEL_API_AVAILABLE = True
except ImportError:
    INTEL_API_AVAILABLE = False
    logging.warning("Intel API not available - using fallback prompt shaping")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PromptShaper:
    """
    CLI Prompt Shaper that uses shared intelligence for consistent shaping
    across CLI and VS Code extension.
    """
    
    def __init__(self):
        """Initialize the prompt shaper."""
        self.weights = self._load_weights()
        logger.debug(f"Prompt shaper initialized with weights: {self.weights}")
    
    def _load_weights(self) -> Dict[str, Any]:
        """Load prompt weights from shared intelligence."""
        if INTEL_API_AVAILABLE:
            try:
                weights = load_prompt_weights()
                logger.debug("Loaded prompt weights from intel_api")
                return weights
            except Exception as e:
                logger.warning(f"Failed to load weights from intel_api: {e}")
        
        # Fallback weights
        return {
            "tone": "default",
            "fallback_level": 1,
            "model_hint": "mixtral",
            "shaping_strength": 0.5,
            "fallback_sensitivity": 0.7
        }
    
    def shape_prompt(self, base_prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """
        Shape a prompt based on shared intelligence weights.
        
        Args:
            base_prompt (str): The base prompt to shape
            context (Dict[str, Any], optional): Additional context for shaping
            
        Returns:
            str: The shaped prompt
        """
        if not base_prompt:
            return base_prompt
        
        # Reload weights to get latest shared configuration
        self.weights = self._load_weights()
        
        shaped_prompt = base_prompt
        tone = self.weights.get("tone", "default")
        shaping_strength = self.weights.get("shaping_strength", 0.5)
        
        # Apply tone-based shaping
        if tone != "default" and shaping_strength > 0.3:
            shaped_prompt = self._apply_tone(shaped_prompt, tone, shaping_strength)
        
        # Apply model-specific hints
        model_hint = self.weights.get("model_hint", "mixtral")
        if model_hint and model_hint != "default":
            shaped_prompt = self._apply_model_hints(shaped_prompt, model_hint)
        
        # Apply context-specific shaping
        if context:
            shaped_prompt = self._apply_context(shaped_prompt, context)
        
        logger.debug(f"Shaped prompt with tone={tone}, model={model_hint}, strength={shaping_strength}")
        return shaped_prompt
    
    def _apply_tone(self, prompt: str, tone: str, strength: float) -> str:
        """Apply tone-based shaping to the prompt."""
        tone_modifiers = {
            "formal": {
                "prefix": "Please provide a professional and detailed response. ",
                "suffix": " Ensure the output follows enterprise coding standards."
            },
            "assertive": {
                "prefix": "Generate a confident and decisive solution. ",
                "suffix": " Be direct and specific in your implementation."
            },
            "friendly": {
                "prefix": "Help me with this in a clear and approachable way. ",
                "suffix": " Make the solution easy to understand and implement."
            },
            "neutral": {
                "prefix": "",
                "suffix": ""
            }
        }
        
        modifier = tone_modifiers.get(tone, tone_modifiers["neutral"])
        
        # Apply strength scaling
        if strength < 0.5:
            # Light shaping - only suffix
            return prompt + modifier["suffix"]
        else:
            # Full shaping - prefix and suffix
            return modifier["prefix"] + prompt + modifier["suffix"]
    
    def _apply_model_hints(self, prompt: str, model_hint: str) -> str:
        """Apply model-specific hints to optimize for the target model."""
        model_optimizations = {
            "gpt-4": "\n\nOptimize for GPT-4's advanced reasoning capabilities.",
            "mixtral": "\n\nProvide clear, structured output suitable for Mixtral processing.",
            "llama": "\n\nUse explicit instructions and clear formatting for Llama models.",
            "claude": "\n\nLeverage Claude's analytical strengths with detailed explanations."
        }
        
        hint = model_optimizations.get(model_hint.lower(), "")
        return prompt + hint
    
    def _apply_context(self, prompt: str, context: Dict[str, Any]) -> str:
        """Apply context-specific shaping."""
        # File type specific shaping
        file_type = context.get("file_type", "")
        if file_type:
            if file_type in [".py", ".python"]:
                prompt += "\n\nEnsure Python best practices and PEP 8 compliance."
            elif file_type in [".js", ".ts", ".javascript", ".typescript"]:
                prompt += "\n\nFollow modern JavaScript/TypeScript conventions."
            elif file_type in [".java"]:
                prompt += "\n\nAdhere to Java coding standards and design patterns."
        
        # Complexity level shaping
        complexity = context.get("complexity", "medium")
        if complexity == "high":
            prompt += "\n\nProvide comprehensive solution with error handling and edge cases."
        elif complexity == "low":
            prompt += "\n\nKeep the solution simple and straightforward."
        
        return prompt
    
    def update_weights(self, updates: Dict[str, Any]) -> bool:
        """
        Update prompt shaping weights in shared intelligence.
        
        Args:
            updates (Dict[str, Any]): Weight updates to apply
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        if not INTEL_API_AVAILABLE:
            logger.warning("Intel API not available - cannot update weights")
            return False
        
        try:
            success = update_prompt_weights(updates)
            if success:
                self.weights = self._load_weights()  # Reload updated weights
                logger.info(f"Updated prompt weights: {updates}")
            return success
        except Exception as e:
            logger.error(f"Failed to update prompt weights: {e}")
            return False
    
    def get_current_weights(self) -> Dict[str, Any]:
        """Get current prompt shaping weights."""
        return self.weights.copy()
    
    def tune_for_feedback(self, feedback_score: float, model_used: str) -> bool:
        """
        Auto-tune weights based on feedback score.
        
        Args:
            feedback_score (float): Score from 0-100
            model_used (str): Model that was used
            
        Returns:
            bool: True if tuning was applied, False otherwise
        """
        if not INTEL_API_AVAILABLE:
            return False
        
        updates = {}
        
        # Adjust based on score
        if feedback_score < 70:
            # Poor performance - increase shaping strength
            current_strength = self.weights.get("shaping_strength", 0.5)
            updates["shaping_strength"] = min(current_strength + 0.1, 1.0)
            
            # Try more formal tone for better results
            if self.weights.get("tone") == "default":
                updates["tone"] = "formal"
        
        elif feedback_score > 90:
            # Excellent performance - record successful model
            updates["model_hint"] = model_used.lower()
        
        if updates:
            return self.update_weights(updates)
        
        return False

# Global instance for CLI usage
_prompt_shaper = None

def get_prompt_shaper() -> PromptShaper:
    """Get the global prompt shaper instance."""
    global _prompt_shaper
    if _prompt_shaper is None:
        _prompt_shaper = PromptShaper()
    return _prompt_shaper

def shape_prompt(base_prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
    """
    Convenience function to shape a prompt using shared intelligence.
    
    Args:
        base_prompt (str): The base prompt to shape
        context (Dict[str, Any], optional): Additional context for shaping
        
    Returns:
        str: The shaped prompt
    """
    shaper = get_prompt_shaper()
    return shaper.shape_prompt(base_prompt, context)

def tune_weights(updates: Dict[str, Any]) -> bool:
    """
    Convenience function to update prompt weights.
    
    Args:
        updates (Dict[str, Any]): Weight updates to apply
        
    Returns:
        bool: True if update was successful, False otherwise
    """
    shaper = get_prompt_shaper()
    return shaper.update_weights(updates)

if __name__ == "__main__":
    # Test the prompt shaper
    shaper = PromptShaper()
    
    test_prompt = "Generate a Python function to calculate fibonacci numbers"
    shaped = shaper.shape_prompt(test_prompt, {"file_type": ".py", "complexity": "medium"})
    
    print("Original prompt:")
    print(test_prompt)
    print("\nShaped prompt:")
    print(shaped)
    print(f"\nCurrent weights: {shaper.get_current_weights()}")
