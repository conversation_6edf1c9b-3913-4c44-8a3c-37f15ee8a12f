import typer
import async<PERSON>
import os
import json
from pathlib import Path
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

# Import file discovery utilities
from codecrusher.file_discovery import (
    parse_extensions,
    gather_source_files,
    discover_files_recursive,
    scan_files_for_tags,
    filter_files_with_tags,
    print_discovery_summary,
    print_tag_summary,
    detect_injection_tags
)

# Import batch processing utilities
from codecrusher.batch_processor import (
    BatchProcessor,
    BatchCache,
    ParallelInjectionProcessor,
    show_batch_confirmation,
    print_batch_results,
    print_cache_stats
)

# Import tag manager
from codecrusher.tag_manager import (
    generate_internal_tags,
    parse_user_tags,
    combine_tags,
    format_tags_for_comment,
    apply_batch_tags
)

# Import file utils
from codecrusher.file_utils import (
    get_comment_syntax,
    is_code_file,
    format_annotation
)

# Create the Typer app
app = typer.Typer()

# Initialize rich console
console = Console()


def print_parallel_injection_summary(batch_results: dict) -> None:
    """
    Print summary of parallel injection results.

    Args:
        batch_results: Results from parallel injection processing
    """
    console.print("\n" + "="*60)
    console.print(Panel(
        f"[bold green]✅ PARALLEL INJECTION COMPLETED[/bold green]\n\n"
        f"[cyan]Total files processed:[/cyan] {batch_results['total_files']}\n"
        f"[cyan]Successfully injected:[/cyan] {batch_results['successful']}\n"
        f"[cyan]Failed:[/cyan] {batch_results['failed']}\n"
        f"[cyan]Cache hits:[/cyan] {batch_results['cached_hits']}\n"
        f"[cyan]Success rate:[/cyan] {(batch_results['successful'] / batch_results['total_files'] * 100):.1f}%",
        title="Parallel Injection Results",
        border_style="green"
    ))

    # Show successful injections
    if batch_results['results']:
        console.print("\n[bold green]✅ Successfully injected files:[/bold green]")
        for file_path, result in batch_results['results'].items():
            cached_indicator = " [dim](cached)[/dim]" if result.get('cached') else ""
            console.print(f"  [green]✓[/green] {file_path}{cached_indicator}")
            if result.get('result'):
                res = result['result']
                console.print(f"    [dim]Modified {res.get('modified_lines', 0)} lines, "
                            f"used {res.get('tokens_used', 0)} tokens[/dim]")

    # Show skipped files (files without injection tags are already filtered out)

    # Show failed injections
    if batch_results['errors']:
        console.print("\n[bold red]❌ Failed injections:[/bold red]")
        for file_path, error in batch_results['errors'].items():
            console.print(f"  [red]✗[/red] {file_path}")
            console.print(f"    [dim]{error}[/dim]")


# Batch injection item structure
class BatchItem:
    def __init__(self, input_file: str, prompt: str, tags: Optional[List[str]] = None):
        self.input_file = input_file
        self.prompt = prompt
        self.tags = tags or []

@app.command("run")
def run_inject(
    input: str = typer.Option(..., "--input", "-i", help="Path to the input file or directory"),
    prompt: str = typer.Option(..., "--prompt", "-t", help="The prompt to use for injection"),
    provider: str = typer.Option("groq", "--provider", help="AI provider to use"),
    model: str = typer.Option("llama3", "--model", "-m", help="Model to use"),
    cache: bool = typer.Option(False, "--cache", help="Use cached results if available"),
    verbose: bool = typer.Option(False, "--verbose", help="Enable verbose output"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Custom tags to add (space-separated)"),
    confidence: str = typer.Option("medium", "--confidence", help="Confidence level (high, medium, low)"),
    annotate_output: bool = typer.Option(False, "--annotate-output", help="Annotate output with tags"),
    no_telemetry: bool = typer.Option(False, "--no-telemetry", help="Disable telemetry logging"),
    recursive: bool = typer.Option(False, "--recursive", help="Scan directories recursively"),
    ext: Optional[str] = typer.Option(None, "--ext", help="File extensions to include (comma-separated, e.g., py,js,java)"),
):
    """
    Inject AI-generated code based on a prompt.

    This command can process a single file or scan directories for files with injection tags.
    When processing directories, use --recursive to scan subdirectories and --ext to filter
    file types.

    Use the --annotate-output flag to add a comment block at the top of the output
    that includes all tags. The comment syntax is automatically adjusted based on
    the file extension.

    Examples:
        # Single file
        codecrusher inject run -i file.py -t "Add error handling"

        # Directory with recursive scanning
        codecrusher inject run -i ./src -t "Add error handling" --recursive --ext py,js

        # With tags and annotation
        codecrusher inject run -i file.py -t "Add error handling" --tag bugfix performance --annotate-output
    """
    # Parse extensions
    extensions = parse_extensions(ext)

    # Parse user-provided tags
    user_tags = parse_user_tags(tag)

    # Generate internal tags
    internal_tags = generate_internal_tags(
        model=model,
        provider=provider,
        cached=cache,
        fallback=False,  # Will be set to True if fallback is used
        confidence=confidence,
        replay=False
    )

    # Combine tags
    all_tags = combine_tags(internal_tags, user_tags)

    # Format tags for display
    tags_display = ", ".join(all_tags) if all_tags else "None"

    console.print(Panel(
        f"[bold]CodeCrusher Injection[/bold]\n\n"
        f"[cyan]Input:[/cyan] {input}\n"
        f"[cyan]Recursive:[/cyan] {'Enabled' if recursive else 'Disabled'}\n"
        f"[cyan]Extensions:[/cyan] {', '.join(extensions)}\n"
        f"[cyan]Prompt:[/cyan] {prompt}\n"
        f"[cyan]Provider:[/cyan] {provider}\n"
        f"[cyan]Model:[/cyan] {model}\n"
        f"[cyan]Cache:[/cyan] {'Enabled' if cache else 'Disabled'}\n"
        f"[cyan]Verbose:[/cyan] {'Enabled' if verbose else 'Disabled'}\n"
        f"[cyan]Tags:[/cyan] {tags_display}\n"
        f"[cyan]Annotate Output:[/cyan] {'Enabled' if annotate_output else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))

    try:
        # Step 1: Gather source files with tag filtering
        console.print("\n[bold blue]Step 1: Gathering source files and scanning for injection tags...[/bold blue]")
        source_files = gather_source_files(input, recursive, extensions, filter_tags=True)

        if not source_files:
            console.print("[yellow]No files matched the specified criteria or contained injection tags.[/yellow]")
            return False

        # Convert to Path objects and get tags for each file
        files_with_tags = {}
        for file_path in source_files:
            path_obj = Path(file_path)
            tags = detect_injection_tags(path_obj)
            if tags:  # Should always be true since we filtered for tags
                files_with_tags[path_obj] = tags
                if verbose:
                    console.print(f"  [green]Tags found in {file_path}:[/green] {', '.join(tags)}")

        console.print(f"[cyan]Files ready for processing:[/cyan] {len(files_with_tags)}")

        # Print pre-scan summary
        console.print(f"[cyan]Files with injection tags:[/cyan] {len(files_with_tags)}")

        if not files_with_tags:
            console.print("[yellow]No files contain injection tags. Nothing to process.[/yellow]")
            console.print("[dim]Add injection tags like '# AI_INJECT: tag_name' to your source files.[/dim]")
            return False

        # Step 3: User confirmation for >5 files
        if len(files_with_tags) > 5:
            console.print(f"\n[yellow]⚠️  Proceed with injecting into {len(files_with_tags)} files? [y/N][/yellow]")
            response = input().strip().lower()
            if response not in ['y', 'yes']:
                console.print("[yellow]Operation cancelled by user.[/yellow]")
                return False

        # Step 4: Process files
        if recursive:
            # Use parallel processing for recursive mode
            console.print(f"\n[bold blue]Step 4: Processing {len(files_with_tags)} files in parallel...[/bold blue]")

            # Create parallel processor with max 4 threads
            parallel_processor = ParallelInjectionProcessor(max_workers=4, use_cache=cache)

            # Process files in parallel
            batch_results = parallel_processor.process_parallel_injection(
                files_with_tags=files_with_tags,
                prompt=prompt,
                model=model,
                provider=provider,
                confidence=confidence,
                user_tags=parse_user_tags(tag),
                verbose=verbose
            )

            # Print detailed results
            print_parallel_injection_summary(batch_results)

        else:
            # Use sequential processing for single file mode
            console.print(f"\n[bold blue]Step 4: Processing {len(files_with_tags)} files sequentially...[/bold blue]")

            for file_path, injection_tags in files_with_tags.items():
                console.print(f"\n[bold]Processing:[/bold] {file_path}")
                console.print(f"[cyan]Injection tags:[/cyan] {', '.join(injection_tags)}")

                # In a real implementation, this would call the AI engine
                console.print("[dim]  → AI processing would happen here[/dim]")

    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] {str(e)}")
        raise typer.Exit(code=1)

    console.print(f"\n[bold green]✅ Processing completed for {len(files_with_tags)} files.[/bold green]")
    return True

@app.command("batch")
def run_batch_inject(
    batch_file: str = typer.Option(..., "--batch-file", "-b", help="Path to the batch file (JSON)"),
    provider: str = typer.Option("groq", "--provider", help="AI provider to use"),
    model: str = typer.Option("llama3", "--model", "-m", help="Model to use"),
    cache: bool = typer.Option(False, "--cache", help="Use cached results if available"),
    verbose: bool = typer.Option(False, "--verbose", help="Enable verbose output"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Global tags to add to all items (space-separated)"),
    confidence: str = typer.Option("medium", "--confidence", help="Confidence level (high, medium, low)"),
    annotate_output: bool = typer.Option(False, "--annotate-output", help="Annotate output with tags"),
):
    """
    Run a batch of injections from a JSON file.

    The batch file should be a JSON file with the following structure:

    ```json
    [
        {
            "input_file": "file1.py",
            "prompt": "Add error handling",
            "tags": ["bugfix", "error-handling"]
        },
        {
            "input_file": "file2.py",
            "prompt": "Optimize performance",
            "tags": ["performance"]
        }
    ]
    ```

    Global tags specified with --tag will be applied to all items in the batch.

    Example:
        codecrusher inject batch -b batch.json --tag global-tag
    """
    # Parse global tags
    global_tags = parse_user_tags(tag)

    # Format global tags for display
    global_tags_display = ", ".join(global_tags) if global_tags else "None"

    console.print(Panel(
        f"[bold]CodeCrusher Batch Injection[/bold]\n\n"
        f"[cyan]Batch file:[/cyan] {batch_file}\n"
        f"[cyan]Provider:[/cyan] {provider}\n"
        f"[cyan]Model:[/cyan] {model}\n"
        f"[cyan]Cache:[/cyan] {'Enabled' if cache else 'Disabled'}\n"
        f"[cyan]Verbose:[/cyan] {'Enabled' if verbose else 'Disabled'}\n"
        f"[cyan]Global Tags:[/cyan] {global_tags_display}\n"
        f"[cyan]Annotate Output:[/cyan] {'Enabled' if annotate_output else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))

    # Load the batch file
    try:
        with open(batch_file, "r", encoding="utf-8") as f:
            batch_data = json.load(f)
    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] Failed to load batch file: {str(e)}")
        raise typer.Exit(code=1)

    # Validate the batch data
    if not isinstance(batch_data, list):
        console.print(f"[bold red]❌ Error:[/bold red] Batch file must contain a JSON array")
        raise typer.Exit(code=1)

    # Process each item in the batch
    for i, item_data in enumerate(batch_data):
        # Extract item data
        try:
            input_file = item_data["input_file"]
            prompt = item_data["prompt"]
            item_tags = item_data.get("tags", [])

            # Convert item_tags to list if it's a string
            if isinstance(item_tags, str):
                item_tags = parse_user_tags(item_tags)

            # Create a BatchItem
            item = BatchItem(input_file, prompt, item_tags)
        except KeyError as e:
            console.print(f"[bold red]❌ Error:[/bold red] Missing required field in batch item {i+1}: {str(e)}")
            continue

        # Apply global tags to item tags
        combined_tags = apply_batch_tags(item.tags, global_tags)

        # Generate internal tags
        internal_tags = generate_internal_tags(
            model=model,
            provider=provider,
            cached=cache,
            fallback=False,
            confidence=confidence,
            replay=False
        )

        # Combine all tags
        all_tags = combine_tags(internal_tags, combined_tags)

        # Format tags for display
        tags_display = ", ".join(all_tags) if all_tags else "None"

        console.print(f"\n[bold]Processing item {i+1}/{len(batch_data)}:[/bold]")
        console.print(f"[cyan]Input file:[/cyan] {item.input_file}")
        console.print(f"[cyan]Prompt:[/cyan] {item.prompt}")
        console.print(f"[cyan]Tags:[/cyan] {tags_display}")

        # In a real implementation, we would pass the tags to the AI engine
        # and include them in the cache entry

        # Generate the output (placeholder for now)
        output = f"def hello_world_{i}():\n    print('Hello, World {i}!')\n\ndef add_{i}(a, b):\n    return a + b"

        # Add annotation if enabled
        if annotate_output:
            # Determine if the output is code or plain text
            is_code = True  # Assume code for now

            # Format the annotation
            annotation = format_annotation(all_tags, item.input_file, is_code)

            # Add the annotation to the output
            output = annotation + output

        # Display the output
        console.print(f"\n[bold blue]Generated Output for {item.input_file}:[/bold blue]")
        console.print(output)

    console.print("\n[bold green]Batch processing completed[/bold green]")
    return True


@app.command("recursive")
async def run_recursive_inject(
    source: str = typer.Option(..., "--source", "-s", help="Source directory to scan recursively"),
    prompt: str = typer.Option(..., "--prompt", "-t", help="The prompt to use for injection"),
    ext: Optional[str] = typer.Option(None, "--ext", help="File extensions to include (comma-separated, e.g., py,js,java)"),
    provider: str = typer.Option("groq", "--provider", help="AI provider to use"),
    model: str = typer.Option("llama3", "--model", "-m", help="Model to use"),
    cache: bool = typer.Option(False, "--cache", help="Use cached results if available"),
    verbose: bool = typer.Option(False, "--verbose", help="Enable verbose output"),
    tag: Optional[str] = typer.Option(None, "--tag", help="Custom tags to add (space-separated)"),
    confidence: str = typer.Option("medium", "--confidence", help="Confidence level (high, medium, low)"),
    annotate_output: bool = typer.Option(False, "--annotate-output", help="Annotate output with tags"),
    no_telemetry: bool = typer.Option(False, "--no-telemetry", help="Disable telemetry logging"),
    dry_run: bool = typer.Option(True, "--dry-run/--no-dry-run", help="Only discover and list files, don't process them"),
    max_workers: int = typer.Option(3, "--max-workers", help="Maximum number of concurrent AI requests"),
    skip_confirmation: bool = typer.Option(False, "--skip-confirmation", help="Skip user confirmation prompt"),
):
    """
    Recursively scan a directory for source files and inject AI-generated code.

    This command recursively scans the specified directory for source files
    matching the given extensions, detects injection tags, and processes
    the files with AI-generated code.

    By default, this command runs in dry-run mode and only lists the files
    that would be processed. Use --no-dry-run to actually perform injections.

    Examples:
        # Discover Python files with injection tags
        codecrusher inject recursive -s ./src -t "Add error handling"

        # Discover multiple file types
        codecrusher inject recursive -s ./src -t "Add error handling" --ext py,js,java

        # Actually perform injections (not just discovery)
        codecrusher inject recursive -s ./src -t "Add error handling" --no-dry-run
    """
    # Parse extensions
    extensions = parse_extensions(ext)

    console.print(Panel(
        f"[bold]CodeCrusher Recursive Injection[/bold]\n\n"
        f"[cyan]Source directory:[/cyan] {source}\n"
        f"[cyan]Extensions:[/cyan] {', '.join(extensions)}\n"
        f"[cyan]Prompt:[/cyan] {prompt}\n"
        f"[cyan]Provider:[/cyan] {provider}\n"
        f"[cyan]Model:[/cyan] {model}\n"
        f"[cyan]Cache:[/cyan] {'Enabled' if cache else 'Disabled'}\n"
        f"[cyan]Verbose:[/cyan] {'Enabled' if verbose else 'Disabled'}\n"
        f"[cyan]Dry Run:[/cyan] {'Enabled' if dry_run else 'Disabled'}",
        title="Configuration",
        border_style="blue"
    ))

    try:
        # Step 1: Discover files recursively
        console.print("\n[bold blue]Step 1: Discovering files...[/bold blue]")
        discovered_files = discover_files_recursive(source, extensions, verbose)

        # Print discovery summary
        print_discovery_summary(discovered_files, extensions, source)

        if not discovered_files:
            console.print("[yellow]No source files found matching the specified extensions.[/yellow]")
            return False

        # Step 2: Scan files for injection tags
        console.print("\n[bold blue]Step 2: Scanning for injection tags...[/bold blue]")
        file_tags = scan_files_for_tags(discovered_files, verbose)
        files_with_tags = filter_files_with_tags(file_tags)

        # Print tag summary
        print_tag_summary(files_with_tags, len(discovered_files))

        if not files_with_tags:
            console.print("\n[yellow]No files contain injection tags. Nothing to process.[/yellow]")
            console.print("[dim]Add injection tags like '# AI_INJECT: tag_name' to your source files.[/dim]")
            return False

        # If this is a dry run, stop here
        if dry_run:
            console.print(f"\n[bold green]Dry run completed.[/bold green]")
            console.print(f"[cyan]Found {len(files_with_tags)} files with injection tags.[/cyan]")
            console.print(f"[dim]Use --no-dry-run to actually perform injections.[/dim]")
            return True

        # Step 3: User confirmation
        console.print("\n[bold blue]Step 3: User confirmation...[/bold blue]")
        if not skip_confirmation:
            if not show_batch_confirmation(files_with_tags, prompt, model, provider, cache):
                console.print("[yellow]Batch injection cancelled by user.[/yellow]")
                return False
        else:
            console.print("[yellow]Skipping user confirmation (--skip-confirmation enabled)[/yellow]")

        # Step 4: Process files with batch processor
        console.print("\n[bold blue]Step 4: Processing files with AI injection...[/bold blue]")

        # Create batch processor
        batch_processor = BatchProcessor(max_workers=max_workers, use_cache=cache)

        # Show cache stats if enabled
        if cache:
            print_cache_stats(batch_processor.cache)

        # Process batch asynchronously
        try:
            batch_results = await batch_processor.process_batch(
                files_with_tags=files_with_tags,
                prompt=prompt,
                model=model,
                provider=provider,
                confidence=confidence,
                user_tags=parse_user_tags(tag),
                verbose=verbose
            )

            # Print results
            print_batch_results(batch_results)

            # Show updated cache stats
            if cache:
                console.print()
                print_cache_stats(batch_processor.cache)

            return batch_results['successful'] > 0

        except Exception as e:
            console.print(f"[bold red]❌ Batch processing failed:[/bold red] {str(e)}")
            return False

    except Exception as e:
        console.print(f"[bold red]❌ Error:[/bold red] {str(e)}")
        raise typer.Exit(code=1)
