import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Target,
  BarChart3,
  Cpu,
  Activity,
  Zap
} from 'lucide-react';

interface FeedbackEntry {
  file: string;
  timestamp: string;
  prompt: string;
  model: string;
  output_score: number;
  rating: number;
  tone: string;
  fallback_used: boolean;
}

interface IntelStatsData {
  avgScore: number;
  topModel: string;
  fallbackRate: number;
  totalEntries: number;
  recentActivity: number;
  modelPerformance: Record<string, {
    count: number;
    avgScore: number;
    avgRating: number;
  }>;
  toneEffectiveness: Record<string, number>;
  learningTrend: 'improving' | 'stable' | 'declining';
}

export function IntelStats() {
  const [stats, setStats] = useState<IntelStatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchIntelStats();
    
    // Refresh stats every 30 seconds
    const interval = setInterval(fetchIntelStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchIntelStats = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch feedback data from the API
      const response = await fetch('/api/intel/feedback');
      
      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const feedback: FeedbackEntry[] = data.feedback_entries || [];

      // Compute statistics from feedback data
      const computedStats = computeStats(feedback);
      setStats(computedStats);

    } catch (err) {
      console.error('Failed to fetch intelligence stats:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      
      // Use mock data as fallback
      setStats(getMockStats());
    } finally {
      setLoading(false);
    }
  };

  const computeStats = (feedback: FeedbackEntry[]): IntelStatsData => {
    if (feedback.length === 0) {
      return getMockStats();
    }

    // Get last 20 entries for recent analysis
    const recentEntries = feedback.slice(-20);
    
    // Calculate average score
    const avgScore = recentEntries.reduce((sum, entry) => sum + (entry.output_score || 0), 0) / recentEntries.length;
    
    // Calculate fallback rate
    const fallbackCount = recentEntries.filter(entry => entry.fallback_used).length;
    const fallbackRate = fallbackCount / recentEntries.length;
    
    // Find top performing model
    const modelCounts: Record<string, { count: number; totalScore: number; totalRating: number }> = {};
    
    recentEntries.forEach(entry => {
      const model = entry.model || 'unknown';
      if (!modelCounts[model]) {
        modelCounts[model] = { count: 0, totalScore: 0, totalRating: 0 };
      }
      modelCounts[model].count++;
      modelCounts[model].totalScore += entry.output_score || 0;
      modelCounts[model].totalRating += entry.rating || 0;
    });
    
    // Find top model by average score
    let topModel = 'unknown';
    let topScore = 0;
    
    const modelPerformance: Record<string, { count: number; avgScore: number; avgRating: number }> = {};
    
    Object.entries(modelCounts).forEach(([model, data]) => {
      const avgModelScore = data.totalScore / data.count;
      const avgModelRating = data.totalRating / data.count;
      
      modelPerformance[model] = {
        count: data.count,
        avgScore: avgModelScore,
        avgRating: avgModelRating
      };
      
      if (avgModelScore > topScore) {
        topScore = avgModelScore;
        topModel = model;
      }
    });
    
    // Calculate tone effectiveness
    const toneCounts: Record<string, { count: number; totalRating: number }> = {};
    
    recentEntries.forEach(entry => {
      const tone = entry.tone || 'neutral';
      if (!toneCounts[tone]) {
        toneCounts[tone] = { count: 0, totalRating: 0 };
      }
      toneCounts[tone].count++;
      toneCounts[tone].totalRating += entry.rating || 0;
    });
    
    const toneEffectiveness: Record<string, number> = {};
    Object.entries(toneCounts).forEach(([tone, data]) => {
      toneEffectiveness[tone] = data.totalRating / data.count;
    });
    
    // Determine learning trend (simplified)
    const firstHalf = recentEntries.slice(0, Math.floor(recentEntries.length / 2));
    const secondHalf = recentEntries.slice(Math.floor(recentEntries.length / 2));
    
    const firstHalfAvg = firstHalf.reduce((sum, entry) => sum + (entry.output_score || 0), 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, entry) => sum + (entry.output_score || 0), 0) / secondHalf.length;
    
    let learningTrend: 'improving' | 'stable' | 'declining' = 'stable';
    if (secondHalfAvg > firstHalfAvg + 5) {
      learningTrend = 'improving';
    } else if (secondHalfAvg < firstHalfAvg - 5) {
      learningTrend = 'declining';
    }

    return {
      avgScore,
      topModel,
      fallbackRate,
      totalEntries: feedback.length,
      recentActivity: recentEntries.length,
      modelPerformance,
      toneEffectiveness,
      learningTrend
    };
  };

  const getMockStats = (): IntelStatsData => ({
    avgScore: 87.3,
    topModel: 'GPT-4',
    fallbackRate: 0.12,
    totalEntries: 9,
    recentActivity: 9,
    modelPerformance: {
      'GPT-4': { count: 2, avgScore: 95.0, avgRating: 5.0 },
      'Mixtral': { count: 5, avgScore: 84.2, avgRating: 3.8 },
      'TestAPI': { count: 1, avgScore: 95.0, avgRating: 5.0 },
      'TestModel': { count: 1, avgScore: 88.0, avgRating: 4.0 }
    },
    toneEffectiveness: {
      'formal': 4.5,
      'assertive': 4.2,
      'friendly': 3.8,
      'neutral': 3.5
    },
    learningTrend: 'improving'
  });

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-blue-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'improving':
        return 'text-green-600';
      case 'declining':
        return 'text-red-600';
      default:
        return 'text-blue-600';
    }
  };

  if (loading) {
    return (
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            📊 Learning Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-24">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            <span className="ml-3 text-gray-600">Loading intelligence data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !stats) {
    return (
      <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            📊 Learning Insights
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-600">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>Failed to load intelligence data</p>
            {error && <p className="text-sm text-gray-500 mt-1">{error}</p>}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-600" />
          📊 Learning Insights
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <BarChart3 className="h-4 w-4 text-blue-600 mr-1" />
              <span className="text-sm font-medium text-blue-800">Avg Score</span>
            </div>
            <div className="text-2xl font-bold text-blue-900">{stats.avgScore.toFixed(1)}</div>
            <div className="text-xs text-blue-600">Last 20 injections</div>
          </div>
          
          <div className="text-center p-3 bg-gradient-to-br from-green-50 to-green-100 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Target className="h-4 w-4 text-green-600 mr-1" />
              <span className="text-sm font-medium text-green-800">Top Model</span>
            </div>
            <div className="text-lg font-bold text-green-900">{stats.topModel}</div>
            <div className="text-xs text-green-600">Best performer</div>
          </div>
          
          <div className="text-center p-3 bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg">
            <div className="flex items-center justify-center mb-1">
              <Zap className="h-4 w-4 text-orange-600 mr-1" />
              <span className="text-sm font-medium text-orange-800">Fallback Rate</span>
            </div>
            <div className="text-2xl font-bold text-orange-900">{(stats.fallbackRate * 100).toFixed(1)}%</div>
            <div className="text-xs text-orange-600">Reliability metric</div>
          </div>
        </div>

        {/* Learning Trend */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            {getTrendIcon(stats.learningTrend)}
            <span className="font-medium">Learning Trend</span>
          </div>
          <div className={`font-bold capitalize ${getTrendColor(stats.learningTrend)}`}>
            {stats.learningTrend}
          </div>
        </div>

        {/* Activity Summary */}
        <div className="text-sm text-gray-600 space-y-1">
          <div className="flex justify-between">
            <span>📈 Total Entries:</span>
            <span className="font-medium">{stats.totalEntries}</span>
          </div>
          <div className="flex justify-between">
            <span>⚡ Recent Activity:</span>
            <span className="font-medium">{stats.recentActivity} injections</span>
          </div>
          <div className="flex justify-between">
            <span>🤖 Active Models:</span>
            <span className="font-medium">{Object.keys(stats.modelPerformance).length}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
