#!/usr/bin/env python3
"""
CodeCrusher CLI Main Entry Point with Authentication
Modern CLI interface using Typer with authentication support
"""

import typer
import logging
from rich.console import Console
from rich.panel import Panel

# Import CLI modules
from cli.inject import inject_app
from cli.auth import auth_app

# Try to import optional modules
try:
    from cli.scan import scan_app
except ImportError:
    scan_app = None

try:
    from cli.optimize import optimize_app
except ImportError:
    optimize_app = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

console = Console()

# Create main CLI app
app = typer.Typer(
    name="codecrusher",
    help="🚀 CodeCrusher - AI-powered code injection and optimization",
    add_completion=False,
    rich_markup_mode="rich"
)

# Add authentication commands
app.add_typer(auth_app, name="auth", help="🔐 Authentication commands")

# Add injection commands
app.add_typer(inject_app, name="inject", help="🔌 Code injection commands")

# Add scan commands if available
if scan_app:
    app.add_typer(scan_app, name="scan", help="🔍 Code scanning commands")

# Add optimization commands if available
if optimize_app:
    app.add_typer(optimize_app, name="optimize", help="⚡ Code optimization commands")

@app.command()
def version():
    """📋 Show CodeCrusher version information."""
    console.print(Panel(
        "[bold]CodeCrusher v2.0.0[/bold]\n"
        "AI-powered code injection and optimization tool\n"
        "🔗 https://github.com/codecrusher/codecrusher",
        title="[bold cyan]Version Info[/bold cyan]",
        border_style="cyan"
    ))

@app.command()
def status():
    """📊 Show CodeCrusher system status."""
    from cli.auth_client import get_auth_client

    console.print(Panel(
        "[bold]CodeCrusher System Status[/bold]",
        title="[bold blue]📊 Status[/bold blue]",
        border_style="blue"
    ))

    # Check authentication status
    auth_client = get_auth_client()
    if auth_client.is_authenticated():
        user_email = auth_client.user_info.get('email', 'Unknown')
        console.print(f"[green]🔐 Authentication:[/green] [bold]Logged in as {user_email}[/bold]")
    else:
        console.print(f"[yellow]🔐 Authentication:[/yellow] [bold]Not logged in[/bold]")

    # Check API connectivity
    try:
        import requests
        response = requests.get(f"{auth_client.api_base}/health", timeout=5)
        if response.status_code == 200:
            console.print(f"[green]🌐 API Connection:[/green] [bold]Connected to {auth_client.api_base}[/bold]")
        else:
            console.print(f"[yellow]🌐 API Connection:[/yellow] [bold]API responding with status {response.status_code}[/bold]")
    except Exception as e:
        console.print(f"[red]🌐 API Connection:[/red] [bold]Failed to connect to {auth_client.api_base}[/bold]")

    # Show available commands
    console.print("\n[cyan]📋 Available Commands:[/cyan]")
    console.print("  [bold]auth[/bold]     - Authentication (login, logout, status)")
    console.print("  [bold]inject[/bold]   - Code injection with AI")
    console.print("  [bold]scan[/bold]     - Code scanning and analysis")
    console.print("  [bold]optimize[/bold] - Code optimization")
    console.print("  [bold]version[/bold]  - Show version information")
    console.print("  [bold]status[/bold]   - Show system status")

@app.command()
def login(
    email: str = typer.Option(None, "--email", "-e", help="📧 Email address"),
    password: str = typer.Option(None, "--password", "-p", help="🔑 Password"),
    api_base: str = typer.Option("http://localhost:8001", "--api-base", "-a", help="🌐 API base URL")
):
    """🔐 Quick login command (alias for 'auth login')."""
    from cli.auth_client import get_auth_client

    console.print(Panel(
        "[bold]CodeCrusher Quick Login[/bold]",
        title="[bold cyan]🔐 Login[/bold cyan]",
        border_style="cyan"
    ))

    client = get_auth_client()
    client.api_base = api_base

    success = client.login(email, password)
    if success:
        console.print("[green]✅ Login successful! Use 'codecrusher status' to see your session info.[/green]")
    else:
        console.print("[red]❌ Login failed. Use 'codecrusher auth login' for more detailed login.[/red]")
        raise typer.Exit(code=1)

@app.command()
def logout():
    """🚪 Quick logout command (alias for 'auth logout')."""
    from cli.auth_client import logout

    console.print(Panel(
        "[bold]CodeCrusher Quick Logout[/bold]",
        title="[bold yellow]🚪 Logout[/bold yellow]",
        border_style="yellow"
    ))

    success = logout()
    if success:
        console.print("[green]✅ Logout successful![/green]")
    else:
        console.print("[red]❌ Logout failed.[/red]")
        raise typer.Exit(code=1)

@app.command()
def whoami():
    """👤 Show current user information."""
    from cli.auth_client import get_auth_client

    client = get_auth_client()
    if client.is_authenticated():
        user_info = client.user_info
        console.print(Panel(
            f"[bold]Email:[/bold] {user_info.get('email', 'Unknown')}\n"
            f"[bold]Role:[/bold] {user_info.get('role', 'user')}\n"
            f"[bold]Created:[/bold] {user_info.get('created_at', 'Unknown')}\n"
            f"[bold]API:[/bold] {client.api_base}",
            title="[bold green]👤 Current User[/bold green]",
            border_style="green"
        ))
    else:
        console.print(Panel(
            "[bold red]Not logged in[/bold red]\n"
            "Use 'codecrusher login' to authenticate",
            title="[bold red]❌ No User Session[/bold red]",
            border_style="red"
        ))

@app.callback()
def main(
    verbose: bool = typer.Option(False, "--verbose", "-v", help="🔍 Enable verbose logging"),
    api_base: str = typer.Option("http://localhost:8001", "--api-base", "-a", help="🌐 API base URL")
):
    """
    🚀 CodeCrusher - AI-powered code injection and optimization tool.

    CodeCrusher helps you inject AI-generated code into your projects with
    intelligent prompt shaping, user authentication, and real-time feedback.

    Examples:
      codecrusher login                    # Login to your account
      codecrusher inject run --help        # See injection options
      codecrusher auth status              # Check authentication
      codecrusher status                   # System status overview
    """
    if verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("Verbose logging enabled")

    # Set global API base for auth client
    from cli.auth_client import get_auth_client
    client = get_auth_client()
    client.api_base = api_base

if __name__ == "__main__":
    try:
        app()
    except KeyboardInterrupt:
        console.print("\n[yellow]⚠️ Operation cancelled by user[/yellow]")
        raise typer.Exit(code=1)
    except Exception as e:
        console.print(f"[red]❌ Unexpected error: {e}[/red]")
        logger.exception("Unexpected error in CLI")
        raise typer.Exit(code=1)
