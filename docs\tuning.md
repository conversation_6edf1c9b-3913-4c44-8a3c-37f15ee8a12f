# Tuning Guide

Learn how to configure and optimize CodeCrusher's intelligence system for your specific needs.

## 🎯 Overview

CodeCrusher's tuning system allows you to customize how the AI generates code, handles fallbacks, and learns from feedback. The system automatically adapts based on your usage patterns, but you can also manually configure parameters for optimal results.

---

## 🧠 Intelligence Parameters

### Core Parameters

#### 1. **Prompt Style**
Controls how detailed and comprehensive the AI prompts are.

```bash
# Set prompt style
codecrusher tune --set prompt_style=comprehensive

# Available options:
# - basic: Simple, direct prompts
# - detailed: More context and requirements
# - comprehensive: Extensive detail and examples
```

#### 2. **Verbosity Level**
Determines how much code and documentation the AI generates.

```bash
# Set verbosity
codecrusher tune --set verbosity=high

# Available options:
# - low: Minimal code, basic functionality
# - medium: Balanced approach with some documentation
# - high: Comprehensive code with extensive documentation
```

#### 3. **Error Handling Approach**
Configures how extensively the AI adds error handling.

```bash
# Set error handling level
codecrusher tune --set error_handling=extensive

# Available options:
# - basic: Simple try/catch blocks
# - comprehensive: Detailed error handling with logging
# - extensive: Full error recovery and validation
```

#### 4. **Fallback Sensitivity**
Controls when the system escalates to better models.

```bash
# Set fallback sensitivity (0.0 to 1.0)
codecrusher tune --set fallback_sensitivity=0.5

# Lower values = more aggressive escalation
# Higher values = more conservative escalation
```

---

## ⚙️ Tuning Commands

### View Current Configuration

```bash
# Show all current settings
codecrusher tune --show

# Show specific parameter
codecrusher tune --show prompt_style

# Show with explanations
codecrusher tune --show --verbose
```

### Modify Settings

```bash
# Set individual parameters
codecrusher tune --set prompt_style=comprehensive
codecrusher tune --set verbosity=high
codecrusher tune --set error_handling=extensive
codecrusher tune --set fallback_sensitivity=0.7

# Set multiple parameters at once
codecrusher tune --set prompt_style=detailed verbosity=medium error_handling=comprehensive

# Reset to defaults
codecrusher tune --reset

# Reset specific parameter
codecrusher tune --reset prompt_style
```

### Preset Configurations

```bash
# Apply preset configurations
codecrusher tune --preset beginner
codecrusher tune --preset professional
codecrusher tune --preset enterprise

# Custom presets
codecrusher tune --preset rapid_prototyping
codecrusher tune --preset production_ready
```

---

## 🎛️ Configuration Presets

### Beginner Preset
Optimized for new users learning CodeCrusher.

```yaml
prompt_style: basic
verbosity: medium
error_handling: basic
fallback_sensitivity: 0.8
```

```bash
codecrusher tune --preset beginner
```

### Professional Preset
Balanced configuration for experienced developers.

```yaml
prompt_style: detailed
verbosity: medium
error_handling: comprehensive
fallback_sensitivity: 0.6
```

```bash
codecrusher tune --preset professional
```

### Enterprise Preset
Maximum quality for production environments.

```yaml
prompt_style: comprehensive
verbosity: high
error_handling: extensive
fallback_sensitivity: 0.4
```

```bash
codecrusher tune --preset enterprise
```

### Rapid Prototyping Preset
Fast iteration with minimal overhead.

```yaml
prompt_style: basic
verbosity: low
error_handling: basic
fallback_sensitivity: 0.9
```

```bash
codecrusher tune --preset rapid_prototyping
```

### Production Ready Preset
Comprehensive code for production deployment.

```yaml
prompt_style: comprehensive
verbosity: high
error_handling: extensive
fallback_sensitivity: 0.3
```

```bash
codecrusher tune --preset production_ready
```

---

## 🔄 Model Preferences

### Configure Model Order

```bash
# Set preferred model order
codecrusher tune --models llama3-8b,mixtral-8x7b,llama3-70b,gpt-4-turbo

# View current model preferences
codecrusher tune --show models

# Reset to default order
codecrusher tune --reset models
```

### Model-Specific Settings

```bash
# Configure settings per model
codecrusher tune --model llama3-8b --set verbosity=low
codecrusher tune --model llama3-70b --set verbosity=high
codecrusher tune --model gpt-4-turbo --set error_handling=extensive
```

---

## 📊 Quality Thresholds

### Configure Quality Scoring

```bash
# Set minimum quality threshold for caching
codecrusher tune --set quality_threshold=85

# Set quality threshold for model escalation
codecrusher tune --set escalation_threshold=70

# Configure retry limits
codecrusher tune --set max_retries=3
```

### Quality Metrics Configuration

```bash
# Configure what constitutes quality improvements
codecrusher tune --quality-metrics logging=10 error_handling=15 validation=10

# View current quality metrics
codecrusher tune --show quality_metrics
```

---

## 🎯 Context-Aware Tuning

### Project-Specific Configuration

Create a `.codecrusher.yaml` file in your project root:

```yaml
# Project-specific CodeCrusher configuration
project_name: "MyProject"
language: "python"

# Tuning parameters
prompt_style: "comprehensive"
verbosity: "high"
error_handling: "extensive"
fallback_sensitivity: 0.5

# Model preferences for this project
model_preferences:
  - llama3-70b
  - gpt-4-turbo
  - mixtral-8x7b

# Quality thresholds
quality_threshold: 90
escalation_threshold: 75

# Custom prompts for this project
custom_prompts:
  error_handling: "Add comprehensive error handling with logging and recovery"
  validation: "Add input validation with detailed error messages"
  testing: "Add unit tests with edge case coverage"
```

### Language-Specific Tuning

```bash
# Configure settings for specific languages
codecrusher tune --language python --set verbosity=high
codecrusher tune --language javascript --set error_handling=comprehensive
codecrusher tune --language java --set prompt_style=detailed
```

---

## 🔧 Advanced Configuration

### Environment-Based Configuration

```bash
# Development environment
export CODECRUSHER_ENV=development
codecrusher tune --preset rapid_prototyping

# Staging environment
export CODECRUSHER_ENV=staging
codecrusher tune --preset professional

# Production environment
export CODECRUSHER_ENV=production
codecrusher tune --preset enterprise
```

### Team Configuration

```bash
# Export team configuration
codecrusher tune --export team_config.yaml

# Import team configuration
codecrusher tune --import team_config.yaml

# Sync with team settings
codecrusher tune --sync-team
```

---

## 📈 Monitoring and Analytics

### View Tuning Analytics

```bash
# Show tuning effectiveness
codecrusher tune --analytics

# Show parameter impact on quality
codecrusher tune --analytics --parameter prompt_style

# Show learning progress
codecrusher tune --analytics --learning
```

### Performance Metrics

```bash
# View performance impact of current settings
codecrusher tune --performance

# Compare different configurations
codecrusher tune --compare beginner professional enterprise
```

---

## 🔄 Automatic Learning Integration

### Learning-Based Tuning

The system automatically adjusts parameters based on your feedback:

```bash
# Enable automatic tuning
codecrusher tune --auto-tune enable

# Disable automatic tuning
codecrusher tune --auto-tune disable

# Configure learning sensitivity
codecrusher tune --learning-rate 0.1

# View learning history
codecrusher tune --learning-history
```

### Feedback Integration

```bash
# Configure how ratings affect tuning
codecrusher tune --feedback-weight 0.8

# Set minimum feedback count for parameter changes
codecrusher tune --min-feedback 5

# View feedback impact on parameters
codecrusher tune --feedback-analytics
```

---

## 🛠️ Troubleshooting Tuning

### Common Issues

#### 1. **Code Too Verbose**
```bash
# Reduce verbosity
codecrusher tune --set verbosity=low

# Use basic prompt style
codecrusher tune --set prompt_style=basic
```

#### 2. **Insufficient Error Handling**
```bash
# Increase error handling
codecrusher tune --set error_handling=extensive

# Lower fallback sensitivity for better models
codecrusher tune --set fallback_sensitivity=0.4
```

#### 3. **Slow Performance**
```bash
# Use faster models
codecrusher tune --models llama3-8b,mixtral-8x7b

# Increase fallback sensitivity
codecrusher tune --set fallback_sensitivity=0.8
```

#### 4. **Inconsistent Quality**
```bash
# Lower quality threshold
codecrusher tune --set quality_threshold=75

# Enable more aggressive escalation
codecrusher tune --set fallback_sensitivity=0.5
```

### Diagnostic Commands

```bash
# Run tuning diagnostics
codecrusher tune --diagnose

# Test current configuration
codecrusher tune --test

# Validate configuration
codecrusher tune --validate
```

---

## 📋 Best Practices

### 1. **Start with Presets**
Begin with a preset that matches your use case, then fine-tune as needed.

### 2. **Monitor Quality Metrics**
Regularly check analytics to understand the impact of your tuning changes.

### 3. **Use Project-Specific Configuration**
Create `.codecrusher.yaml` files for different projects with varying requirements.

### 4. **Leverage Automatic Learning**
Enable auto-tune and provide regular feedback to let the system optimize itself.

### 5. **Team Synchronization**
Use team configuration files to ensure consistent settings across team members.

---

## 🔗 Integration with Other Features

### Dashboard Integration
View and modify tuning parameters through the [Dashboard](dashboard.md).

### CLI Integration
All tuning parameters affect [CLI usage](usage.md) behavior.

### Learning Integration
Tuning works seamlessly with the intelligence learning system.

---

## 🔗 Next Steps

- **[Explore Dashboard](dashboard.md)** - Visual tuning interface
- **[Learn Usage Patterns](usage.md)** - Apply tuning to real workflows
- **[Troubleshooting](troubleshooting.md)** - Solve tuning-related issues

---

**[← Usage Guide](usage.md)** | **[Next: Dashboard Guide →](dashboard.md)**
