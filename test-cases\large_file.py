#!/usr/bin/env python3
"""
Large Python file for testing CodeCrusher performance
This file contains multiple classes and functions to test handling of large files
"""

import os
import sys
import json
import logging
import datetime
from typing import List, Dict, Optional, Union, Any
from dataclasses import dataclass
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class User:
    """User data class."""
    id: int
    name: str
    email: str
    created_at: datetime.datetime
    is_active: bool = True
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class UserManager:
    """Manages user operations."""
    
    def __init__(self, data_file: Optional[str] = None):
        self.data_file = data_file or "users.json"
        self.users: Dict[int, User] = {}
        self.load_users()
    
    def load_users(self) -> None:
        """Load users from file."""
        try:
            if Path(self.data_file).exists():
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    for user_data in data:
                        user = User(**user_data)
                        self.users[user.id] = user
                logger.info(f"Loaded {len(self.users)} users")
        except Exception as e:
            logger.error(f"Failed to load users: {e}")
    
    def save_users(self) -> None:
        """Save users to file."""
        try:
            data = []
            for user in self.users.values():
                user_dict = {
                    'id': user.id,
                    'name': user.name,
                    'email': user.email,
                    'created_at': user.created_at.isoformat(),
                    'is_active': user.is_active,
                    'metadata': user.metadata
                }
                data.append(user_dict)
            
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved {len(self.users)} users")
        except Exception as e:
            logger.error(f"Failed to save users: {e}")
    
    def create_user(self, name: str, email: str) -> User:
        """Create a new user."""
        user_id = max(self.users.keys(), default=0) + 1
        user = User(
            id=user_id,
            name=name,
            email=email,
            created_at=datetime.datetime.now()
        )
        self.users[user_id] = user
        logger.info(f"Created user: {user.name} ({user.email})")
        return user
    
    def get_user(self, user_id: int) -> Optional[User]:
        """Get user by ID."""
        return self.users.get(user_id)
    
    def update_user(self, user_id: int, **kwargs) -> bool:
        """Update user information."""
        user = self.users.get(user_id)
        if not user:
            return False
        
        for key, value in kwargs.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        logger.info(f"Updated user {user_id}")
        return True
    
    def delete_user(self, user_id: int) -> bool:
        """Delete user by ID."""
        if user_id in self.users:
            user = self.users.pop(user_id)
            logger.info(f"Deleted user: {user.name}")
            return True
        return False
    
    def list_users(self, active_only: bool = True) -> List[User]:
        """List all users."""
        users = list(self.users.values())
        if active_only:
            users = [u for u in users if u.is_active]
        return users
    
    # AI_INJECT:search_functionality
    # Add user search functionality here (by name, email, etc.)
    # AI_INJECT:search_functionality:end
    
    # AI_INJECT:bulk_operations
    # Add bulk operations (import/export, batch updates, etc.)
    # AI_INJECT:bulk_operations:end

class ReportGenerator:
    """Generates various reports."""
    
    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
    
    def user_count_report(self) -> Dict[str, int]:
        """Generate user count report."""
        users = self.user_manager.list_users(active_only=False)
        active_count = sum(1 for u in users if u.is_active)
        inactive_count = len(users) - active_count
        
        return {
            'total': len(users),
            'active': active_count,
            'inactive': inactive_count
        }
    
    def user_creation_report(self) -> Dict[str, int]:
        """Generate user creation report by month."""
        users = self.user_manager.list_users(active_only=False)
        monthly_counts = {}
        
        for user in users:
            month_key = user.created_at.strftime('%Y-%m')
            monthly_counts[month_key] = monthly_counts.get(month_key, 0) + 1
        
        return monthly_counts
    
    # AI_INJECT:advanced_reports
    # Add more advanced reporting features here
    # AI_INJECT:advanced_reports:end

class DataValidator:
    """Validates user data."""
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format."""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_name(name: str) -> bool:
        """Validate name format."""
        return len(name.strip()) >= 2 and name.replace(' ', '').isalpha()
    
    # AI_INJECT:validation_rules
    # Add more validation rules here
    # AI_INJECT:validation_rules:end

def main():
    """Main application function."""
    logger.info("Starting User Management System")
    
    # Initialize managers
    user_manager = UserManager("test_users.json")
    report_generator = ReportGenerator(user_manager)
    
    # Create some test users
    test_users = [
        ("John Doe", "<EMAIL>"),
        ("Jane Smith", "<EMAIL>"),
        ("Bob Johnson", "<EMAIL>"),
        ("Alice Brown", "<EMAIL>"),
        ("Charlie Wilson", "<EMAIL>")
    ]
    
    for name, email in test_users:
        if DataValidator.validate_email(email) and DataValidator.validate_name(name):
            user_manager.create_user(name, email)
        else:
            logger.warning(f"Invalid user data: {name}, {email}")
    
    # Generate reports
    count_report = report_generator.user_count_report()
    creation_report = report_generator.user_creation_report()
    
    print("\n=== User Count Report ===")
    for key, value in count_report.items():
        print(f"{key.capitalize()}: {value}")
    
    print("\n=== User Creation Report ===")
    for month, count in creation_report.items():
        print(f"{month}: {count} users")
    
    # Save data
    user_manager.save_users()
    
    # AI_INJECT:main_enhancements
    # Add more main function features here
    # AI_INJECT:main_enhancements:end
    
    logger.info("User Management System completed")

if __name__ == "__main__":
    main()
