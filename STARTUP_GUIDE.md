# 🚀 CodeCrusher Startup Guide

This guide explains how to run CodeCrusher's different components.

## 🏗️ Architecture Overview

CodeCrusher has **three separate components**:

1. **🖥️ CLI Tool** (Python) - Command-line interface
2. **🔧 Backend API** (Python) - FastAPI server with WebSocket
3. **🌐 Frontend Dashboard** (React) - Web-based user interface

## 🎯 Quick Start Options

### Option 1: CLI Only (Simplest)

```bash
# Install CodeCrusher
pip install -e .

# Use CLI directly
codecrusher inject ./src --prompt "Add error handling"
```

### Option 2: Web Dashboard (Full Experience)

```bash
# Terminal 1: Start Backend
python app/backend_main.py

# Terminal 2: Start Frontend
cd frontend
npm install
npm run dev

# Open browser to http://localhost:3000
```

## 📋 Detailed Setup

### 🖥️ CLI Tool Setup

```bash
# 1. Navigate to CodeCrusher directory
cd CodeCruncher

# 2. Verify CLI works
python codecrusher_cli.py --version

# 3. Use CLI directly
python codecrusher_cli.py inject ./example.py --prompt "Add error handling"

# Alternative: Install as package (optional)
pip install -e .
codecrusher --version
```

### 🔧 Backend API Setup

```bash
# 1. Install Python dependencies
pip install -r requirements.txt

# 2. Start backend server
python app/backend_main.py

# 3. Verify backend is running
curl http://localhost:8001/health
```

**Backend will be available at:** `http://localhost:8001`

### 🌐 Frontend Dashboard Setup

```bash
# 1. Navigate to frontend directory
cd frontend

# 2. Install Node.js dependencies
npm install

# 3. Start development server
npm run dev

# 4. Open browser to http://localhost:3000
```

**Frontend will be available at:** `http://localhost:3000`

## 🔧 Configuration

### Backend Configuration
- **Port:** 8001 (configurable in `app/backend_main.py`)
- **WebSocket:** `ws://localhost:8001/ws/logs`
- **Health Check:** `http://localhost:8001/health`

### Frontend Configuration
- **Development Port:** 3000 (Vite configured)
- **Production Port:** 3000 (after `npm run build`)
- **Backend URL:** Configured in `frontend/.env`

### Environment Variables
Create `frontend/.env`:
```bash
VITE_SERVER_URL=http://localhost:8001
VITE_WS_URL=ws://localhost:8001/ws/logs
VITE_DEV_MODE=true
```

## 🧪 Testing the Setup

### Test CLI
```bash
codecrusher inject ./CodeCruncher/example.py --prompt "Add logging"
```

### Test Backend
```bash
curl http://localhost:8001/health
```

### Test Frontend
1. Open `http://localhost:3000`
2. Enter file path: `./CodeCruncher/example.py`
3. Enter prompt: "Add error handling"
4. Click "AI Optimization"
5. Watch real-time logs

## ❗ Important Notes

### ✅ What Each Component Does

- **CLI:** Direct command-line code injection
- **Backend:** API server + WebSocket for real-time communication
- **Frontend:** Web interface that talks to the backend

### ❌ Common Misconceptions

- **Frontend is NOT a Python app** - It's React/TypeScript
- **Python HTTP server is NOT the frontend** - It just serves built React files
- **Backend and Frontend are separate** - They communicate via API

### 🔄 Development vs Production

**Development:**
- Frontend: `npm run dev` (Vite dev server with hot reload)
- Backend: `python app/backend_main.py`

**Production:**
- Frontend: `npm run build` then serve `dist/` folder
- Backend: Same as development

## 🆘 Troubleshooting

### Backend Issues
- **Port 8001 in use:** Change port in `app/backend_main.py`
- **Dependencies missing:** Run `pip install -r requirements.txt`

### Frontend Issues
- **Port 3000 in use:** Vite will automatically use next available port
- **Dependencies missing:** Run `npm install` in `frontend/` directory
- **Backend connection failed:** Check backend is running on port 8001

### CLI Issues
- **Command not found:** Run `pip install -e .` to install CLI
- **Import errors:** Ensure you're in the correct virtual environment

## 🎉 Success Indicators

✅ **CLI Working:** `codecrusher --version` shows version number
✅ **Backend Working:** `curl http://localhost:8001/health` returns JSON
✅ **Frontend Working:** Browser shows CodeCrusher dashboard at `http://localhost:3000`
✅ **Integration Working:** Frontend can send requests and receive real-time logs

---

**Need help?** Check the individual README files in each component directory for more detailed information.
