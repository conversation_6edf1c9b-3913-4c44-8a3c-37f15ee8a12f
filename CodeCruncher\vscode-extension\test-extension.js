#!/usr/bin/env node
/**
 * Simple test script to verify VS Code extension functionality
 * Run this after building the extension to check basic functionality
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing CodeCrusher VS Code Extension...\n');

// Test 1: Check if package.json has required fields
console.log('1. Checking package.json...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

  const requiredFields = ['name', 'version', 'engines', 'activationEvents', 'contributes'];
  const missingFields = requiredFields.filter(field => !packageJson[field]);

  if (missingFields.length > 0) {
    console.log('   ❌ Missing required fields:', missingFields.join(', '));
  } else {
    console.log('   ✅ All required fields present');
  }

  // Check for new CLI hook command
  const commands = packageJson.contributes?.commands || [];
  const hasInjectCommand = commands.some(cmd => cmd.command === 'codecrusher.inject');

  if (hasInjectCommand) {
    console.log('   ✅ CLI hook command "codecrusher.inject" found');
  } else {
    console.log('   ❌ CLI hook command "codecrusher.inject" missing');
  }

  // Check for workspace settings command
  const hasCopySettingsCommand = commands.some(cmd => cmd.command === 'codecrusher.copySettingsToGlobal');
  if (hasCopySettingsCommand) {
    console.log('   ✅ Workspace settings command "codecrusher.copySettingsToGlobal" found');
  } else {
    console.log('   ❌ Workspace settings command "codecrusher.copySettingsToGlobal" missing');
  }

  // Check activation events
  const activationEvents = packageJson.activationEvents || [];
  const hasInjectActivation = activationEvents.includes('onCommand:codecrusher.inject');

  if (hasInjectActivation) {
    console.log('   ✅ Activation event for CLI hook found');
  } else {
    console.log('   ❌ Activation event for CLI hook missing');
  }

  // Check for configuration settings
  const configuration = packageJson.contributes?.configuration?.properties || {};

  const hasDefaultModeSetting = configuration['codecrusher.defaultMode'];
  if (hasDefaultModeSetting) {
    console.log('   ✅ Configuration setting "codecrusher.defaultMode" found');
    if (hasDefaultModeSetting.enum && hasDefaultModeSetting.enum.includes('preview') && hasDefaultModeSetting.enum.includes('apply')) {
      console.log('   ✅ Preview/Apply enum values configured correctly');
    } else {
      console.log('   ❌ Preview/Apply enum values missing or incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.defaultMode" missing');
  }

  const hasDefaultModelSetting = configuration['codecrusher.defaultModel'];
  if (hasDefaultModelSetting) {
    console.log('   ✅ Configuration setting "codecrusher.defaultModel" found');
    const expectedModels = ['auto', 'mistral', 'gemma', 'mixtral', 'llama3-8b', 'llama3-70b'];
    const hasAllModels = expectedModels.every(model => hasDefaultModelSetting.enum?.includes(model));
    if (hasAllModels) {
      console.log('   ✅ All AI model enum values configured correctly');
    } else {
      console.log('   ❌ AI model enum values missing or incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.defaultModel" missing');
  }

  const hasFallbackSetting = configuration['codecrusher.enableFallback'];
  if (hasFallbackSetting) {
    console.log('   ✅ Configuration setting "codecrusher.enableFallback" found');
    if (hasFallbackSetting.type === 'boolean' && hasFallbackSetting.default === true) {
      console.log('   ✅ Fallback setting configured correctly (boolean, default: true)');
    } else {
      console.log('   ❌ Fallback setting configuration incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.enableFallback" missing');
  }

  const hasLogServerPortSetting = configuration['codecrusher.logServerPort'];
  if (hasLogServerPortSetting) {
    console.log('   ✅ Configuration setting "codecrusher.logServerPort" found');
    if (hasLogServerPortSetting.type === 'number' && hasLogServerPortSetting.default === 11434) {
      console.log('   ✅ Log server port setting configured correctly (number, default: 11434)');
    } else {
      console.log('   ❌ Log server port setting configuration incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.logServerPort" missing');
  }

  const hasAutoShowLogsSetting = configuration['codecrusher.autoShowLogs'];
  if (hasAutoShowLogsSetting) {
    console.log('   ✅ Configuration setting "codecrusher.autoShowLogs" found');
    if (hasAutoShowLogsSetting.type === 'boolean' && hasAutoShowLogsSetting.default === false) {
      console.log('   ✅ Auto show logs setting configured correctly (boolean, default: false)');
    } else {
      console.log('   ❌ Auto show logs setting configuration incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.autoShowLogs" missing');
  }

  const hasDashboardUrlSetting = configuration['codecrusher.dashboardUrl'];
  if (hasDashboardUrlSetting) {
    console.log('   ✅ Configuration setting "codecrusher.dashboardUrl" found');
    if (hasDashboardUrlSetting.type === 'string' && hasDashboardUrlSetting.default === 'http://localhost:8000') {
      console.log('   ✅ Dashboard URL setting configured correctly (string, default: "http://localhost:8000")');
    } else {
      console.log('   ❌ Dashboard URL setting configuration incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.dashboardUrl" missing');
  }

  const hasSyncIntervalSetting = configuration['codecrusher.syncInterval'];
  if (hasSyncIntervalSetting) {
    console.log('   ✅ Configuration setting "codecrusher.syncInterval" found');
    if (hasSyncIntervalSetting.type === 'number' && hasSyncIntervalSetting.default === 120) {
      console.log('   ✅ Sync interval setting configured correctly (number, default: 120)');
    } else {
      console.log('   ❌ Sync interval setting configuration incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.syncInterval" missing');
  }

  const hasEnableSettingsSyncSetting = configuration['codecrusher.enableSettingsSync'];
  if (hasEnableSettingsSyncSetting) {
    console.log('   ✅ Configuration setting "codecrusher.enableSettingsSync" found');
    if (hasEnableSettingsSyncSetting.type === 'boolean' && hasEnableSettingsSyncSetting.default === true) {
      console.log('   ✅ Enable settings sync setting configured correctly (boolean, default: true)');
    } else {
      console.log('   ❌ Enable settings sync setting configuration incorrect');
    }
  } else {
    console.log('   ❌ Configuration setting "codecrusher.enableSettingsSync" missing');
  }

} catch (error) {
  console.log('   ❌ Error reading package.json:', error.message);
}

// Test 2: Check if TypeScript compiled successfully
console.log('\n2. Checking compiled output...');
const outDir = 'out';
const mainFile = path.join(outDir, 'extension.js');

if (fs.existsSync(outDir)) {
  console.log('   ✅ Output directory exists');

  if (fs.existsSync(mainFile)) {
    console.log('   ✅ Main extension file compiled');

    // Check if the compiled file contains our new function
    try {
      const compiledContent = fs.readFileSync(mainFile, 'utf8');

      if (compiledContent.includes('injectWithPreview')) {
        console.log('   ✅ CLI hook function found in compiled output');
      } else {
        console.log('   ❌ CLI hook function missing from compiled output');
      }

      if (compiledContent.includes('executeCliCommand')) {
        console.log('   ✅ CLI execution function found in compiled output');
      } else {
        console.log('   ❌ CLI execution function missing from compiled output');
      }

    } catch (error) {
      console.log('   ❌ Error reading compiled file:', error.message);
    }
  } else {
    console.log('   ❌ Main extension file not found');
  }
} else {
  console.log('   ❌ Output directory not found - run "npm run compile" first');
}

// Test 3: Check media directory
console.log('\n3. Checking media assets...');
const mediaDir = 'media';

if (fs.existsSync(mediaDir)) {
  console.log('   ✅ Media directory exists');

  const mediaFiles = fs.readdirSync(mediaDir);
  console.log(`   📁 Media files: ${mediaFiles.join(', ')}`);
} else {
  console.log('   ❌ Media directory not found');
}

// Test 4: Check source files
console.log('\n4. Checking source files...');
const srcDir = 'src';
const extensionTs = path.join(srcDir, 'extension.ts');

if (fs.existsSync(extensionTs)) {
  console.log('   ✅ Main TypeScript source file exists');

  try {
    const sourceContent = fs.readFileSync(extensionTs, 'utf8');

    // Check for key functions
    const keyFunctions = [
      'injectWithPreview',
      'showLiveLogs',
      'getDefaultLogPanelHTML',
      'executeCliCommand',
      'showPreviewDocument',
      'showDiffViewer',
      'extractModifiedContentFromOutput',
      'applyChangesToFile',
      'reloadDocument',
      'CodeCrusherPreviewProvider',
      'CodeCrusherDiffContentProvider'
    ];

    keyFunctions.forEach(func => {
      if (sourceContent.includes(func)) {
        console.log(`   ✅ Function "${func}" found`);
      } else {
        console.log(`   ❌ Function "${func}" missing`);
      }
    });

    // Check for Preview/Apply toggle features
    const toggleFeatures = [
      'showQuickPick',
      'Preview changes (safe mode)',
      'Apply changes (overwrite file)',
      '--preview',
      '--apply'
    ];

    console.log('\n   🎯 Preview/Apply Toggle Features:');
    toggleFeatures.forEach(feature => {
      if (sourceContent.includes(feature)) {
        console.log(`   ✅ Feature "${feature}" found`);
      } else {
        console.log(`   ❌ Feature "${feature}" missing`);
      }
    });

    // Check for Diff Viewer features
    const diffFeatures = [
      'vscode.diff',
      'codecrusher-original',
      'codecrusher-modified',
      'Apply Changes',
      'View Raw Output',
      'Creating diff view',
      'registerTextDocumentContentProvider'
    ];

    console.log('\n   🔍 Diff Viewer Features:');
    diffFeatures.forEach(feature => {
      if (sourceContent.includes(feature)) {
        console.log(`   ✅ Feature "${feature}" found`);
      } else {
        console.log(`   ❌ Feature "${feature}" missing`);
      }
    });

    // Check for Model + Fallback Selector features
    const modelFallbackFeatures = [
      'Choose AI model for this injection',
      'CodeCrusher Model Selection',
      'Enable fallback to other models',
      'CodeCrusher Fallback Settings',
      '--model',
      '--fallback',
      'defaultModel',
      'defaultFallback',
      'modelOptions',
      'fallbackOptions',
      'auto',
      'mistral',
      'gemma',
      'mixtral',
      'llama3-8b',
      'llama3-70b'
    ];

    console.log('\n   🤖 Model + Fallback Selector Features:');
    modelFallbackFeatures.forEach(feature => {
      if (sourceContent.includes(feature)) {
        console.log(`   ✅ Feature "${feature}" found`);
      } else {
        console.log(`   ❌ Feature "${feature}" missing`);
      }
    });

    // Check for Live Logs WebSocket Panel features
    const liveLogsFeatures = [
      'showLiveLogs',
      'liveLogsPanel',
      'createWebviewPanel',
      'codecrusherLiveLogs',
      'CodeCrusher Live Logs',
      'logServerPort',
      'autoShowLogs',
      'webview.postMessage',
      'WebSocket',
      'ws://localhost'
    ];

    console.log('\n   📡 Live Logs WebSocket Panel Features:');
    liveLogsFeatures.forEach(feature => {
      if (sourceContent.includes(feature)) {
        console.log(`   ✅ Feature "${feature}" found`);
      } else {
        console.log(`   ❌ Feature "${feature}" missing`);
      }
    });

    // Check for Settings Sync features
    const settingsSyncFeatures = [
      'SettingsSync',
      'VSCodeSettings',
      'WorkspaceSettings',
      'pullRemoteSettings',
      'pushLocalSettings',
      'applyRemoteSettings',
      'getWorkspaceId',
      'getWorkspaceSettingsKey',
      'loadWorkspaceSettings',
      'getEffectiveSettings',
      'copySettingsToGlobal',
      'currentWorkspaceId',
      'workspace_id',
      'lastSyncTimestamp',
      'dashboardUrl',
      'enableSettingsSync',
      'syncInterval',
      'axios.get',
      'axios.post',
      '/api/settings/vscode',
      'workspaceState',
      'ConfigurationTarget.Workspace',
      'crypto.createHash',
      'sha256'
    ];

    console.log('\n   🔄 Settings Sync Features:');
    settingsSyncFeatures.forEach(feature => {
      if (sourceContent.includes(feature)) {
        console.log(`   ✅ Feature "${feature}" found`);
      } else {
        console.log(`   ❌ Feature "${feature}" missing`);
      }
    });

  } catch (error) {
    console.log('   ❌ Error reading source file:', error.message);
  }
} else {
  console.log('   ❌ Main TypeScript source file not found');
}

console.log('\n🎯 Test Summary:');
console.log('   - Run "npm install" to install dependencies');
console.log('   - Run "npm run compile" to build the extension');
console.log('   - Open VS Code and press F5 to test in Extension Development Host');
console.log('   - Right-click any code file and look for "Inject with CodeCrusher"');
console.log('\n✨ Extension scaffold complete!');
